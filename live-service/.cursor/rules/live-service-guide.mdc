---
description: 
globs: 
alwaysApply: false
---
# 直播服务项目开发指南

## 项目概述
本项目是基于Spring Boot的直播服务后端API，使用Java 21和Spring Boot 3.x架构。

## 核心架构组件

### 主要技术栈
- **Java 21** - 项目使用最新的Java版本
- **Spring Boot 3.x** - 主要Web框架
- **Spring Cloud** - 微服务架构支持  
- **MyBatis** - 数据持久层框架
- **MySQL** - 主数据库
- **Redis/Redisson** - 缓存和分布式锁
- **RabbitMQ** - 消息队列
- **XXL-Job** - 分布式任务调度
- **Dynamic ThreadPool** - 动态线程池管理
- **Spring State Machine** - 状态机

## 项目结构

### 主要模块结构
```
src/main/java/com/bbh/live/
├── LiveServiceApplication.java        # 主启动类
├── config/                           # 配置类
├── controller/                       # REST API控制器
├── service/                         # 业务服务层
├── dao/                            # 数据访问层
├── core/                           # 核心业务逻辑
├── handler/                        # 各类处理器
├── enums/                          # 枚举定义
├── constant/                       # 常量定义
├── util/                           # 工具类
├── feign/                          # Feign客户端
├── aspect/                         # AOP切面
├── thread/                         # 线程相关
└── statemachine/                   # 状态机相关
```

### 主要入口文件
- 主启动类: [LiveServiceApplication.java](mdc:src/main/java/com/bbh/live/LiveServiceApplication.java)
- Maven配置: [pom.xml](mdc:pom.xml)
- 主配置文件: [application.yaml](mdc:src/main/resources/application.yaml)

## 开发规范

### 包命名规范
- 所有类都应该在 `com.bbh.live` 包下
- 按功能模块组织包结构
- Controller层使用 `@RestController` 注解
- Service层使用 `@Service` 注解  
- DAO层使用 `@Mapper` 注解

### 配置管理
- 多环境配置文件:
  - `application-dev.yaml` - 开发环境
  - `application-test.yaml` - 测试环境  
  - `application-uat.yaml` - UAT环境
  - `application-prod.yaml` - 生产环境
  - `application-local.yaml` - 本地环境

### 注解使用规范
- 主类启用的关键注解:
  - `@EnableDiscoveryClient` - 服务发现
  - `@EnableAsync` - 异步处理
  - `@EnableDynamicTp` - 动态线程池
  - `@EnableRetry` - 重试机制
  - `@EnableAspectJAutoProxy` - AOP代理

## 常用开发模式

### REST API开发
- 所有API控制器放在 `controller` 包下
- 使用统一的响应格式
- 遵循RESTful设计原则

### 业务服务开发  
- 业务逻辑封装在 `service` 包下
- 复杂业务逻辑使用 `core` 包进行核心处理
- 使用 `handler` 包处理特定的业务处理器

### 数据访问开发
- DAO接口放在 `dao` 包下
- MyBatis Mapper XML文件放在 `resources/mapper` 目录
- SQL脚本放在 `resources/sql` 目录

### 配置类开发
- 所有配置类放在 `config` 包下
- 使用 `@Configuration` 注解标记配置类
- Bean命名使用自定义的 `LiveBeanNameGenerator`

## 特殊功能模块

### 状态机模块
- 状态机相关代码放在 `statemachine` 包下
- 使用Spring State Machine框架处理复杂状态转换

### 消息队列
- 使用RabbitMQ进行消息处理
- 相关配置在各环境配置文件中定义

### 分布式任务
- 使用XXL-Job进行分布式任务调度
- 任务相关代码应明确注释调度策略

### 缓存和锁
- 使用Redisson作为Redis客户端
- 分布式锁和缓存操作的工具类放在 `util` 包

## 开发最佳实践

### 代码质量
- 使用Lombok减少样板代码
- 遵循阿里巴巴Java开发规范
- 保持代码的可读性和可维护性

### 异常处理
- 使用统一的异常处理机制
- 自定义业务异常应继承合适的基类

### 日志记录
- 使用logback作为日志框架
- 日志配置文件: [logback.xml](mdc:src/main/resources/logback.xml)
- 生产日志输出到 `logs/` 目录

### 性能优化
- 使用动态线程池管理线程资源
- 合理使用缓存机制
- 数据库查询优化使用合适的索引

## 测试规范
- 单元测试放在 `src/test/java` 目录下
- 测试类命名遵循 `*Test` 模式
- 使用Spring Boot Test进行集成测试
