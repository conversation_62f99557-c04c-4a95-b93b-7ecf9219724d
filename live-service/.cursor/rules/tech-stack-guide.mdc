---
description: 
globs: 
alwaysApply: false
---
# 技术栈使用指南

## Spring Boot 3.x 配置

### 主要配置文件
- 主配置: [application.yaml](mdc:src/main/resources/application.yaml)
- 环境配置: [application-dev.yaml](mdc:src/main/resources/application-dev.yaml), [application-prod.yaml](mdc:src/main/resources/application-prod.yaml)
- 动态线程池配置: [application-dynamic-tp.yaml](mdc:src/main/resources/application-dynamic-tp.yaml)

### 核心注解配置
```java
@SpringBootApplication(
    scanBasePackages = {"com.bbh.live"}, 
    nameGenerator = LiveBeanNameGenerator.class
)
@EnableDiscoveryClient      // 服务发现
@EnableAsync               // 异步处理  
@EnableDynamicTp          // 动态线程池
@EnableRetry              // 重试机制
@EnableAspectJAutoProxy(exposeProxy = true) // AOP代理
```

## 数据库配置

### MySQL + MyBatis
- 使用Druid连接池
- 支持多数据源配置 (`dynamic-datasource-spring-boot3-starter`)
- Mapper XML文件位置: `resources/mapper/`

### 配置示例
```yaml
spring:
  datasource:
    dynamic:
      primary: master
      datasource:
        master:
          driver-class-name: com.mysql.cj.jdbc.Driver
          url: ***********************************
          username: ${DB_USERNAME}
          password: ${DB_PASSWORD}
```

## Redis配置 (Redisson)

### 主要用途
- 分布式缓存
- 分布式锁
- 会话存储

### 配置要点
- 使用 `redisson-spring-boot-starter`
- 支持集群和单机模式
- 配置连接池参数

## 消息队列 (RabbitMQ)

### 依赖配置
```xml
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-amqp</artifactId>
</dependency>
```

### 使用规范
- 消息生产者和消费者分离
- 使用死信队列处理失败消息
- 配置消息确认机制

## 微服务组件

### Spring Cloud OpenFeign
- 用于服务间调用
- Feign客户端放在 `feign` 包下
- 配置负载均衡策略

### 服务发现
- 使用 `@EnableDiscoveryClient` 启用
- 支持Nacos等注册中心

## 任务调度 (XXL-Job)

### 配置依赖
```xml
<dependency>
    <groupId>com.xuxueli</groupId>
    <artifactId>xxl-job-core</artifactId>
</dependency>
```

### 使用规范
- 任务类继承 `IJobHandler`
- 使用 `@XxlJob` 注解标记任务方法
- 配置任务执行器信息

## 动态线程池 (Dynamic ThreadPool)

### 配置文件
- 专用配置: [application-dynamic-tp.yaml](mdc:src/main/resources/application-dynamic-tp.yaml)
- 支持运行时动态调整线程池参数

### 使用方式
```java
@EnableDynamicTp
public class Application {
    // 启动类配置
}
```

## 状态机 (Spring State Machine)

### 依赖配置
```xml
<dependency>
    <groupId>org.springframework.statemachine</groupId>
    <artifactId>spring-statemachine-core</artifactId>
</dependency>
```

### 使用规范
- 状态机配置类放在 `statemachine` 包
- 定义状态枚举和事件枚举
- 配置状态转换规则

## 日志配置 (Logback)

### 配置文件
- 主配置: [logback.xml](mdc:src/main/resources/logback.xml)
- 支持多环境日志配置
- 日志文件输出到 `logs/` 目录

### 日志级别
- 开发环境: DEBUG
- 测试环境: INFO  
- 生产环境: WARN

## 缓存配置

### Spring Cache
- 使用Redis作为缓存存储
- 支持 `@Cacheable`, `@CacheEvict` 等注解
- 配置缓存过期策略

### 使用示例
```java
@Cacheable(value = "user", key = "#userId")
public User getUserById(Long userId) {
    // 方法实现
}
```

## 异步处理配置

### 线程池配置
- 使用 `@EnableAsync` 启用异步处理
- 配置自定义线程池
- 异步方法使用 `@Async` 注解

### 最佳实践
```java
@Async("taskExecutor")
public CompletableFuture<String> processAsync(String data) {
    // 异步处理逻辑
    return CompletableFuture.completedFuture(result);
}
```

## 监控和健康检查

### Actuator端点
- 启用必要的监控端点
- 配置健康检查指标
- 集成应用性能监控

### 配置示例
```yaml
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
```

## 安全配置

### 输入验证
- 使用Bean Validation
- 自定义校验注解
- 全局异常处理

### 权限控制
- 集成Spring Security
- 配置JWT认证
- API权限拦截

## 测试配置

### 测试依赖
```xml
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-test</artifactId>
    <scope>test</scope>
</dependency>
```

### 测试注解
- `@SpringBootTest` - 集成测试
- `@MockBean` - Mock依赖
- `@TestConfiguration` - 测试配置

## 构建和部署

### Maven配置
- 主配置: [pom.xml](mdc:pom.xml)
- 支持多环境Profile
- Java 21编译配置

### 环境Profile
- `dev` - 开发环境 (默认激活)
- `prod` - 生产环境
- 不同环境使用不同的core版本
