---
description: 
globs: 
alwaysApply: false
---
# 代码标准和开发风格指南

## Java编码规范

### 命名规范
- **类名**: 使用PascalCase命名法，如 `LiveServiceApplication`, `UserController`
- **方法名**: 使用camelCase命名法，如 `getUserInfo()`, `processLiveData()`  
- **变量名**: 使用camelCase命名法，如 `userId`, `liveRoomId`
- **常量名**: 使用UPPER_SNAKE_CASE，如 `MAX_RETRY_COUNT`, `DEFAULT_TIMEOUT`
- **包名**: 全小写，使用点分隔，如 `com.bbh.live.controller`

### 注解使用规范

#### Controller层
```java
@RestController
@RequestMapping("/api/live")
@Slf4j
@Validated
public class LiveController {
    // Controller实现
}
```

#### Service层  
```java
@Service
@Slf4j
@Transactional(rollbackFor = Exception.class)
public class LiveServiceImpl implements LiveService {
    // Service实现
}
```

#### DAO层
```java
@Mapper
public interface LiveMapper extends BaseMapper<Live> {
    // Mapper方法定义
}
```

## Spring Boot最佳实践

### 配置属性绑定
- 使用 `@ConfigurationProperties` 绑定配置属性
- 配置类放在 `config` 包下
- 使用 `@EnableConfigurationProperties` 启用配置

### 依赖注入
- 推荐使用构造器注入而非字段注入
- 使用 `@RequiredArgsConstructor` (Lombok) 简化构造器

### 异常处理
- 使用 `@ControllerAdvice` 全局异常处理
- 自定义业务异常继承 `RuntimeException`
- 返回统一的错误响应格式

## 数据库和持久层规范

### MyBatis使用规范
- Mapper接口放在 `dao` 包下
- XML映射文件放在 `resources/mapper` 目录
- 使用 `@Param` 注解多参数方法
- SQL语句使用参数化查询防止SQL注入

### 事务管理
- Service层方法使用 `@Transactional` 注解
- 明确指定 `rollbackFor = Exception.class`
- 读操作使用 `@Transactional(readOnly = true)`

## 日志规范

### 日志级别使用
- **ERROR**: 系统错误，需要立即关注
- **WARN**: 警告信息，需要注意但不影响系统运行  
- **INFO**: 重要的业务流程信息
- **DEBUG**: 调试信息，生产环境不输出

### 日志格式
```java
// 正确的日志使用方式
log.info("用户[{}]开始直播，房间ID[{}]", userId, roomId);
log.error("处理直播数据失败，用户ID[{}]", userId, e);

// 避免使用字符串拼接
log.info("用户" + userId + "开始直播"); // ❌ 错误方式
```

## API设计规范

### RESTful API设计
- GET请求用于查询数据
- POST请求用于创建资源
- PUT请求用于更新资源  
- DELETE请求用于删除资源

### 请求响应格式
```java
// 统一响应格式
public class ApiResponse<T> {
    private Integer code;
    private String message;
    private T data;
    private Long timestamp;
}
```

### 参数校验
- 使用Bean Validation注解进行参数校验
- Controller层使用 `@Validated` 注解
- 自定义校验注解放在合适的包下

## 性能优化规范

### 缓存使用
- 使用 `@Cacheable`, `@CacheEvict` 注解管理缓存
- 缓存key设计遵循命名规范
- 设置合适的缓存过期时间

### 异步处理
- 使用 `@Async` 注解进行异步处理
- 配置专用的线程池
- 异步方法返回 `CompletableFuture`

### 数据库优化
- 避免N+1查询问题
- 使用合适的分页查询
- 建立必要的数据库索引

## 代码质量要求

### 代码复用
- 提取公共方法到util包
- 使用设计模式解决通用问题
- 避免重复代码(DRY原则)

### 代码可读性
- 方法长度控制在50行以内
- 类长度控制在500行以内  
- 使用有意义的变量名和方法名
- 添加必要的注释说明复杂逻辑

### 单元测试
- 核心业务方法必须有单元测试
- 测试覆盖率不低于70%
- 使用 `@MockBean` 模拟外部依赖
- 测试方法命名清晰表达测试意图

## 安全规范

### 输入验证
- 所有外部输入必须进行校验
- 使用白名单方式过滤输入
- 防止XSS和SQL注入攻击

### 权限控制  
- 使用Spring Security进行权限管理
- API接口添加适当的权限检查
- 敏感操作添加操作日志

## Git提交规范

### 提交信息格式
```
type(scope): subject

body

footer
```

### 提交类型
- `feat`: 新功能
- `fix`: 修复bug
- `docs`: 文档更新
- `style`: 代码格式调整
- `refactor`: 代码重构
- `test`: 添加测试
- `chore`: 构建过程或辅助工具变动
