package com.bbh.live.core.util;

import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;

/**
 * 飞书机器人工具类
 */
public class RobotMsgExUtil {

    // 发送地址
    public static String url = "https://open.feishu.cn/open-apis/bot/v2/hook/";

    /**
     * @param key 机器人key
     * @param msg 发送消息内容
     * @描述 发送文本信息1
     */
    public static void sendTextMsg(String key, String msg) {
        JSONObject b = new JSONObject();
        b.set("text", msg);
        JSONObject a = new JSONObject();
        a.set("msg_type", "text");
        a.set("content", b);
        HttpUtil.post(url + key, a.toString());
    }

}
