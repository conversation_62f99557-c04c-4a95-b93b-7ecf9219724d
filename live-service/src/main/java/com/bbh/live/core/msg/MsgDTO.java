package com.bbh.live.core.msg;

import lombok.Data;

import java.util.Set;

/**
 * <AUTHOR>
 * @Date 2024/7/17
 * @description:
 */
@Data
public class MsgDTO<T> {

    /**
     * 自动生成msgId 请勿设置
     */
    private String msgId;

    /***
     *  消息渠道 是直播间还是图文拍卖
     */
    private String msgChannel;

    /***
     * 消息类型
     */
    private String msgType;

    /***
     * 发送人userId
     */
    private Long senderUserId;

    /***
     * 发送人席位id
     */
    private Long senderSeatId;

    /***
     * 目标聊天室id
     */
    private Set<String> chatRoomIds;

    /**
     * 直播间ids
     */
    private Set<Long> liveRoomIds;

    /**
     * 业务字段
     */
    T data;


}
