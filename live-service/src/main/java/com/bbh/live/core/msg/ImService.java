package com.bbh.live.core.msg;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.json.JSONUtil;
import com.bbh.live.util.JSONUtils;
import com.bbh.util.LogExUtil;
import io.rong.RongCloud;
import io.rong.messages.TxtMessage;
import io.rong.models.chatroom.ChatroomDataModel;
import io.rong.models.chatroom.ChatroomMember;
import io.rong.models.chatroom.ChatroomModel;
import io.rong.models.message.ChatroomMessage;
import io.rong.models.response.*;
import io.rong.models.user.UserModel;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @Date 2024/7/18
 * @description:
 */
@Component
@AllArgsConstructor
@Slf4j
public class ImService {
    private final RongCloud rongCloud;

    /***
     * @param msgDTO 消息体
     */
    public <T> void sendChatroomTxtMessage(@NotNull MsgDTO<T> msgDTO) {
        LocalDateTime now = LocalDateTime.now();
        String msgId = LocalDateTimeUtil.format(now, DatePattern.PURE_DATETIME_MS_FORMATTER) + RandomUtil.randomNumbers(4);
        msgDTO.setMsgId(msgId);
        String msgJsonStr = JSONUtils.toUnderlineJsonStr(msgDTO);
        TxtMessage tm = new TxtMessage(msgJsonStr, msgDTO.getMsgType());
        ChatroomMessage chatroomMessage = new ChatroomMessage();
        if (msgDTO.getSenderUserId() != null) {
            chatroomMessage.setSenderId(msgDTO.getSenderUserId().toString());
        }
        chatroomMessage.setIsIncludeSender(1);
        chatroomMessage.setTargetId(msgDTO.getChatRoomIds().toArray(String[]::new));
        chatroomMessage.setObjectName(msgDTO.getMsgType());
        chatroomMessage.setContent(tm);
        try {
            var startTime = System.currentTimeMillis();
            log.info("发送融云消息Id:{},消息体：\n{}", msgDTO.getMsgId(), JSONUtil.toJsonStr(chatroomMessage));
            ResponseResult chatroomResult = this.rongCloud.message.chatroom.send(chatroomMessage);
            log.info("融云消息返回Id:{},消息体：{}, 耗时：{} ms", msgId, chatroomResult.toString(), System.currentTimeMillis() - startTime);
        } catch (Exception e) {
            LogExUtil.errorLog("融云消息发送失败", e);
        }
    }


    /**
     * 创建一个保持活跃的聊天室。
     * 该方法通过调用RongCloud SDK的接口来创建一个聊天室，设置聊天室为不会自动销毁的模式，并定义了聊天室的销毁时间。
     *
     * @param chatroomId 聊天室的唯一标识ID。
     * @return 如果聊天室创建成功，返回创建结果的字符串表示；如果创建失败，返回null。
     */
    public String createChatRoom(String chatroomId) {
        ChatroomDataModel chatroomDataModel = new ChatroomDataModel();
        chatroomDataModel.setId(chatroomId);
        chatroomDataModel.setDestroyType(0);
        chatroomDataModel.setDestroyTime(24 * 60);
        ResponseResult result;
        try {
            log.info("创建聊天室参数：{}", JSONUtil.toJsonStr(chatroomDataModel));
            result = rongCloud.chatroom.createV2(chatroomDataModel);
            log.info("创建聊天室结果：{}", result.toString());
            return result.toString();
        } catch (Exception var6) {
            LogExUtil.errorLog("创建聊天室失败", var6);
        }
        return null;
    }

    /**
     * 聊天室 1 小时内无人说话，同时没有人加入聊天室时，即时通讯服务端会自动把聊天室内所有成员踢出聊天室并销毁聊天室。<br>
     * 聊天室保活功能，可以确保聊天室在此状态下不被自动销毁，只能通过调用 API 接口销毁聊天室。<br>
     * <br>
     * 使用聊天室保活功能前，请确认已为当前 App Key 开通相关服务。开通后可设置 5 个聊天室为保活状态，如需要多个请联系商务。<br>
     * <a href="https://doc.rongcloud.cn/imserver/server/v1/chatroom/add-to-keep-alive">API保活文档</a>
     * @param chatroomId 聊天室ID
     */
    public void keepLiveChatroom(String chatroomId) {
        ChatroomModel chatroomModel = (new ChatroomModel()).setId(chatroomId);
        try {
            rongCloud.chatroom.keepalive.add(chatroomModel);
        } catch (Exception var5) {
            LogExUtil.errorLog("keepLiveChatroom", var5);
        }
    }

    /***
     * 创建全量聊天室并保活
     * @param chatroomId
     */
    public void createChatRoomKeepLive(String chatroomId) {
        ChatroomModel chatroomModel = new ChatroomModel().setId(chatroomId);
        Integer queryCode=null;
        try {
            ChatroomQueryResult query = rongCloud.chatroom.query(chatroomModel);
            queryCode = query.getCode();
        } catch (Exception e) {
            LogExUtil.errorLog("查询聊天室失败", e);
        }
        if (!Integer.valueOf(200).equals(queryCode)){
            createChatRoom(chatroomId);
            keepLiveChatroom(chatroomId);
        }

    }





    public ChatroomUserQueryResult getChatroomInfo(String chatroomId, int count) {
        ChatroomModel chatroomModel = (new ChatroomModel()).setId(chatroomId).setCount(count).setOrder(1);
        ChatroomUserQueryResult chatroomQueryUserResult = null;

        try {
            chatroomQueryUserResult = rongCloud.chatroom.get(chatroomModel);
        } catch (Exception var6) {
            LogExUtil.errorLog("getChatroomInfo", var6);
        }

        return chatroomQueryUserResult;
    }


    public Boolean checkChatroomUserResult(String uid, String chatroomId) {
        ChatroomMember member = (new ChatroomMember()).setId(uid).setChatroomId(chatroomId);
        CheckChatRoomUserResult checkMemberResult;

        try {
            checkMemberResult = rongCloud.chatroom.isExist(member);
            return checkMemberResult.isInChrm;
        } catch (Exception var6) {
            LogExUtil.errorLog("checkChatroomUserResult", var6);
        }
        return false;

    }

    /***
     * 查询聊天室信息
     * @param chatRoomId 聊天室ID
     */
    public ChatroomQueryResult queryChatroom(String chatRoomId) throws Exception {
        ChatroomModel chatroomModel = new ChatroomModel().setId(chatRoomId);
        return rongCloud.chatroom.query(chatroomModel);
    }

    /**
     * 用户注册，生成token
     */
    public TokenResult register(String userId, String name, String avatar) throws Exception {
        UserModel userModel = new UserModel().setId(userId).setName(name).setPortrait(avatar);
        return rongCloud.user.register(userModel);
    }
}
