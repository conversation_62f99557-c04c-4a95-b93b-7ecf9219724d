package com.bbh.live.core.msg;

/**
 * <AUTHOR>
 * @Date 2024/7/17
 * @description: im消息类型
 */
public interface MsgType {

    /**
     * 平台公告，后台发送的公告
     */
    String PLATFORM_NOTICE = "platformNotice";

    /**
     * 系统公告，看播进入直播间后自动发
     */
    String SYSTEM_NOTICE = "systemNotice";

    /**
     * 普通文本消息
     */
    String TEXT = "text";

    /**
     * 表情包
     */
    String MEME = "meme";

    /***
     * 用户进入直播间
     */
    String USER_ENTER = "userEnter";

    /**
     * 上架讲解
     */
    String PUTAWAY = "putaway";

    /**
     * 出价成功
     */
    String BID_SUCCESS = "bidSuccess";

    /***
     * 结束直播
     */
    String END_LIVE = "endLive";

    /**
     * 开始直播
     */
    String START_LIVE = "startLive";

    /***
     *  撤回
     */
    String REVOKE = "revoke";

    /***
     * 竞价失败(流拍)
     */
    String AUCTION_ABORTED = "auctionAborted";

    /***
     * 中拍
     */
    String AUCTION_SUCCESS = "auctionSuccess";

    /**
     * 发起传送
     */
    String TRANSFER = "transfer";

    /**
     * 传送关闭/传送取消
     */
    String TRANSFER_CLOSE = "transferClose";

    /**
     * 传送同意
     */
    String TRANSFER_AGREE = "transferAgree";

    /**
     * 直播间公告更新
     */
    String CHANGE_NOTICE = "changeNotice";
    
    /**
     * 添加黑名单踢出直播间
     */
    String USER_OUT_NOW = "userOutNow";

    /** 用户被禁言 */
    String USER_CHAT_BANNED = "userChatBanned";
    /** 已解禁 */
    String USER_CHAT_UNBANNED = "userChatUnbanned";

    /**
     * 移出白名单踢出直播间
     */
    String USER_OUT_NOW_WHITE = "userOutNowWhite";

    /**
     * 开始竞拍
     */
    String AUCTION_BID_START = "auctionBidStart";

    /**
     * 直播间议价消息
     */
    String BARGAIN = "bargain";

    /**
     * 直播间拒绝议价消息
     */
    String BARGAIN_REJECT = "bargainReject";

    /**
     * 直播间议价通过
     */
    String BARGAIN_AGREED = "bargainAgreed";

    /**
     * 商品预约提醒的消息
     */
    String GOODS_SUBSCRIBE = "goodsSubscribe";

    /**
     * 商品预约提醒的消息关闭
     */
    String GOODS_SUBSCRIBE_CLOSED = "goodsSubscribeClosed";

    /**
     * 开启试播消息
     */
    String TEST_BROADCAST_BEGIN = "testBroadcastBegin";

    /**
     * 结束试播消息
     */
    String TEST_BROADCAST_END = "testBroadcastEnd";

    /**
     * 添加商品
     */
    String GOODS_INSERT = "goodsInsert";

    /**
     * 修改商品
     */
    String GOODS_UPDATE = "goodsUpdate";

    /**
     * 删除商品
     */
    String GOODS_DELETE = "goodsDelete";

    /**
     * 直播间中断
     */
    String LIVE_INTERRUPT = "liveInterrupt";

    /**
     * 直播间开始倒计时
     */
    String LIVE_COUNTDOWN_START = "liveCountdownStart";

    /**
     * 直播间关闭倒计时
     */
    String LIVE_COUNTDOWN_CLOSED = "liveCountdownClosed";

    /** XX关注了商家 */
    String ORG_ATTENTION = "orgAttention";

    /** 秒杀抢购开始（一口价） */
    String SEC_KILL_START = "secKillStart";
}
