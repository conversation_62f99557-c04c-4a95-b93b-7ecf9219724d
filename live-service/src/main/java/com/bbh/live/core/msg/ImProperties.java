package com.bbh.live.core.msg;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @Date 2024/7/17
 * @description:
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "rong-yun")
public class ImProperties {

    private String appKey;

    private String appSecret;

    /***
     * 虎头帮直播间聊天室渠道前缀 live_room
     */
    public static final String LIVE_ROOM_CHAT_ROOM_ID_PREFIX="htbLr";
    /***
     * 虎头帮直播间聊天室全量直播间id
     */
    public static final String LIVE_ROOM_CHAT_ROOM_FULL_INFO="htbLrFi";

    /***
     * 虎头帮图文拍聊天室渠道前缀  pt_auction_activity
     */
    public static final String PT_AUCTION_ACTIVITY_CHAT_ROOM_ID_PREFIX="htbAa";

    /***
     * 虎头帮图文拍聊天室全量直播间id
     */
    public static final String PT_AUCTION_ACTIVITY_CHAT_ROOM="htbAaFi";


}
