package com.bbh.live.core.msg;

import io.rong.RongCloud;
import lombok.AllArgsConstructor;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024/7/22 下午3:34
 */
@Component
@AllArgsConstructor
public class RongYunConfig {

    private final ImProperties imProperties;

    @Bean
    public RongCloud initRongCloud() {
        return RongCloud.getInstance(imProperties.getAppKey(), imProperties.getAppSecret());
    }
}
