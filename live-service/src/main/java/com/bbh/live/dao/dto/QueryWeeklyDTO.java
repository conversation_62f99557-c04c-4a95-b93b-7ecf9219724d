package com.bbh.live.dao.dto;

import com.bbh.base.PageBase;
import com.bbh.util.AssertUtil;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 行情周报的请求参数
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class QueryWeeklyDTO extends PageBase {


    /**
     * 第几周
     */
    private Integer week;

    /**
     * 年
     */
    private Integer year;

    /**
     * 月
     */
    private Integer month;

    /**
     * 是否查询历史
     */
    private Boolean ifHistory = false;

    public void check(){
        AssertUtil.assertTrue(week != null && week > 0, "参数非法：周数无效");
        AssertUtil.assertTrue(year != null && year > 0, "参数非法：年份无效");
        AssertUtil.assertTrue(month != null && month > 0, "参数非法：月份无效");
    }
}
