package com.bbh.live.dao.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.bbh.model.GlobalOrder;
import com.bbh.model.GlobalOrderGoodsSnapshot;

/**
* <AUTHOR>
* @description 针对表【global_order_goods_snapshot(订单商品快照)】的数据库操作Service
* @createDate 2024-09-02 18:50:46
*/
public interface GlobalOrderGoodsSnapshotService extends IService<GlobalOrderGoodsSnapshot> {

    /**
     * 保存订单商品快照
     * @param globalOrder
     */
    void saveGoodsSnapshot(GlobalOrder globalOrder);
}
