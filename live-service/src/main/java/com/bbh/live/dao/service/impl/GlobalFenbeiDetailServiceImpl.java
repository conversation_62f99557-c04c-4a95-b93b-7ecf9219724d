package com.bbh.live.dao.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bbh.enums.FenbeiDetailEventEnum;
import com.bbh.enums.GlobalFenbeiDetailOperateSourceEnum;
import com.bbh.enums.GlobalFenbeiDetailPayWayEnum;
import com.bbh.enums.GlobalFenbeiDetailTargetTypeEnum;
import com.bbh.live.dao.dto.BaseSeatDTO;
import com.bbh.live.dao.dto.FenbeiOfflineTransferRemarkDTO;
import com.bbh.live.dao.mapper.GlobalFenbeiDetailMapper;
import com.bbh.live.dao.service.GlobalFenbeiDetailService;
import com.bbh.live.dao.service.GlobalOrgSeatService;
import com.bbh.live.enums.FenbeiOfflineTransferStateEnum;
import com.bbh.live.service.buyer.vip.vo.UserBuyerVipInfoVO;
import com.bbh.live.service.order.dto.OrderBuilderContext;
import com.bbh.live.service.vipdeduction.GlobalVipDeductionLogService;
import com.bbh.live.service.vipdeduction.GlobalVipDeductionService;
import com.bbh.model.*;
import com.bbh.util.LogExUtil;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【global_fenbei_detail(分贝明细)】的数据库操作Service实现
 * @createDate 2024-08-15 10:18:26
 */
@Service
@AllArgsConstructor
public class GlobalFenbeiDetailServiceImpl extends ServiceImpl<GlobalFenbeiDetailMapper, GlobalFenbeiDetail> implements GlobalFenbeiDetailService {

    private final GlobalVipDeductionService globalVipDeductionService;
    private final GlobalVipDeductionLogService globalVipDeductionLogService;
    private final GlobalOrgSeatService globalOrgSeatService;

    @Override
    public void savePayFenbeiDetail(GlobalVirtualGoodsOrder order, Integer orgFb) {
        GlobalFenbeiDetail fenbeiDetail = new GlobalFenbeiDetail();
        fenbeiDetail.setOrgId(order.getOrgId());
        fenbeiDetail.setAccountBalance(orgFb + order.getGoodsNumber());
        fenbeiDetail.setChangeNumber(order.getGoodsNumber());
        // app 充值 事件
        FenbeiDetailEventEnum eventEnum = FenbeiDetailEventEnum.APP_RECHARGE;
        fenbeiDetail.setType(eventEnum.getType());
        fenbeiDetail.setEvent(eventEnum.getEvent());
        fenbeiDetail.setEventDescribe(eventEnum.getEventDescribe());
        fenbeiDetail.setRemark("分贝充值，商品名称：" + order.getGoodsName());
        fenbeiDetail.setPayMoney(order.getPayPrice());
        fenbeiDetail.setPayWay(transferPayWay(order.getPayType()));
        fenbeiDetail.setPayCode(order.getOrderNo());
        fenbeiDetail.setTargetType(GlobalFenbeiDetailTargetTypeEnum.APP_PAY);
        fenbeiDetail.setTargetId(order.getId().toString());
        fenbeiDetail.setOperateSource(GlobalFenbeiDetailOperateSourceEnum.APP_BBH);
        fenbeiDetail.setCreateSeatId(order.getBuyerSeatId());
        fenbeiDetail.setCreateUserId(order.getBuyerUserId());
        this.save(fenbeiDetail);
    }

    private GlobalFenbeiDetailPayWayEnum transferPayWay(String payType) {
        return switch (payType) {
            case "WX_PAY" -> GlobalFenbeiDetailPayWayEnum.WECHAT;
            case "ALI_PAY" -> GlobalFenbeiDetailPayWayEnum.ALIPAY;
            case "BANK_PAY" -> GlobalFenbeiDetailPayWayEnum.BANK_CARD;
            default -> null;
        };
    }

    @Override
    public void saveSignFenbeiDetail(Integer changeNumber, Integer orgFb, Integer consecutiveSignDaysThisWeek) {
        GlobalFenbeiDetail globalFenbeiDetail = new GlobalFenbeiDetail();
        globalFenbeiDetail.setAccountBalance(orgFb + changeNumber);
        globalFenbeiDetail.setChangeNumber(changeNumber);
        FenbeiDetailEventEnum eventEnum = FenbeiDetailEventEnum.CHECK_IN_REWARD;
        globalFenbeiDetail.setType(eventEnum.getType());
        globalFenbeiDetail.setEvent(eventEnum.getEvent());
        globalFenbeiDetail.setEventDescribe(eventEnum.getEventDescribe());
        globalFenbeiDetail.setRemark("签到奖励, 连续签到天数" + consecutiveSignDaysThisWeek);
        globalFenbeiDetail.setTargetType(GlobalFenbeiDetailTargetTypeEnum.FENBEI_SHOP_GOODS);
        globalFenbeiDetail.setOperateSource(GlobalFenbeiDetailOperateSourceEnum.APP_BBH);
        this.save(globalFenbeiDetail);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void savePayDeductFenbeiDetail(List<GlobalOrderItem> orderItems, Integer orgFb) {

        if (CollectionUtil.isEmpty(orderItems)) {
            return;
        }
        List<GlobalFenbeiDetail> fenbeiDetails = new ArrayList<>();

        FenbeiDetailEventEnum eventEnum = FenbeiDetailEventEnum.PAYMENT_DEDUCTION;
        for (GlobalOrderItem orderItem : orderItems) {
            GlobalFenbeiDetail fenbeiDetail = new GlobalFenbeiDetail();
            fenbeiDetail.setOrgId(orderItem.getBuyerOrgId());
            fenbeiDetail.setAccountBalance(orgFb - orderItem.getFenbeiDeductionCount());
            fenbeiDetail.setChangeNumber(orderItem.getFenbeiDeductionCount());
            fenbeiDetail.setType(eventEnum.getType());
            fenbeiDetail.setEvent(eventEnum.getEvent());
            fenbeiDetail.setEventDescribe(eventEnum.getEventDescribe(orderItem.getTargetId()));
            // 支付抵扣
            fenbeiDetail.setTargetType(GlobalFenbeiDetailTargetTypeEnum.PAYMENT_DEDUCTION);
            // 订单明细 id
            fenbeiDetail.setTargetId(orderItem.getId().toString());
            fenbeiDetail.setCreateSeatId(orderItem.getBuyerSeatId());
            fenbeiDetail.setCreateUserId(orderItem.getBuyerUserId());
            orgFb = orgFb - orderItem.getFenbeiDeductionCount();
            fenbeiDetails.add(fenbeiDetail);
        }
        this.saveBatch(fenbeiDetails);
    }

    /**
     * 保存线下转账分贝明细
     *
     * @param changeNumber 变动数量
     * @param orgOldFb     商户原来的分贝
     * @param order        订单
     */
    @Override
    public void saveTransferFenbeiDetail(Integer changeNumber, Integer orgOldFb, GlobalOrder order, UserBuyerVipInfoVO vipInfo) {
        GlobalFenbeiDetail globalFenbeiDetail = new GlobalFenbeiDetail();
        globalFenbeiDetail.setOrgId(order.getBuyerOrgId());
        globalFenbeiDetail.setAccountBalance(orgOldFb - changeNumber);
        globalFenbeiDetail.setChangeNumber(changeNumber);
        FenbeiDetailEventEnum eventEnum = FenbeiDetailEventEnum.OFFLINE_TRANSFER_FEE;
        globalFenbeiDetail.setType(eventEnum.getType());
        globalFenbeiDetail.setEvent(eventEnum.getEvent());
        globalFenbeiDetail.setEventDescribe(eventEnum.getEventDescribe());
        globalFenbeiDetail.setTargetType(GlobalFenbeiDetailTargetTypeEnum.PAYMENT_DEDUCTION);
        globalFenbeiDetail.setTargetId(order.getId().toString());
        globalFenbeiDetail.setPayCode(order.getId().toString());
        globalFenbeiDetail.setPayMoney(order.getNeedPayAmount());
        globalFenbeiDetail.setOperateSource(GlobalFenbeiDetailOperateSourceEnum.APP_BBH);
        // 线下转账：10-已扣除，20-已返还
        globalFenbeiDetail.setPayState(FenbeiOfflineTransferStateEnum.DEDUCTED.getCode());
        // 备注当前的会员信息，如果要返还需要用到
        FenbeiOfflineTransferRemarkDTO remark = new FenbeiOfflineTransferRemarkDTO();
        remark.setVipId(vipInfo == null ? 0 : vipInfo.getId() == null ? 0 : vipInfo.getId()).setIsVip(vipInfo != null && vipInfo.getIsVip());
        globalFenbeiDetail.setRemark(JSONUtil.toJsonStr(remark));
        // 执行保存
        this.save(globalFenbeiDetail);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveCancelPayOrderFenbeiDetail(List<GlobalOrderItem> orderItems, Integer orgFb) {
        if (CollectionUtil.isEmpty(orderItems)) {
            return;
        }
        List<GlobalFenbeiDetail> fenbeiDetails = new ArrayList<>();

        FenbeiDetailEventEnum eventEnum = FenbeiDetailEventEnum.ORDER_CANCEL;
        for (GlobalOrderItem orderItem : orderItems) {
            GlobalFenbeiDetail fenbeiDetail = new GlobalFenbeiDetail();
            fenbeiDetail.setOrgId(orderItem.getBuyerOrgId());
            fenbeiDetail.setAccountBalance(orgFb + orderItem.getFenbeiDeductionCount());
            fenbeiDetail.setChangeNumber(orderItem.getFenbeiDeductionCount());
            fenbeiDetail.setType(eventEnum.getType());
            fenbeiDetail.setEvent(eventEnum.getEvent());
            fenbeiDetail.setEventDescribe(eventEnum.getEventDescribe());
            fenbeiDetail.setTargetType(GlobalFenbeiDetailTargetTypeEnum.PAYMENT_DEDUCTION);
            fenbeiDetail.setTargetId(orderItem.getId().toString());
            fenbeiDetail.setCreateSeatId(orderItem.getBuyerSeatId());
            fenbeiDetail.setCreateUserId(orderItem.getBuyerUserId());
            fenbeiDetails.add(fenbeiDetail);
        }
        this.saveBatch(fenbeiDetails);
    }

    /**
     * 取消支付
     *
     * @param orgOldFb 商户原来的分贝
     * @param order    订单
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public FenbeiOfflineTransferRemarkDTO saveCancelPayOrderFenbeiDetail(Integer orgOldFb, GlobalOrder order) {
        // 找到上一笔扣除未返还的记录，更新状态
        GlobalFenbeiDetail old = getOne(Wrappers.lambdaQuery(GlobalFenbeiDetail.class).eq(GlobalFenbeiDetail::getPayCode, order.getId()).eq(GlobalFenbeiDetail::getPayState, FenbeiOfflineTransferStateEnum.DEDUCTED.getCode()).orderByDesc(GlobalFenbeiDetail::getId).last("limit 1"));
        // null说明之前没扣除
        if (old == null) {
            return new FenbeiOfflineTransferRemarkDTO().setChangeNumber(0);
        }

        // 更新上一笔的状态是已返还
        LambdaUpdateWrapper<GlobalFenbeiDetail> updateWrapper = Wrappers.lambdaUpdate(GlobalFenbeiDetail.class);
        updateWrapper.eq(GlobalFenbeiDetail::getId, old.getId());
        updateWrapper.set(GlobalFenbeiDetail::getPayState, FenbeiOfflineTransferStateEnum.RETURNED.getCode());
        update(updateWrapper);
        int changeNumber = old.getChangeNumber();

        // 再保存一笔返还的记录
        GlobalFenbeiDetail globalFenbeiDetail = new GlobalFenbeiDetail();
        globalFenbeiDetail.setOrgId(order.getBuyerOrgId());
        globalFenbeiDetail.setAccountBalance(orgOldFb + changeNumber);
        globalFenbeiDetail.setChangeNumber(changeNumber);
        globalFenbeiDetail.setType(FenbeiDetailEventEnum.ORDER_CANCEL.getType());
        globalFenbeiDetail.setEvent(FenbeiDetailEventEnum.ORDER_CANCEL.getEvent());
        globalFenbeiDetail.setEventDescribe(FenbeiDetailEventEnum.ORDER_CANCEL.getEventDescribe());
        globalFenbeiDetail.setTargetType(GlobalFenbeiDetailTargetTypeEnum.PAYMENT_DEDUCTION);
        globalFenbeiDetail.setTargetId(order.getId().toString());
        globalFenbeiDetail.setPayCode(order.getId().toString());
        globalFenbeiDetail.setPayMoney(order.getNeedPayAmount());
        globalFenbeiDetail.setOperateSource(GlobalFenbeiDetailOperateSourceEnum.APP_BBH);
        // 线下转账：10-已扣除，20-已返还
        globalFenbeiDetail.setPayState(FenbeiOfflineTransferStateEnum.RETURNED.getCode());
        globalFenbeiDetail.setCreateSeatId(order.getCreateSeatId());
        globalFenbeiDetail.setCreateUserId(order.getCreateUserId());
        this.save(globalFenbeiDetail);

        // 返回要补回去的金额
        String remark = old.getRemark();
        return JSONUtil.toBean(remark, FenbeiOfflineTransferRemarkDTO.class).setChangeNumber(changeNumber);
    }

    @Override
    public void saveTransferVipDetail(BigDecimal totalChannelAmount, BigDecimal addVipDeduction, GlobalOrder order, OrderBuilderContext context, Long vipDeductionId) {
        BaseSeatDTO seatInfoBySeatId = globalOrgSeatService.getSeatInfoBySeatId(order.getBuyerSeatId());
        GlobalVipDeduction deduction = globalVipDeductionService.getById(vipDeductionId);

        GlobalVipDeductionLog gLog = new GlobalVipDeductionLog();
        gLog.setOrgId(order.getBuyerOrgId());
        gLog.setOldDeduction(NumberUtil.sub(deduction.getVipDeduction(), deduction.getUsedVipDeduction()));
        gLog.setNewDeduction(NumberUtil.sub(deduction.getVipDeduction(), deduction.getUsedVipDeduction(), totalChannelAmount));
        gLog.setDeduction(totalChannelAmount);
        gLog.setVipDeductionId(vipDeductionId);
        gLog.setPayOsn(context.getOrderNo());
        LogExUtil.warnLog("插入 2 --订单流水号：{}", context.getOrderNo());
        gLog.setGoodsNum(order.getGoodsNum());
        gLog.setCreateName(seatInfoBySeatId.getNickName());
        gLog.setCreatedAt(DateUtil.date());
        gLog.setUserId(seatInfoBySeatId.getUserId());
        globalVipDeductionLogService.save(gLog);
        // 再扣除
        globalVipDeductionService.update(Wrappers.lambdaUpdate(GlobalVipDeduction.class).setIncrBy(GlobalVipDeduction::getUsedVipDeduction, totalChannelAmount).set(GlobalVipDeduction::getUpdatedAt, DateUtil.date()).eq(GlobalVipDeduction::getId, vipDeductionId));

    }
}
