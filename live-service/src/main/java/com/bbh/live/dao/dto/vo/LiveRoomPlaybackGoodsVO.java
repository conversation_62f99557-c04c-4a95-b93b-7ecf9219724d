package com.bbh.live.dao.dto.vo;

import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR> dsy
 * @Date: 2024/8/29
 * @Description:
 */
@Data
@Accessors(chain = true)
public class LiveRoomPlaybackGoodsVO {

    /**
     * 直播商品ID
     */
    private Long liveGoodsId;

    /**
     * 直播间ID
     */
    private Long liveRoomId;

    /**
     * erp商品ID
     */
    private Long globalGoodsId;

    /**
     * 云展商品ID
     */
    private Long ceGoodsId;

    /**
     * 加密后的ERP商品ID
     */
    private String globalEncryptGoodsId;

    /**
     * 商品名称
     */
    private String goodsName;

    /**
     * 商品编号
     */
    private String goodsCode;

    /**
     * 商品价格
     */
    private BigDecimal price;

    /**
     * 商品图片
     */
    private List<String> imgUrlList;

    /**
     * 商品成色
     */
    private String quality;

    /**
     * 商品回放地址
     */
    private String videoUrl;

    /**
     * 购买状态 0-可加购  10-自己已加购 20-同事已加购
     */
    private Integer buyStatus = 0;
}
