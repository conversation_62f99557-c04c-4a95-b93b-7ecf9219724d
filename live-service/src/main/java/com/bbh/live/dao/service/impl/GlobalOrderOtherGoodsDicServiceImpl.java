package com.bbh.live.dao.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bbh.enums.GlobalVirtualGoodsSourceEnum;
import com.bbh.live.dao.dto.vo.VirtualGoodsVO;
import com.bbh.live.dao.mapper.GlobalOrderOtherGoodsDicMapper;
import com.bbh.live.dao.service.GlobalOrderOtherGoodsDicService;
import com.bbh.model.GlobalOrderOtherGoodsDic;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【global_order_other_goods_dic(其他商品（保证金、分贝、门票等）中订单信息字典表)】的数据库操作Service实现
* @createDate 2024-08-19 10:10:16
*/
@Service
public class GlobalOrderOtherGoodsDicServiceImpl extends ServiceImpl<GlobalOrderOtherGoodsDicMapper, GlobalOrderOtherGoodsDic>
    implements GlobalOrderOtherGoodsDicService{


    @Override
    public List<VirtualGoodsVO> getVipTypeList() {
        List<VirtualGoodsVO> goodsVOList = getVirtualGoodsListBySource(GlobalVirtualGoodsSourceEnum.BUYER_VIP);
        Collections.reverse(goodsVOList);
        return goodsVOList;
    }

    @Override
    public List<VirtualGoodsVO> getFenbeiTypeList() {
        return getVirtualGoodsListBySource(GlobalVirtualGoodsSourceEnum.FEN_BEI);
    }

    @Override
    public List<VirtualGoodsVO> getVirtualGoodsListBySource(GlobalVirtualGoodsSourceEnum source) {
        List<VirtualGoodsVO> result = new ArrayList<>();
        List<GlobalOrderOtherGoodsDic> list = this.lambdaQuery()
                .eq(GlobalOrderOtherGoodsDic::getGoodsSource, source.getValue())
                .eq(GlobalOrderOtherGoodsDic::getDisabled, false)
                .list();
        for (GlobalOrderOtherGoodsDic globalOrderOtherGoodsDic : list) {
            VirtualGoodsVO virtualGoodsVO = VirtualGoodsVO.build(globalOrderOtherGoodsDic);
            result.add(virtualGoodsVO);
        }
        return result;
    }

}




