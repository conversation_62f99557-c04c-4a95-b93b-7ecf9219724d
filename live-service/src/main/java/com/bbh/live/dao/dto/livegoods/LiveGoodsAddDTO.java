package com.bbh.live.dao.dto.livegoods;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR> dsy
 * @Date: 2024/8/7
 * @Description:
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class LiveGoodsAddDTO {

    private Long liveRoomId;

    /**
     * 新增的商品id列表
     */
    private List<Long> addGoodsIdList;

    /**
     * 移除的商品id列表
     */
    private List<Long> removeGoodsIdList;
}
