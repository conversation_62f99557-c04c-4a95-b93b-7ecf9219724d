package com.bbh.live.dao.service.impl;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bbh.base.ListBase;
import com.bbh.enums.CreateFromEnum;
import com.bbh.enums.GlobalVirtualGoodsOrderStateEnum;
import com.bbh.enums.VipBuyerPayRecordTypeEnum;
import com.bbh.live.dao.dto.vo.VirtualGoodsOrderVO;
import com.bbh.live.dao.mapper.VipBuyerPayRecordMapper;
import com.bbh.live.dao.service.VipBuyerPayRecordService;
import com.bbh.model.GlobalVirtualGoodsOrder;
import com.bbh.model.VipBuyerCard;
import com.bbh.model.VipBuyerPayRecord;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;

/**
 * <AUTHOR>
 * @description 针对表【vip_buyer_pay_record(买家会员充值记录)】的数据库操作Service实现
 * @createDate 2024-08-14 17:51:46
 */
@Service
public class VipBuyerPayRecordServiceImpl extends ServiceImpl<VipBuyerPayRecordMapper, VipBuyerPayRecord>
    implements VipBuyerPayRecordService {

    @Override
    public Long insertVipBuyerPayRecord(GlobalVirtualGoodsOrder order, Date startDate, VipBuyerCard vipBuyerCard,
        String userName) {
        VipBuyerPayRecord vipBuyerPayRecord = new VipBuyerPayRecord();
        vipBuyerPayRecord.setOrderId(order.getId());
        vipBuyerPayRecord.setVipBuyerCardId(vipBuyerCard.getId());
        vipBuyerPayRecord.setStartTime(startDate);
        vipBuyerPayRecord.setEndTime(vipBuyerCard.getTimeVipEnd());
        vipBuyerPayRecord.setSeatId(order.getBuyerSeatId());
        vipBuyerPayRecord.setOrgId(order.getBuyerOrgId());
        vipBuyerPayRecord.setType(VipBuyerPayRecordTypeEnum.getByCode(order.getGoodsNumber()));
        vipBuyerPayRecord.setName(order.getGoodsName());
        vipBuyerPayRecord.setPayType(1);
        vipBuyerPayRecord.setPrice(order.getOrderPrice());
        vipBuyerPayRecord.setCreateId(order.getBuyerSeatId());
        vipBuyerPayRecord.setCreateFrom(CreateFromEnum.APP);
        vipBuyerPayRecord.setCreateName(userName);
        this.save(vipBuyerPayRecord);
        return vipBuyerPayRecord.getId();
    }

    @Override
    public Long getVipUsedDays(Long vipId) {
        long usedDays = 0L;
        Date now = new Date();
        // 所有已使用过的VIP充值记录
        List<VipBuyerPayRecord> payRecords =
            this.lambdaQuery().eq(VipBuyerPayRecord::getVipBuyerCardId, vipId).lt(VipBuyerPayRecord::getStartTime, now)
                .select(VipBuyerPayRecord::getId, VipBuyerPayRecord::getStartTime, VipBuyerPayRecord::getEndTime)
                .orderByDesc(VipBuyerPayRecord::getStartTime).list();

        if (CollectionUtil.isEmpty(payRecords)) {
            return usedDays;
        }
        // 最近一条vip充值记录是否正在使用
        VipBuyerPayRecord latestVipPayRecord = payRecords.removeFirst();
        if (latestVipPayRecord.getEndTime().after(now)) {
            usedDays += DateUtil.between(latestVipPayRecord.getStartTime(), now, DateUnit.DAY);
        } else {
            usedDays +=
                DateUtil.between(latestVipPayRecord.getStartTime(), latestVipPayRecord.getEndTime(), DateUnit.DAY);
        }

        usedDays += payRecords.stream()
            .mapToLong(record -> DateUtil.between(record.getStartTime(), record.getEndTime(), DateUnit.DAY)).sum();

        return usedDays;
    }

    @Override
    public ListBase<VirtualGoodsOrderVO> getVipBuyerPayRecordList(int currentPage, int perPage, Long seatId) {
        IPage<VipBuyerPayRecord> pageResult =
            this.lambdaQuery().eq(VipBuyerPayRecord::getSeatId, seatId).in(VipBuyerPayRecord::getPayType, 1, 20)
                .orderByDesc(VipBuyerPayRecord::getCreatedAt).page(Page.of(currentPage, perPage));

        List<VirtualGoodsOrderVO> collect =
            pageResult.getRecords().stream().map(this::buildVirtualGoodsOrderVO).collect(Collectors.toList());
        ListBase<VirtualGoodsOrderVO> listBase = ListBase.of();
        listBase.setRecords(collect);
        listBase.setTotal(pageResult.getTotal());
        listBase.setCurrentPage(pageResult.getCurrent());
        listBase.setPerPage(pageResult.getSize());
        return listBase;
    }

    private VirtualGoodsOrderVO buildVirtualGoodsOrderVO(VipBuyerPayRecord record) {
        VirtualGoodsOrderVO vo = new VirtualGoodsOrderVO();
        vo.setGoodsPrice(record.getPrice());
        vo.setOrderPrice(record.getPrice());
        vo.setPayPrice(record.getPrice());
        vo.setOrderId(record.getId());
        vo.setGoodsName(record.getName());
        vo.setDeliverDatetime(record.getCreatedAt());
        vo.setOrderState(GlobalVirtualGoodsOrderStateEnum.DONE_DELIVER);
        vo.setPayDatetime(record.getCreatedAt());
        vo.setReason(record.getReason());
        vo.setRemark(record.getRemark());
        return vo;
    }
}
