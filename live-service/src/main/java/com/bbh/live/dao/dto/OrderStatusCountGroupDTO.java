package com.bbh.live.dao.dto;

import com.bbh.enums.LiveGoodsBuyerCancelRecordStatusEnum;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 订单状态数量分组
 * <AUTHOR>
 */
@Data
@Accessors
public class OrderStatusCountGroupDTO {

    /** 用户信息 */
    private SimpleUserInfoDTO userInfo;

    /** 采购相关数据 */
    private Purchase purchase;

    /** 销售相关数据 */
    private Sale sale;

    /** 采购新订单数据 */
    private Purchase purchaseNew;

    /** 销售新订单数据 */
    private Sale saleNew;

    /** 消息相关数据 */
    private Message message;

    /**
     * 采购相关数据内部类，要获取个人的身份，区分微商还是企业
     */
    @Data
    @Accessors(chain = true)
    public static class Purchase {
        /** 收银台商品数量 */
        private Long cashierProductCount;
        /** 收银台商品数量 -个人 */
        private Long individualProductCount;
        /** 收银台商品数量 -全店 */
        private Long wholeShopProductCount;
        /** 待付款数量 */
        private Long pendingPaymentCount;

        /** 线下转账待审核数量 */
        private Long offlineTransferPendingReviewCount;

        /** 待发货数量 */
        private Long pendingShipmentCount;

        /** 待收货数量 */
        private Long pendingReceiptCount;

        /** 退款售后数量 */
        private Long refundAfterSalesCount;

        /** 以上所有数量的总和 */
        private Long totalCount;

        /** 取消成交中的数量 */
        private Long goodsCancelCount;

        /** 取消成交中的数量 路由状态 */
        private LiveGoodsBuyerCancelRecordStatusEnum goodsCancelRouteStatus;
    }

    /**
     * 销售相关数据内部类
     */
    @Data
    @Accessors(chain = true)
    public static class Sale {
        /** 收银台商品数量 */
        private Long cashierProductCount;
        /** 待付款数量 */
        private Long pendingPaymentCount;

        /** 线下转账待审核数量 */
        private Long offlineTransferPendingReviewCount;

        /** 待发货数量 */
        private Long pendingShipmentCount;

        /** 待收货数量 */
        private Long pendingReceiptCount;

        /** 退款售后数量 */
        private Long refundAfterSalesCount;

        /** 以上所有数量的总和 */
        private Long totalCount;

        /** 取消成交中，商家审核中的数量 */
        private Long goodsCancelCount;

        /** 取消成交中的数量 路由状态 */
        private LiveGoodsBuyerCancelRecordStatusEnum goodsCancelRouteStatus;
    }

    /**
     * 消息，APP右上角总的消息数量
     */
    @Data
    @Accessors(chain = true)
    public static class Message {
        /** 未读消息数量 */
        private Integer unreadCount;
    }

}
