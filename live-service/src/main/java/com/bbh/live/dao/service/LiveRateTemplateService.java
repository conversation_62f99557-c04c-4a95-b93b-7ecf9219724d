package com.bbh.live.dao.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.bbh.live.dao.dto.RateTemplateItemDTO;
import com.bbh.live.dao.dto.RoomRateTemplateDTO;
import com.bbh.model.LiveRateTemplate;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【live_rate_template(费率模板：根据商品价格计算模板)】的数据库操作Service
* @createDate 2024-07-25 11:53:19
*/
public interface LiveRateTemplateService extends IService<LiveRateTemplate> {

    /**
     * 获取直播间的费率列表
     * @param roomIdList 直播间ID
     * @return 直播间与费率的对照关系
     */
    List<RoomRateTemplateDTO> getRoomRateTemplateList(List<Long> roomIdList);

    /**
     * 获取直播间的费率项列表
     * @param roomId    直播间ID
     * @return          费率项列表
     */
    List<RateTemplateItemDTO> getRateTemplateItemList(Long roomId);

}
