package com.bbh.live.dao.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR> dsy
 * @Date: 2024/8/2
 * @Description:
 */
@Data
@Accessors(chain = true)
public class BuyerLiveGoodsCheckListDTO {

    /**
     * 总数量
     */
    private Long totalCount;

    /**
     *  待完善商品数量
     */
    private Long waitCompleteCount;

    /**
     * 待上架商品数量
     */
    private Long waitPutAwayCount;

    /**
     *  成交商品数量
     */
    private Long tradeCount;

    /**
     * 流拍商品数量
     */
    private Long abortiveAuctionCount;

    /**
     * 直播间成交额
     */
    private BigDecimal soldAmount;

    /**
     *  商品列表
     */
    private List<BuyerLiveGoodsDTO> liveGoodsList;
}
