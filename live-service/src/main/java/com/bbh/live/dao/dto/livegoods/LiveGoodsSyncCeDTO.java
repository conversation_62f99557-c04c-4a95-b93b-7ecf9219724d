package com.bbh.live.dao.dto.livegoods;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR> dsy
 * @Date: 2024/8/27
 * @Description:
 */
@Data
public class LiveGoodsSyncCeDTO {

    private Long liveRoomId;

    private List<LiveGoodsSyncCe> liveGoods;

    @Data
    public static class LiveGoodsSyncCe{
        private Long liveGoodsId;
        //同行价
        private BigDecimal peerPrice;
        //加价幅度
        private BigDecimal increasePrice;
        //0不同步 1 同步至货架 2同步至微拍卖
        private Integer type;
        //起拍价
        private BigDecimal startPrice;
    }
}
