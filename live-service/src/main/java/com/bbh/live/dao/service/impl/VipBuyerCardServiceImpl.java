package com.bbh.live.dao.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.RandomUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bbh.enums.CreateFromEnum;
import com.bbh.exception.ServiceException;
import com.bbh.live.dao.dto.OrgVipResourcePO;
import com.bbh.live.dao.mapper.VipBuyerCardMapper;
import com.bbh.live.dao.service.VipBuyerCardService;
import com.bbh.live.enums.BuyerVipTypeEnum;
import com.bbh.live.service.buyer.vip.vo.BuyerVipConfigVO;
import com.bbh.live.service.buyer.vip.vo.UserBuyerVipInfoVO;
import com.bbh.live.service.buyer.vip.vo.VipUpdateVO;
import com.bbh.model.GlobalVirtualGoodsOrder;
import com.bbh.model.VipBuyerCard;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【vip_buyer_card(买手会员权益表)】的数据库操作Service实现
 * @createDate 2024-08-14 17:51:46
 */
@Service
public class VipBuyerCardServiceImpl extends ServiceImpl<VipBuyerCardMapper, VipBuyerCard>
        implements VipBuyerCardService {

    @Override
    public UserBuyerVipInfoVO getUserBuyerVipInfoBySeatId(Long seatId) {
        return baseMapper.getUserBuyerVipInfoBySeatId(seatId);
    }

    @Override
    public UserBuyerVipInfoVO getUserBuyerVipInfoByVipId(Long vipId) {
        return baseMapper.getUserBuyerVipInfoByVipId(vipId);
    }

    @Override
    public UserBuyerVipInfoVO getDefaultVipInfoBySeatId(Long seatId) {
        UserBuyerVipInfoVO info = new UserBuyerVipInfoVO();
        info.setBuyerVipType(BuyerVipTypeEnum.NO_VIP);
        info.setIsVip(false);
        info.setSeatId(seatId);
        info.setIsAnnualFeeVip(false);
        info.setAfterSaleServiceUnUsedTimes(0);
        info.setLotteryUnUsedTimes(0);
        info.setModifyNicknameUnUsedTimes(0);
        info.setPeepBuyerUnUsedTimes(0);
        info.setUnUseAllVipTimes(0);
        info.setOfflineFbDeductNum(0);
        info.setOfflineFbDeductTimes(0);
        BuyerVipConfigVO buyerVipConfig = getNoVipConfig();
        info.setUnVipNearShopDistance(buyerVipConfig.getNearShopDistance());
        info.setVipConfig(buyerVipConfig);
        info.setCreateFrom(CreateFromEnum.APP);
        return info;
    }

    private BuyerVipConfigVO getNoVipConfig() {
        BuyerVipConfigVO buyerVipConfig = new BuyerVipConfigVO();
        buyerVipConfig.setVipLevel(-1);
        buyerVipConfig.setNeedExp(0);
        buyerVipConfig.setVipMedal(0);
        buyerVipConfig.setNearShopDistance(30000);
        buyerVipConfig.setSignExtraFenbei(1);
        buyerVipConfig.setPeepBuyerNum(0);
        buyerVipConfig.setLotteryNum(0);
        buyerVipConfig.setModifyNicknameNum(0);
        buyerVipConfig.setSaleAfterServiceNum(0);
        buyerVipConfig.setFenbeiMallDiscount(new BigDecimal(0));
        buyerVipConfig.setNextLevelExp(0);
        buyerVipConfig.setOfflineFbDeductNum(0);
        return buyerVipConfig;
    }

    @Override
    public List<UserBuyerVipInfoVO> getUserBuyerVipInfoBySeatIdList(List<Long> seatIdList) {
        return baseMapper.getUserBuyerVipInfoBySeatIdList(seatIdList);
    }

    @Override
    public boolean updateTimesBySeatId(Long seatId, String timesType, int num) {
        return baseMapper.updateTimesBySeatId(seatId, timesType, num);
    }

    @Override
    public boolean updateTimesBySeatIdAndFenbei(Long seatId, String timesType, int num, int sum) {
        return baseMapper.updateTimesBySeatIdAndFenbei(seatId, timesType, num, sum);
    }

    @Override
    public boolean updateVipInfo(VipUpdateVO vipUpdateVO) {
        UserBuyerVipInfoVO vipInfo = this.getUserBuyerVipInfoBySeatId(vipUpdateVO.getBuyerSeatId());
        if (vipInfo == null || !Boolean.TRUE.equals(vipInfo.getIsAnnualFeeVip())) {
            throw new ServiceException("非年会会员不可调整");
        }

        return this.lambdaUpdate().eq(VipBuyerCard::getId, vipInfo.getId())
                .set(VipBuyerCard::getUnderwater, vipUpdateVO.getUnderwater())
                .update();
    }

    @Override
    public VipBuyerCard createNewVipCard(GlobalVirtualGoodsOrder order, String userName) {
        Date now = new Date();
        // 计算会员有效期天数
        boolean isAnnualFeeVip = order.getGoodsNumber() == 12;
        int plusVipDays = isAnnualFeeVip ? 365 : order.getGoodsNumber() * 30;

        VipBuyerCard vipBuyerCard = new VipBuyerCard();
        vipBuyerCard.setOrgId(order.getBuyerOrgId());
        vipBuyerCard.setFirstOpenTime(new Date());
        vipBuyerCard.setOpenTime(new Date());
        vipBuyerCard.setTimeVipStart(now);
        vipBuyerCard.setTimeVipEnd(DateUtil.offsetDay(now, plusVipDays));
        vipBuyerCard.setIsAnnualFeeVip(isAnnualFeeVip);
        initVipCard(vipBuyerCard);
        vipBuyerCard.setSaveMoney(BigDecimal.ZERO);
        vipBuyerCard.setCreateFrom(CreateFromEnum.APP);
        vipBuyerCard.setCreateId(order.getBuyerSeatId());
        vipBuyerCard.setCreateName(userName);
        // 随机生成4位不重复邀请码
        vipBuyerCard.setVipInviteCode(getVipBuyerInviteCode());
        return vipBuyerCard;
    }

    public String getVipBuyerInviteCode() {
        String randomStr;
        do {
            randomStr = RandomUtil.randomStringUpper(4);
        } while (this.lambdaQuery().eq(VipBuyerCard::getVipInviteCode, randomStr).exists());
        return randomStr;
    }

    @Override
    public void renewVipCard(GlobalVirtualGoodsOrder order, VipBuyerCard vipBuyerCard) {
        Date now = new Date();
        vipBuyerCard.setOpenTime(now);

        boolean isAnnualFeeVipOrder = order.getGoodsNumber() == 12;
        int plusVipDays = isAnnualFeeVipOrder ? 365 : order.getGoodsNumber() * 30;

        // 会员未过期
        if (DateUtil.endOfDay(vipBuyerCard.getTimeVipEnd()).after(DateUtil.beginOfDay(new Date()))) {
            // 如果原本是年费会员，再次续费不管多少也是年费会员
            vipBuyerCard.setIsAnnualFeeVip(isAnnualFeeVipOrder ? Boolean.TRUE : vipBuyerCard.getIsAnnualFeeVip());
            vipBuyerCard.setTimeVipEnd(DateUtil.offsetDay(vipBuyerCard.getTimeVipEnd(), plusVipDays));
        } else {
            //会员已过期
            vipBuyerCard.setIsAnnualFeeVip(isAnnualFeeVipOrder);
            vipBuyerCard.setTimeVipStart(now);
            vipBuyerCard.setTimeVipEnd(DateUtil.offsetDay(now, plusVipDays));
            initVipCard(vipBuyerCard);
        }
    }

    /**
     * 初始化会员权益
     */
    private void initVipCard(VipBuyerCard vipBuyerCard) {
        Date tomorrow = DateUtil.offsetDay(new Date(), 1);
        Date oneMonthLater = DateUtil.offsetMonth(new Date(), 1);
        vipBuyerCard.setPeepBuyerUsedTimes(0);
        vipBuyerCard.setPeepBuyerNextRefreshTime(tomorrow);
        vipBuyerCard.setLotteryUsedTimes(0);
        vipBuyerCard.setLotteryNextRefreshTime(tomorrow);
        vipBuyerCard.setModifyNicknameUsedTimes(0);
        vipBuyerCard.setModifyNicknameNextRefreshTime(oneMonthLater);
        vipBuyerCard.setAfterSaleServiceUsedTimes(0);
        vipBuyerCard.setAfterSaleServiceNextRefreshTime(oneMonthLater);
    }

    @Override
    public List<OrgVipResourcePO> getOrgVipResources(Long orgId) {
        return getBaseMapper().getOrgVipResources(orgId);
    }

    @Override
    public void delete(Long vipId) {
        // 仅管理后台添加的可以删除
        boolean existsAdmin = this.lambdaQuery()
                .eq(VipBuyerCard::getCreateFrom, CreateFromEnum.ADMIN.getCode())
                .eq(VipBuyerCard::getId, vipId)
                .exists();
        if (!existsAdmin) {
            throw new ServiceException("仅管理后台添加的可以删除");
        }
        this.removeById(vipId);
    }
}




