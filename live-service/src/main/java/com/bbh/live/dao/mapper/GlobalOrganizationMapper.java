package com.bbh.live.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bbh.live.dao.dto.OrgFenbeiDTO;
import com.bbh.live.feign.dto.GlobalOrgDetailDTO;
import com.bbh.live.service.buyer.orgmap.vo.OrgAddressInfoVO;
import com.bbh.model.GlobalOrganization;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Param;

import java.math.BigInteger;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
* <AUTHOR>
* @description 针对表【global_organization(组织主体表)】的数据库操作Mapper
* @createDate 2024-06-21 10:20:49
* @Entity generator.domain.GlobalOrganization
*/
public interface GlobalOrganizationMapper extends BaseMapper<GlobalOrganization> {

    List<GlobalOrganization> getAnomalyNameOrg();

    /**
     * 判断商家是否有新的商品 （7天内）
     * @param orgIds
     * @return
     */
    @MapKey("orgId")
    Map<BigInteger, Map<String, Integer>> ifOrgHasNewGoods(@Param("orgIdList") Set<Long> orgIds);

    /**
     * 获取店铺详情
     * @param orgId
     * @return
     */
    GlobalOrgDetailDTO getOrgDetailById(@Param("orgId") Long orgId);


    /**
     * 批量更新店铺分贝
     * @param orgFenbeiList
     */
    void batchUpdateOrgFenbei(@Param("orgFenbeiList") List<OrgFenbeiDTO> orgFenbeiList);

    /**
     * 批量获取店铺地址信息
     * @param orgIdSet
     * @return
     */
    List<OrgAddressInfoVO> getOrgAddressInfoByIdList(@Param("orgIdSet") Set<Long> orgIdSet);

    List<GlobalOrganization> getMapOrg(@Param("ids")Set<Long> ids);
}




