package com.bbh.live.dao.dto;

import com.bbh.model.GlobalOrgSeat;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 简单用户信息
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SimpleUserInfoDTO extends GlobalOrgSeat {

    private String seatName;

    private Long orgId;

    private String orgName;

    private String orgLogoUrl;

    /** 商户类型：1微商 2企业 */
    private Integer orgType;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 席位ID
     */
    private Long seatId;

    // 席位拍号
    private String auctionCode;

    // 是否会员
    private Boolean isVip = false;

    // 会员等级
    private Integer vipLevel = -1;

    // 是否年费会员
    private Boolean isAnnualFeeVip = false;

    // 昵称
    private String nickName;

    // 头像
    private String avatar;

    /**
     * 会员卡ID
     */
    private Long vipCardId;

    /**
     * 会员开始时间
     */
    private Date timeVipStart;

    /**
     * 会员结束时间
     */
    private Date timeVipEnd;

    /**
     * 会员当前经验值
     */
    private Integer exp;

}
