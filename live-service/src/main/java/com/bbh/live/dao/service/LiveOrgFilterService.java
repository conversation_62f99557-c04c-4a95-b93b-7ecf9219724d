package com.bbh.live.dao.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.bbh.base.PageBase;
import com.bbh.enums.LiveOrgFilterSourceTypeEnum;
import com.bbh.live.dao.dto.LiveFilterSearchUserDTO;
import com.bbh.live.dao.dto.vo.LiveOrgFilterVO;
import com.bbh.model.LiveOrgFilter;
import com.bbh.vo.AuthUser;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【live_org_filter(直播间黑白名单)】的数据库操作Service
 * @createDate 2024-07-25 11:53:19
 */
public interface LiveOrgFilterService extends IService<LiveOrgFilter> {


    IPage<LiveOrgFilterVO> pageListUser(PageBase pageBase, AuthUser user, LiveOrgFilterSourceTypeEnum liveOrgFilterSourceTypeEnum);

    List<LiveOrgFilter> getByOrgId(Long orgId, LiveOrgFilterSourceTypeEnum liveOrgFilterSourceTypeEnum);

    IPage<LiveOrgFilterVO> pageListOrg(PageBase pageBase, AuthUser user, LiveOrgFilterSourceTypeEnum liveOrgFilterSourceTypeEnum);

    IPage<LiveOrgFilterVO> searchOrg(AuthUser user, LiveFilterSearchUserDTO dto);



}
