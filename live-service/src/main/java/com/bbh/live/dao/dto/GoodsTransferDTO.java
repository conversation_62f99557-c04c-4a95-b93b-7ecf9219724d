package com.bbh.live.dao.dto;

import com.bbh.util.AssertUtil;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024/7/25 下午3:00
 */
@Data
@Accessors(chain = true)
public class GoodsTransferDTO {

    // 商品传送业务

    private Long liveRoomId;
    private Long liveGoodsId;
    private BigDecimal transferPrice;
    private Long targetUserId;
    private Long targetSeatId;
    private Long targetOrgId;

    /**
     * --------------------------------------------------------------------
     **/

    // 查找拍号业务

    private String auctionCode;

    public void goodsTransferModelCheck() {
        AssertUtil.assertNotNull(liveRoomId, "直播间id不能为空");
        AssertUtil.assertNotNull(liveGoodsId, "商品id不能为空");
        AssertUtil.assertNotNull(transferPrice, "传送价格不能为空");
        AssertUtil.assertNotNull(targetSeatId, "用户席位不存在");
    }

    public void auctionCodeModelCheck() {
        AssertUtil.assertNotNull(auctionCode, "拍号不能为空");
    }
}
