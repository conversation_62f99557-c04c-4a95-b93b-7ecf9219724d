package com.bbh.live.dao.dto;

import com.bbh.enums.GlobalOrderItemDeliveryTypeEnum;
import com.bbh.enums.GlobalPayTypeEnum;
import com.bbh.live.dao.dto.livegoods.LiveGoodsDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;

/**
 * 生成订单
 *
 * <AUTHOR>
 */
@Data
public class CreateOrderDTO {

    /**
     * 订单备注
     */
    private String remark;

    /**
     * 支付方式：1微信 ,2支付宝，3线下
     */
    private GlobalPayTypeEnum payType;

    /**
     * 发货方式：0无需发货 10物流 20自提 <br>
     * 收银台选择发货方式，写入到所有OrderItem的{@code OrderDeliveryType} 和 {@code OrderDeliveryId} <br>
     * OrderSub的 {@code DeliveryType } 和 {@code DeliveryId} 由PHP写入
     */
    private GlobalOrderItemDeliveryTypeEnum deliveryType;

    /**
     * 收货地址
     */
    private Long receiveAddressId;

    /**
     * 收货地址补充信息
     * 收货地址信息
     */
    private String receiveAddressInfo;

    /**
     * 是否使用分贝抵扣通道费，默认不使用
     */
    private Boolean ifUseFenbei = Boolean.FALSE;

    /**
     * 按商户进行分组
     */
    List<Org> orgList;

    @Data
    @Accessors(chain = true)
    public static class Org {

        /**
         * 商户ID
         */
        private Long orgId;

        /**
         * 商户商品清单
         */
        private List<Goods> goodsList;

        /**
         * 商户订单备注
         */
        private String remark;

        // region 以下参数：收银台和生成订单时返回，创建订单时不需要传
        private String name;
        private String logoUrl;
        private Boolean ifLived = false;
        private Long liveRoomId;
        private BigDecimal totalGoodsPrice;
        private BigDecimal totalNeedPayAmount;
        private BigDecimal totalServiceAmount;
        private BigDecimal totalBuyerServiceAmount;
        private BigDecimal totalChannelAmount;
        // endregion
    }

    @Data
    @EqualsAndHashCode(callSuper = true)
    @Accessors(chain = true)
    public static class Goods extends LiveGoodsDTO {

        /**
         * 直播商品清单ID
         */
        private Long liveGoodsId;

        // region 以下参数：收银台和生成订单时返回，创建订单时不需要传
        private String goodsName;
        private BigDecimal needPayAmount;
        private BigDecimal serviceRate;
        private BigDecimal serviceAmount;
        private BigDecimal buyerServiceAmount;
        private BigDecimal buyerServiceRate;
        private BigDecimal channelAmount;
        // endregion
    }

}
