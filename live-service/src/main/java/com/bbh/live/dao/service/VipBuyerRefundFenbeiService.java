package com.bbh.live.dao.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.bbh.live.dao.dto.vo.AfterSaleVO;
import com.bbh.model.VipBuyerRefundFenbei;

/**
 * <AUTHOR>
 * @date 2024/8/16 08:40
 */
public interface VipBuyerRefundFenbeiService extends IService<VipBuyerRefundFenbei> {

    /**
     * 售后退款分贝明细
     * @param afterSaleVO
     * @return
     */
    VipBuyerRefundFenbei getAfterSaleRefundFenbei(AfterSaleVO afterSaleVO);
}
