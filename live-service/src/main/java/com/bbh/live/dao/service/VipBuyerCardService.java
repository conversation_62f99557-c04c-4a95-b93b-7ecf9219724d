package com.bbh.live.dao.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.bbh.live.dao.dto.OrgVipResourcePO;
import com.bbh.live.service.buyer.vip.vo.UserBuyerVipInfoVO;
import com.bbh.live.service.buyer.vip.vo.VipUpdateVO;
import com.bbh.model.GlobalVirtualGoodsOrder;
import com.bbh.model.VipBuyerCard;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【vip_buyer_card(买手会员权益表)】的数据库操作Service
 * @createDate 2024-08-14 17:51:46
 */
public interface VipBuyerCardService extends IService<VipBuyerCard> {
    /**
     * 根据座位id获取买手会员信息
     *
     * @param seatId 席位Id
     * @return 会员信息
     */
    UserBuyerVipInfoVO getUserBuyerVipInfoBySeatId(Long seatId);

    /**
     * 根据vip id查询
     * @param vipId
     * @return
     */
    UserBuyerVipInfoVO getUserBuyerVipInfoByVipId(Long vipId);

    /**
     *  默认的会员信息，用于未查询到会员信息时
     * @param seatId
     * @return
     */
    UserBuyerVipInfoVO getDefaultVipInfoBySeatId(Long seatId);

    /**
     * 根据座位id列表获取买手会员信息
     *
     * @param seatIdList 席位列表Id
     * @return 会员列表信息
     */
    List<UserBuyerVipInfoVO> getUserBuyerVipInfoBySeatIdList(List<Long> seatIdList);

    /**
     * 更新使用次数
     *
     * @param seatId    seatId
     * @param timesType 类型
     * @param num       次数
     * @return 是否成功
     */
    boolean updateTimesBySeatId(Long seatId, String timesType, int num);

    /**
     * 更新使用次数和分贝
     *
     * @param seatId    席位
     * @param timesType 类型
     * @param num       次数
     * @param sum       分贝
     * @return 是否成功
     */
    boolean updateTimesBySeatIdAndFenbei(Long seatId, String timesType, int num, int sum);

    /**
     * 更新会员信息
     * @param vipUpdateVO
     * @return
     */
    boolean updateVipInfo(VipUpdateVO vipUpdateVO);

    /**
     *  新用户开通会员
     * @param order
     * @return
     */
    VipBuyerCard createNewVipCard(GlobalVirtualGoodsOrder order, String userName);

    /**
     * 续费会员
     * @param order
     * @return
     */
    void renewVipCard(GlobalVirtualGoodsOrder order, VipBuyerCard vipBuyerCard);

    /**
     * 获取店铺所有会员
     * @param orgId
     * @return
     */
    List<OrgVipResourcePO> getOrgVipResources(Long orgId);

    void delete(Long vipId);
}
