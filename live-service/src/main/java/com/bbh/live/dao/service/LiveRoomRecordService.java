package com.bbh.live.dao.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.bbh.model.LiveRoomRecord;

import java.util.Date;

/**
* <AUTHOR>
* @description 针对表【live_room_record(直播间开关播记录)】的数据库操作Service
* @createDate 2024-07-25 11:53:19
*/
public interface LiveRoomRecordService extends IService<LiveRoomRecord> {

    /**
     * 添加直播间开播记录
     * @param roomId
     * @param now
     */
    void addLiveRoomStartRecord(Long roomId, Date now);

    /**
     * 更新直播间停播记录
     * @param roomId
     * @param now
     */
    void updateLiveRoomStopRecord(Long roomId, Date now);
}
