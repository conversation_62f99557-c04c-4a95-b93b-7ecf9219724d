package com.bbh.live.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bbh.live.dao.dto.GlobalOrderItemVO;
import com.bbh.live.dao.dto.QueryOrderCounterDTO;
import com.bbh.model.GlobalOrderItem;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @description 针对表【global_order_item(全局订单item（商品）)】的数据库操作Mapper
 * @createDate 2024-07-25 11:52:55
 * @Entity com.bbh.model.GlobalOrderItem
 */
public interface GlobalOrderItemMapper extends BaseMapper<GlobalOrderItem> {

    Long countOrder(QueryOrderCounterDTO query);

    Page<GlobalOrderItemVO> pageList(@Param("page") Page<GlobalOrderItem> page, @Param("orderId") Long orderId);
}




