package com.bbh.live.dao.dto;

import com.bbh.enums.GlobalLotteryPrizePoolDistributionMethodEnum;
import com.bbh.enums.GlobalLotteryPrizePoolTypeEnum;
import com.bbh.enums.GlobalLotteryWinningRecordProcessStatusEnum;
import lombok.Data;

import java.util.Date;

/**
 * 抽奖中奖记录，包含商品信息
 * <AUTHOR>
 */
@Data
public class LotteryWinningRecordDTO {

    /**
     * 奖品名称
     */
    private String name;

    /**
     * 奖品类型: 0-谢谢惠顾, 1-分贝, 2-其他
     */
    private GlobalLotteryPrizePoolTypeEnum type;

    /**
     * 发放方式: 0-自动, 1-手动
     */
    private GlobalLotteryPrizePoolDistributionMethodEnum distributionMethod;

    /**
     * 奖品图片URL
     */
    private String imageUrl;

    /**
     * 中奖时间
     */
    private Date winningAt;

    /**
     * 处理状态: 0-待处理(Pending), 1-已处理(Processed)
     */
    private GlobalLotteryWinningRecordProcessStatusEnum processStatus;

    /**
     * 处理时间
     */
    private Date processTime;

    private String seatName;

    private String seatNickName;

}
