package com.bbh.live.dao.service.impl;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.convert.Converter;
import cn.hutool.core.convert.ConverterRegistry;
import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bbh.live.constant.RedisKey;
import com.bbh.live.dao.mapper.GlobalBizConfigMapper;
import com.bbh.live.dao.service.GlobalBizConfigService;
import com.bbh.model.GlobalBizConfig;
import lombok.AllArgsConstructor;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * 全局的业务配置，默认将从缓存中获取，如果缓存中没有，再读取数据库并进行缓存 <br>
 * 由于业务配置表存在多源头修改，因此采用定时扫描的方式与缓存比对并更新 <br>
 * 允许直接调用service获取value，也可以通过定义 {@code @BizConfig} 注解定义配置类，将自动注入value
 *
* <AUTHOR>
*/
@Service
@AllArgsConstructor
public class GlobalBizConfigServiceImpl extends ServiceImpl<GlobalBizConfigMapper, GlobalBizConfig> implements GlobalBizConfigService, RedisKey {

    private final StringRedisTemplate redisTemplate;

    private static final String BASE_KEY = "biz-config";
    private static final ScheduledExecutorService SCHEDULED_EXECUTOR_SERVICE = Executors.newSingleThreadScheduledExecutor();

    @Override
    public <T> Optional<T> getOptional(String key, Class<T> clazz, boolean ignoreCache) {
        // 自定义日期转换
        ConverterRegistry converterRegistry = ConverterRegistry.getInstance();
        converterRegistry.putCustom(JSONArray.class, JSONArrayConverter.class);
        converterRegistry.putCustom(JSONObject.class, JSONObjectConverter.class);

        if (!ignoreCache) {
            Object object = redisTemplate.opsForHash().get(buildKey(BASE_KEY), key);
            if (object != null) {
                return Optional.ofNullable(Convert.convertQuietly(clazz, object));
            }
        }

        // 如果没获取到缓存，需要去查数据库再缓存
        List<GlobalBizConfig> configs = list(Wrappers
                .lambdaQuery(GlobalBizConfig.class)
                .select(GlobalBizConfig::getBizValue, GlobalBizConfig::getId)
                .eq(GlobalBizConfig::getBizKey, key)
                .orderByDesc(GlobalBizConfig::getCreatedAt, GlobalBizConfig::getId)
        );
        // 当存在多个同名配置，只取最新的
        if (!configs.isEmpty()) {
            GlobalBizConfig config = configs.getFirst();
            if (!ignoreCache) {
                redisTemplate.opsForHash().put(buildKey(BASE_KEY), key, config.getBizValue());
            }
            return Optional.ofNullable(Convert.convertQuietly(clazz, config.getBizValue()));
        }
        return Optional.empty();
    }

    @Override
    public <T> T get(String key, Class<T> clazz) {
        return getOptional(key, clazz, true).orElse(null);
    }

    @Override
    public JSONArray getJSONArray(String key) {
        return get(key, JSONArray.class);
    }

    @Override
    public JSONObject getJSONObject(String key) {
        return get(key, JSONObject.class);
    }

    @Override
    public String getString(String key) {
        return get(key, String.class);
    }

    @Override
    public Long getLong(String key) {
        return get(key, Long.class);
    }

    @Override
    public Integer getInteger(String key) {
        return get(key, Integer.class);
    }

    @Override
    public Double getDouble(String key) {
        return get(key, Double.class);
    }

    /**
     * 定时扫描缓存，进行比对并更新
     */
//    @PostConstruct
    public void load() {
        scan();
    }

    /**
     * 自动扫描缓存
     */
    private void scan() {
        try {
            list(Wrappers
                    .lambdaQuery(GlobalBizConfig.class)
                    .orderByAsc(GlobalBizConfig::getCreatedAt, GlobalBizConfig::getId)
            ).forEach(x -> redisTemplate.opsForHash().put(buildKey(BASE_KEY), x.getBizKey(), x.getBizValue()));
        } catch (Exception e) {
            log.error("扫描业务配置缓存失败", e);
        } finally {
            SCHEDULED_EXECUTOR_SERVICE.schedule(this::scan, 3, TimeUnit.MINUTES);
        }
    }

    public static class JSONObjectConverter implements Converter<JSONObject> {
        @Override
        public JSONObject convert(Object value, JSONObject defaultValue) {
            return JSONUtil.parseObj(value.toString());
        }
    }

    public static class JSONArrayConverter implements Converter<JSONArray> {
        @Override
        public JSONArray convert(Object value, JSONArray defaultValue) {
            return JSONUtil.parseArray(value.toString());
        }
    }

    /**
     * 自定义时间转换器
     */
    public static class DateConverter implements Converter<Date> {

        @Override
        public Date convert(Object value, Date defaultValue) throws IllegalArgumentException {
            return DateUtil.parse(value.toString());
        }
    }
}




