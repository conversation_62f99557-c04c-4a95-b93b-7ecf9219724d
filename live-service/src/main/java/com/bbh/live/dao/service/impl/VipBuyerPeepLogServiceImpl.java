package com.bbh.live.dao.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bbh.live.dao.dto.VipPeepLogDTO;
import com.bbh.live.dao.mapper.VipBuyerPeepLogMapper;
import com.bbh.live.dao.service.VipBuyerPeepLogService;
import com.bbh.model.VipBuyerPeepLog;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * 会员查看价格
 * <AUTHOR>
 */
@Service
@AllArgsConstructor
public class VipBuyerPeepLogServiceImpl extends ServiceImpl<VipBuyerPeepLogMapper, VipBuyerPeepLog> implements VipBuyerPeepLogService{


    @Override
    public Page<VipPeepLogDTO> getPeepLogsBySeatId(Long seatId, Date startAt, Date endAt, IPage<VipBuyerPeepLog> page) {
        return this.getBaseMapper().getPeepLogsBySeatId(page, seatId, startAt, endAt);
    }

    @Override
    public Page<VipPeepLogDTO> getPeepLogsByVipId(Long vipId, Date startAt, Date endAt, IPage<VipBuyerPeepLog> page) {
        return this.getBaseMapper().getPeepLogsByVipId(page, vipId, startAt, endAt);
    }
}

