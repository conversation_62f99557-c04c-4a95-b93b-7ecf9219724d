package com.bbh.live.dao.dto;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 涉及席位的基础信息
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class BaseSeatDTO {

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 店铺 id
     */
    private Long orgId;

    /**
     * 席位ID
     */
    private Long seatId;

    //region 自动补全的字段

    // 席位拍号
    private String auctionCode;

    // 是否会员
    private Boolean isVip = false;

    // 会员等级
    private Integer vipLevel = -1;

    // 是否年费会员
    private Boolean isAnnualFeeVip = false;

    // 昵称
    private String nickName;

    // 头像
    private String avatar;

    // 商户身份: 1-微信，2-企业
    private Integer orgType;

    //endregion

    public BaseSeatDTO copy(BaseSeatDTO baseSeatDTO){
        this.userId = baseSeatDTO.getUserId();
        this.seatId = baseSeatDTO.getSeatId();
        this.auctionCode = baseSeatDTO.getAuctionCode();
        this.isVip = baseSeatDTO.getIsVip();
        this.vipLevel = baseSeatDTO.getVipLevel();
        this.isAnnualFeeVip = baseSeatDTO.getIsAnnualFeeVip();
        this.nickName = baseSeatDTO.getNickName();
        this.avatar = baseSeatDTO.getAvatar();
        this.orgType = baseSeatDTO.getOrgType();
        return this;
    }
}
