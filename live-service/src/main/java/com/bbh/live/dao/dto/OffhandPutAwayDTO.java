package com.bbh.live.dao.dto;

import cn.hutool.core.collection.CollectionUtil;
import com.bbh.enums.LiveGoodsTradeTypeEnum;
import com.bbh.live.util.NumberUtils;
import com.bbh.util.AssertUtil;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR> dsy
 * @Date: 2024/8/2
 * @Description:
 */
@Data
@Accessors(chain = true)
public class OffhandPutAwayDTO {

    /**
     * 直播间id
     */
    private Long liveRoomId;

    /**
     * 通过扫码添加的erp商品
     */
    private Long globalGoodsId;

    /**
     * 图片
     */
    private List<String> imgUrlList;

    /**
     * 分类
     */
    private  Long classifyId;

    /**
     * 成色
     */
    private String quality;

    /**
     * 商品名称
     */
    private String goodsName;

    /**
     * 起拍价
     */
    private BigDecimal startPrice;

    /**
     * 加价幅度
     */
    private BigDecimal increasePrice;

    /**
     * 竞拍时长，上架时设置
     */
    private Integer auctionDuration;

    /**
     * 仓库id
     */
    private Long storehouseId;

    /**
     * 交易方式
     */
    private LiveGoodsTradeTypeEnum tradeType;

    public void checkProperties() {
        AssertUtil.assertNotNull(this.liveRoomId, "直播间必填");
        AssertUtil.assertNotNull(this.classifyId, "货品分类必填");
        AssertUtil.assertTrue(CollectionUtil.isNotEmpty(this.imgUrlList), "货品图片必填");
        AssertUtil.assertNotNull(this.quality, "货品成色必填");
        AssertUtil.assertNotNull(this.startPrice, "起拍价必填");
        NumberUtils.checkGoodsPrice(this.tradeType, this.startPrice);

        if(tradeType == LiveGoodsTradeTypeEnum.AUCTION){
            AssertUtil.assertNotNull(this.increasePrice, "加价幅度必填");
            AssertUtil.assertNotNull(this.auctionDuration, "竞拍时长必填");
        }
    }
}
