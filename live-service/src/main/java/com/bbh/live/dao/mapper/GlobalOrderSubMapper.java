package com.bbh.live.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bbh.live.dao.dto.QueryOrderCounterDTO;
import com.bbh.model.GlobalOrderSub;

/**
* <AUTHOR>
* @description 针对表【global_order_sub(全局子订单)】的数据库操作Mapper
* @createDate 2024-07-25 11:52:55
* @Entity com.bbh.model.GlobalOrderSub
*/
public interface GlobalOrderSubMapper extends BaseMapper<GlobalOrderSub> {

    Long countOrder(QueryOrderCounterDTO query);

}




