package com.bbh.live.dao.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bbh.live.dao.dto.livegoods.LiveGoodsSubscribeDTO;
import com.bbh.live.dao.mapper.LiveGoodsSubscribeMapper;
import com.bbh.live.dao.service.LiveGoodsSubscribeService;
import com.bbh.model.LiveGoodsSubscribe;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【live_goods_subscribe(商品预约记录)】的数据库操作Service实现
* @createDate 2024-07-25 11:53:19
*/
@Service
public class LiveGoodsSubscribeServiceImpl extends ServiceImpl<LiveGoodsSubscribeMapper, LiveGoodsSubscribe>
    implements LiveGoodsSubscribeService{

    @Override
    public List<LiveGoodsSubscribeDTO> getSubscribeGoodsInfoByBuyerSeatId(Long buyerSeatId) {
        return this.getBaseMapper().getSubscribeGoodsInfoByBuyerSeatId(buyerSeatId, new Date());
    }

    /**
     * 检查商品是否有预约数据
     *
     * @param liveGoodsId
     * @return
     */
    @Override
    public boolean hasGoodsSubscribe(Long liveGoodsId) {
        return lambdaQuery()
                .eq(LiveGoodsSubscribe::getLiveGoodsId, liveGoodsId)
                .exists();
    }
}




