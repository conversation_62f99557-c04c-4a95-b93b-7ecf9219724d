package com.bbh.live.dao.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.bbh.live.dao.dto.VipPeepLogDTO;
import com.bbh.model.VipBuyerPeepLog;

import java.util.Date;

/**
* <AUTHOR>
* @description 针对表【vip_buyer_peep_log(VIP窥探出价人使用记录表)】的数据库操作Service
* @createDate 2024-08-14 17:51:46
*/
public interface VipBuyerPeepLogService extends IService<VipBuyerPeepLog> {

    /**
     * 根据seatId获取窥探记录
     * @param seatId
     * @param startAt
     * @param endAt
     * @param page
     * @return
     */
    Page<VipPeepLogDTO> getPeepLogsBySeatId(Long seatId, Date startAt, Date endAt, IPage<VipBuyerPeepLog> page);

    /**
     * 根据seatId获取窥探记录
     * @param vipId
     * @param startAt
     * @param endAt
     * @param page
     * @return
     */
    Page<VipPeepLogDTO> getPeepLogsByVipId(Long vipId, Date startAt, Date endAt, IPage<VipBuyerPeepLog> page);
}
