package com.bbh.live.dao.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bbh.live.dao.dto.BaseSeatDTO;
import com.bbh.live.dao.dto.QuerySimpleUserDTO;
import com.bbh.live.dao.dto.SimpleUserInfoDTO;
import com.bbh.live.dao.mapper.GlobalOrgSeatMapper;
import com.bbh.live.dao.service.GlobalOrgSeatService;
import com.bbh.live.enums.BuyerVipTypeEnum;
import com.bbh.live.service.buyer.vip.BuyerVipService;
import com.bbh.live.service.buyer.vip.BuyerVipUtil;
import com.bbh.live.service.buyer.vip.vo.BuyerVipConfigVO;
import com.bbh.live.service.buyer.vip.vo.BuyerVipMsgTransferVO;
import com.bbh.model.GlobalOrgSeat;
import com.bbh.util.AssertUtil;
import jakarta.annotation.Resource;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【global_org_seat(主体席位)】的数据库操作Service实现
 * @createDate 2024-06-27 16:30:48
 */
@Service
public class GlobalOrgSeatServiceImpl extends ServiceImpl<GlobalOrgSeatMapper, GlobalOrgSeat>
        implements GlobalOrgSeatService {

    @Lazy
    @Resource
    private BuyerVipService buyerVipService;

    @Override
    public BaseSeatDTO getSeatInfoBySeatId(Long seatId) {
        BaseSeatDTO seat = new BaseSeatDTO();
        seat.setSeatId(seatId);

        // 获取昵称
        GlobalOrgSeat globalOrgSeat = this.getById(seatId);
        if (globalOrgSeat != null) {
            seat.setUserId(globalOrgSeat.getUserId());
            seat.setNickName(globalOrgSeat.getShowName());
            seat.setAvatar(globalOrgSeat.getAvatar());
            seat.setAuctionCode(globalOrgSeat.getAuctionCode());
        }

        // 会员
        BuyerVipMsgTransferVO vipInfo = buyerVipService.getBuyerVipMsgTransferVO(seatId);
        if (vipInfo != null) {
            seat.setIsVip(Objects.equals(vipInfo.getBuyerVipType(), BuyerVipTypeEnum.VIP));
            seat.setVipLevel(vipInfo.getVipLevel());
            seat.setIsAnnualFeeVip(Boolean.TRUE.equals(vipInfo.getIsAnnualFeeVip()));
        }
        return seat;
    }

    @Override
    public Long getOrgIdBySeatId(Long seatId) {
        var globalOrgSeat = this.lambdaQuery()
                .eq(GlobalOrgSeat::getId, seatId)
                .select(GlobalOrgSeat::getId, GlobalOrgSeat::getOrgId)
                .one();
        AssertUtil.assertNotNull(globalOrgSeat, "用户不存在");
        return globalOrgSeat.getOrgId();
    }

    @Override
    public IPage<SimpleUserInfoDTO> selectPageWithVip(IPage<SimpleUserInfoDTO> page, QuerySimpleUserDTO query) {
        IPage<SimpleUserInfoDTO> pages = this.getBaseMapper().selectPageWithVip(page, query);
        pages.getRecords().forEach(x -> {
            // 判断是否会员
            boolean after = x.getTimeVipEnd() != null && DateUtil.endOfDay(x.getTimeVipEnd()).after(DateUtil.beginOfDay(new Date()));
            boolean isVip = Objects.nonNull(x.getVipCardId()) && after;
            x.setIsVip(isVip);

            // 计算会员等级
            if (isVip) {
                BuyerVipConfigVO vipConfig = BuyerVipUtil.getBuyerVipConfig(x.getExp(), x.getIsAnnualFeeVip());
                x.setVipLevel(Objects.requireNonNullElse(vipConfig.getVipLevel(), -1));
            }

            // 昵称
            x.setNickName(x.getShowName());
        });
        return pages;
    }

    /**
     * 查询用户列表
     *
     * @param query
     * @return
     */
    @Override
    public List<SimpleUserInfoDTO> selectListWithVip(QuerySimpleUserDTO query) {
        return selectPageWithVip(Page.of(1, -1), query).getRecords();
    }

    /**
     * 根据商户ID查询
     *
     * @param orgId 商户id
     * @return 席位列表
     */
    @Override
    public List<GlobalOrgSeat> selectListByOrgId(Long orgId) {
        return this.lambdaQuery().eq(GlobalOrgSeat::getOrgId, orgId).list();
    }

    /**
     * 根据商户ID查询
     *
     * @param orgIdList 商户ID列表
     * @return 席位列表
     */
    @Override
    public List<GlobalOrgSeat> selectListByOrgIdList(List<Long> orgIdList) {
        return CollUtil.isEmpty(orgIdList) ? List.of() : this.lambdaQuery().in(GlobalOrgSeat::getOrgId, orgIdList)
                .select(GlobalOrgSeat::getId, GlobalOrgSeat::getOrgId).list();
    }

    @Override
    public List<Long> getSeatIdListByOrgRoleList(List<Long> roleList) {
        return baseMapper.getSeatIdListByOrgRoleList(roleList);
    }

    /**
     * 获取席位名称= showName
     */
    @Override
    public String getSeatName(Long seatId) {
        GlobalOrgSeat seat = this.lambdaQuery().eq(GlobalOrgSeat::getId,seatId)
                .select(GlobalOrgSeat::getShowName, GlobalOrgSeat::getAuctionCode).one();
        return getSeatName(seat);
    }

    @Override
    public String getSeatName(GlobalOrgSeat seat) {
        if (seat == null) {
            return "";
        }
        // 席位昵称+拍号，没有昵称就用名称
        return StrUtil.join("-", seat.getShowName(), seat.getAuctionCode());
    }

    @Override
    public Map<Long, Integer> getSeatWightMap(Collection<Long> bidSeatSet) {
        if(CollUtil.isEmpty(bidSeatSet)){
            return Map.of();
        }
        List<GlobalOrgSeat> orgSeats = this.lambdaQuery().in(GlobalOrgSeat::getId, bidSeatSet)
                .select(GlobalOrgSeat::getId, GlobalOrgSeat::getSecKillRandomWeight).list();
        return orgSeats.stream().collect(Collectors.toMap(GlobalOrgSeat::getId, GlobalOrgSeat::getSecKillRandomWeight));
    }
}




