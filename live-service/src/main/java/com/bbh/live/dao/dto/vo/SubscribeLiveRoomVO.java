package com.bbh.live.dao.dto.vo;

import com.bbh.live.enums.SeatRoleInRoomEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR> dsy
 * @Date: 2024/8/28
 * @Description:
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SubscribeLiveRoomVO {

    private Long roomId;
    private String roomName;
    /**
     * 是否正在直播
     */
    private Boolean inLive;

    /**
     * 直播开始时间
     */
    private Date startAt;

    /**
     * 直播结束时间
     */
    private Date endAt;

    /**
     * 是否是专场直播
     */
    private Boolean ifSpecial;

    /** 商品数量 */
    private Integer liveGoodsCount;

    /** 待拍数量 */
    private Integer waitAuctionCount;

    private Boolean ifGoodsListVisible;
    private Long orgId;
    private String orgName;
    private String logoUrl;
    private Long anchorSeatId;
    /**
     * 用户身份
     */
    private SeatRoleInRoomEnum seatRole;

    /**
     * 是否相关员工
     */
    private Boolean ifRelatedStaff;
}
