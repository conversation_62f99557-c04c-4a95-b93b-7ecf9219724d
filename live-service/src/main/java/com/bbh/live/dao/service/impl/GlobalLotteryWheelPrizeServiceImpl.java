package com.bbh.live.dao.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bbh.live.dao.mapper.GlobalLotteryWheelPrizeMapper;
import com.bbh.live.dao.service.GlobalLotteryWheelPrizeService;
import com.bbh.model.GlobalLotteryWheelPrize;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【global_lottery_wheel_prize(抽奖转盘与奖品关联表)】的数据库操作Service实现
* @createDate 2024-10-08 16:01:43
*/
@Service
@AllArgsConstructor
public class GlobalLotteryWheelPrizeServiceImpl extends ServiceImpl<GlobalLotteryWheelPrizeMapper, GlobalLotteryWheelPrize> implements GlobalLotteryWheelPrizeService{

    /**
     * 递减库存
     *
     * @param wheelId 转盘ID
     * @param prizeId 奖品ID
     * @param delta   递减数量
     */
    @Override
    public void decreaseStock(Long wheelId, Long prizeId, Integer delta) {

    }
}




