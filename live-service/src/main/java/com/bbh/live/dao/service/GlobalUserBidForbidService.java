package com.bbh.live.dao.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.bbh.enums.GlobalOrderTypeEnum;
import com.bbh.model.GlobalUserBidForbid;
import org.jetbrains.annotations.NotNull;

/**
 * <AUTHOR>
 * @Date 2025/5/26
 * @description: ${description}
 */
public interface GlobalUserBidForbidService extends IService<GlobalUserBidForbid> {

    /**
     * 检查是否出价被禁止
     *
     * @param userId
     */
    void checkBidForbid(Long userId, @NotNull GlobalOrderTypeEnum bizType);
}
