package com.bbh.live.dao.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bbh.live.dao.mapper.GlobalAuctionPremiumNumberMapper;
import com.bbh.live.dao.service.GlobalAuctionPremiumNumberService;
import com.bbh.live.service.organization.dto.OrgAuctionNumberResourcesDTO;
import com.bbh.model.GlobalAuctionPremiumNumber;
import org.springframework.stereotype.Service;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【live_auction_premium_number(直播拍号靓号表)】的数据库操作Service实现
* @date 2024-09-19 19:56:43
*/
@Service
public class GlobalAuctionPremiumNumberServiceImpl extends ServiceImpl<GlobalAuctionPremiumNumberMapper, GlobalAuctionPremiumNumber>
    implements GlobalAuctionPremiumNumberService {

    @Override
    public List<OrgAuctionNumberResourcesDTO> getAuctionNumberResources(Long orgId) {
        return this.getBaseMapper().getOrgAuctionNumberResources(orgId);
    }
}




