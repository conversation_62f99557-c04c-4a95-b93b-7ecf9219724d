package com.bbh.live.dao.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bbh.live.dao.dto.BaseSeatDTO;
import com.bbh.live.dao.dto.UnHandleBargainGoodsMsg;
import com.bbh.live.dao.mapper.LiveRoomInteractiveMessageMapper;
import com.bbh.live.dao.service.GlobalOrgSeatService;
import com.bbh.live.dao.service.LiveRoomInteractiveMessageService;
import com.bbh.model.LiveRoomInteractiveMessage;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 直播间互动消息服务
 * <AUTHOR>
 */
@Service
@AllArgsConstructor
public class LiveRoomInteractiveMessageServiceImpl extends ServiceImpl<LiveRoomInteractiveMessageMapper, LiveRoomInteractiveMessage> implements LiveRoomInteractiveMessageService{

    private final GlobalOrgSeatService globalOrgSeatService;

    /**
     * 获取待处理的消息列表
     *
     * @param roomId 直播间id
     * @return 消息列表
     */
    @SuppressWarnings("all")
    @Override
    public List<UnHandleBargainGoodsMsg> getUnhandledMessageList(Long roomId) {
        // 查询未处理的消息
        List<UnHandleBargainGoodsMsg> unhandledMessageList = this.getBaseMapper().getUnhandledMessageList(roomId);
        //会员信息
        Map<Long, BaseSeatDTO> seatMap = new HashMap<>();

        unhandledMessageList.stream().filter(msg -> msg.getSeatId() != null)
                .forEach(msg -> {
                    seatMap.computeIfAbsent(msg.getSeatId(), id -> globalOrgSeatService.getSeatInfoBySeatId(id));
                    var baseSeatDTO = seatMap.get(msg.getSeatId());
                    msg.copy(baseSeatDTO);
                });
        return unhandledMessageList;
    }

    @Override
    public Long getUnhandledMessageCount(Long roomId) {
        return getBaseMapper().getUnhandledMessageCount(roomId);
    }
}




