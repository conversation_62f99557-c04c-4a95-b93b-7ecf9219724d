package com.bbh.live.dao.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bbh.enums.LiveFeedbackHandleStatusEnum;
import com.bbh.enums.LiveFeedbackTypeEnum;
import com.bbh.exception.ServiceException;
import com.bbh.live.config.LiveBizProperties;
import com.bbh.live.dao.dto.FeedbackOptionDTO;
import com.bbh.live.dao.mapper.LiveFeedbackMapper;
import com.bbh.live.dao.service.LiveFeedbackService;
import com.bbh.model.LiveFeedback;
import com.bbh.vo.AuthUser;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【live_feedback(反馈记录)】的数据库操作Service实现
 * @createDate 2024-07-25 11:53:19
 */
@Service
@AllArgsConstructor
public class LiveFeedbackServiceImpl extends ServiceImpl<LiveFeedbackMapper, LiveFeedback> implements LiveFeedbackService {

    private LiveBizProperties liveBizProperties;

    @Override
    public void add(AuthUser user, LiveFeedback liveFeedback) {
        LiveFeedbackTypeEnum type = liveFeedback.getType();
        if (type == null) {
            throw new ServiceException("反馈类型不能为空");
        }
        Date date = new Date();
        long count = this.count(new LambdaQueryWrapper<LiveFeedback>()
                .eq(LiveFeedback::getUserId, user.getUserId())
                .eq(LiveFeedback::getType, type)
                .between(LiveFeedback::getCreatedAt, DateUtil.beginOfDay(date), DateUtil.endOfDay(date))
                .isNull(LiveFeedback::getDeletedAt));
        if (count > 5) {
            throw new ServiceException("您今天反馈已经达到上限了");
        }

        liveFeedback.setId(null);
        liveFeedback.setOrgId(user.getOrgId());
        liveFeedback.setUserId(user.getUserId());
        liveFeedback.setSeatId(user.getSeatId());
        liveFeedback.setLiveRoomId(liveFeedback.getLiveRoomId());

        liveFeedback.setHandleStatus(LiveFeedbackHandleStatusEnum.TO_BE_PROCESSED);
        liveFeedback.setCreatedAt(date);
        liveFeedback.setCreateUserId(user.getUserId());
        liveFeedback.setCreateSeatId(user.getSeatId());

        this.save(liveFeedback);
    }

    /**
     * 获取反馈选项列表
     */
    @Override
    public List<FeedbackOptionDTO> getOptionList() {
        JSONArray jsonArray = liveBizProperties.getFeedbackOptionList();
        return jsonArray.toList(FeedbackOptionDTO.class);
    }
}




