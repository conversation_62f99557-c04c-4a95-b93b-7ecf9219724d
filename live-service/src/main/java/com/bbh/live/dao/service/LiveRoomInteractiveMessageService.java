package com.bbh.live.dao.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.bbh.live.dao.dto.UnHandleBargainGoodsMsg;
import com.bbh.model.LiveRoomInteractiveMessage;

import java.util.List;

/**
 * 互动消息，包含用户求讲解和用户议价
 *
* <AUTHOR>
*/
public interface LiveRoomInteractiveMessageService extends IService<LiveRoomInteractiveMessage> {

    /**
     * 获取待处理的消息列表
     * @param roomId 直播间id
     * @return 消息列表
     */
    List<UnHandleBargainGoodsMsg> getUnhandledMessageList(Long roomId);

    /**
     * 导播待处理消息数量
     * @param roomId
     * @return
     */
    Long getUnhandledMessageCount(Long roomId);

}
