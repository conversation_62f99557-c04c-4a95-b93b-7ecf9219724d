package com.bbh.live.dao.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bbh.enums.VipBuyerRefundFenbeiTypeEnum;
import com.bbh.live.dao.dto.vo.AfterSaleVO;
import com.bbh.live.dao.mapper.VipBuyerRefundFenbeiMapper;
import com.bbh.live.dao.service.VipBuyerRefundFenbeiService;
import com.bbh.model.VipBuyerRefundFenbei;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/8/16 08:40
 */
@Service
public class VipBuyerRefundFenbeiServiceImpl extends ServiceImpl<VipBuyerRefundFenbeiMapper, VipBuyerRefundFenbei>
        implements VipBuyerRefundFenbeiService {

    @Override
    public VipBuyerRefundFenbei getAfterSaleRefundFenbei(AfterSaleVO afterSaleVO) {
        VipBuyerRefundFenbei vbEntity = new VipBuyerRefundFenbei();
        vbEntity.setVipBuyerCardId(afterSaleVO.getVipBuyerCardId());
        vbEntity.setBuyerSeatId(afterSaleVO.getSeatId());
        vbEntity.setBuyerOrgId(afterSaleVO.getOrgId());
        vbEntity.setRefundTime(new Date());
        vbEntity.setRemark(StrUtil.format("未使用 分贝返回: {}", afterSaleVO.getFbNum()));
        vbEntity.setRefundFenbei(afterSaleVO.getFbNum());
        vbEntity.setType(VipBuyerRefundFenbeiTypeEnum.OVERDUE_COMPENSATION);
        return vbEntity;
    }
}
