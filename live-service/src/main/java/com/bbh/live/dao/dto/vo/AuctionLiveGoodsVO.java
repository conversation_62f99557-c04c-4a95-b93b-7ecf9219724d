package com.bbh.live.dao.dto.vo;

import com.bbh.enums.LiveGoodsTradeTypeEnum;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> dsy
 * @Date: 2024/8/29
 * @Description:
 */
@Data
@Accessors(chain = true)
public class AuctionLiveGoodsVO {

    private Long liveGoodsId;

    private Long liveRoomId;

    private String liveRoomName;

    private String goodsName;

    private List<String> imgUrlList;

    private BigDecimal currentPrice;

    private BigDecimal increasePrice;

    private Long remainTime;

    private Long remainTimeMs;

    /**
     * 竞拍商品当前最高出价人
     */
    private Long buyerSeatId;

    private String quality;

    private Date putawayAt;

    private LiveGoodsTradeTypeEnum tradeType = LiveGoodsTradeTypeEnum.AUCTION;
}
