package com.bbh.live.dao.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bbh.base.ListBase;
import com.bbh.enums.LiveGoodsTradeTypeEnum;
import com.bbh.enums.LiveRoomStreamStatusEnum;
import com.bbh.live.config.LiveBizProperties;
import com.bbh.live.core.msg.MsgDTO;
import com.bbh.live.core.msg.MsgType;
import com.bbh.live.dao.dto.GlobalMsgGroupDTO;
import com.bbh.live.dao.dto.livegoods.LiveGoodsSubscribeDTO;
import com.bbh.live.dao.dto.vo.AuctionLiveGoodsVO;
import com.bbh.live.dao.mapper.LiveGoodsMapper;
import com.bbh.live.dao.mapper.LiveRoomMapper;
import com.bbh.live.dao.mapper.LiveRoomMsgMapper;
import com.bbh.live.dao.service.LiveGoodsSubscribeService;
import com.bbh.live.dao.service.LiveRoomMsgService;
import com.bbh.live.dao.service.LiveRoomService;
import com.bbh.live.service.livegoods.LiveGoodsListService;
import com.bbh.live.service.livegoods.cache.LiveGoodsCacheManager;
import com.bbh.live.service.msg.ChatroomService;
import com.bbh.live.service.msg.MsgCacheBizService;
import com.bbh.live.service.msg.MsgContext;
import com.bbh.live.service.msg.MsgService;
import com.bbh.live.service.msg.dto.GoodsPutawayMsgDTO;
import com.bbh.live.service.msg.dto.GoodsTransferMsgDTO;
import com.bbh.live.service.msg.dto.SystemNoticeMsgDTO;
import com.bbh.live.service.msg.dto.UserBidSuccessMsgDTO;
import com.bbh.live.service.msg.dto.base.SourceType;
import com.bbh.live.service.room.LiveRoomBizService;
import com.bbh.live.util.JSONUtils;
import com.bbh.live.util.MapUtils;
import com.bbh.model.LiveRoom;
import com.bbh.model.LiveRoomMsg;
import com.bbh.secure.AuthUtil;
import com.bbh.util.AssertUtil;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
* <AUTHOR>
* @description 针对表【live_room_msg(直播间消息)】的数据库操作Service实现
* @createDate 2024-07-31 09:27:35
*/
@Service
@RequiredArgsConstructor
@Slf4j
public class LiveRoomMsgServiceImpl extends ServiceImpl<LiveRoomMsgMapper, LiveRoomMsg> implements LiveRoomMsgService{

    private final MsgService msgService;
    private final ChatroomService chatroomService;
    private final LiveRoomService liveRoomService;
    private final LiveRoomBizService liveRoomBizService;
    private final LiveBizProperties liveBizProperties;
    private final MsgCacheBizService msgCacheBizService;
    private final LiveGoodsSubscribeService goodsSubscribeService;
    private final LiveGoodsMapper liveGoodsMapper;
    private final LiveGoodsListService goodsListService;
    private final LiveGoodsCacheManager liveGoodsCacheManager;
    private final LiveRoomMapper liveRoomMapper;

    /**
     * 获取直播间弹幕消息列表 <br>
     * 最终返回的结构，外层是驼峰，里面的自定义data是下划线 <br>
     *
     * 因此需要将表里的json结构原样返回，避免被jackson序列化 <br>
     * 只有Map才能绕过jackson序列化，不得已出此下策 <br>
     *
     * @param pageIndex 页码
     * @param pageSize  每页条数
     * @param roomId    直播间ID
     * @return 分页结果
     */
    @SneakyThrows
    @Override
    public ListBase<Map<String, Object>> getLiveRoomMsgList(Integer pageIndex, Integer pageSize, Long roomId, Long liveGoodsId) {
        var liveRoom = liveRoomService.lambdaQuery().eq(LiveRoom::getId, roomId).select(LiveRoom::getId, LiveRoom::getStartAt, LiveRoom::getNote).one();
        AssertUtil.assertNotNull(liveRoom, "直播间不存在");

        String chatRoomId = chatroomService.buildChatroomId(roomId);

        // 过滤需要展示的消息类型
        List<String> typeList = List.of(MsgType.TEXT, MsgType.MEME, MsgType.BID_SUCCESS, MsgType.AUCTION_SUCCESS, MsgType.PLATFORM_NOTICE);

        Date startAt = null, endAt = null;
        if (liveGoodsId != null) {
            // 获取商品的开始时间和截止时间
            var liveGoods =  liveGoodsMapper.selectById(liveGoodsId);
            startAt = liveGoods.getPutawayAt();
            endAt = liveGoods.getEndAt();
        }

        // 查询直播间分页列表，根据时间倒序
        Page<LiveRoomMsg> page = new Page<>(pageIndex, pageSize);
        page.setSearchCount(false);
        page = page(page, Wrappers
                .lambdaQuery(LiveRoomMsg.class)
                .in(LiveRoomMsg::getMsgType, typeList)
                .eq(LiveRoomMsg::getToUserId, chatRoomId)
                .eq(LiveRoomMsg::getMsgChannel, "htbLr")
                // 默认只查当前直播间的
                .ge(LiveRoomMsg::getCreatedAt, liveRoom.getStartAt())
                // 如果指定了商品，就查商品所在时间范围内的
                .ge(startAt != null, LiveRoomMsg::getCreatedAt, startAt)
                .le(endAt != null, LiveRoomMsg::getCreatedAt, endAt)
                // 默认按发送时间（精确到秒）排序，如果时间一致，再按时间戳排序
                // 时间戳是后加的字段，可能是null，所以放到后面
                .orderByDesc(LiveRoomMsg::getMsgTimestamp, LiveRoomMsg::getMsgContentTimestamp)
        );
        List<LiveRoomMsg> records = page.getRecords();
        if (records == null) {
            records = new ArrayList<>(1);
        }
        // 转换成DTO的格式
        List<Map<String, Object>> collected = records.stream().map(x -> {
            // 表里存了3层嵌套的JSON字符串，要解析出来
            JSONObject firstContent = JSONUtils.parseNestedJson(x.getContent());
            JSONObject secondContent = firstContent.getJSONObject("content");
            JSONObject thirdContent = secondContent.getJSONObject("content");
            return Convert.toMap(String.class, Object.class, thirdContent);
        }).collect(Collectors.toList());
        // 按时间反转，重新排序
        Collections.reverse(collected);
        // 把系统公告追加到最后
//        collected.addLast(buildSystemNotice(liveRoom));

        // 转换输出格式
        Page<Map<String, Object>> resultPage = new Page<>(pageIndex, pageSize);
        resultPage.setRecords(collected);
        resultPage.setTotal(page.getTotal());
        resultPage.setPages(page.getPages());
        resultPage.setSize(page.getSize());
        resultPage.setCurrent(page.getCurrent());

        return ListBase.pageConvertToListBase(resultPage);
    }

    /**
     * 构建系统公告消息的 Map 表示。
     *
     * <p>此方法创建一个系统公告消息，并将其转换为下划线命名的 Map 格式。
     * 系统公告被设置为最新的消息，消息 ID 被设置为 "-1"。</p>
     *
     * @param liveRoom 直播间
     * @return 包含系统公告消息信息的 Map，键为下划线命名的字符串，值为对应的对象
     *
     * @see SystemNoticeMsgDTO
     * @see MsgDTO
     * @see MapUtils#toUnderlineMap(Object)
     */
    private Map<String, Object> buildSystemNotice(LiveRoom liveRoom) {
        // 系统公告加到第一个，作为最新的消息
        SystemNoticeMsgDTO systemNoticeMsgDTO = new SystemNoticeMsgDTO();
        systemNoticeMsgDTO.setLiveRoomId(liveRoom.getId());
        String note = StrUtil.blankToDefault(liveRoom.getNote(), liveBizProperties.getSystemNotice());
        systemNoticeMsgDTO.setContent(trimContent(note));
        systemNoticeMsgDTO.setSourceType(SourceType.SYSTEM_NOTICE.getCode());
        MsgDTO<SystemNoticeMsgDTO> noticeMsg = msgService.buildMsgDTO(systemNoticeMsgDTO, new MsgContext().setSenderUserId(0L).setSenderSeatId(0L).setLiveRoomId(liveRoom.getId()));
        noticeMsg.setMsgId("-1");
        return MapUtils.toUnderlineMap(noticeMsg);
    }

    private String trimContent(String content) {
        if (StrUtil.isBlank(content)) {
            return content;
        }
        // 正则移除首尾的"
        return content.replaceAll("^\"|\"$", "");
    }

    /**
     * 全局消息列表，包含预约、传送和上帝视角(上架中、竞拍中)
     *
     * @return Result
     */
    @Override
    public GlobalMsgGroupDTO getGlobalMsgGroup() {
        Long seatId = AuthUtil.getSeatId();
        GlobalMsgGroupDTO group = new GlobalMsgGroupDTO();

        // 传送
        List<GoodsTransferMsgDTO> transferMsgDTOList = msgCacheBizService.getGoodsTransferMsgListForBuyer(seatId);
        group.setTransfer(transferMsgDTOList.stream()
                .map(msg -> msgService.buildMsgDTO(
                        msg,
                        new MsgContext()
                )).toList()
        );

        // 预约商品 过滤已结束直播间，过滤流拍，成交商品
        List<LiveGoodsSubscribeDTO> goodsSubscribeList = goodsSubscribeService.getSubscribeGoodsInfoByBuyerSeatId(seatId);
        if (goodsSubscribeList != null) {
            group.setSubscribeGoodsList(goodsSubscribeList.stream().map(subscribe -> new GlobalMsgGroupDTO.GoodsSubscribeInfo(subscribe.getLiveGoodsId(), subscribe.getRemark())).toList());
        }

        // 上帝视角
        List<Long> godsPerspectiveGoodsIdList = enrichGodsPerspectiveGoods(group);

        // 当前用户需要展示的预约商品 过滤掉已处理，只展示上架中和竞拍中的
        List<Long> canShowSubscribeGoodsIdList = new ArrayList<>();
        if (goodsSubscribeList != null) {
            canShowSubscribeGoodsIdList = goodsSubscribeList.stream()
                    .filter(subscribe -> !subscribe.getIfHandled())
                    .map(LiveGoodsSubscribeDTO::getLiveGoodsId)
                    .collect(Collectors.toList());
        }
        // 使用 HashSet 来提高交集操作的效率
        Set<Long> godsPerspectiveSet = new HashSet<>(godsPerspectiveGoodsIdList);
        canShowSubscribeGoodsIdList.retainAll(godsPerspectiveSet);
        group.setShowSubscribeGoodsList(canShowSubscribeGoodsIdList);

        return group;
    }

    private List<Long> enrichGodsPerspectiveGoods(GlobalMsgGroupDTO group){
        List<MsgDTO<?>> godsPerspectiveGoods = getGodsPerspectiveGoodsList(AuthUtil.getUserId(), AuthUtil.getSeatId());
        group.setGodsPerspective(godsPerspectiveGoods);

        // 上帝视角商品id集合
        return godsPerspectiveGoods.stream()
                .map(msg -> {
                    if (msg.getData() instanceof UserBidSuccessMsgDTO) {
                        return ((UserBidSuccessMsgDTO) msg.getData()).getGoods().getLiveGoodsId();
                    } else if (msg.getData() instanceof GoodsPutawayMsgDTO) {
                        return ((GoodsPutawayMsgDTO) msg.getData()).getGoods().getLiveGoodsId();
                    }
                    return null;
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    @Override
    public List<MsgDTO<?>> getGodsPerspectiveGoodsList(Long userId, Long seatId) {
        List<MsgDTO<?>> godsPerspectiveGoods = new ArrayList<>();

        var inLiveRooms = liveRoomMapper.getGodViewRoomList(userId);
        if(CollectionUtil.isEmpty(inLiveRooms)){
            return godsPerspectiveGoods;
        }

        return getGodsPerspectiveGoodsList(inLiveRooms.stream().map(LiveRoom::getId).collect(Collectors.toList()));
    }

    private List<MsgDTO<?>> getGodsPerspectiveGoodsList(List<Long> liveRoomIdList) {
        List<MsgDTO<?>> godsPerspectiveGoods = new ArrayList<>();
        if (CollectionUtil.isEmpty(liveRoomIdList)) {
            return godsPerspectiveGoods;
        }

        //黑白名单过滤
        if(AuthUtil.getUser(null) != null){
            liveRoomIdList = liveRoomIdList.stream().filter(room -> liveRoomBizService.liveRoomIsVisible(room, AuthUtil.getUserId())).toList();
        }

        // 获取两种商品列表
        var auctionLiveGoodsList = goodsListService.getAuctionLiveGoodsListByLiveRoom(liveRoomIdList);
        var putAwayLiveGoodsList = goodsListService.getPutAwayLiveGoodsListByLiveRoom(liveRoomIdList);

        // 创建一个Set来存储竞拍商品的ID，用于快速判断
        Set<Long> auctionGoodsIds = new HashSet<>();
        if (CollectionUtil.isNotEmpty(auctionLiveGoodsList)) {
            auctionGoodsIds = auctionLiveGoodsList.stream()
                    .map(AuctionLiveGoodsVO::getLiveGoodsId)
                    .collect(Collectors.toSet());
        }

        // 合并列表并排序
        List<AuctionLiveGoodsVO> allGoodsList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(auctionLiveGoodsList)) {
            allGoodsList.addAll(auctionLiveGoodsList);
        }
        if (CollectionUtil.isNotEmpty(putAwayLiveGoodsList)) {
            allGoodsList.addAll(putAwayLiveGoodsList);
        }

        // 按putawayAt倒序排序
        allGoodsList.sort((a, b) -> b.getPutawayAt().compareTo(a.getPutawayAt()));

        for (AuctionLiveGoodsVO goods : allGoodsList) {
            // 竞拍中的
            if (auctionGoodsIds.contains(goods.getLiveGoodsId())) {
                UserBidSuccessMsgDTO bidSuccessMsg = new UserBidSuccessMsgDTO();
                bidSuccessMsg.setLiveRoomId(goods.getLiveRoomId());
                bidSuccessMsg.setGoods(msgService.retrieveGoodsInfo(goods.getLiveGoodsId()))
                        .setBidAmount(goods.getCurrentPrice())
                        .setUser(goods.getBuyerSeatId() != null ? msgService.retrieveSeatInfo(goods.getBuyerSeatId()) : null)
                        .setBiddenList(liveGoodsCacheManager.getLiveGoodsAuctionCache().getAuctionGoodsBidInfo(goods.getLiveRoomId(), goods.getLiveGoodsId()))
                        .setRemainTime(goods.getRemainTime())
                        .setRemainTimeMs(goods.getRemainTimeMs())
                        .setTradeType(goods.getTradeType().getCode());
                godsPerspectiveGoods.add(msgService.buildMsgDTO(bidSuccessMsg, new MsgContext()));
            }
            // 讲解中的
            else {
                GoodsPutawayMsgDTO dto = new GoodsPutawayMsgDTO();
                dto.setLiveRoomId(goods.getLiveRoomId());
                dto.setGoods(msgService.retrieveGoodsInfo(goods.getLiveGoodsId()));
                dto.setTradeType(goods.getTradeType() != null ? goods.getTradeType().getCode() : LiveGoodsTradeTypeEnum.AUCTION.getCode());
                godsPerspectiveGoods.add(msgService.buildMsgDTO(dto, new MsgContext()));
            }
        }

        return godsPerspectiveGoods;
    }

    @Override
    public List<MsgDTO<?>> getGodsPerspectiveGoodsList() {
        Date now = new Date();
        List<LiveRoom> liveRoomList = liveRoomService.lambdaQuery()
                .eq(LiveRoom::getStreamStatus, LiveRoomStreamStatusEnum.ON)
                .le(LiveRoom::getStartAt, now)
                .ge(LiveRoom::getEndAt, now)
                .select(LiveRoom::getId, LiveRoom::getCreatedAt)
                .list();

        return getGodsPerspectiveGoodsList(liveRoomList.stream().map(LiveRoom::getId).collect(Collectors.toList()));
    }

}




