package com.bbh.live.dao.dto.vo;

import com.bbh.enums.LiveSyncSceEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Map;

/**
 * 导播的直播间详情
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class DirectorRoomDetailVO extends LiveRoomVO {

    /** 待上架数量 */
    private Long waitPutAwayCount;

    /** 待完成数量 */
    private Long waitCompleteCount;

    /** 是否显示倒计时 */
    private Boolean ifShowCountdown = false;

    /** 当前剩余的倒计时秒数 */
    private Long countdownSeconds = 0L;

    /** 是否在开播后关闭过商品清单 */
    private Boolean ifClosedGoodsListAfterLiveStart = false;

    /** 商品清单是否使用wgt */
    private Boolean ifSwitchWgtGoodsList = false;

    /**
     * 直播商品直播结束后是否直接同步到云展
     */
    private LiveSyncSceEnum ifNeedLiveSyncSce;

    /** 同步云展弹框倒计时秒数 */
    private Integer syncSceSeconds;

    /**
     * 进入直播间发的系统公告 等待时间（秒）
     */
    private Long systemNoticeWaitSeconds = 5 * 60L;

    /***
     * 系统公告
     */
   private Map<String, Object> systemNotice;

}
