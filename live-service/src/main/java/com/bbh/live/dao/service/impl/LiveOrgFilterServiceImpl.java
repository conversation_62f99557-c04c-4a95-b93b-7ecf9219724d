package com.bbh.live.dao.service.impl;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bbh.base.PageBase;
import com.bbh.enums.LiveOrgFilterModeEnum;
import com.bbh.enums.LiveOrgFilterSourceTypeEnum;
import com.bbh.exception.ServiceException;
import com.bbh.live.dao.dto.LiveFilterSearchUserDTO;
import com.bbh.live.dao.dto.vo.LiveOrgFilterVO;
import com.bbh.live.dao.mapper.LiveOrgFilterMapper;
import com.bbh.live.dao.service.LiveOrgFilterService;
import com.bbh.live.service.buyer.vip.BuyerVipUtil;
import com.bbh.live.service.buyer.vip.vo.BuyerVipConfigVO;
import com.bbh.model.LiveOrgFilter;
import com.bbh.vo.AuthUser;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Service
@AllArgsConstructor
public class LiveOrgFilterServiceImpl extends ServiceImpl<LiveOrgFilterMapper, LiveOrgFilter> implements LiveOrgFilterService {

    @Override
    public IPage<LiveOrgFilterVO> pageListUser(PageBase pageBase, AuthUser user, LiveOrgFilterSourceTypeEnum liveOrgFilterSourceTypeEnum) {
        Long orgId = user.getOrgId();

        Page<LiveOrgFilterVO> page = new Page<>(pageBase.getCurrentPage(), pageBase.getPerPage());
        IPage<LiveOrgFilterVO> pageResult = this.baseMapper.pageListUser(page, orgId, liveOrgFilterSourceTypeEnum == null ? null : liveOrgFilterSourceTypeEnum.getCode(), LiveOrgFilterModeEnum.BLACKLIST.getCode());
        pageResult.getRecords().forEach(x -> {
            // 判断是否会员
            boolean after = Objects.nonNull(x.getTimeVipEnd()) && DateUtil.endOfDay(x.getTimeVipEnd()).after(DateUtil.beginOfDay(new Date()));
            boolean isVip = Objects.nonNull(x.getVipCardId()) && after;
            x.setIsVip(isVip);

            // 计算会员等级
            if (isVip) {
                BuyerVipConfigVO vipConfig = BuyerVipUtil.getBuyerVipConfig(x.getExp(), x.getIsAnnualFeeVip());
                x.setVipLevel(vipConfig.getVipLevel());
            }

            // 处理昵称
            x.setNickName(x.getShowName());
        });
        return pageResult;
    }

    @SuppressWarnings("all")
    @Override
    public List<LiveOrgFilter> getByOrgId(Long orgId, LiveOrgFilterSourceTypeEnum liveOrgFilterSourceTypeEnum) {
        return this.list(new LambdaQueryWrapper<LiveOrgFilter>()
                .eq(LiveOrgFilter::getOrgId, orgId).eq(LiveOrgFilter::getSourceType, liveOrgFilterSourceTypeEnum)
                .isNull(LiveOrgFilter::getDeletedAt)
        );
    }

    @Override
    public IPage<LiveOrgFilterVO> searchOrg(AuthUser user, LiveFilterSearchUserDTO dto) {
        String searchKey = dto.getSearchKey();
        if (StringUtils.isBlank(searchKey)) {
            throw new ServiceException("搜索词不能为空");
        }
        Long userId = user.getUserId();
        Page<LiveOrgFilterVO> page = new Page<>(dto.getCurrentPage(), dto.getPerPage());
        return baseMapper.searchOrg(page, userId, dto.getSearchKey());
    }


    @Override
    public IPage<LiveOrgFilterVO> pageListOrg(PageBase pageBase, AuthUser user, LiveOrgFilterSourceTypeEnum liveOrgFilterSourceTypeEnum) {
        Page<LiveOrgFilterVO> page = new Page<>(pageBase.getCurrentPage(), pageBase.getPerPage());
        return baseMapper.pageListOrg(page,user.getUserId(),liveOrgFilterSourceTypeEnum.getCode());
    }
}




