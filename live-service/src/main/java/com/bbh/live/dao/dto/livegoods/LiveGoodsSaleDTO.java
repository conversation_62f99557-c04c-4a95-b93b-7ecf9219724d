package com.bbh.live.dao.dto.livegoods;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR> dsy
 * @Date: 2024/9/12
 * @Description:
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class LiveGoodsSaleDTO {

    private Long liveGoodsId;

    private String globalGoodsName;

    private String liveGoodsCode;

    private String qualify;

    private List<String> imageUrlList;

    private BigDecimal sellPrice;

    private Long belongSeatId;

    private String belongUserName;

    private String belongUserAuctionCode;
}
