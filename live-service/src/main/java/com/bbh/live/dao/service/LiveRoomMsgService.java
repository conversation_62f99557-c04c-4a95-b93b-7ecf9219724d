package com.bbh.live.dao.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.bbh.base.ListBase;
import com.bbh.live.core.msg.MsgDTO;
import com.bbh.live.dao.dto.GlobalMsgGroupDTO;
import com.bbh.live.service.msg.dto.SystemNoticeMsgDTO;
import com.bbh.live.util.MapUtils;
import com.bbh.model.LiveRoom;
import com.bbh.model.LiveRoomMsg;

import java.util.List;
import java.util.Map;

/**
* <AUTHOR>
* @description 针对表【live_room_msg(直播间消息)】的数据库操作Service
* @createDate 2024-07-31 09:27:35
*/
public interface LiveRoomMsgService extends IService<LiveRoomMsg> {

    /**
     * 获取直播间弹幕消息列表 <br>
     * 最终返回的结构，外层是驼峰，里面的自定义data是下划线 <br>
     *
     * 因此需要将表里的json结构原样返回，避免被jackson序列化 <br>
     * 只有Map才能绕过jackson序列化，不得已出此下策 <br>
     *
     * @param pageIndex     当前页码
     * @param pageSize      每页条数
     * @param roomId        直播间ID
     * @param liveGoodsId   商品ID, 获取该商品的时间，获取时间段内的消息
     * @return  分页结果
     */
    ListBase<Map<String, Object>> getLiveRoomMsgList(Integer pageIndex, Integer pageSize, Long roomId, Long liveGoodsId);

    /**
     * 全局消息列表，包含预约、传送和上帝视角(上架中、竞拍中)
     * @return Result
     */
    GlobalMsgGroupDTO getGlobalMsgGroup();

    /**
     * 上帝视角商品列表
     */
    List<MsgDTO<?>> getGodsPerspectiveGoodsList(Long userId, Long seatId);

    List<MsgDTO<?>> getGodsPerspectiveGoodsList();

}
