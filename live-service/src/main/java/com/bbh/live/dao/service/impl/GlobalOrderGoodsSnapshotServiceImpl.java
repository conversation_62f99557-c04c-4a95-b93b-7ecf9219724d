package com.bbh.live.dao.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bbh.live.dao.dto.GlobalOrderGoodsSnapshotDTO;
import com.bbh.live.dao.mapper.GlobalOrderGoodsSnapshotMapper;
import com.bbh.live.dao.service.GlobalOrderGoodsSnapshotService;
import com.bbh.live.dao.service.GlobalOrderService;
import com.bbh.model.GlobalOrder;
import com.bbh.model.GlobalOrderGoodsSnapshot;
import com.bbh.model.GlobalOrderItem;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
* <AUTHOR>
* @description 针对表【global_order_goods_snapshot(订单商品快照)】的数据库操作Service实现
* @createDate 2024-09-02 18:50:46
*/
@Service
@AllArgsConstructor
public class GlobalOrderGoodsSnapshotServiceImpl extends ServiceImpl<GlobalOrderGoodsSnapshotMapper, GlobalOrderGoodsSnapshot>
    implements GlobalOrderGoodsSnapshotService{

    private final GlobalOrderService globalOrderService;

    @Override
    @SuppressWarnings("all")
    public void saveGoodsSnapshot(GlobalOrder globalOrder) {
        var orderItems = globalOrderService.getOrderItems(globalOrder.getId(), GlobalOrderItem::getId, GlobalOrderItem::getTargetId);
        if(orderItems.isEmpty()){
            return;
        }

        Map<Long, GlobalOrderItem> erpIdOrderMap = orderItems.stream().collect(Collectors.toMap(GlobalOrderItem::getTargetId, Function.identity()));

        List<GlobalOrderGoodsSnapshotDTO> goodsSnapshotList = this.getBaseMapper().selectSnapshotGoodsInfo(erpIdOrderMap.keySet());

        goodsSnapshotList.forEach(goodsSnapshot -> {
            goodsSnapshot.setGlobalOrderItemId(erpIdOrderMap.get(goodsSnapshot.getErpGoodsId()).getId());
            goodsSnapshot.setPrice(erpIdOrderMap.get(goodsSnapshot.getErpGoodsId()).getRealPayedAmount());
        });

        var goodsSnapshotSet = goodsSnapshotList.stream().map(x -> (GlobalOrderGoodsSnapshot) x).collect(Collectors.toSet());
        this.saveBatch(goodsSnapshotSet);
    }
}




