package com.bbh.live.dao.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.bbh.base.PageBase;
import com.bbh.live.dao.dto.LotteryWinningRecordDTO;
import com.bbh.model.GlobalLotteryWinningRecord;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【global_lottery_winning_record(抽奖中奖记录表)】的数据库操作Service
* @createDate 2024-10-08 16:01:43
*/
public interface GlobalLotteryWinningRecordService extends IService<GlobalLotteryWinningRecord> {

    /**
     * 查询用户的转盘中奖记录，默认排除谢谢惠顾的类型
     * @param wheelId    转盘ID
     * @param seatId     席位ID
     * @return           转盘中奖记录
     */
    List<LotteryWinningRecordDTO> findListByWheelIdAndSeatId(Long wheelId, Long seatId);

    List<LotteryWinningRecordDTO> findListByWheelIdAndSeatId(Long wheelId, Long seatId, Boolean excludeThanksgiving);

    List<LotteryWinningRecordDTO> findListByWheelIdAndSeatId(Long wheelId, Long seatId, Boolean excludeThanksgiving, String lastSql);

    Page<LotteryWinningRecordDTO> findPageByWheelIdAndSeatId(PageBase page, Long wheelId, Long seatId, Boolean excludeThanksgiving);
}
