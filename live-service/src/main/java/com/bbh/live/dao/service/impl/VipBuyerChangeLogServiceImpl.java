package com.bbh.live.dao.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bbh.model.VipBuyerChangeLog;
import com.bbh.live.dao.mapper.VipBuyerChangeLogMapper;
import com.bbh.live.dao.service.VipBuyerChangeLogService;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【vip_buyer_change_log(买手权益变更记录)】的数据库操作Service实现
* @createDate 2024-08-14 17:51:46
*/
@Service
public class VipBuyerChangeLogServiceImpl extends ServiceImpl<VipBuyerChangeLogMapper, VipBuyerChangeLog>
    implements VipBuyerChangeLogService{

}




