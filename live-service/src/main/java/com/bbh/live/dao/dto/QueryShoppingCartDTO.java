package com.bbh.live.dao.dto;

import com.bbh.enums.GlobalBizTypeEnum;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 查询购物车的Mybatis查询参数
 * <AUTHOR>
 */
@Data
public class QueryShoppingCartDTO {

    /**
     * 买手席位id
     */
    private Long buyerSeatId;

    /**
     * 买手商户id
     */
    private Long buyerOrgId;

    /**
     * 买手商户id列表
     */
    private List<Long> buyerOrgIdList;

    /**
     * 业务商品id列表
     */
    private List<Long> bizGoodsIdList;

    /**
     * 业务id列表
     */
    private List<Long> bizIdList;

    /**
     * 业务类型，默认为直播
     */
    private Integer bizType = GlobalBizTypeEnum.LIVE.getCode();

    /**
     * 卖家商户id
     */
    private Long sellerOrgId;

    /**
     * 货品名称
     */
    private String globalGoodsName;

    /**
     * 成交时间-开始时间
     */
    private Date liveSellTimeStart;

    /**
     * 成交时间-结束时间
     */
    private Date liveSellTimeEnd;

    /** 自定义的排序 */
    private String orderBy;
    /**
     * 订单状态+收银台状态查询
     */
    private Integer orderStatus;
}
