package com.bbh.live.dao.dto;

import com.bbh.base.PageBase;
import com.bbh.base.Sort;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 直播列表的请求参数
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class QueryLiveRoomDTO extends PageBase {

    /**
     * 默认50条数据
     */
    private int perPage = 50;

    /**
     * 当前用户ID，仅用于黑名单过滤
     */
    private Long currentUserId;

    /**
     * 当前席位ID
     */
    private Long currentSeatId;

    /**
     * 房间ID，精确查询
     */
    private Long roomId;

    /**
     * 直播间名称，模糊查询
     */
    private String roomName;

    /**
     * 商户ID
     */
    private Long orgId;

    /**
     * 商户名称，模糊查询
     */
    private String orgName;

    /**
     * 排序，支持多个字段排序
     */
    private List<Sort> sort;

    /**
     * 状态： 0: 未开始  1: 进行中  2: 已结束 <br>
     * 默认 进行中
     */
    private Integer status;

    /**
     * 状态列表，支持同时查询多个状态
     */
    private List<Integer> statusList;

    /**
     * 过滤白名单（邀请名单），必须同时传当前用户id
     */
    private Boolean filterWhitelist;

    /**
     * 过滤黑名单，必须同时传当前用户id
     */
    private Boolean filterBlacklist;

    /**
     * 是否排除掉相关工作人员，即导播和主播
     */
    private Boolean excludeRelatedStaff;

    /**
     * 过滤模式:  10-白名单 20-黑名单
     */
    private List<Integer> filterModeList;

    /**
     * 排除掉的主播id
     */
    private List<Long> notInAnchorIdList;

    /**
     * 排除掉的导播id
     */
    private List<Long> notInDirectorIdList;

    /**
     * 是否显示
     */
    private Boolean showable;

    /**
     * 直播流状态
     */
    private Integer streamStatus;

    /**
     * 用于排序的直播间ID <br>
     * 如果没有传room_id，就走原来的查询和排序逻辑。<br>
     * 如果有room_id，就单独在查出来这个直播间，放到列表第一个
     */
    private Long sortRoomId;

    /**
     * 是否导播的直播间列表
     */
    private Boolean ifDirector;

    /**
     * 是否是推荐直播间
     */
    private Boolean ifRecommend;

    /** 是否过滤 白名单可见不可进，不过滤就默认都是不可见，只有白名单里面的才可见 */
    private Boolean filterWhitelistShow = true;

    /** 是否有成交过的 */
    private Boolean hasDeal;
}
