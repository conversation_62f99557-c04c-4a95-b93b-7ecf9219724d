package com.bbh.live.dao.dto;

import com.bbh.enums.LiveRoomInteractiveMessageTypeEnum;
import com.bbh.util.AssertUtil;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR> dsy
 * @Date: 2024/8/2
 * @Description:
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class InteractiveMsgDTO extends BargainGoodsDTO {

    /**
     * 消息id
     */
    private Long msgId;

    /**
     * 消息类型
     */
    private LiveRoomInteractiveMessageTypeEnum type;

    /**
     * 是否处理。false关闭，true处理
     */
    private Boolean ifHandle;


    public void checkProperties(){
        AssertUtil.assertNotNull(this.getMsgId(), "消息不存在");
        AssertUtil.assertNotNull(this.getLiveGoodsId(), "商品不存在");
        AssertUtil.assertNotNull(this.getBuyerSeatId(), "买手不存在");
        AssertUtil.assertNotNull(this.getLiveRoomId(), "直播间不存在");
    }
}
