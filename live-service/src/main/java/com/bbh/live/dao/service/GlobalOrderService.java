package com.bbh.live.dao.service;

import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.baomidou.mybatisplus.extension.service.IService;
import com.bbh.model.GlobalOrder;
import com.bbh.model.GlobalOrderItem;
import com.bbh.model.GlobalOrderSub;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【global_order(全局订单)】的数据库操作Service
* @createDate 2024-07-25 11:52:55
*/
public interface GlobalOrderService extends IService<GlobalOrder> {

    /**
     * 更新订单状态，同步更新子订单和订单项
     * @param globalOrderId   订单ID
     * @param status          订单状态
     */
    void updateOrderStatus(Long globalOrderId, Integer status);

    /**
     * 更新订单流水号，同步更新子订单，订单项不需要更新
     * @param globalOrderId     订单ID
     * @param newOrderNo        新的订单流水号
     */
    void updateOrderNo(Long globalOrderId, String newOrderNo);

    /**
     * 根据订单ID获取订单明细
     * @param globalOrderId
     * @param columns 查询字段 eg订单. GlobalOrderItem::getBizType
     * @return
     */
    List<GlobalOrderItem> getOrderItems(Long globalOrderId, SFunction<GlobalOrderItem, ?>... columns);

    /**
     * 根据订单ID获取商家订单列表
     * @param globalOrderId
     * @return
     */
    List<GlobalOrderSub> getOrderSubList(Long globalOrderId);

    /**
     *  订单已支付
     * @param globalOrder
     * @param orderPaidInfo
     */
    //void orderPaid(GlobalOrder globalOrder, OrderPaidInfo orderPaidInfo);
}
