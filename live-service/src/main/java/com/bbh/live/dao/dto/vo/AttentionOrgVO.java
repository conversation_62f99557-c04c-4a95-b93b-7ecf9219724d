package com.bbh.live.dao.dto.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR> dsy
 * @Date: 2024/8/28
 * @Description: 关注商家vo
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AttentionOrgVO {

    private Long orgId;
    private String encryptOrgId;
    private String orgName;
    private String logoUrl;

    /**
     * 身份1 微商 2 企业
     */
    private Integer orgType;

    /**
     * 关注时间
     */
    private Date attentionDate;

    /**
     * 当前或下一场直播
     */
    private Long roomId;
    private String roomName;
    /**
     * 是否正在直播
     */
    private Boolean inLive;

    /**
     * 是否是专场直播
     */
    private Boolean ifSpecial;

    /**
     * 直播开始时间
     */
    private Date startAt;

    /**
     * 商家有上新
     */
    private Boolean hasNewGoods = false;
}
