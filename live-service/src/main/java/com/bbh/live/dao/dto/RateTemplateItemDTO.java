package com.bbh.live.dao.dto;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class RateTemplateItemDTO {

    private Long id;

    /**
     * 模板id
     */
    private Long templateId;

    private String templateName;

    private Long roomId;

    /**
     * 一级分类id
     */
    private Integer oneClassifyId;

    /**
     * 最小值
     */
    private Integer minValue;

    /**
     * 最大值
     */
    private Integer maxValue;

    /**
     * 费率
     */
    private BigDecimal rate;

}
