package com.bbh.live.dao.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bbh.model.LiveRateTemplateItem;
import com.bbh.live.dao.mapper.LiveRateTemplateItemMapper;
import com.bbh.live.dao.service.LiveRateTemplateItemService;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【live_rate_template_item(费率模板项)】的数据库操作Service实现
* @createDate 2024-07-25 11:53:19
*/
@Service
public class LiveRateTemplateItemServiceImpl extends ServiceImpl<LiveRateTemplateItemMapper, LiveRateTemplateItem>
    implements LiveRateTemplateItemService{

}




