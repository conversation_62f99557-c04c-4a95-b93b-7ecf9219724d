package com.bbh.live.dao.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bbh.live.dao.mapper.VipBuyerBindLogMapper;
import com.bbh.live.dao.service.GlobalOrgSeatService;
import com.bbh.live.dao.service.IGlobalUserService;
import com.bbh.live.dao.service.VipBuyerBindLogService;
import com.bbh.model.GlobalOrgSeat;
import com.bbh.model.GlobalUser;
import com.bbh.model.VipBuyerBindLog;
import com.bbh.secure.AuthUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.function.BiConsumer;

/**
* <AUTHOR>
* @description 针对表【vip_buyer_bind_log(买手vip换绑记录)】的数据库操作Service实现
* @createDate 2024-11-11 08:37:17
*/
@Service
@Slf4j
@AllArgsConstructor
public class VipBuyerBindLogServiceImpl extends ServiceImpl<VipBuyerBindLogMapper, VipBuyerBindLog>
    implements VipBuyerBindLogService {

    private final GlobalOrgSeatService globalOrgSeatService;
    private final IGlobalUserService globalUserService;

    @Override
    public void saveVipBuyerBindLog(Long oldSeatId, Long newSeatId, Long vipId) {

        VipBuyerBindLog vipBuyerBindLog = new VipBuyerBindLog();
        vipBuyerBindLog.setVipBuyerCardId(vipId);
        vipBuyerBindLog.setOrgId(AuthUtil.getOrgId());
        vipBuyerBindLog.setCreatedAt(new Date());

        // 原用户信息
        seatInfo(oldSeatId, (orgSeat, globalUser) -> {
            vipBuyerBindLog.setOldSeatId(orgSeat.getId());
            vipBuyerBindLog.setOldSeatShowName(orgSeat.getShowName());
            vipBuyerBindLog.setOldSeatUserPhone(globalUser.getPhone());
            vipBuyerBindLog.setOldSeatUserPurePhone(globalUser.getPurePhone());
        });
        // 新用户信息
        seatInfo(newSeatId, (orgSeat, globalUser) -> {
            vipBuyerBindLog.setSeatId(orgSeat.getId());
            vipBuyerBindLog.setSeatShowName(orgSeat.getShowName());
            vipBuyerBindLog.setSeatUserPhone(globalUser.getPhone());
            vipBuyerBindLog.setSeatUserPurePhone(globalUser.getPurePhone());
        });
        this.save(vipBuyerBindLog);
    }

    private void seatInfo(Long seatId, BiConsumer<GlobalOrgSeat, GlobalUser> consumer) {
        if(seatId != null){
            GlobalOrgSeat globalOrgSeat = globalOrgSeatService.lambdaQuery().eq(GlobalOrgSeat::getId, seatId)
                    .select(GlobalOrgSeat::getShowName, GlobalOrgSeat::getUserId)
                    .one();
            if(globalOrgSeat == null){
                log.error("换绑人不存在,seatId:{}", seatId);
            } else {
                GlobalUser newUser = globalUserService.lambdaQuery().eq(GlobalUser::getId, globalOrgSeat.getUserId())
                        .select(GlobalUser::getPurePhone, GlobalUser::getPhone)
                        .one();
                consumer.accept(globalOrgSeat, newUser);
            }
        }
    }

}




