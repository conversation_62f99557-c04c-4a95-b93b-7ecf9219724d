package com.bbh.live.dao.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bbh.live.dao.mapper.GlobalVirtualOrderPaymentMapper;
import com.bbh.live.dao.service.GlobalVirtualOrderPaymentService;
import com.bbh.model.GlobalVirtualOrderPayment;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【global_virtual_order_payment(全局虚拟订单支付信息表)】的数据库操作Service实现
* @createDate 2024-11-12 11:02:19
*/
@Service
public class GlobalVirtualOrderPaymentServiceImpl extends ServiceImpl<GlobalVirtualOrderPaymentMapper, GlobalVirtualOrderPayment>
    implements GlobalVirtualOrderPaymentService {

}




