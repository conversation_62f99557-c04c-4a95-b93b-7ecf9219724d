package com.bbh.live.dao.dto.livegoods;

import com.bbh.enums.LiveGoodsTradeTypeEnum;
import com.bbh.util.AssertUtil;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR> dsy
 * @Date: 2024/7/26
 * @Description:
 */
@Data
public class LiveGoodsPutAwayDTO {

    /**
     * 直播间id
     */
    private Long liveRoomId;

    /**
     * 商品id
     */
    private Long liveGoodsId;

    /**
     * 起拍价
     */
    private BigDecimal startPrice;

    /**
     * 加价幅度
     */
    private BigDecimal increasePrice;

    /**
     * 竞拍时长，上架时设置
     */
    private Integer auctionDuration;

    /**
     * 导播备注
     */
    private String directorRemark;

    /**
     * 交易方式
     */
    private LiveGoodsTradeTypeEnum tradeType;

    public void check(){
        AssertUtil.assertNotNull(liveRoomId, "请指定直播间");
        AssertUtil.assertNotNull(liveGoodsId, "请选择商品");
    }
}
