package com.bbh.live.dao.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bbh.base.PageBase;
import com.bbh.enums.GlobalLotteryPrizePoolTypeEnum;
import com.bbh.live.dao.dto.LotteryWinningRecordDTO;
import com.bbh.live.dao.dto.QuerySimpleUserDTO;
import com.bbh.live.dao.dto.SimpleUserInfoDTO;
import com.bbh.live.dao.mapper.GlobalLotteryPrizePoolMapper;
import com.bbh.live.dao.mapper.GlobalLotteryWinningRecordMapper;
import com.bbh.live.dao.service.GlobalLotteryWinningRecordService;
import com.bbh.live.dao.service.GlobalOrgSeatService;
import com.bbh.model.GlobalLotteryPrizePool;
import com.bbh.model.GlobalLotteryWinningRecord;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
* <AUTHOR>
* @description 针对表【global_lottery_winning_record(抽奖中奖记录表)】的数据库操作Service实现
* @createDate 2024-10-08 16:01:43
*/
@Service
@AllArgsConstructor
@Slf4j
public class GlobalLotteryWinningRecordServiceImpl extends ServiceImpl<GlobalLotteryWinningRecordMapper, GlobalLotteryWinningRecord> implements GlobalLotteryWinningRecordService{

    private final GlobalLotteryPrizePoolMapper globalLotteryPrizePoolMapper;
    private final GlobalOrgSeatService globalOrgSeatService;

    /**
     * 查询用户的转盘中奖记录，默认排除谢谢惠顾的类型
     *
     * @param wheelId 转盘ID
     * @param seatId  席位ID
     * @return 转盘中奖记录
     */
    @Override
    public List<LotteryWinningRecordDTO> findListByWheelIdAndSeatId(Long wheelId, Long seatId) {
        return findListByWheelIdAndSeatId(wheelId, seatId, false);
    }

    @Override
    public List<LotteryWinningRecordDTO> findListByWheelIdAndSeatId(Long wheelId, Long seatId, Boolean excludeThanksgiving) {
        return findListByWheelIdAndSeatId(wheelId, seatId, excludeThanksgiving, null);
    }

    @Override
    public List<LotteryWinningRecordDTO> findListByWheelIdAndSeatId(Long wheelId, Long seatId, Boolean excludeThanksgiving, String lastSql) {
        // 先查中奖记录
        LambdaQueryWrapper<GlobalLotteryWinningRecord> lqw = new LambdaQueryWrapper<>();
        lqw.eq(wheelId != null, GlobalLotteryWinningRecord::getWheelId, wheelId)
                .eq(seatId != null, GlobalLotteryWinningRecord::getSeatId, seatId)
                // 排除谢谢惠顾
                .ne(Boolean.TRUE.equals(excludeThanksgiving), GlobalLotteryWinningRecord::getPrizeType, GlobalLotteryPrizePoolTypeEnum.THANKS)
                // 时间倒序
                .orderByDesc(GlobalLotteryWinningRecord::getCreatedAt);
        // 允许带个SQL，比如limit 10
        if (StrUtil.isNotBlank(lastSql)) {
            lqw.last(lastSql);
        }
        List<GlobalLotteryWinningRecord> winningRecordList = list(lqw);
        if (winningRecordList.isEmpty()) {
            return List.of();
        }

        return autoFillAndConvertPrizeInfo(list(lqw));
    }

    @Override
    public Page<LotteryWinningRecordDTO> findPageByWheelIdAndSeatId(PageBase pageBase, Long wheelId, Long seatId, Boolean excludeThanksgiving) {
        Page<GlobalLotteryWinningRecord> page = new Page<>(pageBase.getCurrentPage(), pageBase.getPerPage());
        LambdaQueryWrapper<GlobalLotteryWinningRecord> lqw = new LambdaQueryWrapper<>();
        lqw.eq(GlobalLotteryWinningRecord::getWheelId, wheelId)
                .eq(seatId != null, GlobalLotteryWinningRecord::getSeatId, seatId)
                // 排除谢谢惠顾
                .ne(Boolean.TRUE.equals(excludeThanksgiving), GlobalLotteryWinningRecord::getPrizeType, GlobalLotteryPrizePoolTypeEnum.THANKS)
                // 时间倒序
                .orderByDesc(GlobalLotteryWinningRecord::getCreatedAt);
        IPage<GlobalLotteryWinningRecord> pageList = page(page, lqw);
        // 补充奖品信息
        return new Page<LotteryWinningRecordDTO>(pageList.getCurrent(), pageList.getSize()).setTotal(pageList.getTotal()).setRecords(autoFillAndConvertPrizeInfo(pageList.getRecords()));
    }

    /**
     * 自动补充奖品信息，并转换为DTO
     * @param winningRecordList 中奖记录列表
     * @return  转换后的DTO列表
     */
    private List<LotteryWinningRecordDTO> autoFillAndConvertPrizeInfo(List<GlobalLotteryWinningRecord> winningRecordList) {
        if (winningRecordList.isEmpty()) {
            return List.of();
        }

        // 预加载中奖记录对应的奖品
        List<GlobalLotteryPrizePool> lotteryPrizePoolList = globalLotteryPrizePoolMapper.selectList(Wrappers.lambdaQuery(GlobalLotteryPrizePool.class)
                .in(GlobalLotteryPrizePool::getId, winningRecordList.stream().map(GlobalLotteryWinningRecord::getPrizeId).toList())
        );

        // 预加载中奖人信息
        var seatIdList = winningRecordList.stream().map(GlobalLotteryWinningRecord::getSeatId).toList();
        List<SimpleUserInfoDTO> userInfoList = globalOrgSeatService.selectListWithVip(new QuerySimpleUserDTO().setSeatIdList(seatIdList));

        return winningRecordList.stream().map(x -> {
            LotteryWinningRecordDTO dto = new LotteryWinningRecordDTO();
            dto.setType(x.getPrizeType());
            dto.setWinningAt(x.getCreatedAt());
            dto.setProcessTime(x.getProcessTime());
            dto.setProcessStatus(x.getProcessStatus());

            // 补充奖品信息
            lotteryPrizePoolList.stream().filter(y -> y.getId().equals(x.getPrizeId())).findFirst().ifPresent(y -> {
                dto.setName(y.getName());
                dto.setImageUrl(y.getImageUrl());
                dto.setDistributionMethod(y.getDistributionMethod());
            });

            // 补充中奖人信息
            userInfoList.stream().filter(y -> y.getSeatId().equals(x.getSeatId())).findFirst().ifPresent(y -> {
                dto.setSeatNickName(y.getShowName());
                dto.setSeatName(y.getName());
            });

            return dto;
        }).collect(Collectors.toList());
    }
}




