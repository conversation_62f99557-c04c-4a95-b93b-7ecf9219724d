package com.bbh.live.dao.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bbh.live.dao.dto.ErpGoodsFieldChangeDTO;
import com.bbh.live.dao.mapper.ErpGoodsLogMapper;
import com.bbh.live.dao.service.IErpGoodsLogService;
import com.bbh.model.ErpGoods;
import com.bbh.model.ErpGoodsLog;
import com.bbh.util.ParamsUtil;
import org.springframework.stereotype.Service;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【erp_goods_log(货品变动日志表)】的数据库操作Service实现
* @createDate 2024-09-06 09:56:45
*/
@Service
public class ErpGoodsLogServiceImpl extends ServiceImpl<ErpGoodsLogMapper, ErpGoodsLog>
    implements IErpGoodsLogService {


    @Override
    public void erpGoodsInsertLog(ErpGoods erpGoods) {

        ErpGoodsFieldChangeDTO erpGoodsFieldChangeDTO = new ErpGoodsFieldChangeDTO("warehousing", new ErpGoodsFieldChangeDTO.ChangeDataDTO("", erpGoods.getName()));
        var jsonStr = ParamsUtil.convertObjectToString(List.of(erpGoodsFieldChangeDTO));

        var goodsJsonStr = ParamsUtil.convertObjectToString(erpGoods);
        ErpGoodsLog erpGoodsLog = new ErpGoodsLog();
        erpGoodsLog.setGoodsId(erpGoods.getId());
        erpGoodsLog.setLogInfo(jsonStr);
        erpGoodsLog.setDetailInfo(jsonStr);
        erpGoodsLog.setPreviousGoodsInfo(goodsJsonStr);
        erpGoodsLog.setCurrentGoodsInfo(goodsJsonStr);
        this.save(erpGoodsLog);
    }

}




