package com.bbh.live.dao.service.impl;

import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bbh.base.Page;
import com.bbh.enums.GlobalVirtualGoodsOrderStateEnum;
import com.bbh.live.controller.req.VirtualGoodsOrderQueryReq;
import com.bbh.live.dao.dto.vo.VirtualGoodsOrderVO;
import com.bbh.live.dao.mapper.GlobalVirtualGoodsOrderMapper;
import com.bbh.live.dao.service.GlobalVirtualGoodsOrderService;
import com.bbh.live.enums.GlobalVirtualGoodsOrderCancelSource;
import com.bbh.live.util.PageUtils;
import com.bbh.model.GlobalVirtualGoodsOrder;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【global_virtual_goods_order(虚拟商品订单)】的数据库操作Service实现
* @createDate 2024-08-20 09:18:39
*/
@Service
public class GlobalVirtualGoodsOrderServiceImpl extends ServiceImpl<GlobalVirtualGoodsOrderMapper, GlobalVirtualGoodsOrder>
    implements GlobalVirtualGoodsOrderService {

    @Override
    public boolean cancelOrder(Long orderId, GlobalVirtualGoodsOrderCancelSource cancelSource) {
        GlobalVirtualGoodsOrderStateEnum orderState = cancelSource == GlobalVirtualGoodsOrderCancelSource.USER ?
                GlobalVirtualGoodsOrderStateEnum.BUYER_CANCEL : GlobalVirtualGoodsOrderStateEnum.OVERTIME_CANCEL;

        return this.lambdaUpdate().eq(GlobalVirtualGoodsOrder::getId, orderId)
                .eq(GlobalVirtualGoodsOrder::getOrderState, GlobalVirtualGoodsOrderStateEnum.WAIT_PAY)
                .set(GlobalVirtualGoodsOrder::getOrderState, orderState)
                .update();
    }

    @Override
    public List<VirtualGoodsOrderVO> getVirtualGoodsOrderList(VirtualGoodsOrderQueryReq queryReq) {
        List<VirtualGoodsOrderVO> result = new ArrayList<>();
        LambdaQueryChainWrapper<GlobalVirtualGoodsOrder> query = this.lambdaQuery();
        //买家id
        if(queryReq.getBuyerSeatId() != null){
            query.eq(GlobalVirtualGoodsOrder::getBuyerSeatId, queryReq.getBuyerSeatId());
        }
        //订单状态
        if(queryReq.getOrderState() != null){
            query.eq(GlobalVirtualGoodsOrder::getOrderState, queryReq.getOrderState());
        }

        // 订单id
        if(queryReq.getOrderId() != null){
            query.eq(GlobalVirtualGoodsOrder::getId, queryReq.getOrderId());
        }
        //订单创建开始时间
        if(queryReq.getStartDatetime() != null){
            query.ge(GlobalVirtualGoodsOrder::getCreatedAt, queryReq.getStartDatetime());
        }
        //订单创建结束时间
        if(queryReq.getEndDatetime() != null){
            query.le(GlobalVirtualGoodsOrder::getCreatedAt, queryReq.getEndDatetime());
        }
        // 订单类型
        if(queryReq.getGoodsType() != null){
            query.eq(GlobalVirtualGoodsOrder::getGoodsType, queryReq.getGoodsType());
        }
        Page<GlobalVirtualGoodsOrder> pageQuery = PageUtils.getPage(queryReq, GlobalVirtualGoodsOrder.class);
        pageQuery.addOrder(queryReq.getSort());
        Page<GlobalVirtualGoodsOrder> page = query.page(pageQuery);

        page.getRecords().forEach(order -> result.add(VirtualGoodsOrderVO.build(order)));
        return result;
    }
}




