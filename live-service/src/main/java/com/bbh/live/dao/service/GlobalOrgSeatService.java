package com.bbh.live.dao.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.bbh.live.dao.dto.BaseSeatDTO;
import com.bbh.live.dao.dto.QuerySimpleUserDTO;
import com.bbh.live.dao.dto.SimpleUserInfoDTO;
import com.bbh.model.GlobalOrgSeat;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
* <AUTHOR>
* @description 针对表【global_org_seat(主体席位)】的数据库操作Service
* @createDate 2024-06-27 16:30:48
*/
public interface GlobalOrgSeatService extends IService<GlobalOrgSeat> {

    /**
     * 根据seatId获取席位基本信息
     * @param seatId
     * @return
     */
    BaseSeatDTO getSeatInfoBySeatId(Long seatId);

    /**
     * 根据seatId获取机构id
     * @param seatId
     * @return
     */
    Long getOrgIdBySeatId(Long seatId);

    /**
     * 分页查询用户列表
     * @param page
     * @param query
     * @return
     */
    IPage<SimpleUserInfoDTO> selectPageWithVip(IPage<SimpleUserInfoDTO> page, QuerySimpleUserDTO query);

    /**
     * 查询用户列表
     * @param query
     * @return
     */
    List<SimpleUserInfoDTO> selectListWithVip(QuerySimpleUserDTO query);

    /**
     * 根据商户ID查询
     * @param orgId 商户id
     * @return      席位列表
     */
    List<GlobalOrgSeat> selectListByOrgId(Long orgId);

    /**
     * 根据商户ID查询
     * @param orgIdList 商户ID列表
     * @return          席位列表
     */
    List<GlobalOrgSeat> selectListByOrgIdList(List<Long> orgIdList);

    /**
     * 获取拥有对应权限的席位 id
     * @param roleList
     * @return
     */
    List<Long> getSeatIdListByOrgRoleList(List<Long> roleList);

    /**
     * 获取席位名称=席位昵称+拍号
     */
    String getSeatName(Long seatId);

    /**
     * 获取席位名称=席位昵称+拍号
     */
    String getSeatName(GlobalOrgSeat seat);

    /**
     * 获取席位对应的权重
     * @param bidSeatSet
     * @return
     */
    Map<Long, Integer> getSeatWightMap(Collection<Long> bidSeatSet);
}
