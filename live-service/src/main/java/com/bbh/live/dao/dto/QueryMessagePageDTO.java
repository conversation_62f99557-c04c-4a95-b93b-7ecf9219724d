package com.bbh.live.dao.dto;

import com.bbh.base.PageBase;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 消息查询类
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class QueryMessagePageDTO extends PageBase {

    private Long roomId;

    /**
     * 消息类型
     */
    private Integer type;

    /**
     * 商品id，获取该商品的时间，获取时间段内的消息
     */
    private Long liveGoodsId;

    /**
     * 固定查询100条
     */
    private int perPage = 100;

}
