package com.bbh.live.dao.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bbh.live.dao.dto.vo.AttentionOrgVO;
import com.bbh.live.dao.dto.vo.SubscribeLiveRoomVO;
import com.bbh.live.dao.mapper.LiveRoomSubscribeMapper;
import com.bbh.live.dao.service.LiveRoomSubscribeService;
import com.bbh.model.LiveRoomSubscribe;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【live_room_subscribe(直播间预约记录)】的数据库操作Service实现
* @createDate 2024-07-25 11:53:19
*/
@Service
public class LiveRoomSubscribeServiceImpl extends ServiceImpl<LiveRoomSubscribeMapper, LiveRoomSubscribe>
    implements LiveRoomSubscribeService{


    @Override
    public List<SubscribeLiveRoomVO> getSubscribeLiveRoomBySeatId(Long seatId) {
        return this.getBaseMapper().getSubscribeLiveRoomBySeatId(seatId, new Date());
    }

    @Override
    public List<AttentionOrgVO> getAttentionOrgListBySeatId(String keywords, Long seatId) {
        return this.getBaseMapper().getAttentionOrgListBySeatId(keywords, seatId);
    }

    @Override
    public List<AttentionOrgVO> getAttentionOrgLiveByOrgList(Collection<Long> orgList) {
        return this.getBaseMapper().getAttentionOrgLiveByOrgIdList(orgList, new Date());
    }
}




