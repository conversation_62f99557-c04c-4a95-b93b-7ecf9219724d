package com.bbh.live.dao.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.bbh.live.dao.dto.OffhandPutAwayDTO;
import com.bbh.model.ErpGoods;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【erp_goods(erp货品表)】的数据库操作Service
 * @createDate 2024-06-26 14:19:58
 */
public interface ErpGoodsService extends IService<ErpGoods> {

    /**
     * 批量检查erp商品是否存在
     * @param globalGoodsIdList
     * @return 不存在的商品id集合
     */
    List<ErpGoods> checkErpGoodsExists(List<Long> globalGoodsIdList);

    /**
     * 创建即拍即上erp商品
     * @return
     */
    ErpGoods createErpGoodsByOffhand(OffhandPutAwayDTO offhandPutAwayDTO);
}
