package com.bbh.live.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bbh.live.service.buyer.orgmap.GeoService;
import com.bbh.live.service.buyer.orgmap.geo.GeoSearchParams;
import com.bbh.live.service.buyer.orgmap.vo.OrgAddressInfoVO;
import com.bbh.live.service.buyer.orgmap.vo.OrgDistanceVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/10/9 10:46
 * @description
 */
public interface OrgGeoMapper extends BaseMapper<OrgAddressInfoVO> {

    /**
     * 计算两点距离
     * @param position1
     * @param position2
     * @return
     */
    double getDistance(@Param("position1") GeoService.GeoPosition position1, @Param("position2") GeoService.GeoPosition position2);

    /**
     * 查询商家信息
     * @param args
     * @return
     */
    List<OrgDistanceVO> searchOrgsWithDistance(@Param("args") GeoSearchParams args);
}
