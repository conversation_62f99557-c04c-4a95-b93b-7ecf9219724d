package com.bbh.live.dao.dto;

import com.bbh.enums.LiveGoodsTradeTypeEnum;
import com.bbh.exception.ServiceException;
import com.bbh.util.AssertUtil;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <AUTHOR> dsy
 * @Date: 2024/8/1
 * @Description:
 */
@Data
@Accessors(chain = true)
public class BargainGoodsDTO {

    /**
     * 直播间id
     */
    private Long liveRoomId;

    /**
     * 商品Id
     */
    private Long liveGoodsId;

    /**
     * ERP货品ID
     */
    private Long globalGoodsId;

    /**
     * 商品价格
     */
    private BigDecimal bidPrice;

    /**
     * 出价人席位
     */
    private Long buyerSeatId;

    /**
     * 出价人主体
     */
    private Long buyerOrgId;


    public void checkProperties() {
        // 商品id或者货品id，必须要有一个
        if (liveGoodsId == null && globalGoodsId == null) {
            throw new ServiceException("商品id或者货品id，必须要有一个");
        }
        AssertUtil.assertNotNull(bidPrice, "请输入价格");
        AssertUtil.assertNotNull(liveRoomId, "直播间不存在");
    }

    public void checkProperties(LiveGoodsTradeTypeEnum tradeType) {
        // 商品id或者货品id，必须要有一个
        if (liveGoodsId == null && globalGoodsId == null) {
            throw new ServiceException("商品id或者货品id，必须要有一个");
        }
        AssertUtil.assertNotNull(bidPrice, "请输入价格");
        AssertUtil.assertNotNull(liveRoomId, "直播间不存在");
    }
}
