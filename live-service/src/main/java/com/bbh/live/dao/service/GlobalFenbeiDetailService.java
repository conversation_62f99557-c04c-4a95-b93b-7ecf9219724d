package com.bbh.live.dao.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.bbh.live.dao.dto.FenbeiOfflineTransferRemarkDTO;
import com.bbh.live.service.buyer.vip.vo.UserBuyerVipInfoVO;
import com.bbh.live.service.order.dto.OrderBuilderContext;
import com.bbh.model.GlobalFenbeiDetail;
import com.bbh.model.GlobalOrder;
import com.bbh.model.GlobalOrderItem;
import com.bbh.model.GlobalVirtualGoodsOrder;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【global_fenbei_detail(分贝明细)】的数据库操作Service
 * @createDate 2024-08-15 10:18:26
 */
public interface GlobalFenbeiDetailService extends IService<GlobalFenbeiDetail> {

    void savePayFenbeiDetail(GlobalVirtualGoodsOrder order, Integer orgFb);

    /**
     * 保存签到明细
     *
     * @param changeNumber
     * @param orgFb
     * @param consecutiveSignDaysThisWeek
     */
    void saveSignFenbeiDetail(Integer changeNumber, Integer orgFb, Integer consecutiveSignDaysThisWeek);

    /**
     * 保存支付分贝抵扣明细
     *
     * @param orderItems
     * @param orgFb
     */
    void savePayDeductFenbeiDetail(List<GlobalOrderItem> orderItems, Integer orgFb);

    /**
     * 保存转账分贝明细
     *
     * @param changeNumber 变动数量
     * @param orgOldFb     商户原来的分贝
     * @param order        订单
     */
    void saveTransferFenbeiDetail(Integer changeNumber, Integer orgOldFb, GlobalOrder order, UserBuyerVipInfoVO vipInfo);

    /**
     * 取消支付 分贝明细记录
     *
     * @param orderItems
     * @param orgFb
     */
    void saveCancelPayOrderFenbeiDetail(List<GlobalOrderItem> orderItems, Integer orgFb);

    /**
     * 取消支付
     *
     * @param orgOldFb 商户原来的分贝
     * @param order    订单
     * @return 需要返还的金额
     */
    FenbeiOfflineTransferRemarkDTO saveCancelPayOrderFenbeiDetail(Integer orgOldFb, GlobalOrder order);

    void saveTransferVipDetail(BigDecimal totalChannelAmount, BigDecimal subVipNumber, GlobalOrder globalOrder, OrderBuilderContext context, Long vipDeductionId);
}
