package com.bbh.live.dao.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bbh.model.LiveGoodsBid;
import com.bbh.live.dao.mapper.LiveGoodsBidMapper;
import com.bbh.live.dao.service.LiveGoodsBidService;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【live_goods_bid(出价记录)】的数据库操作Service实现
* @createDate 2024-07-25 11:53:19
*/
@Service
public class LiveGoodsBidServiceImpl extends ServiceImpl<LiveGoodsBidMapper, LiveGoodsBid>
    implements LiveGoodsBidService{

}




