package com.bbh.live.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bbh.live.service.organization.dto.OrgAuctionNumberResourcesDTO;
import com.bbh.model.GlobalAuctionPremiumNumber;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【live_auction_premium_number(直播拍号靓号表)】的数据库操作Mapper
* @createDate 2024-09-19 19:56:43
* @Entity generator.domain.GlobalAuctionPremiumNumber
*/
public interface GlobalAuctionPremiumNumberMapper extends BaseMapper<GlobalAuctionPremiumNumber> {

    /**
     * 获取机构拍号靓号资源
     * @param orgId
     * @return
     */
    List<OrgAuctionNumberResourcesDTO> getOrgAuctionNumberResources(Long orgId);
}




