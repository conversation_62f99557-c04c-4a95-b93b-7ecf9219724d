package com.bbh.live.dao.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bbh.live.dao.mapper.LiveGoodsTransferMapper;
import com.bbh.live.dao.service.LiveGoodsTransferService;
import com.bbh.model.LiveGoodsTransfer;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @description 针对表【live_goods_transfer(商品传送记录)】的数据库操作Service实现
 * @createDate 2024-07-25 11:53:19
 */
@Service
public class LiveGoodsTransferServiceImpl extends ServiceImpl<LiveGoodsTransferMapper, LiveGoodsTransfer> implements LiveGoodsTransferService {

}




