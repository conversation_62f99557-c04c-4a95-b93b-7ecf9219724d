package com.bbh.live.dao.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.bbh.model.ErpStorehouse;
import org.jetbrains.annotations.NotNull;

/**
* <AUTHOR>
* @description 针对表【erp_storehouse(仓库)】的数据库操作Service
* @createDate 2024-06-28 15:08:19
*/
public interface IErpStorehouseService extends IService<ErpStorehouse> {


    /**
     * 创建一个直播间即拍即上仓库
     *
     * @param liveRoomId
     * @return 仓库id
     */
    Long getLiveOffhandStorehouse(@NotNull Long liveRoomId);
}
