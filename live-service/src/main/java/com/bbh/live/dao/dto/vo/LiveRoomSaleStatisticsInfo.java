package com.bbh.live.dao.dto.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <AUTHOR> dsy
 * @Date: 2024/9/12
 * @Description:
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
public class LiveRoomSaleStatisticsInfo {

    /**
     * 成交额
     */
    private BigDecimal soldAmount;

    /**
     * 成交数量
     */
    private Integer soldCount;

    /**
     * 累计上架商品金额
     */
    private BigDecimal accumulatePutawayAmount;

    /**
     * 上架商品数量
     */
    private Integer accumulatePutawayCount;

    /**
     * 流拍商品金额
     */
    private BigDecimal abortiveAmount;

    /**
     * 流拍商品数量
     */
    private Integer abortiveCount;

    /**
     *  客单价
     */
    private BigDecimal perCustomerTransactionAmount;

    /**
     *  成交商品均价
     */
    private BigDecimal avgSoldAmount;
}
