package com.bbh.live.dao.dto;

import com.bbh.live.core.msg.MsgDTO;
import com.bbh.live.service.msg.dto.GoodsTransferMsgDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 全局消息组
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class GlobalMsgGroupDTO {

    /**
     * 传送
     */
    private List<MsgDTO<GoodsTransferMsgDTO>> transfer;

    /**
     *  订阅的商品列表 (全量)
     */
    private List<GoodsSubscribeInfo> subscribeGoodsList;

    /**
     * 当前需要展示的预约商品列表 (过滤掉已处理的，并且是当前正在上架或竞拍的)
     */
    private List<Long> showSubscribeGoodsList;

    /**
     * 上帝视角
     */
    private List<MsgDTO<?>> godsPerspective;


    @Data
    @Accessors(chain = true)
    @AllArgsConstructor
    @NoArgsConstructor
    public static class GoodsSubscribeInfo {
        private Long liveGoodsId;
        private String remark;
    }
}
