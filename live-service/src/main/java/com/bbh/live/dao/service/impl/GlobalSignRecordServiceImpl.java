package com.bbh.live.dao.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bbh.model.GlobalSignRecord;
import com.bbh.live.dao.mapper.GlobalSignRecordMapper;
import com.bbh.live.dao.service.GlobalSignRecordService;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【vip_buyer_sign_record(签到记录表)】的数据库操作Service实现
* @createDate 2024-08-14 17:51:46
*/
@Service
public class GlobalSignRecordServiceImpl extends ServiceImpl<GlobalSignRecordMapper, GlobalSignRecord>
    implements GlobalSignRecordService {

}




