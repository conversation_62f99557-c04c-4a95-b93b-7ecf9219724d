package com.bbh.live.dao.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bbh.model.VipBuyerChangeNicknameLog;
import com.bbh.live.dao.mapper.VipBuyerChangeNicknameLogMapper;
import com.bbh.live.dao.service.VipBuyerChangeNicknameLogService;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【vip_buyer_change_nickname_log(买手修改昵称记录)】的数据库操作Service实现
* @createDate 2024-08-14 17:51:46
*/
@Service
public class VipBuyerChangeNicknameLogServiceImpl extends ServiceImpl<VipBuyerChangeNicknameLogMapper, VipBuyerChangeNicknameLog>
    implements VipBuyerChangeNicknameLogService{

}




