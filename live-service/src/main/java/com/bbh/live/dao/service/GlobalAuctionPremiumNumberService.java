package com.bbh.live.dao.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.bbh.live.service.organization.dto.OrgAuctionNumberResourcesDTO;
import com.bbh.model.GlobalAuctionPremiumNumber;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【live_auction_premium_number(直播拍号靓号表)】的数据库操作Service
* @createDate 2024-09-19 19:56:43
*/
public interface GlobalAuctionPremiumNumberService extends IService<GlobalAuctionPremiumNumber> {

    /**
     * 获取店铺拍号靓号列表
     * @param orgId
     * @return
     */
    List<OrgAuctionNumberResourcesDTO> getAuctionNumberResources(Long orgId);
}
