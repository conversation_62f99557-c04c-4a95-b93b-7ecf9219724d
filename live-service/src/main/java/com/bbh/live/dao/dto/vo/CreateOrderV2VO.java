package com.bbh.live.dao.dto.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 创建订单返回结果，包含支付参数等
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class CreateOrderV2VO {

    /**
     * 订单流水号
     */
    private String orderNo;

    /**
     * 订单ID
     */
    private Long globalOrderId;

}
