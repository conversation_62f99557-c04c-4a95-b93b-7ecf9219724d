package com.bbh.live.dao.dto.livegoods;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <AUTHOR> dsy
 * @Date: 2024/9/5
 * @Description:
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
public class LiveGoodsStatisticsDTO {

    /**
     * 总数量
     */
    private Long totalCount = 0L;

    /**
     *  待完善商品数量
     */
    private Long waitCompleteCount = 0L;

    /**
     * 待上架商品数量
     */
    private Long waitPutAwayCount= 0L;

    /**
     * 已上架商品数量 0 或 1
     */
    private Long putAwayCount = 0L;

    /**
     * 拍卖中商品数量
     */
    private Long auctionCount = 0L;

    /**
     *  成交商品数量
     */
    private Long tradeCount = 0L;

    /**
     * 流拍商品数量
     */
    private Long abortiveAuctionCount = 0L;
}
