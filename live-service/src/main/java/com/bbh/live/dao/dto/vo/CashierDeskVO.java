package com.bbh.live.dao.dto.vo;

import com.bbh.live.dao.dto.LiveShoppingCartGroupVO;
import lombok.Data;

import java.util.List;

/**
 * 收银台信息
 * <AUTHOR>
 */
@Data
public class CashierDeskVO {

    /**
     * 待支付
     */
    private List<LiveShoppingCartGroupVO> listWaitPayInfo;

    /**
     * 超时未支付
     */
    private List<LiveShoppingCartGroupVO> listTimeOutWaitPayInfo;

    /**
     * 超时数量
     */
    private Integer timeOutCount;

}
