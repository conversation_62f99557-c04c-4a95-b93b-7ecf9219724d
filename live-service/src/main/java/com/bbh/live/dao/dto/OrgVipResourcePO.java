package com.bbh.live.dao.dto;

import com.bbh.enums.CreateFromEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/9/20 10:57
 * @description
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class OrgVipResourcePO {

    private Long vipId;

    private Integer exp;

    private Boolean ifAnnualFeeVip;

    private Date vipEndTime;

    private Long seatId;

    private String seatName;

    private String avatar;

    /**
     * 创建来源
     */
    private CreateFromEnum createFrom;
}
