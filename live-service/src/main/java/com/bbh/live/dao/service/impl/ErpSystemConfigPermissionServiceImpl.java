package com.bbh.live.dao.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bbh.live.dao.mapper.ErpSystemConfigPermissionMapper;
import com.bbh.live.dao.service.IErpSystemConfigPermissionService;
import com.bbh.model.ErpSystemConfigPermission;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @description 针对表【erp_system_config_permission(erp功能菜单表)】的数据库操作Service实现
 * @createDate 2024-06-27 16:06:33
 */

@AllArgsConstructor
@Service
@Slf4j
public class ErpSystemConfigPermissionServiceImpl extends ServiceImpl<ErpSystemConfigPermissionMapper, ErpSystemConfigPermission>
        implements IErpSystemConfigPermissionService {

}
