package com.bbh.live.dao.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.bbh.live.dao.dto.LotteryPrizeItemDTO;
import com.bbh.model.GlobalLotteryWheel;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【global_lottery_wheel(抽奖转盘活动管理表)】的数据库操作Service
* @createDate 2024-10-08 16:01:43
*/
public interface GlobalLotteryWheelService extends IService<GlobalLotteryWheel> {

    /**
     * 查询转盘的所有奖项
     *
     * @param wheelId 转盘ID
     */
    List<LotteryPrizeItemDTO> findPrizeList(Long wheelId);

    /**
     * 获取当前时间段内开启的转盘
     */
    GlobalLotteryWheel findAvailableWheel();

}
