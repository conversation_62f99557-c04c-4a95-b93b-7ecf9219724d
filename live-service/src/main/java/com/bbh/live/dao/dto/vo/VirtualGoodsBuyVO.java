package com.bbh.live.dao.dto.vo;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR> dsy
 * @Date: 2024/8/20
 * @Description:
 */
@Data
public class VirtualGoodsBuyVO {

    /**
     * 虚拟商品id
     */
    private Long goodsId;

    /**
     * 购买者座位id
     */
    private Long buyerSeatId;

    /**
     * 支付方式，ALI_PAY:支付宝 WX_PAY:微信
     */
    private String payType;

    /**
     * 订单额外信息，比如给哪个会员续费
     */
    private String extraData;

    /**
     * 订单金额，卖家保证金、卖家补偿金等场景下有值，其他场景为空
     */
    private BigDecimal orderPrice;
}
