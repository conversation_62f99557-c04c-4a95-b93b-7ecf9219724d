package com.bbh.live.dao.dto.vo;

import com.bbh.model.VipWeekly;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 行情周报-SKU列表
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class WeeklyVO extends VipWeekly {

    /**
     * spu
     */
    private List<WeeklySpuVO> spuList = List.of();

    /**
     * sku
     */
    private List<WeeklySkuGoodsVO> skuList = List.of();

    /**
     * 下次周报更新时间
     */
    private String sendDay;

}
