package com.bbh.live.dao.dto.vo;

import com.bbh.model.GlobalOrderOtherGoodsDic;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR> dsy
 * @Date: 2024/8/20
 * @Description:
 */
@Data
public class VirtualGoodsVO {

    /**
     * 商品id
     */
    private Long goodsId;

    /**
     * 商品名称
     */
    private String goodsName;

    /**
     * 商品图片
     */
    private String goodsImages;

    /**
     * 虚拟商品类型( 1=分贝， 2=app原价vip会员 3=app拼团vip会员 4=小程序原价vip会员 5=保证金补缴（补缴金额不固定） 6=卖家保证金, 7=买家保证金, 8=买家升级新卖家保证金, 9=老卖家升级卖家保证金, 10=系统赠送升级卖家保证金, 11=系统赠送升级买家保证金, 12=小程序购票，包含包展票和展位票， 13=小程序展位票，14=买家会员 高价 15=买家会员 低价）
     */
    private Integer goodsType;

    /**
     * 商品价格
     */
    private BigDecimal goodsPrice;

    /**
     * 服务价格 (VIP 原价)
     */
    private BigDecimal goodsServicePrice;

    /**
     * 商品数量
     */
    private Integer goodsNumber;


    public static VirtualGoodsVO build(GlobalOrderOtherGoodsDic globalOrderOtherGoodsDic) {
        VirtualGoodsVO virtualGoodsVO = new VirtualGoodsVO();
        virtualGoodsVO.setGoodsId(globalOrderOtherGoodsDic.getId());
        virtualGoodsVO.setGoodsImages(globalOrderOtherGoodsDic.getGoodsImages());
        virtualGoodsVO.setGoodsName(globalOrderOtherGoodsDic.getGoodsName());
        virtualGoodsVO.setGoodsPrice(globalOrderOtherGoodsDic.getGoodsPrice());
        virtualGoodsVO.setGoodsServicePrice(globalOrderOtherGoodsDic.getGoodsServicePrice());
        virtualGoodsVO.setGoodsNumber(globalOrderOtherGoodsDic.getGoodsNumber());
        virtualGoodsVO.setGoodsType(globalOrderOtherGoodsDic.getGoodsSource().getValue());
        return virtualGoodsVO;
    }
}
