package com.bbh.live.dao.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bbh.model.LiveRoomTicketOrder;
import com.bbh.live.dao.mapper.LiveRoomTicketOrderMapper;
import com.bbh.live.dao.service.LiveRoomTicketOrderService;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【live_room_ticket_order(直播间门票购买记录)】的数据库操作Service实现
* @createDate 2024-07-25 11:53:19
*/
@Service
public class LiveRoomTicketOrderServiceImpl extends ServiceImpl<LiveRoomTicketOrderMapper, LiveRoomTicketOrder>
    implements LiveRoomTicketOrderService{

}




