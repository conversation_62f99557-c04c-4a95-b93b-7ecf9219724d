package com.bbh.live.dao.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bbh.enums.VipMemeExamineEnum;
import com.bbh.live.config.LiveServiceProperties;
import com.bbh.live.dao.dto.MemeClassDTO;
import com.bbh.live.dao.mapper.VipMemeClassInfoMapper;
import com.bbh.live.dao.mapper.VipMemeClassMapper;
import com.bbh.live.dao.mapper.VipMemeClassUserInfoMapper;
import com.bbh.live.dao.service.VipMemeClassService;
import com.bbh.model.VipMemeClass;
import com.bbh.model.VipMemeClassInfo;
import com.bbh.model.VipMemeClassUserInfo;
import com.bbh.secure.AuthUtil;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * 表情包
 * <AUTHOR>
 */
@Service
@AllArgsConstructor
public class VipMemeClassServiceImpl extends ServiceImpl<VipMemeClassMapper, VipMemeClass> implements VipMemeClassService{

    private final VipMemeClassInfoMapper vipMemeClassInfoMapper;
    private final VipMemeClassUserInfoMapper vipMemeClassUserInfoMapper;
    private final LiveServiceProperties liveServiceProperties;

    /**
     * 获取表情包类型及子项列表
     */
    @Override
    public List<MemeClassDTO> getMemeList() {
        var user = AuthUtil.getUser();

        // 查出来分类和分类下的子项，然后组装树结构
        List<VipMemeClass> memeClassList = list(Wrappers.lambdaQuery(VipMemeClass.class)
                .eq(VipMemeClass::getEnabled, true)
                .orderByAsc(VipMemeClass::getSort));

        // 加上用户自定义表情
        List<VipMemeClassUserInfo> userCustomMemeList = vipMemeClassUserInfoMapper.selectList(Wrappers
                .lambdaQuery(VipMemeClassUserInfo.class)
                .eq(VipMemeClassUserInfo::getCreateUserId, user.getUserId())
                .ne(VipMemeClassUserInfo::getExamine, VipMemeExamineEnum.REJECT)
        );
        VipMemeClass memeClass = null;
        if (CollUtil.isNotEmpty(userCustomMemeList)) {
            memeClass = new VipMemeClass();
            memeClass.setId(Long.MAX_VALUE);
            memeClass.setClassName("我的");
            memeClass.setEmojiUrl(liveServiceProperties.getDefaultCustomMemeTabIcon());
            // 放最后面
            memeClass.setSort(Integer.MAX_VALUE);
            memeClass.setEnabled(true);
        }

        // 官方表情组和自定义表情都没有，直接返回空数组
        if (CollUtil.isEmpty(memeClassList)  && memeClass == null) {
            return List.of();
        }

        if (memeClass != null) {
            memeClassList.addFirst(memeClass);
        }

        // 官方表情
        List<VipMemeClassInfo> memeClassInfoList = vipMemeClassInfoMapper.selectList(Wrappers.lambdaQuery(VipMemeClassInfo.class)
                .in(VipMemeClassInfo::getMemeClassId, memeClassList.stream().map(VipMemeClass::getId).toList())
                .eq(VipMemeClassInfo::getEnabled, true)
                .orderByAsc(VipMemeClassInfo::getSort));

        // 把自定义表情加到官方表情里面
        if (CollUtil.isNotEmpty(userCustomMemeList)) {
            memeClassInfoList.addAll(userCustomMemeList.stream().map(x -> {
                VipMemeClassInfo info = new VipMemeClassInfo();
                info.setEmojiUrl(x.getEmojiUrl());
                info.setName("自定义表情");
                info.setMemeClassId(Long.MAX_VALUE);
                info.setId(x.getId());
                return info;
            }).toList());
        }

        // 组装成两级的结构
        return memeClassList.stream().map(x -> {
            MemeClassDTO dto = new MemeClassDTO();
            dto.setClassName(x.getClassName());
            dto.setEmojiUrl(x.getEmojiUrl());
            dto.setId(x.getId());
            dto.setInfoList(memeClassInfoList.stream()
                    .filter(y -> Objects.equals(y.getMemeClassId(), x.getId()))
                    .map(y -> {
                        MemeClassDTO.MemeItemDTO item = new MemeClassDTO.MemeItemDTO();
                        item.setId(y.getId());
                        item.setName(y.getName());
                        item.setEmojiUrl(y.getEmojiUrl());
                        return item;
                    })
                    .toList()
            );
            return dto;
        }).toList();
    }
}




