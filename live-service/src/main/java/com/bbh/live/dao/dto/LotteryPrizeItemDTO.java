package com.bbh.live.dao.dto;

import com.bbh.enums.GlobalLotteryPrizePoolDistributionMethodEnum;
import com.bbh.enums.GlobalLotteryPrizePoolTypeEnum;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 抽奖奖品
 * <AUTHOR>
 */
@Data
public class LotteryPrizeItemDTO {

    /** 奖项ID */
    private Long prizeId;

    /** 转盘ID */
    private Long wheelId;

    /** 转盘关联的奖项ID */
    private Long wheelPrizeId;

    /**
     * 奖品名称
     */
    private String name;

    /**
     * 奖品类型: 0-谢谢惠顾, 1-分贝, 2-其他
     */
    private GlobalLotteryPrizePoolTypeEnum type;

    /**
     * 发放方式: 0-自动, 1-手动
     */
    private GlobalLotteryPrizePoolDistributionMethodEnum distributionMethod;

    /**
     * 奖品图片URL
     */
    private String imageUrl;

    /**
     * 权重
     */
    private BigDecimal weight;

    /**
     * 数量是否受限
     */
    private Boolean quantityLimited;

    /**
     * 奖品数量
     */
    private Integer stockCount;

    /**
     * 附加参数，比如分贝的数量
     */
    private String extraData;

    /**
     * 中奖记录ID，在写入中奖记录后才有值
     */
    private Long winningRecordId;
}
