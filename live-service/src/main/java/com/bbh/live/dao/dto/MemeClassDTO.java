package com.bbh.live.dao.dto;

import lombok.Data;

import java.util.List;

/**
 * 表情包分类组
 * <AUTHOR>
 */
@Data
public class MemeClassDTO {

    private Long id;

    /**
     * 分类name
     */
    private String className;

    /**
     * 图标
     */
    private String emojiUrl;

    /**
     * 表情包列表
     */
    private List<MemeItemDTO> infoList;

    /**
     * 表情包明细
     * <AUTHOR>
     */
    @Data
    public static class MemeItemDTO {
        /**
         * 表情
         */
        private Long id;

        /**
         * 表情名称
         */
        private String name;

        /**
         * 图片
         */
        private String emojiUrl;
    }

}
