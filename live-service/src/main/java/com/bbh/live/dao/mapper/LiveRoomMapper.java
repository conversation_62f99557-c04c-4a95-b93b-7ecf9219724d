package com.bbh.live.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.bbh.live.dao.dto.LiveRoomSaleStatisticsDTO;
import com.bbh.live.dao.dto.QueryLiveRoomDTO;
import com.bbh.live.dao.dto.vo.LiveRoomRecommendVO;
import com.bbh.live.dao.dto.vo.LiveRoomVO;
import com.bbh.live.dao.dto.vo.PlayBackLiveRoomVO;
import com.bbh.model.LiveRoom;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【live_room(直播间)】的数据库操作Mapper
* @createDate 2024-07-25 11:53:19
* @Entity com.bbh.model.LiveRoom
*/
public interface LiveRoomMapper extends BaseMapper<LiveRoom> {

    IPage<LiveRoomVO> selectRoomVOPage(IPage<?> page, @Param("query") QueryLiveRoomDTO queryLiveRoomDTO);

    List<LiveRoomRecommendVO> selectRecommendLiveRoom();

    /**
     * 直播回放列表
     * @param page
     * @param currentUserId
     * @return
     */
    List<PlayBackLiveRoomVO> selectPlaybackRoomVOPage(@Param("page")IPage<?> page, @Param("currentUserId") Long currentUserId, @Param("currentSeatId") Long currentSeatId, @Param("filterBlacklist") Boolean filterBlacklist);

    /**
     * 获取时间冲突直播间数量
     * @param orgId
     * @param startAt
     * @param endAt
     * @return
     */
    Long getConflictLiveCount(@Param("orgId") Long orgId, @Param("startAt")Date startAt, @Param("endAt")Date endAt);


    /**
     * 获取所有正在直播的直播间，过滤黑白名单
     * @param currentUserId
     * @return
     */
    List<LiveRoom> getInLiveRoomList(@Param("now")Date now, @Param("currentUserId") Long currentUserId, @Param("currentSeatId") Long currentSeatId, @Param("filterBlacklist") Boolean filterBlacklist);

    List<LiveRoom> getGodViewRoomList(@Param("currentUserId") Long currentUserId);

    IPage<LiveRoomVO> selectMiniRoomVOPage(IPage<?> page, @Param("query") QueryLiveRoomDTO queryLiveRoomDTO);

    /**
     * 获取直播间销售统计信息
     * @param liveRoomId
     * @return
     */
    LiveRoomSaleStatisticsDTO getLiveRoomSaleStatisticsInfo(Long liveRoomId);
}




