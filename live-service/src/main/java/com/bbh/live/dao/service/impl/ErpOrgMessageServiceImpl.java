package com.bbh.live.dao.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bbh.live.dao.mapper.ErpOrgMessageMapper;
import com.bbh.live.dao.service.ErpOrgMessageService;
import com.bbh.live.service.msg.dto.PushMsgDTO;
import com.bbh.live.service.msg.push.LivePushTypeEnum;
import com.bbh.model.ErpOrgMessage;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【erp_org_message(erp商家消息)】的数据库操作Service实现
* @createDate 2024-10-23 14:20:29
*/
@Service
public class ErpOrgMessageServiceImpl extends ServiceImpl<ErpOrgMessageMapper, ErpOrgMessage>
    implements ErpOrgMessageService{

    @Override
    public ErpOrgMessage create(PushMsgDTO pushMsg) {
        ErpOrgMessage erpOrgMessage = new ErpOrgMessage();
        LivePushTypeEnum pushType = pushMsg.getPushType();
        erpOrgMessage.setOrgId(pushMsg.getOrgId());
        erpOrgMessage.setCreateSeatId(pushMsg.getSeatId().getFirst());
        erpOrgMessage.setCreateUserId(pushMsg.getUserId());
        erpOrgMessage.setGoodsId(pushMsg.getGoodsId());
        erpOrgMessage.setOrderId(pushMsg.getOrderId());
        erpOrgMessage.setTitle(pushType.getTitle());

        erpOrgMessage.setScene(4);
        erpOrgMessage.setType(pushType.getType());

        return erpOrgMessage;
    }
}




