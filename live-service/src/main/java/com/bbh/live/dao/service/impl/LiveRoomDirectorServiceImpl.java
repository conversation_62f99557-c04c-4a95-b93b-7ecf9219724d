package com.bbh.live.dao.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bbh.model.LiveRoomDirector;
import com.bbh.live.dao.mapper.LiveRoomDirectorMapper;
import com.bbh.live.dao.service.LiveRoomDirectorService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【live_room_director(导播表)】的数据库操作Service实现
* @createDate 2024-07-25 11:53:19
*/
@Service
public class LiveRoomDirectorServiceImpl extends ServiceImpl<LiveRoomDirectorMapper, LiveRoomDirector>
    implements LiveRoomDirectorService{

    @Override
    public List<LiveRoomDirector> listByRoomIds(List<Long> liveRoomIds) {
        return list(Wrappers.lambdaQuery(LiveRoomDirector.class).in(LiveRoomDirector::getLiveRoomId, liveRoomIds));
    }

    @Override
    public boolean isLiveRoomDirector(Long roomId, Long seatId) {
        return this.lambdaQuery().eq(LiveRoomDirector::getLiveRoomId, roomId).eq(LiveRoomDirector::getDirectorSeatId, seatId).exists();
    }
}




