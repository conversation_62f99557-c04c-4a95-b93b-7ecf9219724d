package com.bbh.live.dao.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.bbh.enums.GlobalVirtualGoodsSourceEnum;
import com.bbh.live.dao.dto.vo.VirtualGoodsVO;
import com.bbh.model.GlobalOrderOtherGoodsDic;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【global_order_other_goods_dic(其他商品（保证金、分贝、门票等）中订单信息字典表)】的数据库操作Service
* @createDate 2024-08-19 10:10:16
*/
public interface GlobalOrderOtherGoodsDicService extends IService<GlobalOrderOtherGoodsDic> {

    /**
     * 获取vip类型套餐列表
     * @return
     */
    List<VirtualGoodsVO> getVipTypeList();

    /**
     * 获取可开通的分贝套餐列表
     * @return
     */
    List<VirtualGoodsVO> getFenbeiTypeList();

    /**
     * 根据类型查询虚拟商品
     * @param source
     * @return
     */
    List<VirtualGoodsVO> getVirtualGoodsListBySource(GlobalVirtualGoodsSourceEnum source);
}
