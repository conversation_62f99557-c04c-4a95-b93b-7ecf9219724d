package com.bbh.live.dao.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bbh.live.dao.mapper.GlobalOrgCustomerServiceMapper;
import com.bbh.model.GlobalOrgCustomerService;
import com.bbh.live.dao.service.GlobalOrgCustomerServiceService;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【global_org_customer_service(主体客服表)】的数据库操作Service实现
* @createDate 2024-09-18 11:25:43
*/
@Service
public class GlobalOrgCustomerServiceServiceImpl extends ServiceImpl<GlobalOrgCustomerServiceMapper, GlobalOrgCustomerService> implements GlobalOrgCustomerServiceService{

    /**
     * 当前席位是否客服
     */
    @Override
    public Boolean isCustomerService(Long orgId, Long seatId) {
        return exists(Wrappers.lambdaQuery(GlobalOrgCustomerService.class).eq(GlobalOrgCustomerService::getOrgId, orgId).eq(GlobalOrgCustomerService::getSeatId, seatId));
    }
}




