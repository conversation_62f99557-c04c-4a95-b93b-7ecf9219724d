package com.bbh.live.dao.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.bbh.live.dao.service.LiveApiLogService;
import com.bbh.log.ApiLogCommiter;
import com.bbh.log.ApiLogEntity;
import com.bbh.log.ApiLogTypeEnum;
import com.bbh.model.LiveApiLog;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 录入业务日志
 * <AUTHOR>
 */
@Slf4j
@Service
@AllArgsConstructor
public class LiveApiLogCommiter implements ApiLogCommiter {

    private final LiveApiLogService apiLogService;

    /**
     * 正常保存
     *
     * @param data
     */
    @Override
    public void save(ApiLogEntity data) {
        if (data.getLogType() == ApiLogTypeEnum.PAY_API) {
            data.setLogType(ApiLogTypeEnum.FEIGN_CALL);
        }
        LiveApiLog apiLog = new LiveApiLog();
        BeanUtil.copyProperties(data, apiLog);
        apiLog.setUa(data.getUserAgent());
        apiLogService.save(apiLog);
    }
}
