package com.bbh.live.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bbh.live.dao.dto.vo.AttentionOrgVO;
import com.bbh.live.dao.dto.vo.SubscribeLiveRoomVO;
import com.bbh.model.LiveRoomSubscribe;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【live_room_subscribe(直播间预约记录)】的数据库操作Mapper
* @createDate 2024-07-25 11:53:19
* @Entity com.bbh.model.LiveRoomSubscribe
*/
public interface LiveRoomSubscribeMapper extends BaseMapper<LiveRoomSubscribe> {

    /**
     * 根据用户座位id查询预约的直播间
     * @param seatId
     * @return
     */
    List<SubscribeLiveRoomVO> getSubscribeLiveRoomBySeatId(@Param("seatId") Long seatId, @Param("currentDate") Date currentDate);

    /**
     * 用户关注的商家
     * @param seatId
     * @return
     */
    List<AttentionOrgVO> getAttentionOrgListBySeatId(@Param("keywords") String keywords, @Param("seatId") Long seatId);

    /**
     * 查看关注的商家列表最近的一场的直播
     * @param orgIdList
     * @param currentDate
     * @return
     */
    List<AttentionOrgVO> getAttentionOrgLiveByOrgIdList(@Param("orgIdList") Collection<Long> orgIdList, @Param("currentDate") Date currentDate);
}




