package com.bbh.live.dao.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bbh.live.dao.dto.RateTemplateItemDTO;
import com.bbh.live.dao.dto.RoomRateTemplateDTO;
import com.bbh.live.dao.mapper.LiveRateTemplateMapper;
import com.bbh.live.dao.service.LiveRateTemplateService;
import com.bbh.model.LiveRateTemplate;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
* <AUTHOR>
* @description 针对表【live_rate_template(费率模板：根据商品价格计算模板)】的数据库操作Service实现
* @createDate 2024-07-25 11:53:19
*/
@Service
@AllArgsConstructor
public class LiveRateTemplateServiceImpl extends ServiceImpl<LiveRateTemplateMapper, LiveRateTemplate> implements LiveRateTemplateService{

    @Override
    public List<RoomRateTemplateDTO> getRoomRateTemplateList(List<Long> roomIdList) {
        List<RateTemplateItemDTO> rateTemplateItemList = baseMapper.getRateTemplateItemList(roomIdList);
        // 拆解重组成直播间的费率模板
        return rateTemplateItemList.stream()
                .collect(Collectors.groupingBy(RateTemplateItemDTO::getRoomId))
                .entrySet().stream()
                .map(entry -> {
                    RoomRateTemplateDTO roomRateTemplateDTO = new RoomRateTemplateDTO();
                    roomRateTemplateDTO.setRoomId(entry.getKey());
                    roomRateTemplateDTO.setItemList(entry.getValue());
                    return roomRateTemplateDTO;
                })
                .collect(Collectors.toList());
    }

    /**
     * 获取直播间的费率项列表
     *
     * @param roomId 直播间ID
     * @return 费率项列表
     */
    @Override
    public List<RateTemplateItemDTO> getRateTemplateItemList(Long roomId) {
        return Optional.ofNullable(roomId)
                // 将roomId转换为单元素的List
                .map(List::of)
                // 获取RoomRateTemplateDTO列表，里面包含id和itemList
                .map(this::getRoomRateTemplateList)
                // 获取列表的第一个元素（如果存在）
                .flatMap(list -> list.stream().findFirst())
                // 获取费率项列表
                .map(RoomRateTemplateDTO::getItemList)
                // 如果在任何步骤中遇到问题，返回空列表
                .orElse(List.of());
    }
}




