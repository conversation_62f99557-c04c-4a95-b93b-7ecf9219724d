package com.bbh.live.dao.service;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.extension.service.IService;
import com.bbh.model.GlobalBizConfig;

import java.util.Optional;

/**
* <AUTHOR>
* @description 针对表【global_biz_config(业务配置)】的数据库操作Service
* @createDate 2024-07-26 16:32:50
*/
public interface GlobalBizConfigService extends IService<GlobalBizConfig> {

    <T> Optional<T> getOptional(String key, Class<T> clazz, boolean ignoreCache);

    <T> T get(String key, Class<T> clazz);

    JSONArray getJSONArray(String key);

    JSONObject getJSONObject(String key);

    String getString(String key);

    Long getLong(String key);

    Integer getInteger(String key);

    Double getDouble(String key);

}
