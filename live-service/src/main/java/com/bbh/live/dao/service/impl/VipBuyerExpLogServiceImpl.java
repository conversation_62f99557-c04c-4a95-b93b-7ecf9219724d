package com.bbh.live.dao.service.impl;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bbh.enums.VipBuyerChangeLogStatusEnum;
import com.bbh.enums.VipBuyerExpLogExpTypeEnum;
import com.bbh.live.dao.mapper.VipBuyerExpLogMapper;
import com.bbh.live.dao.service.VipBuyerExpLogService;
import com.bbh.live.enums.BuyerVipEventEnum;
import com.bbh.live.service.buyer.vip.vo.UserBuyerVipInfoVO;
import com.bbh.model.VipBuyerExpLog;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @description 针对表【vip_buyer_exp_log(会员经验变化记录)】的数据库操作Service实现
 * @createDate 2024-08-14 17:51:46
 */
@Service
public class VipBuyerExpLogServiceImpl extends ServiceImpl<VipBuyerExpLogMapper, VipBuyerExpLog>
        implements VipBuyerExpLogService {

    @Override
    public void insertVipExpChangeLog(UserBuyerVipInfoVO vipInfo, Integer exp, BuyerVipEventEnum vipEvent) {
        // 创建日志实体
        VipBuyerExpLog entity = new VipBuyerExpLog();
        entity.setSeatId(vipInfo.getSeatId());
        entity.setOldExp(vipInfo.getExp());
        entity.setOrgId(vipInfo.getOrgId());
        entity.setExpType(VipBuyerExpLogExpTypeEnum.NEW_OLD);
        entity.setCreatedAt(DateUtil.date());
        entity.setExpDesc(vipEvent.getDesc());
        entity.setUpdateExp(exp);
        entity.setVipBuyerCardId(vipInfo.getId());
        entity.setAddOrSub(vipEvent.getFlag() ? VipBuyerChangeLogStatusEnum.ADD : VipBuyerChangeLogStatusEnum.SUB);
        // 新的经验值
        entity.setNewExp(vipInfo.getExp() + (vipEvent.getFlag() ? exp : -exp));
        if (BuyerVipEventEnum.MARGIN_BREAK == vipEvent) {
            // 注销保证金 清空经验值
            entity.setUpdateExp(entity.getOldExp());
            entity.setNewExp(0);
        }

        this.save(entity);
    }
}




