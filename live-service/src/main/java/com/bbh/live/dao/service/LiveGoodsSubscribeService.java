package com.bbh.live.dao.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.bbh.live.dao.dto.livegoods.LiveGoodsSubscribeDTO;
import com.bbh.model.LiveGoodsSubscribe;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【live_goods_subscribe(商品预约记录)】的数据库操作Service
* @createDate 2024-07-25 11:53:19
*/
public interface LiveGoodsSubscribeService extends IService<LiveGoodsSubscribe> {

    /**
     * 指定席位预约的商品列表
     * @param buyerSeatId
     * @return
     */
    List<LiveGoodsSubscribeDTO> getSubscribeGoodsInfoByBuyerSeatId(Long buyerSeatId);

    /**
     * 检查商品是否有预约数据
     * @param liveGoodsId
     * @return
     */
    boolean hasGoodsSubscribe(Long liveGoodsId);
}
