package com.bbh.live.dao.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.bbh.model.GlobalSeatMessageData;

/**
* <AUTHOR>
* @description 针对表【global_seat_message_data】的数据库操作Service
* @createDate 2024-09-18 11:16:48
*/
public interface GlobalSeatMessageDataService extends IService<GlobalSeatMessageData> {

    /**
     * 计算席位的未读消息
     *
     * @return 未读消息数
     */
    Integer countSeatUnReadMessage(Long orgId, Long searId);

}
