package com.bbh.live.dao.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bbh.live.config.ChangelogCommiter;
import com.bbh.live.dao.mapper.LiveGoodsLogMapper;
import com.bbh.live.dao.service.LiveGoodsLogService;
import com.bbh.model.LiveGoodsLog;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Objects;

/**
* <AUTHOR>
* @description 针对表【live_goods_log(商品变更日志)】的数据库操作Service实现
* @createDate 2024-07-25 11:53:19
*/
@Service
public class LiveGoodsLogServiceImpl extends ServiceImpl<LiveGoodsLogMapper, LiveGoodsLog> implements LiveGoodsLogService, ChangelogCommiter {

    @Async
    @Transactional
    @Override
    public void commit(CommitResult commitResult) {
        if (CollUtil.isEmpty(commitResult.getChangedData())) {
            return;
        }
        List<LiveGoodsLog> logList = commitResult.getChangedData().stream().map(x -> {
            LiveGoodsLog goodsLog = new LiveGoodsLog();
            if (ObjectUtil.isNotEmpty(x.getPkColumnVal())) {
                goodsLog.setLiveGoodsId(Long.parseLong(x.getPkColumnVal().toString()));
            }
            goodsLog.setModifyContent(JSONUtil.toJsonStr(x));
            goodsLog.setPreGoodsInfo(JSONUtil.toJsonStr(x.getOriginalColumnDatas()));
            goodsLog.setAfterGoodsInfo(JSONUtil.toJsonStr(x.getUpdatedColumns()));
            return goodsLog;
        }).toList();
        saveBatch(logList);
    }
}




