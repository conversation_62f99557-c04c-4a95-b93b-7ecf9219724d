package com.bbh.live.dao.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bbh.live.dao.dto.LotteryPrizeItemDTO;
import com.bbh.live.dao.mapper.GlobalLotteryPrizePoolMapper;
import com.bbh.live.dao.mapper.GlobalLotteryWheelMapper;
import com.bbh.live.dao.mapper.GlobalLotteryWheelPrizeMapper;
import com.bbh.live.dao.service.GlobalLotteryWheelService;
import com.bbh.model.GlobalLotteryPrizePool;
import com.bbh.model.GlobalLotteryWheel;
import com.bbh.model.GlobalLotteryWheelPrize;
import com.bbh.service.lock.HtbLockService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【global_lottery_wheel(抽奖转盘活动管理表)】的数据库操作Service实现
* @createDate 2024-10-08 16:01:43
*/
@Service
@AllArgsConstructor
public class GlobalLotteryWheelServiceImpl extends ServiceImpl<GlobalLotteryWheelMapper, GlobalLotteryWheel> implements GlobalLotteryWheelService{

    private final GlobalLotteryWheelPrizeMapper globalLotteryWheelPrizeMapper;
    private final GlobalLotteryPrizePoolMapper globalLotteryPrizePoolMapper;
    private final HtbLockService htbLockService;

    /**
     * 查询转盘的所有奖项
     *
     * @param wheelId       转盘ID
     * @return              转盘的所有奖项
     */
    @Override
    public List<LotteryPrizeItemDTO> findPrizeList(Long wheelId) {
        // 查询这个转盘配置的奖池
        List<GlobalLotteryWheelPrize> lotteryWheelPrizeList = globalLotteryWheelPrizeMapper.selectList(Wrappers.lambdaQuery(GlobalLotteryWheelPrize.class)
                .select(
                        GlobalLotteryWheelPrize::getId,
                        GlobalLotteryWheelPrize::getPrizeId,
                        GlobalLotteryWheelPrize::getWheelId,
                        GlobalLotteryWheelPrize::getWeight,
                        GlobalLotteryWheelPrize::getQuantityLimited,
                        GlobalLotteryWheelPrize::getStockCount
                )
                .eq(GlobalLotteryWheelPrize::getWheelId, wheelId)
        );
        if (CollUtil.isEmpty(lotteryWheelPrizeList)) {
            return List.of();
        }

        // 查询奖品的名称等信息
        List<GlobalLotteryPrizePool> prizeList = globalLotteryPrizePoolMapper.selectList(Wrappers.lambdaQuery(GlobalLotteryPrizePool.class)
                .select(
                        GlobalLotteryPrizePool::getId,
                        GlobalLotteryPrizePool::getType,
                        GlobalLotteryPrizePool::getName,
                        GlobalLotteryPrizePool::getDistributionMethod,
                        GlobalLotteryPrizePool::getImageUrl,
                        GlobalLotteryPrizePool::getExtraData
                )
                .in(GlobalLotteryPrizePool::getId, lotteryWheelPrizeList.stream().map(GlobalLotteryWheelPrize::getPrizeId).toList())
        );

        // 组装成返回结果
        return lotteryWheelPrizeList.stream().map(lotteryWheelPrize -> {
            LotteryPrizeItemDTO lotteryPrizeItemDTO = new LotteryPrizeItemDTO();
            // 配置的概率等信息
            lotteryPrizeItemDTO.setWheelPrizeId(lotteryWheelPrize.getId());
            lotteryPrizeItemDTO.setWheelId(lotteryWheelPrize.getWheelId());
            lotteryPrizeItemDTO.setPrizeId(lotteryWheelPrize.getPrizeId());
            lotteryPrizeItemDTO.setWeight(lotteryWheelPrize.getWeight());
            lotteryPrizeItemDTO.setQuantityLimited(lotteryWheelPrize.getQuantityLimited());
            lotteryPrizeItemDTO.setStockCount(lotteryWheelPrize.getStockCount());

            // 匹配奖池中的奖项名称
            prizeList.stream().filter(prize -> prize.getId().equals(lotteryWheelPrize.getPrizeId()))
                    .findFirst().ifPresent(prize -> {
                        lotteryPrizeItemDTO.setName(prize.getName());
                        lotteryPrizeItemDTO.setDistributionMethod(prize.getDistributionMethod());
                        lotteryPrizeItemDTO.setImageUrl(prize.getImageUrl());
                        lotteryPrizeItemDTO.setType(prize.getType());
                        lotteryPrizeItemDTO.setExtraData(prize.getExtraData());
                    });

            return lotteryPrizeItemDTO;
        }).toList();
    }

    /**
     * 获取当前时间段内开启的转盘
     */
    @Override
    public GlobalLotteryWheel findAvailableWheel() {
        LambdaQueryWrapper<GlobalLotteryWheel> lqw = new LambdaQueryWrapper<>();
        lqw.le(GlobalLotteryWheel::getStartAt, new Date())
                .ge(GlobalLotteryWheel::getEndAt, new Date())
                .eq(GlobalLotteryWheel::getEnabled, true)
                .orderByDesc(GlobalLotteryWheel::getStartAt)
                .last("limit 1");
        return getOne(lqw);
    }
}




