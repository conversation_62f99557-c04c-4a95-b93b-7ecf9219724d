package com.bbh.live.dao.dto.vo;

import lombok.Data;

import java.util.Date;

/**
 * 直播间到ce检查的结果
 * <AUTHOR>
 */
@Data
public class LiveRoomToCeCheckVO {

    /** 直播间ID */
    private Long roomId;

    /** 场次名称 */
    private String roomName;

    /** 直播场次时间 */
    private Date startAt;

    /** 清单实际结束时间 */
    private Date actualGoodsListEndAt;

    /** 结束倒计时-剩余时间 */
    private Long remainingSeconds;

    /** 未同步的数量 */
    private Long unSyncGoodsCount;

    /** 已同步的数量 */
    private Long hasSyncGoodsCount;

}
