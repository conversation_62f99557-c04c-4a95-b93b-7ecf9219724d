package com.bbh.live.dao.dto;

import com.bbh.model.GlobalOrder;
import com.bbh.model.GlobalOrderPayment;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <AUTHOR> dsy
 * @Date: 2024/9/3
 * @Description:
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
public class PaySuccessContext {

    /**
     * 订单支付记录
     */
    private GlobalOrderPayment orderPayment;

    /**
     * 订单
     */
    private GlobalOrder order;
}
