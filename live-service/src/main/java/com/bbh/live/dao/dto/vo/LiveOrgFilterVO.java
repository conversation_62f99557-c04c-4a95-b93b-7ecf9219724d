package com.bbh.live.dao.dto.vo;

import com.bbh.model.LiveOrgFilter;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2024/7/27
 * @description:
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class LiveOrgFilterVO extends LiveOrgFilter {

    /***
     * 是否已经在名单内
     */
    private Boolean has;

    /**
     * 头像
     */
    private String avatar;


    /**
     * 拍号
     */
    private String auctionCode;

    /**
     * 昵称
     */
    private String nickName;

    /**
     * 展示名称
     */
    private String showName;

    /**
     * 是否年费VIP
     */
    private Boolean isAnnualFeeVip;

    /**
     * 是否VIP，通过后期业务判断
     */
    private Boolean isVip;

    /**
     * 会员等级，通过后期业务计算
     */
    private Integer vipLevel;

    /***
     * orgId
     */
    private Long orgId;

    /***
     *
     */
    private String orgName;

    /**
     *
     */
    private String orgLogoUrl;

    /**
     *
     */
    private String orgMasterUserId;

    // region 会员字段，需要在业务中进行解析

    /**
     * 会员卡ID
     */
    private Long vipCardId;

    /**
     * 会员开始时间
     */
    private Date timeVipStart;

    /**
     * 会员结束时间
     */
    private Date timeVipEnd;

    /**
     * 会员当前经验值
     */
    private Integer exp;

    // endregion


}
