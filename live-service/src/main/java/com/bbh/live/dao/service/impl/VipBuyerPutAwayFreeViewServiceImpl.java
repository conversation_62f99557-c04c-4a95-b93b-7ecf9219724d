package com.bbh.live.dao.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bbh.model.VipBuyerPutAwayFreeView;
import com.bbh.live.dao.mapper.VipBuyerPutAwayFreeViewMapper;
import com.bbh.live.dao.service.VipBuyerPutAwayFreeViewService;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【vip_buyer_put_away_free_view(用户免费试用查看正在上架商品次数表)】的数据库操作Service实现
* @createDate 2024-08-14 17:51:46
*/
@Service
public class VipBuyerPutAwayFreeViewServiceImpl extends ServiceImpl<VipBuyerPutAwayFreeViewMapper, VipBuyerPutAwayFreeView>
    implements VipBuyerPutAwayFreeViewService{

}




