package com.bbh.live.dao.dto.vo;

import com.bbh.enums.GlobalVirtualGoodsOrderStateEnum;
import com.bbh.model.GlobalVirtualGoodsOrder;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR> dsy
 * @Date: 2024/8/20
 * @Description:
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
public class VirtualGoodsOrderVO {

    /**
     * 订单ID
     */
    private Long orderId;

    /**
     * 买家机构ID
     */
    private Long orgId;

    /**
     * 买家店铺名
     */
    private String orgName;

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 商品ID
     */
    private Long goodsId;

    /**
     * 商品类型(1=分贝，2=app原价vip会员，3=app拼团vip会员，4=小程序原价vip会员，5=保证金补缴（补缴金额不固定），6=卖家保证金，7=买家保证金，8=买家升级新卖家保证金，9=老卖家升级卖家保证金，10=系统赠送升级卖家保证金，11=系统赠送升级买家保证金，12=小程序购票（包含包展票和展位票），13=小程序展位票，14=买家会员 高价，15=买家会员 低价）
     */
    private Integer goodsType;

    /**
     * 商品名称
     */
    private String goodsName;

    /**
     * 商品封面图
     */
    private String goodsCoverImg;

    /**
     * 商品价格
     */
    private BigDecimal goodsPrice;

    /**
     * 订单金额
     */
    private BigDecimal orderPrice;

    /**
     * 订单状态 10=WAIT_PAY=等待支付 20=BUYER_CANCEL=买家取消 30=OVERTIME_CANCEL=超时取消 40=HAVE_MONEY=支付完成 50=DONE_DELIVER=完成交付
     */
    private GlobalVirtualGoodsOrderStateEnum orderState;

    /**
     * 优惠金额
     */
    private BigDecimal discountMoney;

    /**
     * 支付方式
     */
    private String payType;

    /**
     * 支付金额
     */
    private BigDecimal payPrice;

    /**
     * 支付时间
     */
    private Date payDatetime;

    /**
     * 发货时间
     */
    private Date deliverDatetime;

    /**
     * 买家席位id
     */
    private Long buyerSeatId;

    /**
     * 买家id
     */
    private Long buyerUserId;

    private String reason;

    private String remark;

    public static VirtualGoodsOrderVO build(GlobalVirtualGoodsOrder globalVirtualGoodsOrder) {
        return new VirtualGoodsOrderVO()
                .setOrderId(globalVirtualGoodsOrder.getId())
                .setOrgId(globalVirtualGoodsOrder.getOrgId())
                .setOrgName(globalVirtualGoodsOrder.getOrgName())
                .setGoodsId(globalVirtualGoodsOrder.getGoodsId())
                .setGoodsType(globalVirtualGoodsOrder.getGoodsType())
                .setGoodsName(globalVirtualGoodsOrder.getGoodsName())
                .setGoodsCoverImg(globalVirtualGoodsOrder.getGoodsCoverImg())
                .setGoodsPrice(globalVirtualGoodsOrder.getGoodsPrice())
                .setOrderPrice(globalVirtualGoodsOrder.getOrderPrice())
                .setOrderState(globalVirtualGoodsOrder.getOrderState())
                .setDiscountMoney(globalVirtualGoodsOrder.getDiscountMoney())
                .setPayType(globalVirtualGoodsOrder.getPayType())
                .setPayPrice(globalVirtualGoodsOrder.getPayPrice())
                .setPayDatetime(globalVirtualGoodsOrder.getPayAt())
                .setDeliverDatetime(globalVirtualGoodsOrder.getDeliverAt())
                .setBuyerSeatId(globalVirtualGoodsOrder.getBuyerSeatId())
                .setBuyerUserId(globalVirtualGoodsOrder.getBuyerUserId());
    }
}
