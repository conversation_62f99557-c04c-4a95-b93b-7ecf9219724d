package com.bbh.live.dao.dto.vo;

import com.bbh.model.LiveRoom;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 直播首页直播推荐
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class LiveRoomRecommendVO extends LiveRoom {

    /**
     * 商户名称
     */
    private String orgName;

    /**
     * 商户头像
     */
    private String orgLogoUrl;

    /**
     * 是否认证
     */
    private Boolean ifAuthed;


}
