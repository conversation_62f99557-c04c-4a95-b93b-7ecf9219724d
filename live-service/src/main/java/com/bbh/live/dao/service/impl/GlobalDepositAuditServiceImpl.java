package com.bbh.live.dao.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bbh.enums.AuditStateEnum;
import com.bbh.enums.AuditTypeEnum;
import com.bbh.live.dao.mapper.GlobalDepositAuditMapper;
import com.bbh.live.dao.service.GlobalDepositAuditService;
import com.bbh.model.GlobalDepositAudit;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description 针对表【global_deposit_audit(保证金违规扣款记录表)】的数据库操作Service实现
 * @date 2024-10-17 10:37:25
 */
@Slf4j
@Service
@AllArgsConstructor
public class GlobalDepositAuditServiceImpl extends ServiceImpl<GlobalDepositAuditMapper, GlobalDepositAudit> implements GlobalDepositAuditService {

    /**
     * 是否有违规记录
     *
     * @param orgId 商户ID
     * @return true:有违规记录 false:无违规记录
     */
    @Override
    public boolean hasIllegalRecord(Long orgId) {
        return exists(Wrappers.lambdaQuery(GlobalDepositAudit.class)
                // 从当前商户扣的
                .eq(GlobalDepositAudit::getOutOrgId, orgId)
                // 卖家
                .eq(GlobalDepositAudit::getOutType, AuditTypeEnum.SELLER)
                // 未补缴的
                .eq(GlobalDepositAudit::getSupplementaryDepositLogId, 0)
                // 审核通过的
                .eq(GlobalDepositAudit::getState, AuditStateEnum.PASS)
                // 金额大于零的才显示
                .gt(GlobalDepositAudit::getOutAmount, BigDecimal.ZERO)
        );
    }

    /**
     * 查询违规记录数量
     *
     * @param orgId 商户ID
     * @return 违规记录数量
     */
    @Override
    public Long getIllegalCount(Long orgId) {
        return count(Wrappers.lambdaQuery(GlobalDepositAudit.class)
                // 从当前商户扣的
                .eq(GlobalDepositAudit::getOutOrgId, orgId)
                // 卖家
                .eq(GlobalDepositAudit::getOutType, AuditTypeEnum.SELLER)
                // 未补缴的
                .eq(GlobalDepositAudit::getSupplementaryDepositLogId, 0)
                // 审核通过的
                .eq(GlobalDepositAudit::getState, AuditStateEnum.PASS)
        );
    }
}




