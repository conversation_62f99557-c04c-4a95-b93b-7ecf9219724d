package com.bbh.live.dao.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bbh.live.dao.dto.GlobalOrderItemVO;
import com.bbh.live.dao.dto.OrderNoDTO;
import com.bbh.live.dao.mapper.GlobalOrderItemMapper;
import com.bbh.live.dao.mapper.GlobalOrderMapper;
import com.bbh.live.dao.service.GlobalOrderGoodsSnapshotService;
import com.bbh.live.dao.service.GlobalOrderItemService;
import com.bbh.model.GlobalOrder;
import com.bbh.model.GlobalOrderGoodsSnapshot;
import com.bbh.model.GlobalOrderItem;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
* <AUTHOR>
*/
@Service
@AllArgsConstructor
public class GlobalOrderItemServiceImpl extends ServiceImpl<GlobalOrderItemMapper, GlobalOrderItem> implements GlobalOrderItemService{

    private final GlobalOrderMapper globalOrderMapper;
    private final GlobalOrderGoodsSnapshotService globalOrderGoodsSnapshotService;

    @Override
    public List<GlobalOrderItem> listByGlobalOrderNo(String globalOrderNo) {
        // 先查总订单，拿到总订单id
        GlobalOrder globalOrder = globalOrderMapper.selectOne(Wrappers.lambdaQuery(GlobalOrder.class).eq(GlobalOrder::getOrderNo, globalOrderNo));
        if (globalOrder == null) {
            return List.of();
        }
        // 再根据总订单id查子订单
        return this.list(Wrappers.lambdaQuery(GlobalOrderItem.class).eq(GlobalOrderItem::getGlobalOrderId, globalOrder.getId()));
    }

    @Override
    public String getGoodsImageByGlobalOrderSubId(Long globalOrderSubId) {
        GlobalOrderItem orderItem = this.lambdaQuery().eq(GlobalOrderItem::getGlobalOrderSubId, globalOrderSubId)
                .last("limit 1").one();
        if(orderItem == null){
            return null;
        }
        GlobalOrderGoodsSnapshot orderGoodsSnapshot = globalOrderGoodsSnapshotService.lambdaQuery().eq(GlobalOrderGoodsSnapshot::getGlobalOrderItemId, orderItem.getId())
                .select(GlobalOrderGoodsSnapshot::getImgUrlList)
                .last("limit 1").one();
        if(orderGoodsSnapshot == null || CollectionUtil.isEmpty(orderGoodsSnapshot.getImgUrlList())){
            return null;
        }
        return orderGoodsSnapshot.getImgUrlList().getFirst();
    }

    @Override
    public Page<GlobalOrderItemVO> pageList(Page<GlobalOrderItem> page, Long orderId) {
        return baseMapper.pageList(page,orderId);
    }
}




