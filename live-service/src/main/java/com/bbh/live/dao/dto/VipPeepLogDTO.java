package com.bbh.live.dao.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/9/23 16:55
 * @description
 */
@Data
public class VipPeepLogDTO {

    private Long id;

    private Long peepSeatId;

    /**
     * 创建时间
     */
    private Date createdAt;
    /**
     * 出价人
     */
    private String bidSeatName;

    /**
     * 出价人拍号
     */
    private String auctionCode;

    /**
     * 出价金额
     */
    private BigDecimal bidAmount;

    /**
     * 商品名称
     */
    private String goodsName;

    /**
     * 活动名称
     */
    private String bizName;
}
