package com.bbh.live.dao.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bbh.enums.GlobalOrderTypeEnum;
import com.bbh.exception.ServiceException;
import com.bbh.live.dao.mapper.GlobalUserBidForbidMapper;
import com.bbh.live.dao.service.GlobalUserBidForbidService;
import com.bbh.model.GlobalUserBidForbid;

import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @Date 2025/5/26
 * @description: ${description}
 */
@Service
public class GlobalUserBidForbidServiceImpl extends ServiceImpl<GlobalUserBidForbidMapper, GlobalUserBidForbid> implements GlobalUserBidForbidService {

    @Override
    public void checkBidForbid(Long userId, @NotNull GlobalOrderTypeEnum bizType) {
        LocalDateTime now = LocalDateTime.now();
        LambdaQueryWrapper<GlobalUserBidForbid> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(GlobalUserBidForbid::getUserId, userId);
        wrapper.eq(GlobalUserBidForbid::getBizType, bizType.getCode());
        wrapper.le(GlobalUserBidForbid::getForbidStartAt, now);
        wrapper.ge(GlobalUserBidForbid::getForbidEndAt, now);
        wrapper.orderByDesc(GlobalUserBidForbid::getId);
        GlobalUserBidForbid forbid = this.getOne(wrapper, false);
        if (forbid != null) {
            throw new ServiceException(forbid.getForbidHint());
        }
    }
}
