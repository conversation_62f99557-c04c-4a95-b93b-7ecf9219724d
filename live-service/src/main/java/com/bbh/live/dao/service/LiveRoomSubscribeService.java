package com.bbh.live.dao.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.bbh.live.dao.dto.vo.AttentionOrgVO;
import com.bbh.live.dao.dto.vo.SubscribeLiveRoomVO;
import com.bbh.model.LiveRoomSubscribe;

import java.util.Collection;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【live_room_subscribe(直播间预约记录)】的数据库操作Service
* @createDate 2024-07-25 11:53:19
*/
public interface LiveRoomSubscribeService extends IService<LiveRoomSubscribe> {

    /**
     *  根据用户id查询预约直播间, 未开播与直播中的直播间
     * @param seatId
     * @return
     */
    List<SubscribeLiveRoomVO> getSubscribeLiveRoomBySeatId(Long seatId);

    /**
     * 查看用户关注的商家列表
     * @param keywords
     * @param seatId
     * @return
     */
    List<AttentionOrgVO> getAttentionOrgListBySeatId(String keywords, Long seatId);

    /**
     * 查看关注的商家列表最近的直播场次
     * @param orgList
     * @return
     */
    List<AttentionOrgVO> getAttentionOrgLiveByOrgList(Collection<Long> orgList);
}
