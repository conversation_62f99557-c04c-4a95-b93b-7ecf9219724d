package com.bbh.live.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bbh.live.dao.dto.OrgVipResourcePO;
import com.bbh.live.service.buyer.vip.vo.UserBuyerVipInfoVO;
import com.bbh.model.VipBuyerCard;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【vip_buyer_card(买手会员权益表)】的数据库操作Mapper
 * @createDate 2024-08-14 17:51:46
 * @Entity com.bbh.model.VipBuyerCard
 */
public interface VipBuyerCardMapper extends BaseMapper<VipBuyerCard> {
    /**
     * 根据seatId获取买手会员信息
     *
     * @param seatId 席位Id
     * @return 买手会员信息
     */
    UserBuyerVipInfoVO getUserBuyerVipInfoBySeatId(Long seatId);

    /**
     * 根据vipId获取买手会员信息
     *
     * @param vipId 席位Id
     * @return 买手会员信息
     */
    UserBuyerVipInfoVO getUserBuyerVipInfoByVipId(Long vipId);

    /**
     * 根据seatIdList获取买手会员信息
     *
     * @param seatIdList 席位Id_List
     * @return 买手会员信息_List
     */
    List<UserBuyerVipInfoVO> getUserBuyerVipInfoBySeatIdList(List<Long> seatIdList);

    /**
     * 更新使用次数
     *
     * @param seatId    seatId
     * @param timesType 类型
     * @param num       次数
     * @return 是否成功
     */
    boolean updateTimesBySeatId(@Param("seatId") Long seatId, @Param("timesType") String timesType, @Param("num") int num);

    /**
     * 更新使用次数
     *
     * @param seatId       seatId
     * @param timesType    类型
     * @param num          次数
     * @param refundFenbei 分贝
     * @return 是否成功
     */
    boolean updateTimesBySeatIdAndFenbei(@Param("seatId") Long seatId, @Param("timesType") String timesType, @Param("num") int num, @Param("refundFenbei") int refundFenbei);

    /**
     * 获取店铺所有会员权益
     * @param orgId
     * @return
     */
    List<OrgVipResourcePO> getOrgVipResources(Long orgId);
}




