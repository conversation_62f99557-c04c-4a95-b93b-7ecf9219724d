package com.bbh.live.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bbh.live.dao.dto.RateTemplateItemDTO;
import com.bbh.model.LiveRateTemplate;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【live_rate_template(费率模板：根据商品价格计算模板)】的数据库操作Mapper
* @createDate 2024-07-25 11:53:19
* @Entity com.bbh.model.LiveRateTemplate
*/
public interface LiveRateTemplateMapper extends BaseMapper<LiveRateTemplate> {

    /**
     * 查询直播间的费率单元列表 <br>
     * 注意该方法会返回重复的单元数据，需要再进行去重
     *
     * @param roomIdList 直播间ID
     * @return 费率
     */
    List<RateTemplateItemDTO> getRateTemplateItemList(List<Long> roomIdList);

}




