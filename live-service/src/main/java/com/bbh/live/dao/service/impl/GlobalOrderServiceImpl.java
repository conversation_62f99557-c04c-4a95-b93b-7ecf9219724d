package com.bbh.live.dao.service.impl;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bbh.live.dao.mapper.GlobalOrderItemMapper;
import com.bbh.live.dao.mapper.GlobalOrderMapper;
import com.bbh.live.dao.mapper.GlobalOrderSubMapper;
import com.bbh.live.dao.service.GlobalOrderService;
import com.bbh.model.GlobalOrder;
import com.bbh.model.GlobalOrderItem;
import com.bbh.model.GlobalOrderSub;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 订单业务的Service
 *
 * <AUTHOR>
 */
@Service
@AllArgsConstructor
@Slf4j
public class GlobalOrderServiceImpl extends ServiceImpl<GlobalOrderMapper, GlobalOrder> implements GlobalOrderService {

    private final GlobalOrderSubMapper globalOrderSubMapper;
    private final GlobalOrderItemMapper globalOrderItemMapper;

    /**
     * 更新订单状态，同步更新子订单和订单项
     *
     * @param globalOrderId 订单流水号
     * @param status        订单状态
     */
    @SuppressWarnings("all")
    @Override
    public void updateOrderStatus(Long globalOrderId, Integer status) {
        // 更新总订单
        LambdaUpdateWrapper<GlobalOrder> update = new LambdaUpdateWrapper<>();
        update.set(GlobalOrder::getOrderStatus, status)
                .eq(GlobalOrder::getId, globalOrderId);
        this.update(update);

        // 更新子订单
        LambdaUpdateWrapper<GlobalOrderSub> subUpdate = new LambdaUpdateWrapper<>();
        subUpdate.set(GlobalOrderSub::getOrderStatus, status)
                .eq(GlobalOrderSub::getGlobalOrderId, globalOrderId);
        globalOrderSubMapper.update(subUpdate);

        // 更新订单项
        LambdaUpdateWrapper<GlobalOrderItem> itemUpdate = new LambdaUpdateWrapper<>();
        itemUpdate.set(GlobalOrderItem::getOrderStatus, status)
                .eq(GlobalOrderItem::getGlobalOrderId, globalOrderId);
        globalOrderItemMapper.update(itemUpdate);
    }

    /**
     * 更新订单流水号，同步更新子订单，订单项不需要更新
     * @param globalOrderId     订单ID
     * @param newOrderNo        新的订单流水号
     */
    @SuppressWarnings("all")
    @Override
    public void updateOrderNo(Long globalOrderId, String newOrderNo) {
        LambdaUpdateWrapper<GlobalOrder> update = new LambdaUpdateWrapper<>();
        update.set(GlobalOrder::getOrderNo, newOrderNo)
                .eq(GlobalOrder::getId, globalOrderId);
        this.update(update);

        LambdaUpdateWrapper<GlobalOrderSub> subUpdate = new LambdaUpdateWrapper<>();
        subUpdate.set(GlobalOrderSub::getOrderNo, newOrderNo)
                .eq(GlobalOrderSub::getGlobalOrderId, globalOrderId);
        globalOrderSubMapper.update(subUpdate);
    }

    @Override
    public List<GlobalOrderItem> getOrderItems(Long globalOrderId, SFunction<GlobalOrderItem, ?>... columns) {

        var queryWrapper = Wrappers.lambdaQuery(GlobalOrderItem.class)
                .eq(GlobalOrderItem::getGlobalOrderId, globalOrderId);
        if(columns != null){
            queryWrapper.select(columns);
        }
        return globalOrderItemMapper.selectList(queryWrapper);
    }

    @Override
    public List<GlobalOrderSub> getOrderSubList(Long globalOrderId) {
        var queryWrapper = Wrappers.lambdaQuery(GlobalOrderSub.class)
                .eq(GlobalOrderSub::getGlobalOrderId, globalOrderId);
        return globalOrderSubMapper.selectList(queryWrapper);
    }

    /*@Transactional
    @Override
    public void orderPaid(GlobalOrder globalOrder, OrderPaidInfo orderPaidInfo) {
        Long globalOrderId = globalOrder.getId();

        // 用于批量更新的数据列表
        List<GlobalOrderItem> orderItemUpdateList = new ArrayList<>();
        List<GlobalOrderSub> orderSubUpdateList = new ArrayList<>();

        // 查询item列表
        List<GlobalOrderItem> orderItemList = globalOrderItemMapper.selectList(Wrappers.lambdaQuery(GlobalOrderItem.class).eq(GlobalOrderItem::getGlobalOrderId, globalOrderId));
        // 商品清单id
        var liveGoodsIdList = orderItemList.stream().map(GlobalOrderItem::getBizGoodsId).distinct().toList();
        // 直播间id
        var liveRoomIdList = orderItemList.stream().map(GlobalOrderItem::getBizId).distinct().toList();

        // 查询涉及的classify表
        LiveGoodsQueryReq query = new LiveGoodsQueryReq();
        query.setLiveGoodsIdList(liveGoodsIdList);
        IPage<LiveGoodsDTO> liveGoodsList = liveGoodsMapper.getLiveGoodsList(Page.of(1, -1), query);
        List<LiveGoodsDTO> goodsListWithClassify = Objects.requireNonNullElse(liveGoodsList.getRecords(), new ArrayList<>());

        // 查询出各个直播间绑定的模板费率列表，方便后面匹配计算
        var rateTemplateList = liveRateTemplateService.getRoomRateTemplateList(liveRoomIdList);

        // 计算每个商品的卖家服务费
        for (GlobalOrderItem orderItem : orderItemList) {
            // 匹配到模板费率
            var itemList = rateTemplateList.stream().filter(template -> template.getRoomId().equals(orderItem.getBizId())).findFirst().orElse(new RoomRateTemplateDTO()).getItemList();
            // 现在从pt_goods表获取分类id
            var oneClassifyId = orderCalculator.matchPlatformClassifyId(orderItem, goodsListWithClassify);
            // 要用商品售价去匹配费率
            var sellerServiceRate = orderCalculator.matchSellerServiceRate(itemList, orderItem.getGoodsPrice(), oneClassifyId);
            // 计算卖家服务费，也要用商品售价去计算
            var sellerServiceAmount = orderCalculator.computeSellerServiceAmount(orderItem.getGoodsPrice(), sellerServiceRate);

            // 标记更新
            GlobalOrderItem update = new GlobalOrderItem();
            update.setId(orderItem.getId());
            update.setGlobalOrderSubId(orderItem.getGlobalOrderSubId());
            update.setServiceAmount(sellerServiceAmount);
            update.setServiceRate(sellerServiceRate);
            update.setRealPayedAmount(orderItem.getNeedPayAmount());
            update.setOrderPayAt(orderPaidInfo.getPayAt());
            update.setOrderStatus(GlobalOrderStatusEnum.TO_BE_DELIVERED);
            orderItemUpdateList.add(update);
        }
        globalOrderItemService.updateBatchById(orderItemUpdateList);

        // 查询商户订单，汇总卖家服务费
        List<GlobalOrderSub> orderSubList = globalOrderSubService.list(Wrappers.lambdaQuery(GlobalOrderSub.class).eq(GlobalOrderSub::getGlobalOrderId, globalOrderId));
        for (GlobalOrderSub orderSub : orderSubList) {
            GlobalOrderSub update = new GlobalOrderSub();
            update.setId(orderSub.getId());
            update.setGlobalOrderId(globalOrderId);
            // 累加所有商品的服务费
            update.setServiceAmount(orderItemUpdateList.stream().filter(item -> item.getGlobalOrderSubId().equals(orderSub.getId())).map(GlobalOrderItem::getServiceAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
            // 服务费率直接给零：可能是多个直播间汇总到一条sub数据，无法判定取哪个直播间的费率
            update.setServiceRate(BigDecimal.ZERO);
            update.setOrderStatus(GlobalOrderStatusEnum.TO_BE_DELIVERED);
            orderSubUpdateList.add(update);
        }
        globalOrderSubService.updateBatchById(orderSubUpdateList);

        // 更新总订单 状态，实付金额
        LambdaUpdateWrapper<GlobalOrder> update = new LambdaUpdateWrapper<>();
        update.set(GlobalOrder::getOrderStatus, GlobalOrderStatusEnum.TO_BE_DELIVERED)
                .set(GlobalOrder::getRealPayedAmount, orderPaidInfo.getTransAmt())
                .set(GlobalOrder::getLastPaymentId, orderPaidInfo.getLastPaymentId())
                .eq(GlobalOrder::getId, globalOrderId);
        this.update(update);

    }*/
}




