package com.bbh.live.dao.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bbh.live.dao.mapper.LiveImSendLogMapper;
import com.bbh.live.dao.service.LiveImSendLogService;
import com.bbh.model.LiveImSendLog;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【live_im_send_log(融云消息发送日志)】的数据库操作Service实现
* @createDate 2024-10-10 17:33:19
*/
@Service
public class LiveImSendLogServiceImpl extends ServiceImpl<LiveImSendLogMapper, LiveImSendLog>
    implements LiveImSendLogService{

}




