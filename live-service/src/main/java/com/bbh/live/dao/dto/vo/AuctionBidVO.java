package com.bbh.live.dao.dto.vo;

import cn.hutool.core.util.StrUtil;
import com.bbh.service.deposit.dto.BidToFrozenDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * 竞拍出价结果
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
public class AuctionBidVO {

    /** 校验成功 */
    private Boolean success = false;

    /** 是否被禁言 */
    private Boolean ifChatBanned = false;

    private String chatBannedRemark;

    /** 是否需要充值保证金 */
    private Boolean ifNeedDepositCharge = false;

    /** 是否需要实名认证 */
    private Boolean ifNeedAuth = false;

    /** 是否有对应的采购（销售）权益，默认有权限 */
    private Boolean ifHasRights = true;

    /** 当前主体的未过期的席位数量 */
    private Long buyerRightsSeatCount;

    /** 实名认证状态 */
    private Integer authStatus;

    /** 认证提示信息 */
    private String authMessage;

    /** 最低出价保证金 */
    private BigDecimal minBidDeposit;

    /** 买手保证金余额 */
    private BigDecimal buyerDeposit;

    /** 余额类型文本（"账户余额"/"可用余额"） */
    private String balanceTypeText;

    /** 余额金额 */
    private BigDecimal balanceAmount;

    /** 提示信息 */
    private String warningMessage;

    /** 需要冻结的保证金 */
    private BidToFrozenDTO bidToFrozen;

    public AuctionBidVO setChatBanned(String remark) {
        this.ifChatBanned = true;
        this.chatBannedRemark = StrUtil.format("您因{}，已被平台禁言", remark);
        this.success = false;
        return this;
    }

    /** 需要权益 */
    public AuctionBidVO setNeedRights(Long buyerRightsSeatCount) {
        this.buyerRightsSeatCount = buyerRightsSeatCount;
        this.ifHasRights = false;
        this.success = false;
        return this;
    }

    /** 需要实名认证 */
    public AuctionBidVO setNeedAuth(AuthStatus authStatus, String actionName) {
        this.ifNeedAuth = true;
        this.authStatus = authStatus.getCode();
        // 特殊处理传送动作
        if ("发起传送".equals(actionName)) {
            this.authMessage = "该买手暂未认证，不支持传送";
        } else {
            this.authMessage = StrUtil.format(authStatus.getMessage(), actionName);
        }
        this.success = false;
        return this;
    }

    /** 设置充值弹窗 */
    public AuctionBidVO setRechargePopup(String balanceTypeText, BigDecimal balanceAmount, String warningMessage) {
        this.balanceTypeText = balanceTypeText;
        this.balanceAmount = balanceAmount;
        this.warningMessage = warningMessage;
        this.ifNeedDepositCharge = true;
        this.success = false;
        return this;
    }

    @AllArgsConstructor
    @Getter
    public enum AuthStatus {

        /** 未认证 */
        NO_AUTH(0, "为保障良好的拍卖环境，{}前请先进行认证"),

        /** 认证中 */
        AUTHING(1, "认证审核中，请耐心等待"),

        /** 认证失败 */
        AUTH_FAILED(2, "认证审核未通过，可前往查看具体原因"),

        ;

        private final Integer code;
        private final String message;
    }


}
