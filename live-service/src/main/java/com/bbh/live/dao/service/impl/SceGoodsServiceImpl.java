package com.bbh.live.dao.service.impl;

import com.bbh.live.dao.mapper.SceGoodsMapper;
import com.bbh.live.dao.service.SceGoodsService;
import com.bbh.model.SceGoods;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * <AUTHOR>
 * @Date  2025/4/1
 * @description: ${description}
*/
@Service
public class SceGoodsServiceImpl extends ServiceImpl<SceGoodsMapper, SceGoods> implements SceGoodsService {

    @Override
    public Long queryScePtAuctionId() {

        Long scePtAuctionId =baseMapper.queryScePtAuctionIdIfLiveSync();
        /*if(scePtAuctionId==null || scePtAuctionId==0){
            scePtAuctionId =baseMapper.queryScePtAuctionIdNew();
        }*/
        return scePtAuctionId;
    }
}
