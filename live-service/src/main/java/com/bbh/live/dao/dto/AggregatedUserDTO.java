package com.bbh.live.dao.dto;

import com.bbh.live.service.buyer.vip.vo.UserBuyerVipInfoVO;
import com.bbh.model.GlobalOrgSeat;
import com.bbh.model.GlobalOrganization;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 用户信息
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class AggregatedUserDTO {

    private String nickName;
    private String showName;
    private Long userId;
    private Long seatId;
    private Long orgId;
    private GlobalOrganization organization;
    private GlobalOrgSeat seat;
    private UserBuyerVipInfoVO buyerVip;

}
