package com.bbh.live.dao.dto;

import com.bbh.live.service.msg.dto.GoodsAuctionStartMsgDTO;
import com.bbh.live.service.msg.dto.GoodsPutawayMsgDTO;
import com.bbh.live.service.msg.dto.GoodsSubscribeMsgDTO;
import com.bbh.live.service.msg.dto.GoodsTransferMsgDTO;
import lombok.Data;

import java.util.List;

/**
 * 直播间详情-卡片提醒 <br>
 * 当存在多个时，返回list
 *
 * <AUTHOR>
 */
@Data
public class RoomCardMessageDTO {

    /**
     * 竞拍中的商品
     */
    GoodsAuctionStartMsgDTO goodsAuctionMsg;

    /**
     * 上架讲解中的商品
     */
    GoodsPutawayMsgDTO goodsPutAwayMsg;

    /**
     * 商品传送消息
     */
    List<GoodsTransferMsgDTO> transferMsgList;

    /**
     * 商品订阅消息
     */
    List<GoodsSubscribeMsgDTO> goodsSubscribeMsgList;

}
