package com.bbh.live.dao.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <AUTHOR> dsy
 * @Date: 2024/9/12
 * @Description:
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
public class LiveRoomSaleStatisticsDTO {

    /**
     * 成交额
     */
    private BigDecimal soldAmount;

    /**
     * 成交数量
     */
    private Integer soldCount;

    /**
     * 累计上架商品金额
     */
    private BigDecimal accumulatePutawayAmount;

    /**
     * 上架商品数量
     */
    private Integer accumulatePutawayCount;

    /**
     * 流拍商品金额
     */
    private BigDecimal abortiveAmount;

    /**
     * 流拍商品数量
     */
    private Integer abortiveCount;

    /**
     * 累计成交客户数
     */
    private Integer customerCount;
}
