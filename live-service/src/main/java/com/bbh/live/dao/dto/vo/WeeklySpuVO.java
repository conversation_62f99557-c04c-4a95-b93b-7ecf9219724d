package com.bbh.live.dao.dto.vo;

import com.bbh.model.VipWeeklySpu;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class WeeklySpuVO extends VipWeeklySpu implements Serializable {
    /**
     * 成色数量
     */
    private Integer qualityNum;
    /**
     * 成色goods
     */
    private List<WeeklySpuGoodsVO> spuGoodsList;
    /**
     * 品牌名称
     */
    private String brandName;
    /**
     * 一级品类名称
     */
    private String oneClassifyName;

    /**
     * 系列 预留字段
     */
    private String brandSeriesName;
    /**
     * 二级品类名称
     */
    private String twoClassifyName;
    /**
     * 型号
     */
    private String type;
    /**
     * 尺寸
     */
    private String size;

    /**
     * 颜色
     */
    private String colour;

	/**
	 * 上升标记
	 */
	private boolean upFlag;

	/**
	 * 下降标记
	 */
	private boolean downFlag;

	/**
	 * 保留标记
	 */
	private boolean middleFlag;

	/** 首图不需要压缩  */
	private String coverImg;
}
