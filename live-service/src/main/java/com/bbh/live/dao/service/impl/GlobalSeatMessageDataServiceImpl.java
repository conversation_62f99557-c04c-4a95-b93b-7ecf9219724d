package com.bbh.live.dao.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bbh.enums.GlobalBizTypeEnum;
import com.bbh.live.dao.mapper.ErpOrgMessageMapper;
import com.bbh.live.dao.mapper.GlobalSeatMessageDataMapper;
import com.bbh.live.dao.service.GlobalOrgCustomerServiceService;
import com.bbh.live.dao.service.GlobalSeatMessageDataService;
import com.bbh.model.ErpOrgMessage;
import com.bbh.model.GlobalSeatMessageData;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【global_seat_message_data】的数据库操作Service实现
* @createDate 2024-09-18 11:16:48
*/
@Service
@AllArgsConstructor
public class GlobalSeatMessageDataServiceImpl extends ServiceImpl<GlobalSeatMessageDataMapper, GlobalSeatMessageData> implements GlobalSeatMessageDataService{

    private final GlobalOrgCustomerServiceService globalOrgCustomerServiceService;
    private final ErpOrgMessageMapper erpOrgMessageMapper;

    /**
     * 计算席位的未读消息
     * 默认取商家客服推送消息数，如果是平台客服，加上买手消息数
     *
     * @return 未读消息数
     */
    @Override
    public Integer countSeatUnReadMessage(Long orgId, Long searId) {
        // 查询席位的汇总消息数据，兼容脏数据，直接查询list
        List<GlobalSeatMessageData> dataList = list(Wrappers.lambdaQuery(GlobalSeatMessageData.class)
                .eq(GlobalSeatMessageData::getBizType, GlobalBizTypeEnum.LIVE)
                .eq(GlobalSeatMessageData::getOrgId, orgId)
                .eq(GlobalSeatMessageData::getSeatId, searId)
                .orderByDesc(GlobalSeatMessageData::getCreatedAt)
                .last("limit 1")
        );

        // 查询商户的未读消息
        // select count(1) from erp_org_message
        //where org_id = 38
        //and create_seat_id = 41
        //and scene = 4
        //and `type` in [21, 22, 23, 25, 26, 27, 28, 29, 30, 31, 32, 33, 101, 102, 103]
        //and if_read = 0
        Long orgMessageCount = erpOrgMessageMapper.selectCount(Wrappers.lambdaQuery(ErpOrgMessage.class)
                .eq(ErpOrgMessage::getOrgId, orgId)
                .eq(ErpOrgMessage::getCreateSeatId, searId)
                .eq(ErpOrgMessage::getScene, 4)
                .in(ErpOrgMessage::getType, 21, 22, 23, 25, 26, 27, 28, 29, 30, 31, 32, 33, 103)
                .eq(ErpOrgMessage::getIfRead, 0)
        );
        orgMessageCount = orgMessageCount == null ? 0 : orgMessageCount;

        // 处理脏数据
        if (CollUtil.isEmpty(dataList) || dataList.getFirst() == null) {
            return orgMessageCount.intValue();
        }

        // 默认取商家客服推送消息数，如果是平台客服，加上买手消息数
        GlobalSeatMessageData data = dataList.getFirst();
        var isConsumerService = globalOrgCustomerServiceService.isCustomerService(orgId, searId);
        return data.getCustomerMessageNum() + (isConsumerService ? data.getBuyerMessageNum() : 0) + orgMessageCount.intValue();
    }
}




