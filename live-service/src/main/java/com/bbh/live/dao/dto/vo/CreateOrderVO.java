package com.bbh.live.dao.dto.vo;

import com.bbh.live.dao.dto.CreateOrderDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 生成订单的返回结果
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class CreateOrderVO extends CreateOrderDTO {

    /**
     * 商品数量
     */
    private Integer totalGoodsNum;

    /**
     * 商品总额
     */
    private BigDecimal totalGoodsPrice;

    /**
     * 需要支付的金额
     */
    private BigDecimal totalNeedPayAmount;

    /**
     * 商家服务费
     */
    private BigDecimal totalServiceAmount;

    /**
     * 买手服务费
     */
    private BigDecimal totalBuyerServiceAmount;

    /**
     * 通道费
     */
    private BigDecimal totalChannelAmount;

    /**
     * 能否使用分贝抵扣
     */
    private Boolean canDeductionFenbei;

    /**
     * 可抵扣的分贝数量
     */
    private Integer deductionFenbeiCount;

    /**
     * 可抵扣的分贝数量对应的金额
     */
    private BigDecimal deductionFenbeiAmount;

    /**
     * 已超额抵扣金额
     */
    private BigDecimal overDeductionFenbeiAmount;

    /**
     * 支付截止时间
     */
    private Date lastPayAt;

    /**
     * 订单ID
     */
    private Long globalOrderId;

    /**
     * 订单流水号
     */
    private String globalOrderNo;
}
