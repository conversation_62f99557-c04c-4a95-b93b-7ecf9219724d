package com.bbh.live.dao.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.bbh.live.dao.dto.FeedbackOptionDTO;
import com.bbh.model.LiveFeedback;
import com.bbh.vo.AuthUser;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【live_feedback(反馈记录)】的数据库操作Service
* @createDate 2024-07-25 11:53:19
*/
public interface LiveFeedbackService extends IService<LiveFeedback> {

    void add(AuthUser user, LiveFeedback liveFeedback);

    /**
     * 获取反馈选项列表
     */
    List<FeedbackOptionDTO> getOptionList();
}
