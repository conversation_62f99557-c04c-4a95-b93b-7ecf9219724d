package com.bbh.live.dao.dto;

import com.bbh.enums.LiveGoodsBelongTypeEnum;
import com.bbh.enums.LiveGoodsBuyerCancelRecordStatusEnum;
import com.bbh.enums.LiveGoodsTradeTypeEnum;
import com.bbh.enums.LiveRoomStreamStatusEnum;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 直播购物车
 * <AUTHOR>
 */
@Data
public class LiveShoppingCartDTO {

    /** 购物车ID */
    private Long id;

    /** 卖家机构ID */
    private Long sellerOrgId;

    /** 买家席位ID */
    private Long buyerSeatId;

    /** 买家机构ID */
    private Long buyerOrgId;

    /** 买家主体名称 */
    private String buyerOrgName;

    /** 买家机构logo URL */
    private String buyerOrgLogoUrl;

    /** 业务商品ID */
    private Long bizGoodsId;

    /** 直播商品ID */
    private Long liveGoodsId;

    /** 业务ID */
    private Long bizId;

    /** 直播间ID */
    private Long liveRoomId;

    /** 创建时间 */
    private Date createdAt;

    /** 商家logo URL */
    private String logoUrl;

    /** 机构名称 */
    private String orgName;

    /** 买家姓名 */
    private String buyerName;

    /** 买家昵称 */
    private String buyerNickName;
    private String buyerShowName;

    /** 买家拍号 */
    private String buyerAuctionCode;

    /** 买家头像 */
    private String buyerAvatar;

    /** 直播间名称 */
    private String roomName;

    /** 是否专场 */
    private Boolean ifSpecial;

    /** 直播状态 */
    private LiveRoomStreamStatusEnum streamStatus;

    /** 直播间开始时间 */
    private Date roomStartAt;

    /** 售价 */
    private BigDecimal sellPrice;

    /** 等同于售价，为前端兼容 */
    private BigDecimal goodsPrice;

    /** 成交时间 */
    private Date sellAt;

    /** 成交类型 */
    private LiveGoodsBelongTypeEnum belongType;

    /** 上架时间（有值代表有回放） */
    private Date putawayAt;

    /** 结束时间 */
    private Date endAt;

    /** 是否同步到CE */
    private Boolean ifSyncCe;

    /** 全局商品ID */
    private Long globalGoodsId;

    /** 商品名称 */
    private String goodsName;

    /** 商品质量 */
    private String quality;

    /** 商品图片URL列表 */
    private List<String> imgUrlList;

    /** 商品拍号 */
    private String liveGoodsCode;

    /** 商品视频URL */
    private String liveVideoUrl;

    /** 直播间的买手服务费率字段 */
    private BigDecimal buyerServiceRate;

    /** 买家取消成交的状态 */
    private LiveGoodsBuyerCancelRecordStatusEnum buyerCancelStatus;

    //region 通过业务计算生成的字段属性

    /** 买手服务费 */
    private BigDecimal buyerServiceAmount;

    /** 通道费 */
    private BigDecimal channelAmount;

    /** 成交类型 */
    private LiveGoodsTradeTypeEnum tradeType;

    //endregion

}
