package com.bbh.live.dao.dto.vo;

import com.bbh.live.dao.dto.LotteryPrizeItemDTO;
import lombok.Data;

import java.util.List;

/**
 * 抽奖页面
 * <AUTHOR>
 */
@Data
public class LotteryWheelVO {

    /** 抽奖公告 */
    private List<String> noticeList;

    /** 抽奖奖项 */
    private List<LotteryPrizeItemDTO> prizeList;

    /**
     * 转盘ID
     */
    private Long wheelId;

    /**
     * 活动名称
     */
    private String name;

    /**
     * 页面图片
     */
    private String imageUrl;

    /**
     * 分享图
     */
    private String shareImageUrl;

    /**
     * 说明
     */
    private String description;

    /**
     * 分贝增加抽奖次数功能: 0-关闭(Off), 1-开启(On)
     */
    private Boolean fenbeiCostEnabled;

    /**
     * 单次抽奖消耗分贝数
     */
    private Integer singleDrawCost;

    /**
     * 5次抽奖消耗分贝数
     */
    private Integer fiveDrawsCost;

    /**
     * 10次抽奖消耗分贝数
     */
    private Integer tenDrawsCost;

    /**
     * 今日剩余抽奖次数
     */
    private Integer remainingCount;

    /**
     * 开通VIP后赠送的抽奖次数，如果是新开通就取0级的次数，如果是过期续费就是当前等级的次数
     */
    private Integer vipFreeTimes;

}
