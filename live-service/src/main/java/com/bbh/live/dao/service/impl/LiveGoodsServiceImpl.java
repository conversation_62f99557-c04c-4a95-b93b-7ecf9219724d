package com.bbh.live.dao.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bbh.base.Page;
import com.bbh.enums.LiveGoodsStatusEnum;
import com.bbh.exception.ServiceException;
import com.bbh.live.constant.LiveGoodsConstant;
import com.bbh.live.constant.ProjectConstant;
import com.bbh.live.controller.req.LiveGoodsQueryReq;
import com.bbh.live.dao.dto.AbortiveAuctionGoodsDTO;
import com.bbh.live.dao.dto.CheckedLiveGoodsDTO;
import com.bbh.live.dao.dto.OffhandPutAwayDTO;
import com.bbh.live.dao.dto.livegoods.LiveGoodsDTO;
import com.bbh.live.dao.dto.livegoods.LiveGoodsSaleDTO;
import com.bbh.live.dao.dto.livegoods.LiveGoodsSortDTO;
import com.bbh.live.dao.dto.livegoods.LiveGoodsStatisticsDTO;
import com.bbh.live.dao.dto.vo.AuctionLiveGoodsVO;
import com.bbh.live.dao.mapper.LiveGoodsMapper;
import com.bbh.live.dao.service.ErpGoodsService;
import com.bbh.live.dao.service.LiveGoodsService;
import com.bbh.live.service.msg.dto.base.BaseGoods;
import com.bbh.live.util.PageUtils;
import com.bbh.model.ErpGoods;
import com.bbh.model.LiveGoods;
import com.bbh.secure.AuthUtil;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【live_goods(直播间商品)】的数据库操作Service实现
 * @createDate 2024-07-25 11:53:19
 */
@Service
@AllArgsConstructor
public class LiveGoodsServiceImpl extends ServiceImpl<LiveGoodsMapper, LiveGoods>
        implements LiveGoodsService {
    private final ErpGoodsService erpGoodsService;

    @Override
    public LiveGoods initializeNewLiveGoods(Long liveRoomId, Long globalGoodsId) {
        LiveGoods liveGoods = new LiveGoods();
        liveGoods.setLiveRoomId(liveRoomId);
        liveGoods.setGlobalGoodsId(globalGoodsId);
        liveGoods.setGoodsStatus(LiveGoodsStatusEnum.WAIT_PUT_AWAY);
        return liveGoods;
    }

    @Override
    public List<Long> checkLiveGoodsExists(List<Long> liveGoodsIdList) {
        Set<Long> liveGoodsIdSet = new HashSet<>(liveGoodsIdList);
        Set<Long> existsLiveGoods = this.lambdaQuery().in(LiveGoods::getId, liveGoodsIdSet)
                .select(LiveGoods::getId).list().stream()
                .map(LiveGoods::getId).collect(Collectors.toSet());
        liveGoodsIdSet.removeAll(existsLiveGoods);
        return new ArrayList<>(liveGoodsIdSet);
    }

    @Override
    public Long getMaxLiveGoodsCodeByLiveRoomId(Long liveRoomId) {
        Optional<LiveGoods> goodsOptional = this.lambdaQuery()
                .eq(LiveGoods::getLiveRoomId, liveRoomId)
                .orderByDesc(LiveGoods::getLiveGoodsCode)
                .last("limit 1")
                .select(LiveGoods::getLiveGoodsCode)
                .oneOpt();

        return goodsOptional.map(LiveGoods::getLiveGoodsCode).orElse(0L);
    }

    @Override
    public Integer getMinSortGoodsByLiveRoomId(Long liveRoomId) {
        LiveGoods one = this.lambdaQuery().eq(LiveGoods::getLiveRoomId, liveRoomId)
                .orderByAsc(LiveGoods::getSort)
                .last("limit 1")
                .select(LiveGoods::getSort)
                .one();
        return one != null ? one.getSort() : LiveGoodsConstant.SORT_BEGIN_VALUE;
    }

    @Override
    public Integer getMaxSortGoodsByLiveRoomId(Long liveRoomId) {
        LiveGoods one = this.lambdaQuery().eq(LiveGoods::getLiveRoomId, liveRoomId)
                .orderByDesc(LiveGoods::getSort)
                .last("limit 1")
                .select(LiveGoods::getSort)
                .one();
        return one != null ? one.getSort() : LiveGoodsConstant.SORT_BEGIN_VALUE;
    }

    @Override
    public IPage<LiveGoodsDTO> getLiveGoodsList(LiveGoodsQueryReq queryReq) {
        Page<LiveGoodsDTO> page = PageUtils.getPage(queryReq, LiveGoodsDTO.class);
        if(queryReq.getSort() != null){
            page.addOrder(queryReq.getSort());
        }
        queryReq.parseKeywords();
        var pages =  this.getBaseMapper().getLiveGoodsList(page, queryReq);
        if (CollUtil.isEmpty(pages.getRecords())) {
            return pages;
        }
        pages.getRecords().forEach(liveGoodsDTO -> {
            // 过滤掉已删除的商品
            liveGoodsDTO.setGlobalGoodsId(liveGoodsDTO.getErpDeletedAt() == null ? liveGoodsDTO.getGlobalGoodsId() : null);
        });
        return pages;
    }

    @Override
    public IPage<LiveGoodsDTO> getWaitCompleteLiveGoodsList(LiveGoodsQueryReq queryReq) {
        Page<LiveGoodsDTO> page = PageUtils.getPage(queryReq, LiveGoodsDTO.class);
        if(queryReq.getSort() != null){
            page.addOrder(queryReq.getSort());
        }
        queryReq.parseKeywords();
        return this.getBaseMapper().getWaitCompleteLiveGoodsList(page, queryReq);
    }

    @Override
    public LiveGoodsDTO getLiveGoodsDetailInfo(Long liveGoodsId) {
        return this.getBaseMapper().getLiveGoodsDetailInfo(liveGoodsId, null);
    }

    @Override
    public BaseGoods getBaseGoodsInfo(Long liveGoodsId) {
        return this.getBaseMapper().getBaseGoodsInfo(liveGoodsId);
    }

    @Override
    public void batchUpdateLiveGoodsSort(List<LiveGoodsSortDTO> sortDTOList) {
        this.getBaseMapper().batchUpdateLiveGoodsSort(sortDTOList);
    }

    @Override
    public List<LiveGoods> batchToCeLogicCheckAndReturnErpGoodsList(List<Long> liveGoodsIds) {
        LambdaQueryWrapper<LiveGoods> liveGoodsLqw = new LambdaQueryWrapper<>();
        liveGoodsLqw.in(LiveGoods::getId, liveGoodsIds)
                .notIn(LiveGoods::getGoodsStatus, LiveGoodsStatusEnum.AUCTION.getCode(), LiveGoodsStatusEnum.TRADED.getCode());
        List<LiveGoods> liveGoods = this.list(liveGoodsLqw);
        if (liveGoods.isEmpty() || liveGoods.size() != liveGoodsIds.size()) {
            throw new ServiceException("存在商品状态竞拍中或已成交");
        }
        List<Long> erpGoodsIds = liveGoods.stream().map(LiveGoods::getGlobalGoodsId).toList();
        LambdaQueryWrapper<ErpGoods> erpGoodsLqw = new LambdaQueryWrapper<>();
        erpGoodsLqw.in(ErpGoods::getId, erpGoodsIds)
                .eq(ErpGoods::getOrgId, AuthUtil.getOrgId())
                .eq(ErpGoods::getSaleStatus, ProjectConstant.ErpGoodsSaleStatus.NORMAL)
                .eq(ErpGoods::getIfLocked, ProjectConstant.ErpGoodsLockStatus.NORMAL)
                .select(ErpGoods::getId);
        List<ErpGoods> erpGoodsList = erpGoodsService.list(erpGoodsLqw);
        if (erpGoodsList.isEmpty() || erpGoodsList.size() != liveGoodsIds.size()) {
            throw new ServiceException("erp商品存在已经锁单或者卖出的商品");
        }
        return liveGoods;
    }

    @Override
    public List<AbortiveAuctionGoodsDTO> getAuctionFailLiveGoodsListByLiveRoomId(Collection<Long> liveRoomIdList, Integer fetchSize) {
        return this.getBaseMapper().getAuctionFailLiveGoodsListByLiveRoomId(liveRoomIdList, fetchSize);
    }

    @Override
    public LiveGoodsStatisticsDTO getLiveGoodsCountBeforeLiveStarted(LiveGoodsQueryReq queryReq) {
        LiveGoodsStatisticsDTO countInfo = new LiveGoodsStatisticsDTO();
        queryReq.parseKeywords();
        var countList = this.getBaseMapper().getLiveGoodsCountBeforeLiveStart(queryReq);
        if(countList.size() == 1){
            // 只有一个说明两个数量相同
            countInfo.setWaitCompleteCount(countList.getFirst()).setTotalCount(countList.getFirst());
        } else {
            countInfo.setWaitCompleteCount(countList.removeFirst()).setTotalCount(countList.removeFirst());
        }
        return countInfo;
    }

    @Override
    public LiveGoodsStatisticsDTO getLiveGoodsCountAfterLiveStarted(LiveGoodsQueryReq queryReq) {
        LiveGoodsStatisticsDTO countInfo = new LiveGoodsStatisticsDTO();
        queryReq.parseKeywords();
        Map<Integer, Map<String, Long>> liveGoodsCountGroupByStatus = this.getBaseMapper().getLiveGoodsCountGroupByStatus(queryReq);
        // 待上架
        countInfo.setWaitPutAwayCount(getCount(LiveGoodsStatusEnum.WAIT_PUT_AWAY, liveGoodsCountGroupByStatus));
        // 正在上架
        countInfo.setPutAwayCount(getCount(LiveGoodsStatusEnum.PUT_AWAY, liveGoodsCountGroupByStatus));
        // 竞拍中
        countInfo.setAuctionCount(getCount(LiveGoodsStatusEnum.AUCTION, liveGoodsCountGroupByStatus));
        // 已成交
        countInfo.setTradeCount(this.getBaseMapper().getLiveGoodsTradeCount(queryReq));
        // 已流拍
        countInfo.setAbortiveAuctionCount(getCount(LiveGoodsStatusEnum.ABORTIVE_AUCTION, liveGoodsCountGroupByStatus));
        // 总数
        countInfo.setTotalCount(liveGoodsCountGroupByStatus.values().stream().mapToLong(m -> m.getOrDefault("count", 0L)).sum());
        return countInfo;
    }

    private Long getCount(LiveGoodsStatusEnum status, Map<Integer, Map<String, Long>> liveGoodsCountGroupByStatus){
        var map = liveGoodsCountGroupByStatus.get(status.getCode());
        if(map == null){
            return 0L;
        }
        return map.getOrDefault("count", 0L);
    }

    @Override
    public void incrGoodsSubscribeCount(Long liveGoodsId, int delta) {
        this.getBaseMapper().incrGoodsSubscribeCount(liveGoodsId, delta);
    }

    @Override
    public List<CheckedLiveGoodsDTO> getCheckedLiveGoodsList(Long liveRoomId) {
        return this.getBaseMapper().getCheckedLiveGoodsList(liveRoomId);
    }

    @Override
    public List<AuctionLiveGoodsVO> getAuctionLiveGoodsListByLiveRoom(List<Long> liveRoomIds) {
        return this.getBaseMapper().getLiveGoodsListByLiveRoomAndStatus(liveRoomIds, LiveGoodsStatusEnum.AUCTION.getCode());
    }

    @Override
    public List<AuctionLiveGoodsVO> getPutAwayLiveGoodsListByLiveRoom(List<Long> liveRoomIds) {
        return this.getBaseMapper().getLiveGoodsListByLiveRoomAndStatus(liveRoomIds, LiveGoodsStatusEnum.PUT_AWAY.getCode());
    }

    @Override
    public LiveGoods createOffhandLiveGoods(OffhandPutAwayDTO offhandPutAwayDTO, Long globalGoodsId) {
        LiveGoods liveGoods = new LiveGoods();
        liveGoods.setLiveRoomId(offhandPutAwayDTO.getLiveRoomId());
        liveGoods.setGlobalGoodsId(globalGoodsId);
        liveGoods.setGoodsStatus(LiveGoodsStatusEnum.WAIT_PUT_AWAY);
        liveGoods.setTradeType(offhandPutAwayDTO.getTradeType());
        liveGoods.setAuctionDuration(offhandPutAwayDTO.getAuctionDuration());
        liveGoods.setStartPrice(offhandPutAwayDTO.getStartPrice());
        liveGoods.setIncreasePrice(offhandPutAwayDTO.getIncreasePrice());
        // 即拍即上商品 不能上架云展
        liveGoods.setIfOffhandGoods(true);

        var maxLiveGoodsCode = this.getMaxLiveGoodsCodeByLiveRoomId(offhandPutAwayDTO.getLiveRoomId());
        liveGoods.setLiveGoodsCode(++maxLiveGoodsCode);

        Integer minSort = this.getMinSortGoodsByLiveRoomId(offhandPutAwayDTO.getLiveRoomId());
        liveGoods.setSort(minSort - 1);
        this.save(liveGoods);
        return liveGoods;
    }

    @Override
    public List<LiveGoodsSaleDTO> getSoldGoodsListByLiveRoom(Long liveRoomId) {
        return this.getBaseMapper().getSoldGoodsListByLiveRoom(liveRoomId);
    }

    @Override
    public Map<Long, Long> getEffectiveAuctionFailLiveGoodsCount(Collection<Long> liveRoomIdList) {
        Map<Long, Map<Long, Long>> countMap = this.getBaseMapper().getEffectiveAuctionFailLiveGoodsCount(liveRoomIdList);
        return countMap.entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey, e -> e.getValue().getOrDefault("count", 0L)));
    }

    @Override
    public IPage<LiveGoodsDTO> getLiveGoodsToSceGoods(LiveGoodsQueryReq queryReq) {
        Page<LiveGoodsDTO> page = PageUtils.getPage(queryReq, LiveGoodsDTO.class);
        if(queryReq.getSort() != null){
            page.addOrder(queryReq.getSort());
        }
        queryReq.parseKeywords();
        var pages =  this.getBaseMapper().getLiveGoodsToSceGoods(page, queryReq);
        if (CollUtil.isEmpty(pages.getRecords())) {
            return pages;
        }
        pages.getRecords().forEach(liveGoodsDTO -> {
            // 过滤掉已删除的商品
            liveGoodsDTO.setGlobalGoodsId(liveGoodsDTO.getErpDeletedAt() == null ? liveGoodsDTO.getGlobalGoodsId() : null);
        });
        return pages;
    }
}




