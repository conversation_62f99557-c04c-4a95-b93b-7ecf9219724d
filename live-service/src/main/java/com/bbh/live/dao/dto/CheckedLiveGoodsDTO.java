package com.bbh.live.dao.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR> dsy
 * @Date: 2024/8/27
 * @Description:
 */
@Data
public class CheckedLiveGoodsDTO {

    /**
     * 直播商品id
     */
    private Long liveGoodsId;

    /**
     *  erp商品id
     */
    private Long globalGoodsId;

    /**
     *  同行价
     */
    private BigDecimal peerPrice;

    /**
     * 图片地址
     */
    private List<String> imgUrlList;

    /**
     *  商品名称
     */
    private String globalGoodsName;

    /**
     * 商品状态
     */
    private Integer goodsStatus;

    /**
     * 结算状态: 00-待结算, 20-已结算
     */
    private Integer settleStatus;

    /**
     * erp是否售出
     */
    private Integer erpSaleStatus;

    /**
     * erp是否开单
     */
    private Boolean erpPlaceOrderStatus;

    /**
     * erp是否锁单
     */
    private Boolean erpIfLocked;

    /**
     * 成色
     */
    private String quality;
}
