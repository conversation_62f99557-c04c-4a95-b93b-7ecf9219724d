package com.bbh.live.dao.service.impl;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bbh.live.dao.mapper.ErpStorehouseMapper;
import com.bbh.live.dao.service.IErpStorehouseService;
import com.bbh.live.dao.service.LiveRoomService;
import com.bbh.model.ErpStorehouse;
import com.bbh.model.LiveRoom;
import com.bbh.util.AssertUtil;
import lombok.AllArgsConstructor;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【erp_storehouse(仓库)】的数据库操作Service实现
* @createDate 2024-06-28 15:08:19
*/
@Service
@AllArgsConstructor
public class ErpStorehouseServiceImpl extends ServiceImpl<ErpStorehouseMapper, ErpStorehouse>
    implements IErpStorehouseService {

    private final LiveRoomService liveRoomService;

    @Override
    public Long getLiveOffhandStorehouse(@NotNull Long liveRoomId) {

        var liveRoom = liveRoomService.lambdaQuery().eq(LiveRoom::getId, liveRoomId)
                .select(LiveRoom::getId, LiveRoom::getOffhandStorehouseId, LiveRoom::getRoomName, LiveRoom::getStartAt).one();
        AssertUtil.assertNotNull(liveRoom, "直播间不存在");
        if(liveRoom.getOffhandStorehouseId() != null){
            return liveRoom.getOffhandStorehouseId();
        }

        // 创建直播仓库
        var storehouse = new ErpStorehouse();
        storehouse.setName(liveRoom.getRoomName() + "-" + DateUtil.format(liveRoom.getStartAt(), DatePattern.PURE_DATE_PATTERN));
        storehouse.setType(2);
        storehouse.setRemark("直播间" + liveRoom.getRoomName() + "的即拍即上商品仓库");
        storehouse.setPermissionIfPublic(1);
        this.save(storehouse);

        // 更新直播间
        liveRoom.setOffhandStorehouseId(storehouse.getId());
        liveRoomService.updateById(liveRoom);

        return storehouse.getId();
    }
}




