package com.bbh.live.dao.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bbh.live.dao.mapper.GlobalUserMapper;
import com.bbh.live.dao.service.IGlobalUserService;
import com.bbh.model.GlobalUser;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【global_user(用户表)】的数据库操作Service实现
* @createDate 2024-06-21 10:18:19
*/
@Service
public class GlobalUserServiceImpl extends ServiceImpl<GlobalUserMapper, GlobalUser>
    implements IGlobalUserService {

}




