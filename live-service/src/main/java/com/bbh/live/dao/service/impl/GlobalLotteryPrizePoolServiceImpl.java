package com.bbh.live.dao.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bbh.live.dao.mapper.GlobalLotteryPrizePoolMapper;
import com.bbh.live.dao.service.GlobalLotteryPrizePoolService;
import com.bbh.model.GlobalLotteryPrizePool;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【global_lottery_prize_pool(抽奖奖品池管理表)】的数据库操作Service实现
* @createDate 2024-10-08 16:01:43
*/
@Service
public class GlobalLotteryPrizePoolServiceImpl extends ServiceImpl<GlobalLotteryPrizePoolMapper, GlobalLotteryPrizePool>
    implements GlobalLotteryPrizePoolService{

}




