package com.bbh.live.dao.dto;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 上帝视角体验状态，保持和老帮帮虎一样的字段
 * <AUTHOR>
 */
@Data
public class GodViewTrialStatusDTO {

    /**
     * 第一次查看时间
     */
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date createTime;

    /**
     * 第一次查看到试用结束时间倒计时 秒
     */
    private Long freeCountSecondsCountdown;

    /**
     * 未使用的免费查看次数
     */
    private Integer freeViewUnUsedTimes;

    /**
     * 能否免费查看 能true不能false vip的默认是true
     */
    public Boolean canFreeView = false;

}
