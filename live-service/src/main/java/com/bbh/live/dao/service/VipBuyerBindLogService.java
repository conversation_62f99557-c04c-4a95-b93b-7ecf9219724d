package com.bbh.live.dao.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.bbh.model.VipBuyerBindLog;

/**
* <AUTHOR>
* @description 针对表【vip_buyer_bind_log(买手vip换绑记录)】的数据库操作Service
* @createDate 2024-11-11 08:37:17
*/
public interface VipBuyerBindLogService extends IService<VipBuyerBindLog> {

    /**
     * 保存买手VIP换绑记录
     * @param oldSeatId
     * @param newSeatId
     * @param vipId
     * @return
     */
    void saveVipBuyerBindLog(Long oldSeatId, Long newSeatId, Long vipId);
}
