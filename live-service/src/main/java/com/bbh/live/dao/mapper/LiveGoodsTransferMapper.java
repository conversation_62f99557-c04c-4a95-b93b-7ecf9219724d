package com.bbh.live.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bbh.live.dao.dto.TransferredGoodsDTO;
import com.bbh.live.dao.dto.TransferredGoodsParamDTO;
import com.bbh.model.LiveGoodsTransfer;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【live_goods_transfer(商品传送记录)】的数据库操作Mapper
* @createDate 2024-07-25 11:53:19
* @Entity com.bbh.model.LiveGoodsTransfer
*/
public interface LiveGoodsTransferMapper extends BaseMapper<LiveGoodsTransfer> {

    List<TransferredGoodsDTO> getTransferredGoodsList(@Param("queryParam") TransferredGoodsParamDTO queryParam);

}




