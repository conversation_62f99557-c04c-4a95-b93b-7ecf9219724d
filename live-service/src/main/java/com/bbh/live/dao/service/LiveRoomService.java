package com.bbh.live.dao.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.bbh.live.dao.dto.LiveRoomSaleStatisticsDTO;
import com.bbh.model.LiveRoom;

import java.util.Date;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【live_room(直播间)】的数据库操作Service
* @createDate 2024-07-25 11:53:19
*/
public interface LiveRoomService extends IService<LiveRoom> {

    /**
     * 获取用户可见的所有正在直播的直播间列表
     * @param currentUserId
     * @return
     */
    List<LiveRoom> getInLiveRoomList(Long currentUserId, Long currentSeatId, Boolean filterBlacklist);

    boolean ifLiveRoomTestBroadcast(Long liveRoomId);

    boolean ifLiveRoomTestBroadcast(Date startAt);

    boolean ifLiveRoomTestBroadcast(LiveRoom liveRoom);

    /**
     * 直播间统计信息
     * @param liveRoomId
     * @return
     */
    LiveRoomSaleStatisticsDTO getLiveRoomSaleStatisticsInfo(Long liveRoomId);

}
