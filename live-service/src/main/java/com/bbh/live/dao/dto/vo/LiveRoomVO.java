package com.bbh.live.dao.dto.vo;

import cn.hutool.json.JSONObject;
import com.bbh.enums.LiveRoomFilterModeEnum;
import com.bbh.enums.LiveRoomStreamStatusEnum;
import com.bbh.enums.LiveSyncSceEnum;
import com.bbh.live.enums.LiveRoomEnhancedStatusEnum;
import com.bbh.live.enums.SeatRoleInRoomEnum;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 直播列表的响应结果
 *
 * <AUTHOR>
 */
@Data
public class LiveRoomVO {

    private Long id;

    /**
     * 直播间聊天室ID
     */
    private String chatroomId;

    /**
     * 直播推流地址
     */
    private String streamPushUrl;

    /**
     * 直播拉流地址: JSON对象, 其中key为存储格式, value为地址
     */
    private JSONObject streamPullUrlJson;

    /**
     * 直播回放地址
     */
    private String liveVideoUrl;

    /**
     * 直播间名称
     */
    private String roomName;

    /**
     * 直播间 直播前的封面
     */
    private String beforeCoverImgUrl;

    /**
     * 直播间 直播中的封面
     */
    private String duringCoverImgUrl;

    /**
     * 直播间 分享封面
     */
    private String shareCoverImgUrl;

    /**
     * 直播间描述
     */
    private String roomDesc;

    /**
     * 直播间公告
     */
    private String notice;

    /**
     * 直播间注意事项
     */
    private String note;

    /**
     * 直播间状态: 00-关闭, 10-开启
     */
    private LiveRoomStreamStatusEnum streamStatus;

    /**
     * 过滤模式: 10-白名单 20-黑名单 ，默认黑名单
     */
    private LiveRoomFilterModeEnum filterMode;

    /**
     * 白名单模式是否可见：0-不可见，1-可见
     */
    private Boolean whitelistShow;

    /** 白名单：列表是否可见提示文案 */
    private String showableTips;

    /** 买手vip是否可直接进入 */
    private Boolean ifVipDirectlyEnter;

    /**
     * 是否极速打款
     */
    private Boolean ifFastPayment;

    /**
     * 是否展示
     */
    private Boolean showable;

    /**
     * 是否专场
     */
    private Boolean ifSpecial;

    /**
     * 是否是推荐直播间
     */
    private Boolean ifRecommend;

    /**
     * 是否要门票
     */
    private Boolean ifNeedTicket;

    /**
     * 门票金额
     */
    private BigDecimal ticketPrice;

    /**
     * 直播间得分，通过算法计算出的得分，用于排序和推荐
     */
    private BigDecimal score;

    /**
     * 用于后台手动调整的排序，优先级高于score
     */
    private Integer sort;

    /**
     * 设置的开播时间
     */
    private Date startAt;

    /**
     * 设置的关播时间
     */
    private Date endAt;

    /**
     * 设置的商品清单保持时长<br>
     * 单位:(小时)
     */
    private Integer goodsListDuration;

    /**
     *  商品清单对看播是否可见
     */
    private Boolean ifGoodsListVisible;

    /**
     * 商品清单最后关闭时间
     */
    private Date goodsListLastCloseAt;

    /**
     * 竞拍时间追加阈值，竞拍时间低于这个阈值，有人出价则追加时间<br>
     * 单位:(s)
     */
    private Integer increaseSurplusTime;

    /**
     *  竞拍时间追加的秒数<br>
     *  单位:(s)
     */
    private Integer increaseTime;

    /**
     * 实际的开播时间
     */
    private Date actualStartAt;

    /**
     * 实际的关播时间
     */
    private Date actualEndAt;

    /**
     * 实际的商品清单结束时间
     */
    private Date actualGoodsListEndAt;

    /**
     * 主播id
     */
    private Long anchorUserId;

    /**
     * 主播席位id
     */
    private Long anchorSeatId;

    /**
     * 费率模板id
     */
    private Long rateTemplateId;

    /**
     * 膏药贴内容
     */
    private String stickerContent;

    /**
     * 直播间观看人次
     */
    private Integer viewCount;

    /**
     * 直播间真实峰值人数
     */
    private Integer realCount;

    /**
     * 商品数量
     */
    private Integer goodsCount;

    /** 待拍商品数量 */
    private Long waitAuctionCount;

    /**
     * 流拍数量
     */
    private Integer unsoldCount;

    /**
     * 预约数量
     */
    private Integer subscribeCount;

    /**
     * 成交金额
     */
    private BigDecimal soldAmount;

    /**
     * 成交数量
     */
    private Integer soldCount;

    /**
     * 买家服务费率
     */
    private BigDecimal buyerServiceRate;

    /**
     * 原始的商户ID
     */
    private Long originalOrgId;

    /**
     * 加密后的商户ID
     */
    private String orgId;

    private String orgName;

    private String orgLogoUrl;

    /**
     * 是否关注
     */
    private Boolean ifFollowed;

    /**
     * 是否预约
     */
    private Boolean ifSubscribed;

    /**
     * 费率模板名称
     */
    private String rateTemplateName;

    /**
     * 复合的直播间状态，包含直播暂停状态
     */
    private LiveRoomEnhancedStatusEnum roomStatus;

    /**
     * 商品列表截止时间
     */
    private Date goodsListExpiredAt;

    /**
     * 是否允许试播
     */
    private Boolean ifTestBroadcast;

    /**
     * 是否官方认证
     */
    private Boolean ifOfficiallyCertified;

    /*
     * 当前席位身份：看播、导播、主播，在业务中查询相关数据后set
     */
    private SeatRoleInRoomEnum seatRole;

    /**
     * 是否相关员工
     */
    private Boolean ifRelatedStaff;

    /**
     * 保证金购买力比例
     */
    private Integer depositRate;

    /**
     * 直播商品是否需要同步到云展 0-不需要，1-需要
     */
    private LiveSyncSceEnum ifNeedSyncCe;

}
