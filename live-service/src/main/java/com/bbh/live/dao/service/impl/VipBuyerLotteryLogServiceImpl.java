package com.bbh.live.dao.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bbh.model.VipBuyerLotteryLog;
import com.bbh.live.dao.mapper.VipBuyerLotteryLogMapper;
import com.bbh.live.dao.service.VipBuyerLotteryLogService;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【vip_buyer_lottery_log(VIP抽奖使用记录表)】的数据库操作Service实现
* @createDate 2024-08-14 17:51:46
*/
@Service
public class VipBuyerLotteryLogServiceImpl extends ServiceImpl<VipBuyerLotteryLogMapper, VipBuyerLotteryLog>
    implements VipBuyerLotteryLogService{

}




