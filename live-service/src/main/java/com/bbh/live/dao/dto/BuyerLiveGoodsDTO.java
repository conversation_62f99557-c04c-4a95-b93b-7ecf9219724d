package com.bbh.live.dao.dto;

import com.bbh.enums.LiveRoomInteractiveMessageHandleStatusEnum;
import com.bbh.live.dao.dto.livegoods.LiveGoodsDTO;
import com.bbh.live.enums.LiveRoomEnhancedStatusEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * <AUTHOR> dsy
 * @Date: 2024/7/31
 * @Description: 买手商品详情对象
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class BuyerLiveGoodsDTO extends LiveGoodsDTO {

    /**
     * 商家名称
     */
    private String orgName;

    /**
     * 商家logo
     */
    private String logoUrl;

    /**
     * 商品是否已经预约
     */
    private Boolean hasSubscribed;

    /**
     * 商品预约记录
     */
    private String subscribeMark;

    /**
     * 议价金额
     */
    private BigDecimal bargainPrice;

    /**
     * 议价消息状态
     */
    private LiveRoomInteractiveMessageHandleStatusEnum bargainStatus;

    /**
     * 直播间状态
     */
    private LiveRoomEnhancedStatusEnum roomStatus;

    /**
     * 加密后的商户id
     */
    private String encryptedOrgId;

    /**
     * 商家融云id
     */
    private String orgRongCloudId;

    /**
     * 取消成交的申请单id
     */
    private Long cancelOrderId;

    /**
     * 是否申请了取消成交
     */
    private Boolean isApplyCancel = false;

}
