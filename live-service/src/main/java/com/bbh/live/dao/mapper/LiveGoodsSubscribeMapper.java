package com.bbh.live.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bbh.live.dao.dto.livegoods.LiveGoodsSubscribeDTO;
import com.bbh.model.LiveGoodsSubscribe;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【live_goods_subscribe(商品预约记录)】的数据库操作Mapper
* @createDate 2024-07-25 11:53:19
* @Entity com.bbh.model.LiveGoodsSubscribe
*/
public interface LiveGoodsSubscribeMapper extends BaseMapper<LiveGoodsSubscribe> {

    /**
     * 查询商品预约列表，会带上商品图片
     * @param createUserId 当前用户
     * @return 商品预约列表
     */
    List<LiveGoodsSubscribeDTO> getSubscribeGoodsList(@Param("createUserId") Long createUserId);


    /**
     * 根据买家座位id查询预约商品信息
     * @param buyerSeatId
     * @return
     */
    List<LiveGoodsSubscribeDTO> getSubscribeGoodsInfoByBuyerSeatId(@Param("createSeatId") Long buyerSeatId, @Param("now") Date now);
}




