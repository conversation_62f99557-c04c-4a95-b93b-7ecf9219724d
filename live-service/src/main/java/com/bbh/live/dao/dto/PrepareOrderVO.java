package com.bbh.live.dao.dto;

import java.math.BigDecimal;
import java.util.List;

import lombok.Data;

/**
 * 准备订单页面
 * 
 * <AUTHOR>
 */
@Data
public class PrepareOrderVO {

    /**
     * 按直播场次分组后的列表
     */
    private List<LiveShoppingCartGroupVO> groupList;

    /**
     * 商品总额
     */
    private BigDecimal totalGoodsPrice;

    /**
     * 应支付总额，此处等于商品总额 + 服务费总额
     */
    private BigDecimal totalNeedPayAmount;

    /**
     * 服务费总额，此处特指买家服务费
     */
    private BigDecimal totalServiceAmount;

    /**
     * 通道费总额
     */
    private BigDecimal totalChannelAmount;

    // region 会员信息

    /**
     * 分贝余额
     */
    private Integer fenbei;

    /**
     * 是否会员
     */
    private Boolean ifVip;

    /**
     * 是否年费会员
     */
    private Boolean ifAnnualFeeVip;

    /**
     * 会员等级
     */
    private Integer vipLevel;

    // endregion

    /**
     * 能否使用分贝抵扣
     */
    private Boolean canDeductionFenbei;

    // region 分贝抵扣信息

    /**
     * 可抵扣的分贝数量
     */
    private Integer deductionFenbeiCount;

    /**
     * 可抵扣的分贝数量对应的金额
     */
    private BigDecimal deductionFenbeiAmount;

    /**
     * 已超额抵扣金额
     */
    private BigDecimal overDeductionFenbeiAmount;

    private Integer vipMaxDeductionFenbeiCount;

    private BigDecimal vipMaxDeductionFenbeiAmount;

    // endregion

    // region 线下转账

    /**
     * 当前每场会扣除的数量，会员和普通用户共用这个
     */
    private long offlinePayDeductFenbeiPerMatch = 0L;

    /**
     * 本次应该扣除的分贝总数 = 场次数量 * 每场会扣除的数量
     */
    private long offlinePayTotalDeductFenbei = 0L;

    /**
     * 场次数量
     */
    private long offlinePayMatchCount = 0;

    /**
     * 开通会员后，每场会扣除的数量
     */
    private long offlinePayVipDeductFenbeiPerMatch = 0L;

    /**
     * 普通用户，每场会扣除的数量
     */
    private long offlinePayDefaultDeductFenbeiPerMatch = 0L;

    private Long vipDeductionId = 0L;

    /**
     * 剩余抵扣金
     */
    private BigDecimal vipDeduction = BigDecimal.ZERO;
    /**
     * 可抵扣的会员抵扣金
     */
    private BigDecimal deductionVipCount;

    /**
     * 可抵扣的分贝数量可用
     */
    private BigDecimal usedDeductionVipCount;
    /**
     * 可抵扣的分贝数量可用
     */
    private BigDecimal canDeductionVipCount;

    // endregion

}
