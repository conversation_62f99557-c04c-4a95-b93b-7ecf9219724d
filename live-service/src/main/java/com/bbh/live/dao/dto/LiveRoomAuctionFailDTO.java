package com.bbh.live.dao.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR> dsy
 * @Date: 2024/7/31
 * @Description:
 */
@Data
@Accessors(chain = true)
public class LiveRoomAuctionFailDTO {

    /**
     * 直播间ID
     */
    private Long liveRoomId;
    /**
     * 直播间名称
     */
    private String liveRoomName;
    /**
     * 是否为专场直播间
     */
    private Boolean liveRoomIfSpecial;
    /**
     * 商家ID
     */
    private Long orgId;
    /**
     * 商家名称
     */
    private String orgName;
    /**
     * 商家logo
     */
    private String logoUrl;

    /**
     * 本场直播成交数量
     */
    private Long tradeCount;

    /**
     * 直播间流拍商品总数
     */
    private Long totalCount;
    /**
     * 直播间流拍商品集合
     */
    private List<AbortiveAuctionGoodsDTO> liveGoodsList;
}
