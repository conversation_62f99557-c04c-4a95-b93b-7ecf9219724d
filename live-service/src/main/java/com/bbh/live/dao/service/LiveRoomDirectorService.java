package com.bbh.live.dao.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.bbh.model.LiveRoomDirector;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【live_room_director(导播表)】的数据库操作Service
* @createDate 2024-07-25 11:53:19
*/
public interface LiveRoomDirectorService extends IService<LiveRoomDirector> {

    List<LiveRoomDirector> listByRoomIds(List<Long> liveRoomIds);

    /**
     * 判断是否导播
     * @param roomId    直播间id
     * @param seatId    席位id
     * @return          是否导播
     */
    boolean isLiveRoomDirector(Long roomId, Long seatId);
}
