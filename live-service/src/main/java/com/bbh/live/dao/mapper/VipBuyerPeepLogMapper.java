package com.bbh.live.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bbh.live.dao.dto.VipPeepLogDTO;
import com.bbh.model.VipBuyerPeepLog;
import org.apache.ibatis.annotations.Param;

import java.util.Date;

/**
* <AUTHOR>
* @description 针对表【vip_buyer_peep_log(VIP窥探出价人使用记录表)】的数据库操作Mapper
* @createDate 2024-08-14 17:51:46
* @Entity com.bbh.model.VipBuyerPeepLog
*/
public interface VipBuyerPeepLogMapper extends BaseMapper<VipBuyerPeepLog> {

    /**
     * 根据seatId获取窥探记录
     * @param page
     * @param seatId
     * @return
     */
    Page<VipPeepLogDTO> getPeepLogsBySeatId(@Param("page") IPage<VipBuyerPeepLog> page, @Param("seatId") Long seatId, @Param("startAt") Date startAt, @Param("startAt") Date endAt);

    /**
     * 根据vipId获取窥探记录
     * @param page
     * @param vipId
     * @param startAt
     * @param endAt
     * @return
     */
    Page<VipPeepLogDTO> getPeepLogsByVipId(@Param("page") IPage<VipBuyerPeepLog> page, @Param("vipId") Long vipId, @Param("startAt") Date startAt, @Param("endAt") Date endAt);
}




