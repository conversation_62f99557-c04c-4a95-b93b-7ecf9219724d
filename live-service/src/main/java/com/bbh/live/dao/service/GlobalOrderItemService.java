package com.bbh.live.dao.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.bbh.live.dao.dto.GlobalOrderItemVO;
import com.bbh.live.dao.dto.OrderNoDTO;
import com.bbh.model.GlobalOrderItem;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【global_order_item(全局订单item（商品）)】的数据库操作Service
* @createDate 2024-07-25 11:52:55
*/
public interface GlobalOrderItemService extends IService<GlobalOrderItem> {

    List<GlobalOrderItem> listByGlobalOrderNo(String globalOrderNo);


    /**
     * 获取商家订单下第一个商品的图片
     * @param globalOrderSubId
     * @return
     */
    String getGoodsImageByGlobalOrderSubId(Long globalOrderSubId);

    Page<GlobalOrderItemVO> pageList(Page<GlobalOrderItem> page, Long orderId);
}
