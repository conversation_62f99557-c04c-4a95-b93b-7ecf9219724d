package com.bbh.live.dao.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 查询订单详情
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class QueryOrderCounterDTO {

    private Integer orderStatus;
    private Long buyerSeatId;
    private Long buyerOrgId;

    private Long sellerOrgId;

    private Integer bizType;
    private Date lastPayAtGt;
    private Date lastPayAtLt;
    private Date updateAtGt;

}
