package com.bbh.live.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bbh.live.dao.dto.LiveRoomEntryStatisticsDTO;
import com.bbh.model.LiveRoomEntryRecord;

/**
* <AUTHOR>
* @description 针对表【live_room_entry_record(直播间进出记录)】的数据库操作Mapper
* @createDate 2024-10-12 09:06:53
* @Entity com.bbh.live.dao.model.LiveRoomEntryRecord
*/
public interface LiveRoomEntryRecordMapper extends BaseMapper<LiveRoomEntryRecord> {

    /**
     * 统计直播间进出记录相关的数据
     * total_users: 不同用户的总数
     * total_duration: 所有用户的总在线时长
     * average_duration_per_user: 每个用户的平均在线时长
     * max_duration_per_user: 单个用户的最高总在线时长
     *
     * @param liveRoomId     直播间id
     * @return              直播间进出记录相关的数据
     */
    LiveRoomEntryStatisticsDTO statisticsEntry(Long liveRoomId);
}




