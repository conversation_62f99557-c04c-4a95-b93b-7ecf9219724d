package com.bbh.live.dao.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bbh.model.VipBuyerClickRecord;
import com.bbh.live.dao.mapper.VipBuyerClickRecordMapper;
import com.bbh.live.dao.service.VipBuyerClickRecordService;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【vip_buyer_click_record(买手会员点击日志)】的数据库操作Service实现
* @createDate 2024-08-14 17:51:46
*/
@Service
public class VipBuyerClickRecordServiceImpl extends ServiceImpl<VipBuyerClickRecordMapper, VipBuyerClickRecord>
    implements VipBuyerClickRecordService{

}




