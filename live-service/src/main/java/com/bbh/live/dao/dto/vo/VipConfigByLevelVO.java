package com.bbh.live.dao.dto.vo;

import com.bbh.model.VipBuyerConfig;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR> dsy
 * @Date: 2024/8/21
 * @Description:
 */
@Data
@Accessors(chain = true)
public class VipConfigByLevelVO {

    /**
     * 会员等级
     */
    private Integer level;

    /**
     * 普通会员配置
     */
    private VipBuyerConfig vipConfig;

    /**
     * 年费会员配置
     */
    private VipBuyerConfig annualVipConfig;
}
