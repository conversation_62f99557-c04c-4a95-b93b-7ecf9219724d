package com.bbh.live.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bbh.live.dao.dto.vo.LiveOrgFilterVO;
import com.bbh.model.LiveOrgFilter;

/**
* <AUTHOR>
* @description 针对表【live_org_filter(直播间黑白名单)】的数据库操作Mapper
* @createDate 2024-07-25 11:53:19
* @Entity com.bbh.model.LiveOrgFilter
*/
public interface LiveOrgFilterMapper extends BaseMapper<LiveOrgFilter> {

    //----------------商家操作用户-----------------
    IPage<LiveOrgFilterVO> searchUser(Page<LiveOrgFilterVO> page, String searchKey);

    IPage<LiveOrgFilterVO> pageListUser(Page<LiveOrgFilterVO> page, Long orgId, Integer sourceTypeCode, Integer filterMode);


    //----------------用户操作商家-----------------
    IPage<LiveOrgFilterVO> searchOrg(Page<LiveOrgFilterVO> page, Long userId, String searchKey);

    IPage<LiveOrgFilterVO> pageListOrg(Page<LiveOrgFilterVO> page, Long userId, Integer sourceTypeCode);
}




