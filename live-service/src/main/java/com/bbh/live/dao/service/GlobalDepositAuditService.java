package com.bbh.live.dao.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.bbh.model.GlobalDepositAudit;

/**
 * <AUTHOR>
 * @description 针对表【global_deposit_audit(保证金违规扣款记录表)】的数据库操作Service
 * @date 2024-10-17 10:37:25
 */
public interface GlobalDepositAuditService extends IService<GlobalDepositAudit> {

    /**
     * 是否有违规记录
     * @param orgId 商户ID
     * @return  true:有违规记录 false:无违规记录
     */
    boolean hasIllegalRecord(Long orgId);

    /**
     * 查询违规记录数量
     * @param orgId 商户ID
     * @return  违规记录数量
     */
    Long getIllegalCount(Long orgId);

}
