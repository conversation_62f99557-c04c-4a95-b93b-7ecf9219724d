package com.bbh.live.dao.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bbh.model.LiveApiLog;
import com.bbh.live.dao.mapper.LiveApiLogMapper;
import com.bbh.live.dao.service.LiveApiLogService;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【live_api_log(接口调用日志)】的数据库操作Service实现
* @createDate 2024-07-25 11:53:19
*/
@Service
public class LiveApiLogServiceImpl extends ServiceImpl<LiveApiLogMapper, LiveApiLog>
    implements LiveApiLogService{

}




