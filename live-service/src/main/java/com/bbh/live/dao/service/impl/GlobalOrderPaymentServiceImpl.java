package com.bbh.live.dao.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bbh.live.dao.mapper.GlobalOrderPaymentMapper;
import com.bbh.live.dao.service.GlobalOrderPaymentService;
import com.bbh.model.GlobalOrderPayment;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【global_order_payment(全局订单支付信息表)】的数据库操作Service实现
* @createDate 2024-09-02 16:18:29
*/
@Service
public class GlobalOrderPaymentServiceImpl extends ServiceImpl<GlobalOrderPaymentMapper, GlobalOrderPayment>
    implements GlobalOrderPaymentService{

}




