package com.bbh.live.dao.dto.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2024/9/27 16:17
 * @description
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
public class BuyerLiveGoodsStatisticsDTO {

    /**
     * 总数量
     */
    private Long totalCount;

    /**
     * 待竞拍商品数量
     */
    private Long waitAuctionCount;

    /**
     *  成交商品数量
     */
    private Long tradeCount;

    /**
     * 流拍商品数量
     */
    private Long abortiveAuctionCount;
}
