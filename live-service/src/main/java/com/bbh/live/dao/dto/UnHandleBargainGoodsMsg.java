package com.bbh.live.dao.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR> dsy
 * @Date: 2024/9/2
 * @Description:
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class UnHandleBargainGoodsMsg extends BaseSeatDTO {

    /**
     * 消息id
     */
    private Long msgId;

    /**
     * 直播商品id
     */
    private Long liveGoodsId;

    /**
     * 直播商品名称
     */
    private String goodsName;

    /**
     * 直播商品编码
     */
    private String goodsCode;

    /**
     * 直播商品图片列表
     */
    private List<String> imgUrlList;

    /**
     * 成色
     */
    private String quality;

    /**
     * 议价价格
     */
    private BigDecimal bargainPrice;

    /**
     * 同行价
     */
    private BigDecimal peerPrice;

    /**
     * 成本价
     */
    private BigDecimal costPrice;

    /**
     * 交易类型 0-竞拍 10-一口价
     */
    private Integer tradeType;

    /**
     * 起拍价
     */
    private BigDecimal startPrice;
}
