package com.bbh.live.dao.dto;

import lombok.Data;
/**
 * 直播间进出统计
 * <AUTHOR>
 */
@Data
public class LiveRoomEntryStatisticsDTO {

    /**
     * 累计人数
     */
    private Integer totalUserCount;

    /**
     * 所有用户的总在线时长
     */
    private Integer totalDuration;

    /**
     * 每个用户的平均在线时长
     */
    private Integer averageDurationPerUser;

    /**
     * 单个用户的最高总在线时长
     */
    private Integer maxDurationPerUser;


}
