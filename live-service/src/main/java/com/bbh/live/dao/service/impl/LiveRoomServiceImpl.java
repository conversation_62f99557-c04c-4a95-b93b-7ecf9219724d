package com.bbh.live.dao.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bbh.live.dao.dto.LiveRoomSaleStatisticsDTO;
import com.bbh.live.dao.mapper.LiveRoomMapper;
import com.bbh.live.dao.service.LiveRoomService;
import com.bbh.model.LiveRoom;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
* <AUTHOR>
*/
@Service
@AllArgsConstructor
@Slf4j
public class LiveRoomServiceImpl extends ServiceImpl<LiveRoomMapper, LiveRoom> implements LiveRoomService {

    @Override
    public List<LiveRoom> getInLiveRoomList(Long currentUserId, Long currentSeatId, Boolean filterBlacklist) {
        return this.getBaseMapper().getInLiveRoomList(new Date(), currentUserId, currentSeatId, filterBlacklist);
    }

    @Override
    public boolean ifLiveRoomTestBroadcast(Long liveRoomId) {
        LiveRoom liveRoom = getById(liveRoomId);
        if(liveRoom != null) {
            return ifLiveRoomTestBroadcast(liveRoom);
        }
        return false;
    }

    @Override
    public boolean ifLiveRoomTestBroadcast(Date startAt) {
        return new Date().before(startAt);
    }

    @Override
    public boolean ifLiveRoomTestBroadcast(LiveRoom liveRoom) {
        return ifLiveRoomTestBroadcast(liveRoom.getStartAt());
    }

    @Override
    public LiveRoomSaleStatisticsDTO getLiveRoomSaleStatisticsInfo(Long liveRoomId) {
        LiveRoomSaleStatisticsDTO liveRoomSaleStatisticsInfo = this.getBaseMapper().getLiveRoomSaleStatisticsInfo(liveRoomId);
        if(liveRoomSaleStatisticsInfo.getSoldAmount() == null){
            liveRoomSaleStatisticsInfo.setSoldAmount(BigDecimal.ZERO);
        }
        if(liveRoomSaleStatisticsInfo.getAbortiveAmount() == null){
            liveRoomSaleStatisticsInfo.setAbortiveAmount(BigDecimal.ZERO);
        }
        if(liveRoomSaleStatisticsInfo.getAccumulatePutawayAmount() == null){
            liveRoomSaleStatisticsInfo.setAccumulatePutawayAmount(BigDecimal.ZERO);
        }
        return liveRoomSaleStatisticsInfo;
    }
}




