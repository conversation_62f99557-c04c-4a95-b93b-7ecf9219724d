package com.bbh.live.dao.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bbh.model.VipBuyerPutAwayViewLog;
import com.bbh.live.dao.mapper.VipBuyerPutAwayViewLogMapper;
import com.bbh.live.dao.service.VipBuyerPutAwayViewLogService;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【vip_buyer_put_away_view_log(用户免费试用查看正在上架商品次数日志表)】的数据库操作Service实现
* @createDate 2024-08-14 17:51:46
*/
@Service
public class VipBuyerPutAwayViewLogServiceImpl extends ServiceImpl<VipBuyerPutAwayViewLogMapper, VipBuyerPutAwayViewLog>
    implements VipBuyerPutAwayViewLogService{

}




