package com.bbh.live.dao.dto.livegoods;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR> dsy
 * @Date: 2024/7/30
 * @Description:
 */
@Data
@Accessors(chain = true)
public class LiveGoodsSubscribeDTO {

    private Long liveGoodsId;

    private Long liveRoomId;

    private String remark;

    private Boolean ifHandled;

    /**
     * 商品图片
     */
    private List<String> imgUrlList;
}
