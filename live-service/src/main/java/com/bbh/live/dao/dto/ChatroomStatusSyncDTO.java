package com.bbh.live.dao.dto;

import com.bbh.vo.AuthUser;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Objects;

/**
 * 聊天室状态同步
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ChatroomStatusSyncDTO {

    @JsonProperty("chatRoomId")
    private String chatRoomId;

    @JsonProperty("userIds")
    private List<String> userIds;

    private Integer status;
    private Integer type;
    private Long time;

    /**
     * 从聊天室ID中解析出直播间ID
     * @return 直播间ID,如果解析失败返回null
     */
    public Long parseLiveRoomId() {
        try {
            // 从聊天室ID中取出直播间ID,如"htbLr244"取出"244"
            String id = this.chatRoomId.replace("htbLr", "");
            return Long.parseLong(id);
        } catch (NumberFormatException e) {
            // 解析失败时返回null
            return null;
        }
    }

    /**
     * 从userId字符串中解析出AuthUser对象
     * userId格式为"live_' . $orgId . '_buyer_' . $seat['id']",如"live_1877_buyer_3719"
     * @return 解析出的AuthUser对象,包含seatId和orgId
     */
    public List<AuthUser> parseUserInfo() {
        return userIds.stream().map(this::parseUserInfo).filter(Objects::nonNull).toList();
    }

    public AuthUser parseUserInfo(String userId) {
        try {
            // live_1877_buyer_3719
            // 先拆分下划线
            String[] split = userId.split("_");
            String orgId = split[1];
            String seatId = split[3];

            AuthUser authUser = new AuthUser();
            authUser.setOrgId(Long.parseLong(orgId));
            authUser.setSeatId(Long.parseLong(seatId));
            return authUser;
        } catch (NumberFormatException | ArrayIndexOutOfBoundsException e) {
            // 解析失败时返回null
            return null;
        }
    }

}
