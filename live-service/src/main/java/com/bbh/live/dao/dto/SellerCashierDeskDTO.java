package com.bbh.live.dao.dto;

import com.bbh.base.PageBase;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

/**
 * 卖家收银台订单
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SellerCashierDeskDTO extends PageBase {

    private String keywords;

    /**
     * 开始时间
     */
    private Date orderAtStart;

    private Date orderAtEnd;

    /** 场次id 多选 */
    private List<Long> liveRoomIdList;

    /**
     * 订单状态+收银台状态查询
     */
    private Integer orderStatus;

}
