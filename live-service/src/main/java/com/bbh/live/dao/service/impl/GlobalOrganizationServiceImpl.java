package com.bbh.live.dao.service.impl;

import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.RandomUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bbh.enums.FenbeiDetailEventEnum;
import com.bbh.enums.GlobalFenbeiDetailOperateSourceEnum;
import com.bbh.live.dao.mapper.GlobalOrganizationMapper;
import com.bbh.live.dao.service.GlobalFenbeiDetailService;
import com.bbh.live.dao.service.GlobalOrgSeatService;
import com.bbh.live.dao.service.IGlobalOrganizationService;
import com.bbh.live.feign.dto.GlobalOrgDetailDTO;
import com.bbh.live.service.buyer.orgmap.vo.OrgAddressInfoVO;
import com.bbh.model.GlobalFenbeiDetail;
import com.bbh.model.GlobalOrgSeat;
import com.bbh.model.GlobalOrganization;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigInteger;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 */
@Service
@AllArgsConstructor
public class GlobalOrganizationServiceImpl extends ServiceImpl<GlobalOrganizationMapper, GlobalOrganization> implements IGlobalOrganizationService {

    private final GlobalFenbeiDetailService globalFenbeiDetailService;
    private final GlobalOrgSeatService globalOrgSeatService;

    @Override
    public void updateOrgNameByTest() {
        List<GlobalOrganization> organizationList = this.baseMapper.getAnomalyNameOrg();
        if (CollectionUtils.isEmpty(organizationList)) {
            return;
        }
        for (GlobalOrganization organization : organizationList) {
            String orgName = getOnlyOrgName();
            LambdaUpdateWrapper<GlobalOrganization> luw = new LambdaUpdateWrapper<>();
            luw.eq(GlobalOrganization::getId, organization.getId());
            luw.set(GlobalOrganization::getName, orgName);
            this.baseMapper.update(luw);
        }
    }

    private String getOnlyOrgName() {
        String orgName = getRandomOrgName();
        if (checkSameOrgName(orgName)) {
            orgName = getOnlyOrgName();
        }
        return orgName;
    }

    private String getRandomOrgName() {
        return "商家-" + RandomUtil.randomString("A0B1C2D3E4F5G6H7I8J1K2L3M4N5O6P7Q8R9S1T2U3V4W5X6Y7Z8", 8);
    }

    private boolean checkSameOrgName(String orgName) {
        LambdaQueryWrapper<GlobalOrganization> lqw = new LambdaQueryWrapper<>();
        lqw.like(GlobalOrganization::getName, orgName).isNull(GlobalOrganization::getDeletedAt);
        GlobalOrganization organization = this.baseMapper.selectOne(lqw);
        return organization != null;
    }

    @Override
    public Map<Long, Boolean> ifOrgHasNewGoods(Set<Long> orgIds) {
        Map<BigInteger, Map<String, Integer>> selectMap = this.baseMapper.ifOrgHasNewGoods(orgIds);

        Map<Long, Boolean> result = new HashMap<>(orgIds.size());
        selectMap.forEach((orgId, goodsMap) -> result.put(orgId.longValue(), BooleanUtil.toBooleanObject(goodsMap.get("hasNewGoods").toString())));
        return result;
    }

    @Override
    public GlobalOrgDetailDTO getOrgDetailById(Long orgId) {
        return getBaseMapper().getOrgDetailById(orgId);
    }

    @Override
    public void presentedFenbei(GlobalOrgSeat presenter, GlobalOrgSeat receiver, Integer presentedFenbeiNum, Integer receviedFenbeiNum) {

        // regin 赠送分贝
        // 修改分贝数量
        this.lambdaUpdate().eq(GlobalOrganization::getId, presenter.getOrgId())
                .setDecrBy(GlobalOrganization::getFenbei, presentedFenbeiNum)
                .update();

        // 分贝明细记录
        GlobalFenbeiDetail globalFenbeiDetail = new GlobalFenbeiDetail();
        globalFenbeiDetail.setAccountBalance(getOrgFenbeiNumber(presenter.getOrgId()));
        globalFenbeiDetail.setChangeNumber(presentedFenbeiNum);
        //分贝赠送
        FenbeiDetailEventEnum presentEvent = FenbeiDetailEventEnum.PRESENTED_FENBEI;
        globalFenbeiDetail.setType(presentEvent.getType());
        globalFenbeiDetail.setEvent(presentEvent.getEvent());
        globalFenbeiDetail.setEventDescribe(presentEvent.getEventDescribe(receiver.getAuctionCode() + "-" + receiver.getShowName(), presentedFenbeiNum));
        globalFenbeiDetail.setOperateSource(GlobalFenbeiDetailOperateSourceEnum.APP_BBH);
        globalFenbeiDetail.setCreateSeatId(presenter.getId());
        globalFenbeiDetailService.save(globalFenbeiDetail);

        // 接受分贝
        this.lambdaUpdate().eq(GlobalOrganization::getId, receiver.getOrgId())
                .setIncrBy(GlobalOrganization::getFenbei, receviedFenbeiNum)
                .update();

        globalFenbeiDetail = new GlobalFenbeiDetail();
        globalFenbeiDetail.setAccountBalance(getOrgFenbeiNumber(receiver.getOrgId()));
        globalFenbeiDetail.setChangeNumber(receviedFenbeiNum);

        //分贝接收
        FenbeiDetailEventEnum receivedEvent = FenbeiDetailEventEnum.RECEIVED_FENBEI;
        globalFenbeiDetail.setType(receivedEvent.getType());
        globalFenbeiDetail.setEvent(receivedEvent.getEvent());
        globalFenbeiDetail.setEventDescribe(receivedEvent.getEventDescribe(presenter.getAuctionCode() + "-" + presenter.getShowName(), receviedFenbeiNum));
        globalFenbeiDetail.setOperateSource(GlobalFenbeiDetailOperateSourceEnum.APP_BBH);
        globalFenbeiDetail.setCreateSeatId(receiver.getId());
        globalFenbeiDetail.setOrgId(receiver.getOrgId());
        globalFenbeiDetailService.save(globalFenbeiDetail);
    }

    @Override
    public Integer getOrgFenbeiNumber(Long orgId) {
        return this.lambdaQuery().eq(GlobalOrganization::getId, orgId).select(GlobalOrganization::getFenbei)
                .oneOpt().map(GlobalOrganization::getFenbei).orElse(0);
    }

    @Override
    public List<OrgAddressInfoVO> getOrgAddressInfoByIdList(Set<Long> orgIdSet) {
        return getBaseMapper().getOrgAddressInfoByIdList(orgIdSet);
    }

    @Override
    public Long getMasterSeatIdByOrgId(Long orgId) {
        List<Long> masterSeatIds = globalOrgSeatService.lambdaQuery()
                .eq(GlobalOrgSeat::getOrgId, orgId)
                .eq(GlobalOrgSeat::getIfMaster, true)
                .eq(GlobalOrgSeat::getStatus, 1)
                .list()
                .stream()
                .map(GlobalOrgSeat::getId)
                .toList();
        return masterSeatIds.isEmpty() ? null : masterSeatIds.getFirst();
    }

    @Override
    public List<GlobalOrganization> getMapOrg(Set<Long> ids) {
        return this.getBaseMapper().getMapOrg(ids);
    }
}




