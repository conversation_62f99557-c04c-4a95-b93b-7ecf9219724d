package com.bbh.live.dao.dto.vo;

import com.bbh.live.dao.dto.livegoods.LiveGoodsSubscribeDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 看播的直播间详情
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class WatcherRoomDetailVO extends DirectorRoomDetailVO {

    /**
     * 直播间内，当前用户预约的所有商品记录
     */
    private List<LiveGoodsSubscribeDTO> subscribeGoodsList;

    /**
     * 当前用户是否在该直播间出过价
     */
    private Boolean ifBidden;

    /**
     * 当前用户是否有查看测试直播间的权限
     */
    private Boolean hasTestBroadcastPermission;

    /**
     * 观看权限：处理员工进直播间、被拉黑、主动拉黑、不在白名单内的情况
     */
    private WatcherPermission watcherPermission;

    /**
     * 发言及出价权限
     */
    private AuctionBidVO speakPermission;

    @Data
    @Accessors(chain = true)
    @AllArgsConstructor
    public static class WatcherPermission {
        private Boolean hasPermission;
        private String title;
        private String message;
    }
}
