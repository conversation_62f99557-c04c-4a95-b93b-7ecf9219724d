package com.bbh.live.dao.dto;

import com.bbh.enums.LiveRoomStreamStatusEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 直播购物车，按直播场次分组
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class LiveShoppingCartGroupVO {

    private Long orgId;

    private String orgName;

    /** 直播间ID */
    private Long liveRoomId;

    /** 商家logo URL */
    private String logoUrl;

    /** 直播间名称 */
    private String roomName;

    /** 是否专场 */
    private Boolean ifSpecial;

    /** 直播状态 */
    private LiveRoomStreamStatusEnum streamStatus;

    /** 商品列表 */
    private List<LiveShoppingCartDTO> goodsList;

    /**.直播间开始时间 */
    private Date startAt;

    /** 合计商品数量 */
    private Integer totalGoodsCount;

    /** 合计金额 */
    private BigDecimal totalGoodsPrice;

}
