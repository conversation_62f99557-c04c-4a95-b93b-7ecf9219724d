package com.bbh.live.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bbh.live.dao.dto.GlobalOrderGoodsSnapshotDTO;
import com.bbh.model.GlobalOrderGoodsSnapshot;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
* <AUTHOR>
* @description 针对表【global_order_goods_snapshot(订单商品快照)】的数据库操作Mapper
* @createDate 2024-09-02 18:50:46
* @Entity com.bbh.live.dao.GlobalOrderGoodsSnapshot
*/
public interface GlobalOrderGoodsSnapshotMapper extends BaseMapper<GlobalOrderGoodsSnapshot> {

    /**
     * 获取商品快照
     * @param erpGoodsIdSet
     * @return
     */
    List<GlobalOrderGoodsSnapshotDTO> selectSnapshotGoodsInfo(@Param("erpGoodsIdSet") Set<Long> erpGoodsIdSet);
}




