package com.bbh.live.dao.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.bbh.live.controller.req.VirtualGoodsOrderQueryReq;
import com.bbh.live.dao.dto.vo.VirtualGoodsOrderVO;
import com.bbh.live.enums.GlobalVirtualGoodsOrderCancelSource;
import com.bbh.model.GlobalVirtualGoodsOrder;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【global_virtual_goods_order(虚拟商品订单)】的数据库操作Service
* @createDate 2024-08-20 09:18:39
*/
public interface GlobalVirtualGoodsOrderService extends IService<GlobalVirtualGoodsOrder> {


    /**
     * 取消订单
     * @param orderId
     * @param cancelSource
     * @return true 取消成功 false 取消失败
     */
    boolean cancelOrder(Long orderId, GlobalVirtualGoodsOrderCancelSource cancelSource);


    /**
     * 查询虚拟商品订单列表
     * @param queryReq
     * @return
     */
    List<VirtualGoodsOrderVO> getVirtualGoodsOrderList(VirtualGoodsOrderQueryReq queryReq);
}
