package com.bbh.live.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bbh.live.controller.req.LiveGoodsQueryReq;
import com.bbh.live.controller.req.PlaybackGoodsQueryReq;
import com.bbh.live.dao.dto.AbortiveAuctionGoodsDTO;
import com.bbh.live.dao.dto.CheckedLiveGoodsDTO;
import com.bbh.live.dao.dto.LiveShoppingCartDTO;
import com.bbh.live.dao.dto.QueryShoppingCartDTO;
import com.bbh.live.dao.dto.livegoods.LiveGoodsDTO;
import com.bbh.live.dao.dto.livegoods.LiveGoodsSaleDTO;
import com.bbh.live.dao.dto.livegoods.LiveGoodsSortDTO;
import com.bbh.live.dao.dto.vo.AuctionLiveGoodsVO;
import com.bbh.live.dao.dto.vo.LiveRoomPlaybackGoodsVO;
import com.bbh.live.service.msg.dto.base.BaseGoods;
import com.bbh.model.LiveGoods;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
* <AUTHOR>
* @description 针对表【live_goods(直播间商品)】的数据库操作Mapper
* @createDate 2024-07-25 11:53:19
* @Entity com.bbh.model.LiveGoods
*/
public interface LiveGoodsMapper extends BaseMapper<LiveGoods> {

    /**
     * 查询直播商品清单
     * @param page
     * @param queryParam
     * @return
     */
    IPage<LiveGoodsDTO> getLiveGoodsList(@Param("page") IPage<LiveGoodsDTO> page, @Param("queryParam") LiveGoodsQueryReq queryParam);

    /**
     * 获取待完善商品列表
     * @param page
     * @param queryParam
     * @return
     */
    IPage<LiveGoodsDTO> getWaitCompleteLiveGoodsList(@Param("page") IPage<LiveGoodsDTO> page, @Param("queryParam") LiveGoodsQueryReq queryParam);

    /**
     * 根据商品id查询详情
     * @param liveGoodsId
     * @param globalGoodsId
     * @return
     */
    LiveGoodsDTO getLiveGoodsDetailInfo(@Param("liveGoodsId") Long liveGoodsId, @Param("globalGoodsId") Long globalGoodsId);

    LiveGoodsDTO getLiveGoodsDetailInfoByGlobalGoodsId(@Param("globalGoodsId") Long globalGoodsId);

    /**
     * 批量更新商品排序
     * @param sortDTOList
     */
    void batchUpdateLiveGoodsSort(@Param("sortList") List<LiveGoodsSortDTO> sortDTOList);


    /**
     * 获取指定直播间流拍商品id集合
     * @param liveRoomIdList
     * @param fetchSize
     * @return
     */
    List<AbortiveAuctionGoodsDTO> getAuctionFailLiveGoodsListByLiveRoomId(@Param("liveRoomIdList") Collection<Long> liveRoomIdList, @Param("size") Integer fetchSize);

    /**
     * 统计不同状态的商品数量
     * @param queryReq
     */
    @MapKey("goodsStatus")
    Map<Integer, Map<String, Long>> getLiveGoodsCountGroupByStatus(@Param("queryParam") LiveGoodsQueryReq queryReq);


    /**
     * 统计不同状态的商品数量
     * @param queryReq
     */
    Long getLiveGoodsTradeCount(@Param("queryParam") LiveGoodsQueryReq queryReq);


    /**
     * 商品预约数量
     * @param liveGoodsId
     * @param delta
     */
    void incrGoodsSubscribeCount(@Param("liveGoodsId") Long liveGoodsId, @Param("delta") int delta);

    /**
     * 获取直播间商品
     * @param liveRoomId
     * @return
     */
    List<CheckedLiveGoodsDTO> getCheckedLiveGoodsList(@Param("liveRoomId") Long liveRoomId);


    /**
     * 直播直播间竞拍中的商品列表
     * @param liveRoomIds 直播间id集合
     * @param status 商品状态 {@link com.bbh.enums.LiveGoodsStatusEnum}
     * @return
     */
    List<AuctionLiveGoodsVO> getLiveGoodsListByLiveRoomAndStatus(@Param("liveRoomIdList") List<Long> liveRoomIds, @Param("status") Integer status);


    /**
     * 获取直播间回放商品列表
     * @param queryReq
     * @param buyerSeatId
     * @param buyerOrgId
     * @return
     */
    List<LiveRoomPlaybackGoodsVO> getPlaybackLiveGoodsList(@Param("queryParam") PlaybackGoodsQueryReq queryReq, @Param("buyerSeatId") Long buyerSeatId, @Param("buyerOrgId") Long buyerOrgId);

    /**
     * 直播前统计信息，待完善和全部
     * @param queryReq
     * @return
     */
    List<Long> getLiveGoodsCountBeforeLiveStart(@Param("queryParam") LiveGoodsQueryReq queryReq);

    /**
     * 直播前统计信息，已售出商品信息
     * @param liveRoomId
     * @return
     */
    List<LiveGoodsSaleDTO> getSoldGoodsListByLiveRoom(@Param("liveRoomId") Long liveRoomId);

    /**
     * 获取直播商品简单详情
     * @param liveGoodsId
     * @return
     */
    BaseGoods getBaseGoodsInfo(@Param("liveGoodsId") Long liveGoodsId);

    /**
     * 获取直播间有效的商品流拍数量
     * @param liveRoomIdList
     * @return
     */
    @MapKey("liveRoomId")
    Map<Long, Map<Long, Long>> getEffectiveAuctionFailLiveGoodsCount(@Param("liveRoomIdList") Collection<Long> liveRoomIdList);

    /**
     * 收银台
     */
    List<LiveShoppingCartDTO> selectLiveShoppingCartList(@Param("query") QueryShoppingCartDTO query);

    Page<LiveShoppingCartDTO> selectSellerLiveShoppingCartList(@Param("page") IPage<LiveGoodsDTO> page, @Param("query") QueryShoppingCartDTO query);

    List<LiveGoodsDTO> getGodViewGoodsInfo();

    List<LiveGoods> getLiveGoodsListForSyncEndLiveRoomToSce(@Param("liveRoomIds")  List<Long> liveRoomIds);

    List<Long> getNeedSyncLiveRoomIdsToSce();

    /**
     * 查询直播商品清单
     * @param page
     * @param queryParam
     * @return
     */
    IPage<LiveGoodsDTO> getLiveGoodsToSceGoods(@Param("page") IPage<LiveGoodsDTO> page, @Param("queryParam") LiveGoodsQueryReq queryParam);

}




