package com.bbh.live.dao.dto.vo;

import com.bbh.live.dao.dto.livegoods.LiveGoodsSaleDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> dsy
 * @Date: 2024/9/12
 * @Description:
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
public class LiveRoomSaleInfo {

    /**
     * 销售金额
     */
    private BigDecimal soldAmount;

    /**
     * 直播间名称
     */
    private String roomName;

    /**
     * 开播时间
     */
    private Date startAt;

    /**
     * 备货数量
     */
    private Integer goodsCount;

    /**
     * 销售数量
     */
    private Integer soldCount;

    /**
     *  售出商品详情
     */
    private List<LiveGoodsSaleDTO> saleGoodsList;

}
