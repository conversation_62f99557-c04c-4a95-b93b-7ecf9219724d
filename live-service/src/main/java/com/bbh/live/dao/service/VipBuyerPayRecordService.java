package com.bbh.live.dao.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.bbh.base.ListBase;
import com.bbh.live.dao.dto.vo.VirtualGoodsOrderVO;
import com.bbh.model.GlobalVirtualGoodsOrder;
import com.bbh.model.VipBuyerCard;
import com.bbh.model.VipBuyerPayRecord;

import java.util.Date;

/**
* <AUTHOR>
* @description 针对表【vip_buyer_pay_record(买家会员充值记录)】的数据库操作Service
* @createDate 2024-08-14 17:51:46
*/
public interface VipBuyerPayRecordService extends IService<VipBuyerPayRecord> {

    /**
     * 记录VIP开通日志
     * @param order
     * @param startDate
     * @param vipBuyerCard
     */
    Long insertVipBuyerPayRecord(GlobalVirtualGoodsOrder order, Date startDate, VipBuyerCard vipBuyerCard, String userName);

    /**
     * 获取VIP已使用天数
     * @param vipId
     * @return
     */
    Long getVipUsedDays(Long vipId);

    ListBase<VirtualGoodsOrderVO> getVipBuyerPayRecordList(int currentPage, int perPage, Long seatId);
}
