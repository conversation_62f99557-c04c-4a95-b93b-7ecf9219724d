package com.bbh.live.dao.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.bbh.live.enums.BuyerVipEventEnum;
import com.bbh.live.service.buyer.vip.vo.UserBuyerVipInfoVO;
import com.bbh.model.VipBuyerExpLog;

/**
* <AUTHOR>
* @description 针对表【vip_buyer_exp_log(会员经验变化记录)】的数据库操作Service
* @createDate 2024-08-14 17:51:46
*/
public interface VipBuyerExpLogService extends IService<VipBuyerExpLog> {

    /**
     * 记录会员经验变更日志
     * @param vipInfo 用户信息
     * @param exp 经验
     * @param vipEventEnum 会员事件
     */
    void insertVipExpChangeLog(UserBuyerVipInfoVO vipInfo, Integer exp, BuyerVipEventEnum vipEventEnum);
}
