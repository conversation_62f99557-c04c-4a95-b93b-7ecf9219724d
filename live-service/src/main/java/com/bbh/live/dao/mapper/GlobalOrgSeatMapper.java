package com.bbh.live.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.bbh.live.dao.dto.QuerySimpleUserDTO;
import com.bbh.live.dao.dto.SimpleUserInfoDTO;
import com.bbh.model.GlobalOrgSeat;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【global_org_seat(主体席位)】的数据库操作Mapper
* @createDate 2024-06-27 16:30:48
* @Entity generator.domain.GlobalOrgSeat
*/
public interface GlobalOrgSeatMapper extends BaseMapper<GlobalOrgSeat> {

    IPage<SimpleUserInfoDTO> selectPageWithVip(IPage<SimpleUserInfoDTO> page, QuerySimpleUserDTO query);

    /**
     * 获取拥有对应权限的席位 id
     * @param roleList
     * @return
     */
    List<Long> getSeatIdListByOrgRoleList(List<Long> roleList);
}




