package com.bbh.live.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bbh.live.dao.dto.vo.WeeklySpuGoodsVO;
import com.bbh.model.VipWeeklySpuGoods;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【vip_weekly_spu_goods(周报spu展示成色)】的数据库操作Mapper
* @createDate 2024-08-21 08:29:02
* @Entity com.bbh.live.dao.VipWeeklySpuGoods
*/
public interface VipWeeklySpuGoodsMapper extends BaseMapper<VipWeeklySpuGoods> {

    List<WeeklySpuGoodsVO> selectAllByWeeklySpuId(Long weeklySpuId);
}




