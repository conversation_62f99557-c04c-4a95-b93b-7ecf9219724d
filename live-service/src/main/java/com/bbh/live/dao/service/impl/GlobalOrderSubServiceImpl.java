package com.bbh.live.dao.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bbh.model.GlobalOrderSub;
import com.bbh.live.dao.mapper.GlobalOrderSubMapper;
import com.bbh.live.dao.service.GlobalOrderSubService;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【global_order_sub(全局子订单)】的数据库操作Service实现
* @createDate 2024-07-25 11:52:55
*/
@Service
public class GlobalOrderSubServiceImpl extends ServiceImpl<GlobalOrderSubMapper, GlobalOrderSub>
    implements GlobalOrderSubService{

}




