package com.bbh.live.dao.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bbh.model.VipBuyerConfig;
import com.bbh.live.dao.mapper.VipBuyerConfigMapper;
import com.bbh.live.dao.service.VipBuyerConfigService;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【vip_buyer_config(会员等级配置)】的数据库操作Service实现
* @createDate 2024-08-14 17:51:46
*/
@Service
public class VipBuyerConfigServiceImpl extends ServiceImpl<VipBuyerConfigMapper, VipBuyerConfig> implements VipBuyerConfigService{

    /**
     * 获取V0的配置
     */
    @Override
    public VipBuyerConfig getCommonV0Config() {
        return getOne(Wrappers.lambdaQuery(VipBuyerConfig.class)
                .eq(VipBuyerConfig::getNeedExp, 0)
                .eq(VipBuyerConfig::getIfYearly, true)
                .orderByAsc(VipBuyerConfig::getNeedExp)
                .last("limit 1")
        );
    }
}




