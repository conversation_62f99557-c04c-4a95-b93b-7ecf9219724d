package com.bbh.live.dao.service.impl;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bbh.live.dao.mapper.LiveRoomRecordMapper;
import com.bbh.live.dao.service.LiveRoomRecordService;
import com.bbh.model.LiveRoomRecord;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
* <AUTHOR>
* @description 针对表【live_room_record(直播间开关播记录)】的数据库操作Service实现
* @createDate 2024-07-25 11:53:19
*/
@Service
public class LiveRoomRecordServiceImpl extends ServiceImpl<LiveRoomRecordMapper, LiveRoomRecord>
    implements LiveRoomRecordService{


    @Override
    public void addLiveRoomStartRecord(Long roomId, Date now) {
        // 先把原来的记录更新掉，不需要检查，避免直播流回调异常导致无法开播
        updateLiveRoomStopRecord(roomId, now);

        // 再去创建新的开播记录
        LiveRoomRecord liveRoomRecord = new LiveRoomRecord();
        liveRoomRecord.setLiveRoomId(roomId);
        liveRoomRecord.setStartAt(now);
        this.save(liveRoomRecord);
    }

    @Override
    public void updateLiveRoomStopRecord(Long roomId, Date now) {
        // 更新关播记录，不需要检查
        LambdaUpdateWrapper<LiveRoomRecord> recordUpdateWrapper = new LambdaUpdateWrapper<>();
        recordUpdateWrapper
                .eq(LiveRoomRecord::getLiveRoomId, roomId)
                .isNull(LiveRoomRecord::getEndAt)
                .set(LiveRoomRecord::getEndAt, now);
        this.update(recordUpdateWrapper);
    }
}




