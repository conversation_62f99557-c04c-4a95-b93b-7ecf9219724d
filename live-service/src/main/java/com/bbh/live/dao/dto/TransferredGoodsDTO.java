package com.bbh.live.dao.dto;

import com.bbh.live.dao.dto.livegoods.LiveGoodsDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 传送的商品信息
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class TransferredGoodsDTO extends LiveGoodsDTO {

    private Long transferId;

    private Long targetUserId;

    private Long targetSeatId;

    private Long targetOrgId;

    private Date transferCreatedAt;

    private Boolean ifHandled;

    private Date expireAt;

}
