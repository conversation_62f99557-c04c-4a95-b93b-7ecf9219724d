package com.bbh.live.dao.service.impl;


import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bbh.live.dao.dto.OffhandPutAwayDTO;
import com.bbh.live.dao.mapper.ErpGoodsMapper;
import com.bbh.live.dao.service.ErpGoodsService;
import com.bbh.live.dao.service.IErpGoodsLogService;
import com.bbh.model.ErpGoods;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.concurrent.ThreadLocalRandom;

/**
 * <AUTHOR>
 * @description 针对表【erp_goods(erp货品表)】的数据库操作Service实现
 * @createDate 2024-06-26 14:19:58
 */
@Service
@Slf4j
@AllArgsConstructor
public class ErpGoodsServiceImpl extends ServiceImpl<ErpGoodsMapper, ErpGoods>
        implements ErpGoodsService {

    private IErpGoodsLogService erpGoodsLogService;

    @Override
    public List<ErpGoods> checkErpGoodsExists(List<Long> globalGoodsIdList) {
        Set<Long> globalGoodsIdSet = new HashSet<>(globalGoodsIdList);
        return this.lambdaQuery().in(ErpGoods::getId, globalGoodsIdSet)
                .eq(ErpGoods::getIfLocked, 0)
                .eq(ErpGoods::getPlaceOrderStatus, 0)
                .eq(ErpGoods::getSaleStatus, 1)
                .select(ErpGoods::getId, ErpGoods::getName)
                .list();
    }

    @Override
    public ErpGoods createErpGoodsByOffhand(OffhandPutAwayDTO offhandPutAwayDTO) {
        ErpGoods erpGoods = new ErpGoods();
        erpGoods.setName(offhandPutAwayDTO.getGoodsName());
        erpGoods.setCode(generateGoodsCode());
        erpGoods.setDescription(offhandPutAwayDTO.getGoodsName());
        erpGoods.setClassifyId(offhandPutAwayDTO.getClassifyId());
        erpGoods.setImgUrlList(offhandPutAwayDTO.getImgUrlList());
        erpGoods.setQuality(offhandPutAwayDTO.getQuality());
        erpGoods.setStorehouseId(offhandPutAwayDTO.getStorehouseId());
        erpGoods.setUpdatedAt(new Date());
        // 自有货品
        erpGoods.setType(1);

        this.save(erpGoods);

        //记录erp_goods_log
        try{
            erpGoodsLogService.erpGoodsInsertLog(erpGoods);
        }catch (Exception e){
            log.error("记录erp_goods_log失败: {}", e.getMessage(), e);
        }

        return erpGoods;
    }

    private String generateGoodsCode(){
        String code;
        do{
            code = DateUtil.format(new Date(), DatePattern.PURE_DATE_PATTERN) + ThreadLocalRandom.current().nextInt(100000, 1000000);
        }while(this.lambdaQuery().eq(ErpGoods::getCode, code).count() > 0);
        return code;
    }

}




