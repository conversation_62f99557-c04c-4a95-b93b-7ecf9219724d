package com.bbh.live.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bbh.live.dao.dto.UnHandleBargainGoodsMsg;
import com.bbh.model.LiveRoomInteractiveMessage;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【live_room_interactive_message(直播间消息)】的数据库操作Mapper
* @createDate 2024-07-25 11:53:19
* @Entity com.bbh.model.LiveRoomInteractiveMessage
*/
public interface LiveRoomInteractiveMessageMapper extends BaseMapper<LiveRoomInteractiveMessage> {

    /**
     * 直播间所有待处理的议价消息
     * @param roomId
     * @return
     */
    List<UnHandleBargainGoodsMsg> getUnhandledMessageList(@Param("roomId") Long roomId);

    /**
     * 获取待处理的消息数量
     * @param roomId
     * @return
     */
    Long getUnhandledMessageCount(@Param("roomId") Long roomId);
}




