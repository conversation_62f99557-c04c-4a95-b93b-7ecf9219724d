package com.bbh.live.dao.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/27 14:02
 */
@Data
@TableName(autoResultMap = true)
public class GlobalOrderItemVO {
    private BigDecimal vipDeductionAmount;
    private String quality;
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<String> imgUrlList;
    private String name;
    private String realPayedAmount;
}
