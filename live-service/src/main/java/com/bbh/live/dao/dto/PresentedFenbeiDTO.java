package com.bbh.live.dao.dto;

import com.bbh.secure.AuthUtil;
import com.bbh.util.AssertUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/9/24 09:58
 * @description
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PresentedFenbeiDTO {

    /**
     * 赠送人席位id
     */
    private Long presentedSeatId;

    /**
     * 赠送人店铺id
     */
    private Long presentedOrgId;

    /**
     * 接收人席位id
     */
    private Long receiverSeatId;

    /**
     * 接收人店铺id
     */
    private Long receiverOrgId;

    /**
     * 赠送的分贝数量
     */
    private Integer presentedFenbeiNum;

    public void check() {
        AssertUtil.assertNotNull(receiverSeatId, "请指定接收人");
        AssertUtil.assertTrue(presentedSeatId.equals(AuthUtil.getSeatId()), "赠送人必须是自己");
        AssertUtil.assertFalse(receiverSeatId.equals(AuthUtil.getSeatId()), "接收人不能是自己");
        AssertUtil.assertFalse(presentedOrgId.equals(receiverOrgId), "同店铺下无法赠送");
        AssertUtil.assertTrue(presentedFenbeiNum != null && presentedFenbeiNum > 0, "赠送的分贝数量必须大于0");
    }
}
