package com.bbh.live.dao.dto.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR> dsy
 * @Date: 2024/8/27
 * @Description:
 */
@Data
public class PlayBackLiveRoomVO {
    /**
     * 直播间id
     */
    private Long roomId;
    /**
     * 直播间名称
     */
    private String roomName;
    /**
     * 直播间封面
     */
    private String roomCoverUrl;
    /**
     * 是否是专场直播
     */
    private Boolean ifSpecial;
    /**
     * 直播间商家id
     */
    private Long orgId;
    /**
     * 商家名称
     */
    private String orgName;
    /**
     * 直播间开播时间，也即回放列表上的回放开始日期
     */
    @JsonFormat(pattern = "MM-dd", timezone = "Asia/Shanghai")
    private Date startAt;
}
