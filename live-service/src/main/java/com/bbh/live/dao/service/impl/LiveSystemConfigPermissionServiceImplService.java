package com.bbh.live.dao.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bbh.live.dao.mapper.LiveSystemConfigPermissionMapper;
import com.bbh.live.dao.service.LiveSystemConfigPermissionService;
import com.bbh.model.LiveSystemConfigPermission;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【live_system_config_permission(直播权限表)】的数据库操作Service实现
* @createDate 2024-09-20 15:40:31
*/
@Service
@AllArgsConstructor
public class LiveSystemConfigPermissionServiceImplService extends ServiceImpl<LiveSystemConfigPermissionMapper, LiveSystemConfigPermission>
    implements LiveSystemConfigPermissionService {

}




