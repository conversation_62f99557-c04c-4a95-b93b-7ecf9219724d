package com.bbh.live.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bbh.live.service.buyerCancel.dto.LiveGoodsBuyerCancelRecordDetailDTO;
import com.bbh.live.service.buyerCancel.dto.param.LiveGoodsBuyerCancelRecordQueryParam;
import com.bbh.model.LiveGoodsBuyerCancelRecord;
import org.apache.ibatis.annotations.Param;

/**
 *
 * <AUTHOR>
 */
public interface LiveGoodsBuyerCancelRecordMapper extends BaseMapper<LiveGoodsBuyerCancelRecord> {

    /**
     * 分页查询取消订单详情
     *
     * @param page 分页参数
     * @param query 查询条件
     * @return 分页结果
     */
    Page<LiveGoodsBuyerCancelRecordDetailDTO> selectRecordDetailPage(
            Page<?> page,
            @Param("query") LiveGoodsBuyerCancelRecordQueryParam query
    );

    Page<LiveGoodsBuyerCancelRecordDetailDTO> sceRecordDetailPage(Page<?> of, @Param("query") LiveGoodsBuyerCancelRecordQueryParam query);
}
