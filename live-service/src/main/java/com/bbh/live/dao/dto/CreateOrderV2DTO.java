package com.bbh.live.dao.dto;

import java.util.List;

import com.bbh.enums.GlobalPayTypeEnum;

import lombok.Data;

/**
 * 生成订单V2
 *
 * <AUTHOR>
 */
@Data
public class CreateOrderV2DTO {

    /**
     * 支付方式：1微信 ,2支付宝，3线下
     */
    private GlobalPayTypeEnum payType;

    /**
     * 收货地址
     */
    private Long receiveAddressId;

    /**
     * 收货地址补充信息 收货地址信息
     */
    private String receiveAddressInfo;

    /**
     * 是否使用分贝抵扣，不传则为不使用
     */
    private Boolean ifUseFenbei = Boolean.FALSE;

    /**
     * 商品ID列表
     */
    private List<Long> liveGoodsIdList;

    /**
     * 支付宝code或者微信code，和支付类型对应
     */
    private String code;

    /**
     * 是否需要质检，1需要，0不需要
     */
    private Integer ifQualityInspect = 0;

    private Long vipDeductionId = 0L;

}
