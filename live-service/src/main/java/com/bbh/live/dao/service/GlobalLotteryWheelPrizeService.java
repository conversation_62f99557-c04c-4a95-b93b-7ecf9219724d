package com.bbh.live.dao.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.bbh.model.GlobalLotteryWheelPrize;

/**
* <AUTHOR>
* @description 针对表【global_lottery_wheel_prize(抽奖转盘与奖品关联表)】的数据库操作Service
* @createDate 2024-10-08 16:01:43
*/
public interface GlobalLotteryWheelPrizeService extends IService<GlobalLotteryWheelPrize> {

    /**
     * 递减库存
     * @param wheelId   转盘ID
     * @param prizeId   奖品ID
     * @param delta     递减数量
     */
    void decreaseStock(Long wheelId, Long prizeId, Integer delta);

}
