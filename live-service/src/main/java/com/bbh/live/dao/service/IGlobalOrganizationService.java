package com.bbh.live.dao.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.bbh.live.feign.dto.GlobalOrgDetailDTO;
import com.bbh.live.service.buyer.orgmap.vo.OrgAddressInfoVO;
import com.bbh.model.GlobalOrgSeat;
import com.bbh.model.GlobalOrganization;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
* <AUTHOR>
* @description 针对表【global_organization(组织主体表)】的数据库操作Service
* @createDate 2024-06-21 10:20:49
*/
public interface IGlobalOrganizationService extends IService<GlobalOrganization> {

    void updateOrgNameByTest();

    /**
     * 判断商家是否有新商品
     * @param orgIds
     * @return
     */
    Map<Long, Boolean> ifOrgHasNewGoods(Set<Long> orgIds);

    /**
     * 获取店铺详情
     * @param orgId
     * @return
     */
    GlobalOrgDetailDTO getOrgDetailById(Long orgId);

    /**
     * 赠送分贝
     * @param presenter 赠送人
     * @param receiver  接收人
     * @param presentedFenbeiNum 赠送分贝数量
     * @param receviedFenbeiNum 接收到的分贝数量
     */
    void presentedFenbei(GlobalOrgSeat presenter, GlobalOrgSeat receiver, Integer presentedFenbeiNum, Integer receviedFenbeiNum);

    /**
     * 获取店铺当前分贝数量
     * @param orgId
     */
    Integer getOrgFenbeiNumber(Long orgId);

    /**
     * 批量获取店铺地址信息
     * @param longs
     * @return
     */
    List<OrgAddressInfoVO> getOrgAddressInfoByIdList(Set<Long> longs);

    /**
     * 获取店铺的老板席位id
     * @param orgId
     * @return
     */
    Long getMasterSeatIdByOrgId(Long orgId);

    List<GlobalOrganization> getMapOrg(Set<Long> ids);
}
