package com.bbh.live.util;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.bbh.util.LogExUtil;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.json.JsonMapper;

/**
 * JSON 工具，对HuTool的JSONUtil的补充
 * <AUTHOR>
 */
public class JSONUtils {

    /**
     * 将对象转换为下划线格式的 JSON 字符串，忽略空值。
     *
     * @param obj 要转换的对象
     * @return 下划线格式的 JSON 字符串，如果转换失败则返回 null
     */
    public static String toUnderlineJsonStr(Object obj) {
        return toUnderlineJsonStr(obj, true);
    }

    /**
     * 将对象转换为下划线格式的 JSON 字符串。
     *
     * @param obj 要转换的对象
     * @param ignoreNull 是否忽略空值
     * @return 下划线格式的 JSON 字符串，如果转换失败则返回 null
     */
    public static String toUnderlineJsonStr(Object obj, boolean ignoreNull) {
        JsonMapper.Builder builder = JsonMapper.builder()
                .propertyNamingStrategy(PropertyNamingStrategies.SNAKE_CASE);

        if (ignoreNull) {
            builder.serializationInclusion(JsonInclude.Include.NON_NULL);
        }

        JsonMapper objectMapper = builder.build();

        try {
            return objectMapper.writeValueAsString(obj);
        } catch (JsonProcessingException e) {
            LogExUtil.errorLog("JSON转换下划线结构失败", e);
            return null;
        }
    }

    /**
     * 解析任意层级嵌套的JSON字符串，保留了原始 JSON 的结构，只是"展开"了嵌套的部分
     * @param jsonString JSON字符串
     * @return  解析后的JSONObject
     */
    public static JSONObject parseNestedJson(String jsonString) {
        if (StrUtil.isBlank(jsonString)) {
            return new JSONObject(0);
        }
        JSONObject result = JSONUtil.parseObj(jsonString);
        return parseNestedJsonObject(result);
    }

    /**
     * 解析任意层级嵌套的JSON字符串，保留了原始 JSON 的结构，只是"展开"了嵌套的部分
     * @param jsonObject 最外层的JSONObject
     * @return  解析后的JSONObject
     */
    private static JSONObject parseNestedJsonObject(JSONObject jsonObject) {
        JSONObject result = new JSONObject();

        for (String key : jsonObject.keySet()) {
            Object value = jsonObject.get(key);
            if (value instanceof String strValue) {
                try {
                    // 尝试解析字符串值为 JSON
                    JSONObject nestedJson = JSONUtil.parseObj(strValue);
                    // 如果成功解析，递归处理嵌套的 JSON
                    result.set(key, parseNestedJsonObject(nestedJson));
                } catch (Exception e) {
                    // 如果解析失败，保留原始字符串值
                    result.set(key, strValue);
                }
            } else {
                // 对于非字符串值，直接保留
                result.set(key, value);
            }
        }

        return result;
    }

}
