package com.bbh.live.util;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.MD5;
import cn.hutool.json.JSONUtil;
import cn.hutool.log.Log;
import cn.hutool.log.LogFactory;

import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.Map;
import java.util.Set;

public class SignUtil {
    private static final Log log = LogFactory.get();

    /**
     * 验证签名是否有效
     *
     * @param base64Str base64编码的字符串
     * @param confuseKey 混淆密钥
     * @return 如果签名有效则返回true，否则返回false
     */
    public static boolean checkSign(String base64Str, String confuseKey) {
        String signMark = "sign";
        // 对base64编码的字符串进行解码
        String json = Base64.decodeStr(base64Str, StandardCharsets.UTF_8);
        // 将JSON字符串转换为Map对象
        Map<String, Object> params = JSONUtil.toBean(json, Map.class);
        // 检查Map对象中是否包含"sign"字段
        if (!params.containsKey(signMark)) {
            return false;
        }
        // 获取"sign"字段的值
        String sign = params.get("sign").toString();
        // 从Map对象中移除"sign"字段
        params.remove("sign");
        // 将Map对象中的其他参数按照特定顺序转换为字符串
        String temp = sortToString(params);
        log.info("验签checkSign :" + sign + " === " + temp + "\n");
        // 计算按照特定顺序排列的参数与混淆密钥拼接后的字符串的MD5哈希值，并转换为大写
        String upperCase = MD5.create().digestHex(temp + "&key=" + confuseKey).toUpperCase();
        // 比较计算得到的哈希值与"sign"字段的值是否相等
        return StrUtil.equals(sign, upperCase);
    }

    /**
     * @param obj 对象
     * @param confuseKey 混淆
     * @return base64
     */
    public static String encodeSign(Object obj, String confuseKey) {
        // 打印出加密的对象和混淆的key
        log.info(StrUtil.format("\n加密对象:{}\n混淆key:{}", JSONUtil.toJsonStr(obj), confuseKey));
        // 将对象转换为map
        Map params = JSONUtil.toBean(JSONUtil.toJsonStr(obj), Map.class);
        String temp = sortToString(params);
        log.info("\n\n" + temp + "\n");
        // 将temp和confuseKey拼接，并转换为大写
        String upperCase = MD5.create().digestHex(temp + "&key=" + confuseKey).toUpperCase();
        // 将拼接后的值放入map中
        params.put("sign", upperCase);
        // 打印出排序后的map
        log.info(StrUtil.format("\n排序之后:{}\nsign:{}", JSONUtil.toJsonStr(params), upperCase));
        // 将map转换为json字符串
        String jsonStr = JSONUtil.toJsonStr(params);
        // 将json字符串转换为base64字符串
        String resultStr = Base64.encode(jsonStr.getBytes(StandardCharsets.UTF_8));
        // 打印出base64字符串
        log.info(StrUtil.format("\nbase64:{}", resultStr));
        // 返回base64字符串
        return resultStr;
    }

    /**
     * key参数 根据 ASCII 编码 排序
     *
     * @param params 参数
     * @return 排序 参数 & 拼接
     */
    private static String sortToString(Map<String, Object> params) {
        // 获取map中的key
        Set<String> keysSet = params.keySet();
        // 将key转换为数组
        Object[] keys = keysSet.toArray();
        // 对key进行排序
        Arrays.sort(keys);
        // 创建一个字符串构建器
        StringBuilder temp = new StringBuilder();
        // 判断是否是第一次
        boolean isFirst = true;
        // 遍历key
        for (Object key : keys) {
            // 判断key是否为空或者value是否为空
            if (ObjectUtil.isNull(key) || ObjectUtil.isNull(params.get(key + ""))) {
                // 如果为空，则从map中移除
                params.remove(key + "");
                continue;
            }
            // 如果是第一次，则将isFirst设置为false
            if (isFirst) {
                isFirst = false;
            } else {
                // 如果不是第一次，则添加&
                temp.append("&");
            }
            // 添加key
            temp.append(key).append("=");
            // 获取value
            Object value = params.get(key + "");
            String valueStr = "";
            // 判断value是否为空
            if (null != value) {
                // 如果不为空，则获取value的值
                valueStr = value.toString();
            }
            // 添加value
            temp.append(valueStr);
        }
        return temp.toString();
    }

    /*public static void main(String[] args) {
        // String base64Str =
        // "eyJ0cmFuc2FjdGlvbl9ubyI6IjI3YmI2ZjhhNmEwNTQ3MTg5MTI1MGJiZjEyYmQxM2E4IiwiYXV0aF9zdGF0dXMiOjMwLCJhdXRoX3R5cGUiOjEsImZhaWxfcmVhc29uIjoi5a6h5qC45LiN6YCa6L+HIOS6uuiEuOivhuWIq+S4jemAmui/h++8jOivt+mHjeaWsOiupOivgeW5tuW9leWItuaXtuehruS/neato+iEuOWvueahhuS4lOa4heaZsOWujOaVtOOAgiIsImNoZWNrX2F0IjoxNzE0MTI5ODQ2MDAwLCJwZXJzb25faW5mbyI6e30sImNvbXBhbnlfaW5mbyI6eyJvcmdhbml6YXRpb25fbm8iOiIifSwic2lnbiI6IkQ2QTY3MDhDRThCOUIxNUMzMzdGRjQzQzc2RDM5N0Q5In0=";
        // System.out.println(checkSign(base64Str, "112233"));
        String base64Str =
            "eyJpZlN1Y2Nlc3MiOnRydWUsIm91dFJlZnVuZE5vIjoiMTAwMDAwMzkyMDI0MDUyODE0NTYwMTcxNjIiLCJzaWduIjoiNzdDMzEwMzJEMUJBODg4QUJBQTRBMjRBNzVGRjkzQTAifQ==eyJpZlN1Y2Nlc3MiOnRydWUsIm91dFJlZnVuZE5vIjoiMTAwMDAwMzkyMDI0MDUyODE0NTYwMTcxNjIiLCJzaWduIjoiNzdDMzEwMzJEMUJBODg4QUJBQTRBMjRBNzVGRjkzQTAifQ==";
        System.out.println(checkSign(base64Str, "112233"));
        // System.out.println(checkSign(base64Str, "c2982cc05653aa7caf8bfda741ebe6e4"));
    }*/
}
