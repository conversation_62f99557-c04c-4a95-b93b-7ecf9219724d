package com.bbh.live.util;

import cn.hutool.core.convert.Convert;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;

import java.util.Map;

/**
 * Map 工具
 * <AUTHOR>
 */
public class MapUtils {

    /**
     * 将对象转换为下划线命名的 Map。
     *
     * <p>此方法使用 Jackson 的 ObjectMapper 将给定对象转换为 Map，
     * 同时将属性名称从驼峰命名法转换为下划线命名法。</p>
     *
     * @param <K> Map 键的类型
     * @param <V> Map 值的类型
     * @param object 要转换的对象
     * @param keyType Map 键的 Class 对象
     * @param valueType Map 值的 Class 对象
     * @return 转换后的 Map，如果转换失败则返回 null
     *
     * @throws NullPointerException 如果 object、keyType 或 valueType 为 null
     *
     * @see com.fasterxml.jackson.databind.ObjectMapper
     * @see com.fasterxml.jackson.databind.PropertyNamingStrategies
     */
    public static <K, V> Map<K, V> toUnderlineMap(Object object, Class<K> keyType, Class<V> valueType) {
        ObjectMapper mapper = new ObjectMapper();
        mapper.setPropertyNamingStrategy(PropertyNamingStrategies.SNAKE_CASE);
        try {
            return Convert.toMap(keyType, valueType, mapper.readValue(mapper.writeValueAsString(object), Map.class));
        } catch (JsonProcessingException e) {
            return null;
        }
    }

    /**
     * 将对象转换为下划线命名的 Map，键类型为 String，值类型为 Object。
     *
     * <p>此方法是 {@link #toUnderlineMap(Object, Class, Class)} 的简化版本，
     * 默认使用 String 作为键类型，Object 作为值类型。</p>
     *
     * @param object 要转换的对象
     * @return 转换后的 Map，键为 String 类型，值为 Object 类型。如果转换失败则返回 null
     *
     * @throws NullPointerException 如果 object 为 null
     *
     * @see #toUnderlineMap(Object, Class, Class)
     */
    public static Map<String, Object> toUnderlineMap(Object object) {
        return toUnderlineMap(object, String.class, Object.class);
    }

}
