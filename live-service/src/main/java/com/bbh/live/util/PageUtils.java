package com.bbh.live.util;

import com.bbh.base.Page;
import com.bbh.base.PageBase;

/**
 * <AUTHOR> dsy
 * @Date: 2024/7/31
 * @Description:
 */
public class PageUtils {

    public static <T> Page<T> getPage(PageBase pageBase, Class<T> clazz) {
        Page<T> page = new Page<>();

        //currentPage小于等于0 则不分页
        if(pageBase.getCurrentPage() > 0){
            page.setCurrent(pageBase.getCurrentPage());
            page.setSize(pageBase.getPerPage());
        }
        return page;
    }
}
