package com.bbh.live.util;

import cn.hutool.extra.spring.SpringUtil;
import com.bbh.live.dao.dto.vo.LiveRoomVO;
import com.bbh.live.dao.service.LiveRoomDirectorService;
import com.bbh.model.LiveRoom;
import com.bbh.secure.AuthUtil;
import com.bbh.util.AssertUtil;
import com.bbh.util.EnvironmentUtil;

import java.util.Objects;

/**
 * <AUTHOR>
 * @description 权限检查
 */
public class LivePermissionChecker {

    private static final LiveRoomDirectorService LIVE_ROOM_DIRECTOR_SERVICE = SpringUtil.getBean(LiveRoomDirectorService.class);

    /**
     * 是否是主播
     * @param liveRoomVO
     */
    public static void assertAnchor(LiveRoomVO liveRoomVO) {
        AssertUtil.assertTrue(Objects.equals(liveRoomVO.getAnchorSeatId(), AuthUtil.getSeatId()), "您没有操作权限");
    }

    /**
     * 是否是主播
     * @param liveRoom
     */
    public static void assertAnchor(LiveRoom liveRoom) {
        AssertUtil.assertTrue(Objects.equals(liveRoom.getAnchorSeatId(), AuthUtil.getSeatId()), "您没有操作权限");
    }

    /**
     * 是否是导播
     * @param liveRoomId
     */
    public static void assertDirector(Long liveRoomId) {
        if (EnvironmentUtil.isProfile("local")) {
            return;
        }
        AssertUtil.assertTrue(LIVE_ROOM_DIRECTOR_SERVICE.isLiveRoomDirector(liveRoomId, AuthUtil.getSeatId()), "您没有操作权限");
    }


}
