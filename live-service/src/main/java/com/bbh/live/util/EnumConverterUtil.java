package com.bbh.live.util;

import cn.hutool.core.map.BiMap;
import com.bbh.enums.GlobalOrderTypeEnum;
import com.bbh.service.deposit.enums.CodeEnum;

import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2025/4/2
 * @description: 枚举转换
 */
public class EnumConverterUtil {


    public static final BiMap<GlobalOrderTypeEnum, CodeEnum> globalOrderTypeEnumCodeEnumBi = new BiMap<>(Map.of(
            GlobalOrderTypeEnum.LIVE, CodeEnum.LIVE,
            GlobalOrderTypeEnum.PT_AUCTION, CodeEnum.PT,
            GlobalOrderTypeEnum.SCE, CodeEnum.SCE
    ));





    public static CodeEnum globalOrderTypeEnum2CodeEnum(GlobalOrderTypeEnum globalOrderTypeEnum) {
        return globalOrderTypeEnumCodeEnumBi.get(globalOrderTypeEnum);

    }

    //枚举转换
    public static GlobalOrderTypeEnum codeEnum2GlobalOrderTypeEnum(CodeEnum codeEnum) {
        return globalOrderTypeEnumCodeEnumBi.getKey(codeEnum);
    }




}
