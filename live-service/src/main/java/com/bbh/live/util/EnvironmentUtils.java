package com.bbh.live.util;

import cn.hutool.extra.spring.SpringUtil;
import com.bbh.live.constant.ProjectConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.env.Environment;
import org.springframework.core.env.Profiles;

import java.util.Arrays;
import java.util.Optional;
import java.util.Set;

/**
 * 环境工具
 * <AUTHOR>
 */
@Slf4j
public class EnvironmentUtils {

    public static boolean isProd() {
        String activeProfile = getActiveProfile();
        return ProjectConstant.Environment.PROD.equals(activeProfile);
    }

    public static boolean isTest() {
        String activeProfile = getActiveProfile();
        return ProjectConstant.Environment.TEST.equals(activeProfile);
    }

    public static boolean isDev() {
        String activeProfile = getActiveProfile();
        return ProjectConstant.Environment.DEV.equals(activeProfile);
    }

    public static String getActiveProfile() {
        String[] activeProfiles = getEnvironment().getActiveProfiles();
        getEnvironment().acceptsProfiles(Profiles.of("dev"));
        Optional<String> first = Arrays.stream(activeProfiles).filter(activeProfile -> !EXCLUDE_PROFILES.contains(activeProfile)).findFirst();
        return first.orElse("default");
    }

    private static final Set<String> EXCLUDE_PROFILES = Set.of("dynamic-tp", "white-list");

    public static Environment getEnvironment() {
        return SpringUtil.getBean(Environment.class);
    }

}
