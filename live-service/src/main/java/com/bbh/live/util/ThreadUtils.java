package com.bbh.live.util;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> dsy
 * @Date: 2024/7/30
 * @Description:
 */
@Slf4j
public class ThreadUtils {

    public static void specifiedThreadName(String threadName) {
        try {
            Thread.currentThread().setName(threadName);
        } catch (Exception e) {
            log.error("SpecifiedThreadNameError: {}", e.getMessage(), e);
        }
    }
}
