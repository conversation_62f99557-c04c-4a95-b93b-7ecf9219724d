package com.bbh.live.util;

import com.bbh.enums.LiveGoodsTradeTypeEnum;
import com.bbh.exception.ServiceException;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024/9/23 14:35
 * @description
 */
public class NumberUtils {

    public static void checkGoodsPrice(LiveGoodsTradeTypeEnum tradeType, BigDecimal price) {
        if(price == null){
            return;
        }
        if((tradeType == null || tradeType == LiveGoodsTradeTypeEnum.AUCTION) && price.compareTo(BigDecimal.ZERO) < 0){
            throw new ServiceException("金额不得小于0元");
        }
        if(price.compareTo(new BigDecimal("99999999")) > 0){
            throw new ServiceException("金额过大，不能超过99999999元");
        }
    }

    public static void checkBargainGoodsPrice(LiveGoodsTradeTypeEnum tradeType, BigDecimal price){
        if(price == null){
            return;
        }
        if((tradeType == null || tradeType == LiveGoodsTradeTypeEnum.AUCTION) && price.compareTo(BigDecimal.ZERO) < 0){
            throw new ServiceException("金额必须大于0元");
        }
        if(price.compareTo(new BigDecimal("99999999")) > 0){
            throw new ServiceException("金额过大，不能超过99999999元");
        }
    }
}
