package com.bbh.live.util;

import cn.hutool.extra.spring.SpringUtil;
import com.bbh.live.dao.dto.BargainGoodsDTO;
import com.bbh.live.dao.service.GlobalOrgSeatService;
import com.bbh.live.service.room.context.LiveRoomContextHolder;
import com.bbh.service.deposit.DepositService;
import com.bbh.service.deposit.dto.*;
import com.bbh.service.deposit.enums.CodeEnum;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * <AUTHOR> dsy
 * @Date: 2024/8/23
 * @Description:
 */
public class DepositUtils {

    private static final DepositService depositService = SpringUtil.getBean(DepositService.class);
    private static final GlobalOrgSeatService globalOrgSeatService = SpringUtil.getBean(GlobalOrgSeatService.class);

    /**
     * 出价冻结保证金
     * @param bargainGoods
     */
    public static void bidToFrozen(BargainGoodsDTO bargainGoods){
        depositService.bidToFrozen(getBidToFrozenDTO(bargainGoods));
    }

    /**
     * 出价冻结保证金
     * @param bidToFrozenDTO
     */
    public static void bidToFrozen(BidToFrozenDTO bidToFrozenDTO){
        depositService.bidToFrozen(bidToFrozenDTO);
    }

    /**
     * 是否出过价
     * @param liveGoodsId
     * @param orgId
     * @return
     */
    public static boolean ifBid(Long liveGoodsId, Long orgId){
        IfBidDTO ifBid = new IfBidDTO();
        ifBid.setUserInfo(getUserInfoDTO(orgId));
        ifBid.setGoodsInfo(getGoodsInfoDTO(liveGoodsId));
        return depositService.ifBid(ifBid);
    }

    /**
     * 归还所有出价的没有成功的用户的保证金, 并生成成交人保证金冻结明细
     *
     * @param liveGoodsId 商品Id
     * @param buyerSeatId 成交人席位id
     */
    public static void bidSuccessBackFrozen(Long liveGoodsId, Long buyerSeatId){
        BidSuccessBackFrozenDTO frozenDTO = new BidSuccessBackFrozenDTO();
        GoodsInfoDTO goodsInfoDTO = DepositUtils.getGoodsInfoDTO(liveGoodsId);
        frozenDTO.setGoodsInfo(goodsInfoDTO);
        if(buyerSeatId != null){
            UserInfoDTO successInfo = DepositUtils.getUserInfoDTO(globalOrgSeatService.getOrgIdBySeatId(buyerSeatId));
            frozenDTO.setSuccessInfo(successInfo);
            frozenDTO.setCreateId(buyerSeatId);
            frozenDTO.setCreateName(SpringUtil.getBean(GlobalOrgSeatService.class).getSeatName(buyerSeatId));
        } else {
            frozenDTO.setCreateId(0L);
            frozenDTO.setCreateName("直播流拍解冻");
        }
        depositService.bidSuccessBackFrozen(frozenDTO);
    }

    public static BidToFrozenDTO getBidToFrozenDTO(BargainGoodsDTO bargainGoods) {
        //需要的保证金，向上取整数
        BigDecimal deposit = computedFrozenDeposit(bargainGoods.getBidPrice());

        // 需要冻结的保证金
        BidToFrozenDTO frozenDTO = new BidToFrozenDTO();
        frozenDTO.setFrozenDeposit(deposit);
        frozenDTO.setAmount(bargainGoods.getBidPrice());

        // 用户信息
        UserInfoDTO userInfoDTO = getUserInfoDTO(bargainGoods.getBuyerOrgId());

        // 商品信息
        GoodsInfoDTO goodsInfoDTO = getGoodsInfoDTO(bargainGoods.getLiveGoodsId());

        frozenDTO.setGoodsInfo(goodsInfoDTO);
        frozenDTO.setUserInfo(userInfoDTO);
        return frozenDTO;
    }

    /**
     * 计算出需要的保证金，向上取整数
     * @param bidPrice          出价金额
     * @return                  需要的保证金
     */
    public static BigDecimal computedFrozenDeposit(BigDecimal bidPrice) {
        return bidPrice.divide(BigDecimal.valueOf(LiveRoomContextHolder.getLiveRoomDepositRate()), 0, RoundingMode.UP);
    }

    public static UserInfoDTO getUserInfoDTO(Long orgId) {
        UserInfoDTO userInfoDTO = new UserInfoDTO();
        userInfoDTO.setId(orgId);
        return userInfoDTO;
    }

    public static GoodsInfoDTO getGoodsInfoDTO(Long liveGoodsId) {
        GoodsInfoDTO goodsInfoDTO = new GoodsInfoDTO();
        goodsInfoDTO.setGoodsId(liveGoodsId);
        goodsInfoDTO.setCode(CodeEnum.LIVE);
        return goodsInfoDTO;
    }

    public enum DepositSource{
        /**
         * 竞拍
         */
        AUCTION("保证金不足，无法出价"),
        /**
         * 提价
         */
        BARGAIN("买家保证金不足，无法成交"),
        /**
         * 传送要了
         */
        TRANSFER("保证金不足"),
        ;

        private String errMsg;

        DepositSource(String errMsg) {
            this.errMsg = errMsg;
        }
    }
}
