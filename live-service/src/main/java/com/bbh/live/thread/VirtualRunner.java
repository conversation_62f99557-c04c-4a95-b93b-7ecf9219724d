package com.bbh.live.thread;

import cn.hutool.core.collection.CollectionUtil;
import org.dromara.dynamictp.core.support.task.wrapper.TaskWrapper;
import org.dromara.dynamictp.core.support.task.wrapper.TaskWrappers;

import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.function.Supplier;

/**
 * <AUTHOR>
 * @date 2024/10/28 16:03
 * @description
 */
public class VirtualRunner {

    /**
     * 需要应用的包装器名称集合
     * mdc: 用于日志上下文传递
     * user: 用于用户信息传递
     */
    public static final Set<String> WRAPPERS = new HashSet<>(){{
        add("mdc");
        add("user");
    }};

    /**
     * 为Runnable任务添加包装器
     *
     * @param runnable 原始的Runnable任务
     * @return 包装后的Runnable任务
     */
    public static Runnable wrapper(Runnable runnable) {
        Runnable wrapperRunnable = runnable;
        // 根据配置的包装器名称获取对应的TaskWrapper实例
        List<TaskWrapper> taskWrappers = TaskWrappers.getInstance().getByNames(WRAPPERS);
        if(CollectionUtil.isEmpty(taskWrappers)){
            return runnable;
        }

        for(TaskWrapper taskWrapper : taskWrappers){
            wrapperRunnable = taskWrapper.wrap(wrapperRunnable);
        }
        return wrapperRunnable;
    }

    /**
     * 为Supplier任务添加包装器
     * 由于TaskWrapper只支持包装Runnable，这里通过适配器模式将Supplier转换为Runnable进行包装
     *
     * @param supplier 原始的Supplier任务
     * @param <T> Supplier的返回值类型
     * @return 包装后的Supplier任务
     */
    public static <T> Supplier<T> wrapper(Supplier<T> supplier){
        return new SupplierAdapt<>(supplier);
    }
    /*public static <T> Supplier<T> wrapper(Supplier<T> supplier) {
        return () -> {
            // 存储Supplier执行结果
            AtomicReference<T> result = new AtomicReference<>();
            // 存储执行过程中可能发生的异常
            AtomicReference<Throwable> exception = new AtomicReference<>();

            Runnable runnable = () -> {
                try {
                    // 执行原始的supplier并保存结果
                    result.set(supplier.get());
                } catch (Throwable e) {
                    exception.set(e);
                }
            };

            // 使用现有的包装器进行包装，并执行
            wrapper(runnable).run();
            if (exception.get() != null) {
                if (exception.get() instanceof RuntimeException) {
                    throw (RuntimeException) exception.get();
                }
                // 其他类型的异常包装成CompletionException再抛出
                throw new CompletionException(exception.get());
            }
            return result.get();
        };
    }*/

    private static class SupplierAdapt<T> implements Supplier<T> {

        private final Runnable runnable;
        private T result;

        public SupplierAdapt(Supplier<T> supplier) {
            this.runnable = wrapper(() -> {result = supplier.get();});
        }

        @Override
        public T get() {
            this.runnable.run();
            return result;
        }
    }

}
