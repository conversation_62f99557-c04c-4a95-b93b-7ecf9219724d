package com.bbh.live.thread.dtpwrapper;

import com.bbh.live.service.room.context.LiveRoomContext;
import com.bbh.live.service.room.context.LiveRoomContextHolder;

/**
 * <AUTHOR> dsy
 * @Date: 2024/8/8
 * @Description:
 */
public class LiveRoomRunner extends AbstractRunner{

    private final LiveRoomContext liveRoomContext;

    public LiveRoomRunner(Runnable runnable) {
        super(runnable);
        this.liveRoomContext = LiveRoomContextHolder.getLiveRoomContext();
    }

    @Override
    public void run() {
        LiveRoomContextHolder.setLiveRoomContext(liveRoomContext);
        try {
            getRunnable().run();
        }finally {
            LiveRoomContextHolder.clearRoom();
        }
    }

    public static LiveRoomRunner get(Runnable runnable) {
        return new LiveRoomRunner(runnable);
    }
}
