package com.bbh.live.thread.dtpwrapper;

import org.dromara.dynamictp.core.support.task.wrapper.TaskWrapper;

/**
 * <AUTHOR> dsy
 * @Date: 2024/8/7
 * @Description:
 */
public class TransactionTaskWrapper implements TaskWrapper {

    @Override
    public String name() {
        return "transaction";
    }

    @Override
    public Runnable wrap(Runnable runnable) {
        return TransactionRunner.get(runnable);
    }
}
