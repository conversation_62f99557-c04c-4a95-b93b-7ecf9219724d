package com.bbh.live.thread;

import org.dromara.dynamictp.core.DtpRegistry;
import org.dromara.dynamictp.core.executor.DtpExecutor;
import org.dromara.dynamictp.core.executor.ScheduledDtpExecutor;

import java.util.concurrent.Executor;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.ThreadFactory;

/**
 * <AUTHOR> dsy
 * @Date: 2024/7/29
 * @Description:
 */
public class ThreadPoolManager {

    /**
     * 全局业务
     *
     * @return
     */
    public static Executor getGlobalBizExecutor() {
        return DtpRegistry.getDtpExecutor(ThreadPoolConstant.GLOBAL_BIZ_EXECUTOR);
    }

    public static Executor getRongYunMsgSendExecutor() {
        return DtpRegistry.getDtpExecutor(ThreadPoolConstant.RONGYUN_MSG_SEND_EXECUTOR);
    }

    /**
     * 竞拍消息
     *
     * @return
     */
    public static Executor getAuctionMessageConsumerExecutor() {
        return DtpRegistry.getDtpExecutor(ThreadPoolConstant.AUCTION_MESSAGE_CONSUMER_EXECUTOR);
    }

    /**
     * 多个消费者自动消费
     *
     * @return
     */
    public static Executor getAutoConsumeExecutor(int corePoolSize) {
        DtpExecutor dtpExecutor = DtpRegistry.getDtpExecutor(ThreadPoolConstant.AUTO_CONSUME_EXECUTOR);
        if (dtpExecutor.getCorePoolSize() != corePoolSize && corePoolSize >= 0) {
            dtpExecutor.setCorePoolSize(corePoolSize);
        }
        return dtpExecutor;
    }

    public static ExecutorService virtualExecutors(String name) {
        ThreadFactory factory = Thread.ofVirtual().name(name + "-", 0L).factory();
        return Executors.newThreadPerTaskExecutor(factory);
    }

    /**
     * 异步执行的线程池
     *
     * @return
     */
    public static ScheduledDtpExecutor getScheduledDtpExecutor() {
        return (ScheduledDtpExecutor) DtpRegistry.getDtpExecutor(ThreadPoolConstant.GLOBAL_BIZ_EXECUTOR);
    }

}
