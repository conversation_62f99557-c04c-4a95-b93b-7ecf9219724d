package com.bbh.live.thread.dtpwrapper;

import com.bbh.secure.AuthUtil;
import com.bbh.vo.AuthUser;

/**
 * <AUTHOR> dsy
 * @Date: 2024/8/8
 * @Description:
 */
public class AuthUserRunner extends AbstractRunner {

    private AuthUser user;

    public AuthUserRunner(Runnable runnable) {
        super(runnable);
        try {
            this.user = AuthUtil.getUser();
        } catch (Throwable t) {
            this.user = null;
        }
    }

    @Override
    public void run() {
        if (user == null) {
            getRunnable().run();
            return;
        }

        AuthUtil.setUser(user);
        try {
            getRunnable().run();
        } finally {
            AuthUtil.clearUser();
        }
    }

    public static AuthUserRunner get(Runnable runnable) {
        return new AuthUserRunner(runnable);
    }
}
