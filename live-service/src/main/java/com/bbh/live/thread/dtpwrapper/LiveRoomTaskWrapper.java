package com.bbh.live.thread.dtpwrapper;

import org.dromara.dynamictp.core.support.task.wrapper.TaskWrapper;

/**
 * <AUTHOR> dsy
 * @Date: 2024/8/8
 * @Description:
 */
public class LiveRoomTaskWrapper implements TaskWrapper {

    @Override
    public Runnable wrap(Runnable runnable) {
        return LiveRoomRunner.get(runnable);
    }

    @Override
    public String name() {
        return "liveRoom";
    }
}
