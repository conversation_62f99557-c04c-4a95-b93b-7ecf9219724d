package com.bbh.live.thread.dtpwrapper;

import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> dsy
 * @Date: 2024/8/7
 * @Description:
 */
@Slf4j
public class TransactionRunner extends AbstractRunner {

    private TransactionRunner.TransactionalResource transactionalResource;
    private final boolean isInTransactional;

    public TransactionRunner(Runnable runnable) {
        super(runnable);
        //当前是否存在事务
        this.isInTransactional = TransactionSynchronizationManager.isSynchronizationActive();
        if(isInTransactional){
            //保存事务信息
            transactionalResource = new TransactionRunner.TransactionalResource();
        }
    }

    public static TransactionRunner get(Runnable runnable) {
        return new TransactionRunner(runnable);
    }

    @Override
    public void run() {
        if(isInTransactional){
            //还原事务上下文
            try {
                transactionalResource.restore();
            }catch (Throwable t){
                log.error("事务恢复异常：{}", t.getMessage(), t);
            }
        }
        try {
            getRunnable().run();
        } catch (Throwable t){
            log.error("事务执行异常：{}", t.getMessage(), t);
        } finally{
            if(isInTransactional){
                //清理事务消息
                transactionalResource.clear();
            }
        }
    }

    private static class TransactionalResource {
        //事务结束后默认会移除集合中的DataSource作为key关联的资源记录
        private final Map<Object, Object> resources;

        private final List<TransactionSynchronization> synchronizations;
        // 当前事务名称
        private final String currentTransactionName;
        // 当前事务是否只读
        private final Boolean currentTransactionReadOnly;
        // 当前事务隔离级别
        private final Integer currentTransactionIsolationLevel;
        // 当前事务是否激活状态
        private final Boolean actualTransactionActive;

        public TransactionalResource() {
            this.resources = TransactionSynchronizationManager.getResourceMap();
            this.synchronizations = TransactionSynchronizationManager.getSynchronizations();
            this.currentTransactionName = TransactionSynchronizationManager.getCurrentTransactionName();
            this.currentTransactionReadOnly = TransactionSynchronizationManager.isCurrentTransactionReadOnly();
            this.currentTransactionIsolationLevel = TransactionSynchronizationManager.getCurrentTransactionIsolationLevel();
            this.actualTransactionActive = TransactionSynchronizationManager.isActualTransactionActive();
        }

        public void restore() {
            resources.forEach(TransactionSynchronizationManager::bindResource);
            TransactionSynchronizationManager.initSynchronization();
            synchronizations.forEach(TransactionSynchronizationManager::registerSynchronization);
            TransactionSynchronizationManager.setActualTransactionActive(actualTransactionActive);
            TransactionSynchronizationManager.setCurrentTransactionName(currentTransactionName);
            TransactionSynchronizationManager.setCurrentTransactionIsolationLevel(currentTransactionIsolationLevel);
            TransactionSynchronizationManager.setCurrentTransactionReadOnly(currentTransactionReadOnly);
        }

        public void clear() {
            resources.forEach((k, v) -> TransactionSynchronizationManager.unbindResourceIfPossible(k));
            TransactionSynchronizationManager.clear();
        }
    }
}
