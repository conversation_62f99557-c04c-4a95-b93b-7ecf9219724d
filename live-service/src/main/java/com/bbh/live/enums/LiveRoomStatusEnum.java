package com.bbh.live.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 直播间状态：0-尚未开始、1-直播中、2-直播已结束，商品清单展示中、3-直播已结束，商品清单也展示结束了
 *
 * <AUTHOR>
 * @deprecated
 * @see com.bbh.live.enums.LiveRoomEnhancedStatusEnum
 */
@AllArgsConstructor
@Getter
@Deprecated
public enum LiveRoomStatusEnum {


    /**
     * 尚未开始
     */
    NOT_STARTED(0),

    /**
     * 直播中
     */
    IN_PROGRESS(1),

    /**
     * 直播已结束，商品清单展示中
     */
    SHOWING_GOODS_LIST(2),

    /**
     * 直播已结束，商品清单也展示结束了
     */
    COMPLETED(3),

    UNKNOWN(-1)

    ;

    @EnumValue
    @JsonValue
    private final Integer code;
}
