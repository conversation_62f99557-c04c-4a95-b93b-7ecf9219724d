package com.bbh.live.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/9/20 16:44
 * @description
 */
@Getter
@AllArgsConstructor
public enum PermissionCodeEnum {

    /**
     * 全部权限
     */
    LIVE_ALL_PERMISSION("live_all_permission", "", PermissionCheckTypeEnum.PERMISSION_CHECK_TYPE_LIVE),

    /**
     * 购买买手会员
     */
    LIVE_OPEN_VIP("live_open_vip", "暂无购买会员权限，可联系管理员开通购买会员权限", PermissionCheckTypeEnum.PERMISSION_CHECK_TYPE_GLOBAL),

    /**
     * 分配买手会员
     */
    LIVE_ALLOCATE_VIP("live_allocate_vip", "暂无操作权限，可联系管理员开通分配会员权限", PermissionCheckTypeEnum.PERMISSION_CHECK_TYPE_GLOBAL),

    /**
     * 分配拍号
     */
    BEAT_EDIT("beat_edit", "暂无操作权限，可联系管理员开通分配拍号权限", PermissionCheckTypeEnum.PERMISSION_CHECK_TYPE_GLOBAL),

    /**
     * 直播中心
     */
    LIVE_CENTER("live_center", "暂无操作权限，可联系管理员开通直播中心权限", PermissionCheckTypeEnum.PERMISSION_CHECK_TYPE_LIVE),

    /**
     * 采购权限
     */
    LIVE_BUY("live_buy", "暂无直播采购权限，可联系管理员开通", PermissionCheckTypeEnum.PERMISSION_CHECK_TYPE_LIVE),


    /**
     * 查看全店收银台
     */
    LIVE_CHECKOUT_CART("live_checkout_cart", "暂无权限，可联系管理员开通", PermissionCheckTypeEnum.PERMISSION_CHECK_TYPE_LIVE),

    /**
     * 订单发货权限
     */
    LIVE_SALES_ORDER_DELIVERY("live_sales_order_delivery", "暂无操作权限，可联系管理员开通发货权限", PermissionCheckTypeEnum.PERMISSION_CHECK_TYPE_LIVE),

    /**
     * 查看全店销售权限
     */
    LIVE_SALES_ORDER("live_sales_order", "暂无操作权限，可联系管理员开通查看销售订单权限", PermissionCheckTypeEnum.PERMISSION_CHECK_TYPE_LIVE),

    ;

    private final String permissionCode;
    private final String errorTips;
    private final PermissionCheckTypeEnum typeEnum;
}
