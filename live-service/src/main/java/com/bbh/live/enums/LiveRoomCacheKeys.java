package com.bbh.live.enums;

import com.bbh.live.constant.RedisKey;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 直播房间缓存键枚举类，统一维护各种与直播房间相关的缓存键
 *
 * <AUTHOR>
 */
@AllArgsConstructor
@Getter
public enum LiveRoomCacheKeys implements RedisKey {

    /**
     * 直播间
     */
    ROOM("room"),

    /**
     * 直播间人数
     */
    ROOM_USER("roomUser"),

    /**
     * 加载缓存分布式锁
     */
    LOAD_CACHE_KEY("loadCacheKey"),

    /**
     * 预约数量
     */
    SUBSCRIBE_COUNT("subscribeCount"),

    /**
     * 观看数量
     */
    VIEW_COUNT("viewCount"),

    /**
     * 真实的累计人数
     */
    REAL_COUNT("realCount"),

    /**
     * 真实的峰值人数
     */
    PEAK_COUNT("realMaxCount"),

    /**
     * 实时人数
     */
    REALTIME_COUNT("realtimeCount"),

    /**
     * 成员集合
     */
    MEMBERS_SET("membersSet"),

    /**
     * 商品数量
     */
    GOODS_COUNT("goodsCount"),

    /**
     * 未售出数量
     */
    UNSOLD_COUNT("unsoldCount"),

    /**
     * 已售出金额
     */
    SOLD_AMOUNT("soldAmount"),

    /**
     * 已售出数量
     */
    SOLD_COUNT("soldCount"),

    /**
     * 直播间名称
     */
    ROOM_NAME("roomName"),

    /**
     * 开始时间
     */
    START_AT("startAt"),

    /**
     * 结束时间
     */
    END_AT("endAt"),

    ACTUAL_START_AT("actualStartAt"),

    ACTUAL_END_AT("actualEndAt"),

    /**
     * 贴纸内容
     */
    STICKER_CONTENT("stickerContent"),

    /**
     * 基础信息
     */
    BASIC_INFO("basicInfo"),

    /**
     * 商品ID列表
     */
    GOODS_IDS("goodsIds"),

    /**
     * 用户进出消息的控制
     */
    USER_ENTER_MSG_EXPIRED("userEnterExpired"),

    ;

    private final String key;
}
