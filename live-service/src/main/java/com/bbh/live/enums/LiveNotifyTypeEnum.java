package com.bbh.live.enums;


import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 直播服务回调类型
 * 1  直播推流状态回调
 * 2  直播录像回调
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum LiveNotifyTypeEnum {
    LIVE_NOTIFY(1),
    LIVE_VIDEO_RECORD_NOTIFY(2),
    ;
    @JsonValue
    private final Integer type;

    // 使用静态 Map 存储 type 到枚举值的映射，提高查找效率
    private static final Map<Integer, LiveNotifyTypeEnum> TYPE_MAP = Arrays.stream(values())
            .collect(Collectors.toUnmodifiableMap(
                    LiveNotifyTypeEnum::getType,
                    Function.identity()
            ));

    /**
     * 根据类型值获取对应的枚举实例
     *
     * @param type 类型值
     * @return 对应的枚举实例，如果找不到则返回 null
     */
    public static LiveNotifyTypeEnum fromType(Integer type) {
        return TYPE_MAP.get(type);
    }

}
