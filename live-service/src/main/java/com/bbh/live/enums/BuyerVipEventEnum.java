package com.bbh.live.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/8/15 13:24
 */
@AllArgsConstructor
@Getter
public enum BuyerVipEventEnum {
    /**
     * 偷看买家  -
     */
    PEEP_BUYER("peep_buyer", false, "窥探出价人"),

    /**
     * 抽奖
     */
    LOTTERY("lottery", false, "抽奖"),


    /**
     * 修改昵称
     */
    MODIFY_NICKNAME("modify_nickname", false, "修改昵称"),


    /**
     * 销售售后服务
     */
    SALE_AFTER_SERVICE("sale_after_service", false, "售后服务"),


    /**
     * 年卡会员经验
     */
    ANNUAL_FEE_VIP_EXP("200", true, "年卡VIP"),

    /**
     * 买东西vip经验
     */
    BUY_EXP("1", true, "购买东西"),

    /**
     * 保证金解约
     */
    MARGIN_BREAK("margin_break", false, "保证金解约"),

    ;


    /**
     * 代码
     */
    private final String code;

    /**
     * true 加
     * false 减
     */
    private final Boolean flag;
    /**
     * desc
     */
    private final String desc;
}
