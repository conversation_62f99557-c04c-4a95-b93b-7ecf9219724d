package com.bbh.live.enums;

import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/8/15 11:02
 */
@Getter
@AllArgsConstructor
public enum BuyerVipTypeEnum {
    /**
     * 0 不是
     * 1 是 没有过期
     * 2 是 但是过期了
     */
    NO_VIP(0, "不是"),
    VIP(1, "是 没有过期"),
    VIP_EXPIRED(2, "是 但是过期了");

    @JsonValue
    private final Integer code;
    private final String desc;
}
