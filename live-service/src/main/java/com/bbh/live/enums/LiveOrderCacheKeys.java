package com.bbh.live.enums;

import com.bbh.live.constant.RedisKey;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 订单缓存
 *
 * <AUTHOR>
 */
@AllArgsConstructor
@Getter
public enum LiveOrderCacheKeys implements RedisKey {


    /***
     * 店铺信息红点
     */
    SHOP_CENTER_RED_POINT("shopCenterRedPoint"),
    /***
     * 个人信息红点
     */
    PERSONAL_CENTER_RED_POINT("personalCenterRedPoint"),

    ;

    private final String key;
}
