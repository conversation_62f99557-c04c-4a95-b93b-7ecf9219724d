package com.bbh.live.enums;


import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 */

@Getter
@AllArgsConstructor
public enum PermissionCheckTypeEnum {
    // 默认
    PERMISSION_CHECK_TYPE_DEFAULT(0, "", ""),
    // 全局
    PERMISSION_CHECK_TYPE_GLOBAL(1, "global_permission_if_all", "global_permission_code_list"),
    // ERP
    PERMISSION_CHECK_TYPE_ERP(2, "erp_permission_if_all", "erp_permission_code_list"),
    // 直播
    PERMISSION_CHECK_TYPE_LIVE(3, "live_permission_if_all", "live_permission_code_list");

    private final Integer type;

    private String ifAllPermissionField;

    private String permissionCodeListField;

}
