package com.bbh.live.enums;

import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/9/23 15:47
 * @description
 */
@Getter
public enum VipRightsTypeEnum {

    /**
     * 官方补偿
     */
    OFFICIAL_COMPENSATION(1, "官方补偿"),
    /**
     * 查看出价记录
     */
    PEEP(2, "出价记录"),
    /**
     * 昵称修改
     */
    NICKNAME_MODIFY(3, "昵称修改"),
    /**
     * 抽奖记录
     */
    LOTTERY(4, "抽奖记录"),
    ;

    @JsonValue
    private final Integer code;
    private final String desc;

    VipRightsTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
