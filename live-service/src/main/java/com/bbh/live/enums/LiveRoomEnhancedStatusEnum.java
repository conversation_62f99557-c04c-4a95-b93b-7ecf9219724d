package com.bbh.live.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 直播间状态：0-尚未开始、1-直播中、2-直播暂停中、3-直播已结束
 *
 * <AUTHOR>
 */
@AllArgsConstructor
@Getter
public enum LiveRoomEnhancedStatusEnum {


    /**
     * 尚未开始
     */
    WAITING(0),

    /**
     * 直播中
     */
    LIVING(1),

    /**
     * 直播暂停中
     */
    PAUSED(2),

    /**
     * 直播已结束，商品清单也展示结束了
     */
    COMPLETED(3),

    /**
     * 试播中
     */
    TEST_BROADCAST(4),

    UNKNOWN(-1)

    ;

    @EnumValue
    @JsonValue
    private final Integer code;
}
