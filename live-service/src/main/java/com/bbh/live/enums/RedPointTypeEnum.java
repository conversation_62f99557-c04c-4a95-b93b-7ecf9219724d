package com.bbh.live.enums;

/**
 * 订单未读红点
 *
 * <AUTHOR>
 */
public enum RedPointTypeEnum {

    /**
     * 采购收银台商品数量
     */
    purchaseCashierProductCount,


    /**
     * 采购收银台商品数量-个人收银台数量
     */
    individualPurchaseCashierProductCount,

    /**
     * 采购收银台商品数量-全店收银台数量
     */
    wholeShopPurchaseCashierProductCount,

    /**
     * 采购订单待付款数量
     */
    purchasePendingPaymentCount,

    /**
     * 采购线下转账待审核数量
     */
    purchaseOfflineTransferPendingReviewCount,

    /**
     * 采购待发货数量
     */
    purchasePendingShipmentCount,

    /**
     * 采购待收货数量
     */
    purchasePendingReceiptCount,

    /**
     * 采购退款售后数量
     */
    purchaseRefundAfterSalesCount,

    /**
     * 销售收银台商品数量
     */
    saleCashierProductCount,
    /**
     * 销售订单待付款数量
     */
    salePendingPaymentCount,
    /**
     * 销售线下转账待审核数量
     */
    saleOfflineTransferPendingReviewCount,

    /**
     * 销售待发货数量
     */
    salePendingShipmentCount,

    /**
     * 销售待收货数量
     */
    salePendingReceiptCount,

    /**
     * 销售退款售后数量
     */
    saleRefundAfterSalesCount,
    ;

}
