package com.bbh.live.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 席位在直播间的身份: 0-看播、1-导播、2-主播
 * <AUTHOR>
 */
@AllArgsConstructor
@Getter
public enum SeatRoleInRoomEnum {

    /**
     * 看播
     */
    WATCHER(0),

    /**
     * 导播
     */
    DIRECTOR(1),

    /**
     * 主播
     */
    ANCHOR(2),

    ;

    @EnumValue
    @JsonValue
    private final Integer code;

}
