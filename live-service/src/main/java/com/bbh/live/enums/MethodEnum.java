package com.bbh.live.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 直播流 接口枚举
 */
@AllArgsConstructor
@Getter
public enum MethodEnum {
    CREATE_LIVE_CONFIG("创建直播推流配置", "/live/api/createLiveStream","post"),

    GET_VIDEO_RECORD("获取直播间回放地址", "/live/api/getVideoRecord","post"),

    LIVE_FORBID("禁止推流", "/live/api/forbidLiveStream","post"),

    LIVE_RESUME("恢复推流", "/live/api/resumeLiveStream","post");


    private final String desc;
    private final String url;
    private final String httpMethod;
}
