package com.bbh.live.constant;

import com.bbh.live.core.msg.MsgType;
import com.bbh.live.util.EnvironmentUtils;

/**
 * <AUTHOR> dsy
 * @Date: 2024/9/9
 * @Description:
 */
public interface DelayQueueTopics {

    /** 商品竞拍时需要放入延时队列，以便到期时取出消费，这是竞拍时的topic，用于延时分组 */
    String AUCTION_TOPIC = EnvironmentUtils.getActiveProfile() + "-auctionTopic";

    /**
     * 虚拟订单放入延时队列的topic
     */
    String VIRTUAL_ORDER_TOPIC = EnvironmentUtils.getActiveProfile() + "-virtualGoodsOrderTopic";

    /**
     * 直播商品传送 剩余时间120s结束消费消息
     */
    String LIVE_TRANSFER_TOPIC = EnvironmentUtils.getActiveProfile() + MsgType.TRANSFER;


    String TEST_TOPIC = EnvironmentUtils.getActiveProfile() + "-testTopic";
}
