package com.bbh.live.constant;

import com.google.common.base.Joiner;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface RedisKey {

    /**
     * 所有缓存的统一前缀
     */
    String BASE_PREFIX = "live";

    /**
     * 统一的分隔符
     */
    String SEPARATOR = ":";

    default String buildKey(String... args) {
        if (args == null || args.length == 0) {
            return BASE_PREFIX;
        }
        List<String> joinList = new ArrayList<>(){{
            add(BASE_PREFIX);
            addAll(Arrays.asList(args));
        }};
        return Joiner.on(SEPARATOR).join(joinList);
    }

}
