package com.bbh.live.constant;

import java.math.BigDecimal;

/**
 * <AUTHOR> dsy
 * @Date: 2024/7/26
 * @Description:
 */
public interface LiveGoodsConstant {

    /**
     * 商品清单默认排序字段
     */
    String DEFAULT_SORT_FIELD = "sort";

    /**
     * 商品经常需要调整到待上架第一位，为了不调整后续商品的顺序，这里初始化时直接在商品拍号上加1000，调整到第一位时，只需要查第一个商品的排序 - 1即可
     */
    Integer SORT_BEGIN_VALUE = 1000;

    /**
     * 通道费：千分之六
     */
    BigDecimal CHANNEL_RATE = new BigDecimal("0.006");

    String STATE_MACHINE_EXCEPTION = "error";

    String STATE_MACHINE_CONTEXT = "context";

    String STATE_MACHINE_GOODS = "goods";

    String BARGAIN_MESSAGE_CONTENT = "%s号商品%s如何";

    String ASK_FOR_EXPLANATION_MESSAGE_CONTENT = "求讲解%s号商品";

    String OFFHAND_PUT_AWAY_REDIS_KEY = "offhand_putAway_key:{%d}";

    String SETTLE_LOCK_KEY = "live:goods_settle_lock:%d-%d";
}
