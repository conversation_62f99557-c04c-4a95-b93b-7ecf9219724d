package com.bbh.live.constant;

/**
 * <AUTHOR>
 * @date 2024/7/25 下午4:23
 */
public interface ProjectConstant {

    interface Environment {
        /**
         * 开发环境
         */
        String DEV = "dev";
        /**
         * 测试环境
         */
        String TEST = "test";
        /**
         * 生产环境
         */
        String PROD = "prod";
    }

    interface ErpGoodsLockStatus {
        /**
         * 0 正常
         */
        int NORMAL = 0;
        /**
         * 1 锁定
         */
        int LOCK = 1;
    }

    interface ErpGoodsSaleStatus {
        /**
         * 1 正常
         */
        int NORMAL = 1;
        /**
         * 2 已售出
         */
        int SOLD_OUT = 2;
    }

    interface GlobalOrgSeatStatus {
        /**
         * 1启用
         */
        int ENABLE = 1;
        /**
         * 2禁用
         */
        int DISABLE = 2;
    }

    interface BuyerVip {
        //过期前10天警告
        int VIP_EXPIRE_WARN_DAY = 10;
    }

    interface VirtualGoodsOrder {

        /**
         * 订单超时时间15分钟
         */
        Long ORDER_OVERTIME_TIME = 15 * 60 * 1000L;
    }

    interface Order {
        /** 订单超时未支付，加入延迟队列的TOPIC */
        String PAY_TIMEOUT_TOPIC = "payTimeoutTopic";
    }
}
