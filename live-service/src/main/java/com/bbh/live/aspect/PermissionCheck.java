package com.bbh.live.aspect;

import com.bbh.live.enums.PermissionCodeEnum;
import org.springframework.core.annotation.AliasFor;

import java.lang.annotation.*;

/**
 * <AUTHOR>
*/

@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Inherited
public @interface PermissionCheck {

    @AliasFor("permissionCode")
    PermissionCodeEnum value() default PermissionCodeEnum.LIVE_ALL_PERMISSION;

    @AliasFor("value")
    PermissionCodeEnum permissionCode() default PermissionCodeEnum.LIVE_ALL_PERMISSION;
}
