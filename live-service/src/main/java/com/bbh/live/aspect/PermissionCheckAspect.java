package com.bbh.live.aspect;

import cn.hutool.core.util.StrUtil;
import com.bbh.enums.ResponseCode;
import com.bbh.live.enums.PermissionCodeEnum;
import com.bbh.live.service.user.permission.PermissionCheckServiceFactory;
import com.bbh.vo.Result;
import lombok.RequiredArgsConstructor;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;

import java.util.ArrayList;

/**
 * <AUTHOR>
 */

@Aspect
@Component
@RequiredArgsConstructor
public class PermissionCheckAspect {

    @Around("@annotation(check)")
    public Object interceptor(ProceedingJoinPoint point, PermissionCheck check) throws Throwable {

        // 具体权限
        PermissionCodeEnum permissionCode = check.value() != null ? check.value() : check.permissionCode();

        if(permissionCode == null || permissionCode == PermissionCodeEnum.LIVE_ALL_PERMISSION){
            return point.proceed();
        }
        // 校验权限
        boolean hasPermission = PermissionCheckServiceFactory.getPermissionCheckService(permissionCode.getTypeEnum()).hasPermissionByPermissionCode(permissionCode);

        if (hasPermission) {
            return point.proceed();
        }

        String errorTips = StrUtil.isNotBlank(permissionCode.getErrorTips()) ? permissionCode.getErrorTips() : ResponseCode.NO_BUSINESS_PERMISSION.getMessage();
        return new Result(ResponseCode.NO_BUSINESS_PERMISSION.getCode(), errorTips, new ArrayList<>());
    }
}
