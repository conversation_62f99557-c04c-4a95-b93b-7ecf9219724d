package com.bbh.live.statemachine.persister;

import org.springframework.statemachine.StateMachineContext;
import org.springframework.statemachine.StateMachinePersist;
import org.springframework.statemachine.support.DefaultStateMachineContext;

import java.util.function.Function;

/**
 * <AUTHOR> dsy
 * @Date: 2024/7/24
 * @Description:
 */
public class CustomStateMachinePersist<S, E, T> implements StateMachinePersist<S, E, T> {

    public Function<T, S> statusFunction;
    public String stateMachineId;

    public CustomStateMachinePersist(Function<T, S> statusFunction, String stateMachineId) {
        this.statusFunction = statusFunction;
        this.stateMachineId = stateMachineId;
    }

    @Override
    public void write(StateMachineContext<S, E> context, T contextObj) throws Exception {}

    @Override
    public StateMachineContext<S, E> read(T contextObj) throws Exception {
        return new DefaultStateMachineContext<>(statusFunction.apply(contextObj), null, null, null, null, stateMachineId);
    }
}
