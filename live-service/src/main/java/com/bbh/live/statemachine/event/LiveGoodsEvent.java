package com.bbh.live.statemachine.event;

/**
 * <AUTHOR> dsy
 * @Date: 2024/7/24
 * @Description:
 */
public enum LiveGoodsEvent {

    /**
     * 完善商品
     */
    COMPLETE,
    /**
     * 重复完善商品
     */
    //REPEAT_COMPLETE,

    /**
     *  重回待上架列表，流拍重新上架| 主动撤回
     */
    RETURN_WAIT_PUT_AWAY,
    /**
     * 上架讲解
     */
    PUT_AWAY,
    /**
     * 开始竞拍
     */
    AUCTION,
    /**
     * 竞拍失败
     */
    AUCTION_FAIL,
    /**
     * 竞拍成功
     */
    AUCTION_SUCCESS,
    /**
     *  传送或议价成功
     */
    TRANSFER_OR_BID_SUCCESS,
    ;

}
