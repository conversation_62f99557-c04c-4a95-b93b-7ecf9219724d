package com.bbh.live.statemachine;

import cn.hutool.extra.spring.SpringUtil;
import com.bbh.enums.LiveGoodsStatusEnum;
import com.bbh.exception.ServiceException;
import com.bbh.live.statemachine.event.LiveGoodsEvent;
import com.bbh.live.statemachine.goods.LiveGoodsStateMachineCustomizer;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.statemachine.StateMachine;

import java.time.Duration;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR> dsy
 * @Date: 2024/7/25
 * @Description:
 */
@Slf4j
@SuppressWarnings("all")
public class StateMachineFactory {

    private static final ConcurrentHashMap<String, org.springframework.statemachine.config.StateMachineFactory<?,?>> STATE_MACHINE_FACTORY_CACHE_MAP = new ConcurrentHashMap<>();

    /**
     * 创建状态机比较复杂，这里把创建好的状态机缓存起来，1小时未使用就清理（后期可根据商品上架到结束时间确定过期时间）
     * 一般商品竞拍持续时间不会太长，在竞拍过程中流量比较大时只要能保证状态机可用即可
     */
    private static LoadingCache<Pair<String, String>, StateMachine> stateMachineCache = CacheBuilder.newBuilder()
            .maximumSize(10000)
            .softValues()
            .expireAfterAccess(Duration.ofHours(1))
            .build(new CacheLoader<Pair<String, String>, StateMachine>() {
                public StateMachine load(Pair<String, String> key) throws Exception {
                    return getStateMachine(key);
                }
            });

    public static StateMachine getStateMachine(Pair<String, String> pair){
        // 状态机
        String stateMachineId = pair.getLeft();
        String bizId = pair.getRight();

        StateMachine<?, ?> stateMachine = STATE_MACHINE_FACTORY_CACHE_MAP.get(stateMachineId).getStateMachine(pair.toString("%s_%s"));
        StateMachineCustomizer stateMachineCustomizer = getStateMachineCustomizer(stateMachineId);
        if(stateMachineCustomizer != null){
            stateMachine.getStateMachineAccessor().doWithRegion(stateMachineCustomizer::customize);
        }
        return stateMachine;
    }

    private static StateMachineCustomizer getStateMachineCustomizer(String stateMachineId) {
        return switch (stateMachineId) {
            case StateMachineIdConstants.LIVE_GOODS_STATEMACHINE_ID -> new LiveGoodsStateMachineCustomizer();
            default -> null;
        };
    }

    /**
     * 获取直播商品状态机，每个商品隔离开，防止多个商品竞拍时的交叉污染
     * @param bizId 商品id
     * @return
     */
    public static StateMachine<LiveGoodsStatusEnum, LiveGoodsEvent> getLiveGoodsStateMachine(String bizId){
        try{
            return stateMachineCache.get(Pair.of(StateMachineIdConstants.LIVE_GOODS_STATEMACHINE_ID, bizId));
        }catch (Exception e){
            log.error("获取状态机异常：{}", e.getMessage(), e);
            throw new ServiceException("获取状态机异常:" + e.getMessage());
        }
    }

    public static void register(String name, org.springframework.statemachine.config.StateMachineFactory<?,?> stateMachineFactory){
        STATE_MACHINE_FACTORY_CACHE_MAP.put(name, stateMachineFactory);
    }

    static {
        register(StateMachineIdConstants.LIVE_GOODS_STATEMACHINE_ID, SpringUtil.getBean("liveGoodsStateMachineFactory", org.springframework.statemachine.config.StateMachineFactory.class));
    }
}
