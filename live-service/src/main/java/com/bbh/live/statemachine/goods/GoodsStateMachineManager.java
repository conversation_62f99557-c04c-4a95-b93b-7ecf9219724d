package com.bbh.live.statemachine.goods;

import com.bbh.enums.LiveGoodsStatusEnum;
import com.bbh.exception.ServiceException;
import com.bbh.live.constant.LiveGoodsConstant;
import com.bbh.live.dao.dto.livegoods.LiveGoodsDTO;
import com.bbh.live.service.livegoods.context.LiveGoodsContext;
import com.bbh.live.statemachine.StateMachineFactory;
import com.bbh.live.statemachine.event.LiveGoodsEvent;
import com.bbh.service.RedisService;
import com.bbh.util.AssertUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.statemachine.StateMachine;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR> dsy
 * @Date: 2024/7/25
 * @Description:
 */
@Slf4j
@Component
@AllArgsConstructor
public class GoodsStateMachineManager<T extends LiveGoodsContext> implements IGoodsStateMachineManager<T> {

    private final RedisService redisService;
    /**
     * 商品状态流转加分布式锁的key
     */
    private static final String LIVE_GOODS_TRANSFER_LOCK_KEY = "live_goods_transfer_lock: %s";
    /**
     * 过期时间 5s
     */
    private static final int LOCK_TIMEOUT = 5;

    @Override
    public void complete(LiveGoodsDTO liveGoods, T context) {
        triggerTransition(buildMessage(LiveGoodsEvent.COMPLETE, liveGoods, context), liveGoods);
    }

    /*@Override
    public void repeatComplete(LiveGoodsDTO liveGoods, T context) {
        triggerTransition(buildMessage(LiveGoodsEvent.REPEAT_COMPLETE, liveGoods, context), liveGoods);
    }*/

    @Override
    public void putAway(LiveGoodsDTO liveGoods) {
        triggerTransition(buildMessage(LiveGoodsEvent.PUT_AWAY, liveGoods, null), liveGoods);
    }

    @Override
    public void auction(LiveGoodsDTO liveGoods) {
        triggerTransition(buildMessage(LiveGoodsEvent.AUCTION, liveGoods, null), liveGoods);
    }

    @Override
    public void trade(LiveGoodsDTO liveGoods, T context) {
        triggerTransition(buildMessage(LiveGoodsEvent.AUCTION_SUCCESS, liveGoods, context), liveGoods);
    }

    @Override
    public void auctionFail(LiveGoodsDTO liveGoods, T context) {
        triggerTransition(buildMessage(LiveGoodsEvent.AUCTION_FAIL, liveGoods, context), liveGoods);
    }

    @Override
    public void putAwayOff(LiveGoodsDTO liveGoods) {
        triggerTransition(buildMessage(LiveGoodsEvent.RETURN_WAIT_PUT_AWAY, liveGoods, null), liveGoods);
    }

    @Override
    public void abortiveAuctionGoodsReShelve(LiveGoodsDTO liveGoods) {
        triggerTransition(buildMessage(LiveGoodsEvent.RETURN_WAIT_PUT_AWAY, liveGoods, null), liveGoods);
    }

    @Override
    public void transferOrBidSuccess(LiveGoodsDTO liveGoods, T context) {
        triggerTransition(buildMessage(LiveGoodsEvent.TRANSFER_OR_BID_SUCCESS, liveGoods, context), liveGoods);
    }

    private Message<LiveGoodsEvent> buildMessage(LiveGoodsEvent event, LiveGoodsDTO liveGoods, T context) {
        return MessageBuilder
                .withPayload(event)
                .setHeader(LiveGoodsConstant.STATE_MACHINE_GOODS, liveGoods)
                .setHeader(LiveGoodsConstant.STATE_MACHINE_CONTEXT, context)
                .build();
    }

    private void triggerTransition(Message<LiveGoodsEvent> message, LiveGoodsDTO liveGoods) {
        StateMachine<LiveGoodsStatusEnum, LiveGoodsEvent> stateMachine = StateMachineFactory.getLiveGoodsStateMachine(liveGoods.getId().toString());
        // 加锁 同个商品同时只能一个人操作
        String lockKey = String.format(LIVE_GOODS_TRANSFER_LOCK_KEY, liveGoods.getId());
        AssertUtil.assertTrue(redisService.lock(lockKey, LOCK_TIMEOUT, TimeUnit.SECONDS), "商品正被其他用户操作，请稍后重试");
        // 启动状态机
        stateMachine.startReactively().block();
        try {
            //开始执行
            stateMachine.sendEvent(Mono.just(message))
                    .doOnNext(result -> {
                        StateMachineUtils.liveGoodsStateMachineResultCheck(stateMachine, result);
                        //执行过程中的异常信息（状态机不会抛异常，这里放到变量中手动抛出。返回前端，并回滚事务）
                        String exception = (String) stateMachine.getExtendedState().getVariables().get(LiveGoodsConstant.STATE_MACHINE_EXCEPTION);
                        AssertUtil.assertNull(exception, exception);
                    })
                    .onErrorResume(t -> {
                        log.error("直播商品状态流转异常：{}", t.getMessage(), t);
                        return Flux.error(new ServiceException(t.getMessage()));
                    })
                    .doFinally(signalType ->
                            // 清理缓存的错误信息
                            stateMachine.getExtendedState().getVariables().clear())
                    .blockFirst();
        } finally {
            stateMachine.stopReactively().block();
            redisService.unLock(lockKey);
        }
    }
}
