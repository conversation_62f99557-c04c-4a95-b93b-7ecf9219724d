package com.bbh.live.statemachine.goods.listener;

import com.bbh.live.constant.LiveGoodsConstant;
import com.bbh.live.dao.dto.livegoods.LiveGoodsDTO;
import com.bbh.live.service.livegoods.transfer.LiveGoodsTransferManager;
import com.bbh.live.statemachine.event.LiveGoodsEvent;
import lombok.AllArgsConstructor;
import org.springframework.messaging.Message;
import org.springframework.statemachine.annotation.OnTransitionEnd;
import org.springframework.statemachine.annotation.WithStateMachine;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR> dsy
 * @Date: 2024/7/24
 * @Description: 商品状态流转监听，可触发非主流程动作，比如发消息，同步其他系统信息
 */
@Component
@AllArgsConstructor
@WithStateMachine
public class GoodsStateListener {

    private static final String WAIT_PUT_AWAY = "WAIT_PUT_AWAY";
    private static final String PUT_AWAY = "PUT_AWAY";
    private static final String AUCTION = "AUCTION";
    private static final String ABORTIVE_AUCTION = "ABORTIVE_AUCTION";
    private static final String TRADED = "TRADED";

    private final List<LiveGoodsTransferManager.Listener> listeners;

    /**
     *  商品上架 发送预约消息
     * @param message
     * @return
     */
    @OnTransitionEnd(source = WAIT_PUT_AWAY, target = PUT_AWAY)
    public boolean waitPutAwayTransitionEnd(Message<LiveGoodsEvent> message) {
        LiveGoodsDTO liveGoods = getLiveGoods(message);

        for (LiveGoodsTransferManager.Listener listener : listeners){
            listener.waitPutAwayTransition(liveGoods);
        }
        return true;
    }

    /**
     * 商品上架或竞拍结束
     *      触发：关闭预约消息 清空缓存
     * @param message
     * @return
     */
    @OnTransitionEnd(source = PUT_AWAY, target = {TRADED, WAIT_PUT_AWAY})
    public boolean putAwayTransitionEnd(Message<LiveGoodsEvent> message) {
        LiveGoodsDTO liveGoods = getLiveGoods(message);
        for (LiveGoodsTransferManager.Listener listener : listeners){
            listener.putAwayTransitionEnd(liveGoods);
        }
        return true;
    }

    /**
     *  竞拍结束 流拍，成交。撤回
     *      触发： 清空竞拍商品缓存
     * @param message
     * @return
     */
    @OnTransitionEnd(source = AUCTION, target = {ABORTIVE_AUCTION, TRADED, WAIT_PUT_AWAY})
    public boolean auctionTransitionEnd(Message<LiveGoodsEvent> message) {
        LiveGoodsDTO liveGoods = getLiveGoods(message);
        for (LiveGoodsTransferManager.Listener listener : listeners){
            listener.auctionTransitionEnd(liveGoods);
        }
        return true;
    }

    /**
     *  商品撤回,重新上架, 到待上架状态
     *      触发：1.关闭预约消息
     *          2. 清空竞拍商品缓存
     * @param message
     * @return
     */
    @OnTransitionEnd(source = {PUT_AWAY, AUCTION, ABORTIVE_AUCTION}, target = WAIT_PUT_AWAY)
    public boolean reShelveTransitionEnd(Message<LiveGoodsEvent> message){
        LiveGoodsDTO liveGoods = getLiveGoods(message);
        for (LiveGoodsTransferManager.Listener listener : listeners){
            listener.reShelveTransition(liveGoods);
        }
        return true;
    }

    /**
     * 商品流转结束触发事件，商品结束流程
     * 1. 商品待上架 -> 传送成交
     * 2. 商品上架中 -> 传送成交
     * 3. 商品竞拍中 -> 竞拍成交
     * 4. 商品竞拍中 -> 流拍
     * 5. 商品下架中 -> 传送成交
     *
     *
     * @param message
     * @return
     */
    @OnTransitionEnd(source = {"WAIT_PUT_AWAY", "PUT_AWAY", "AUCTION", "ABORTIVE_AUCTION" }, target = {"ABORTIVE_AUCTION", "TRADED"})
    public boolean goodsTransferEndTransitionEnd(Message<LiveGoodsEvent> message){
        return true;
    }


    private LiveGoodsDTO getLiveGoods(Message<LiveGoodsEvent> message){
        return (LiveGoodsDTO) message.getHeaders().get(LiveGoodsConstant.STATE_MACHINE_GOODS);
    }
}
