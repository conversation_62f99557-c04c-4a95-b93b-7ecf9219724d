package com.bbh.live.statemachine.goods.action;

import cn.hutool.extra.spring.SpringUtil;
import com.bbh.enums.LiveGoodsStatusEnum;
import com.bbh.live.service.livegoods.transfer.LiveGoodsTransferManager;
import com.bbh.live.statemachine.event.LiveGoodsEvent;
import com.bbh.live.statemachine.goods.StateMachineUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.statemachine.StateContext;

/**
 * <AUTHOR> dsy
 * @Date: 2024/7/25
 * @Description: 直播商品流转时触发的动作
 */
@Slf4j
public class LiveGoodsActions {

    private final LiveGoodsTransferManager liveGoodsTransferManager = SpringUtil.getBean(LiveGoodsTransferManager.class);

    public void completeAction(StateContext<LiveGoodsStatusEnum, LiveGoodsEvent> stateContext) {
        StateMachineUtils.exec((goods, busContext) ->
                        liveGoodsTransferManager.action(LiveGoodsEvent.COMPLETE, goods, busContext),
                stateContext);
    }

    /*public void repeatCompleteAction(StateContext<LiveGoodsStatusEnum, LiveGoodsEvent> stateContext) {
        StateMachineUtils.exec((goods, busContext) ->
                        liveGoodsTransferManager.action(LiveGoodsEvent.REPEAT_COMPLETE, goods, busContext),
                stateContext);
    }*/

    public void putAwayAction(StateContext<LiveGoodsStatusEnum, LiveGoodsEvent> stateContext) {
        StateMachineUtils.exec((goods, busContext) ->
                        liveGoodsTransferManager.action(LiveGoodsEvent.PUT_AWAY, goods, busContext),
                stateContext);
    }

    public void auctionAction(StateContext<LiveGoodsStatusEnum, LiveGoodsEvent> stateContext) {
        StateMachineUtils.exec((goods, busContext) ->
                        liveGoodsTransferManager.action(LiveGoodsEvent.AUCTION, goods, busContext),
                stateContext);
    }

    public void abortiveAuctionAction(StateContext<LiveGoodsStatusEnum, LiveGoodsEvent> stateContext) {
        StateMachineUtils.exec((goods, busContext) ->
                        liveGoodsTransferManager.action(LiveGoodsEvent.AUCTION_FAIL, goods, busContext),
                stateContext);
    }

    public void tradeAction(StateContext<LiveGoodsStatusEnum, LiveGoodsEvent> stateContext) {
        StateMachineUtils.exec((goods, busContext) ->
                        liveGoodsTransferManager.action(LiveGoodsEvent.AUCTION_SUCCESS, goods, busContext),
                stateContext);
    }

    public void transferOrBidSuccessAction(StateContext<LiveGoodsStatusEnum, LiveGoodsEvent> stateContext) {
        StateMachineUtils.exec((goods, busContext) ->
                        liveGoodsTransferManager.action(LiveGoodsEvent.TRANSFER_OR_BID_SUCCESS, goods, busContext),
                stateContext);
    }

    public void returnWaitPutAwayAction(StateContext<LiveGoodsStatusEnum, LiveGoodsEvent> stateContext) {
        StateMachineUtils.exec((goods, busContext) ->
                        liveGoodsTransferManager.action(LiveGoodsEvent.RETURN_WAIT_PUT_AWAY, goods, busContext),
                stateContext);
    }
}

