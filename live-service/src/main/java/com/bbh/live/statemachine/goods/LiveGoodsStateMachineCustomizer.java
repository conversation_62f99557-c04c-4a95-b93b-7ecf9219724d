package com.bbh.live.statemachine.goods;

import com.bbh.enums.LiveGoodsStatusEnum;
import com.bbh.exception.ServiceException;
import com.bbh.live.constant.LiveGoodsConstant;
import com.bbh.live.dao.dto.livegoods.LiveGoodsDTO;
import com.bbh.live.statemachine.StateMachineCustomizer;
import com.bbh.live.statemachine.event.LiveGoodsEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.messaging.Message;
import org.springframework.statemachine.StateMachine;
import org.springframework.statemachine.StateMachineContext;
import org.springframework.statemachine.access.StateMachineAccess;
import org.springframework.statemachine.support.DefaultStateMachineContext;
import org.springframework.statemachine.support.StateMachineInterceptorAdapter;

/**
 * <AUTHOR>
 * @date 2024/10/31 23:18
 * @description
 */
@Slf4j
public class LiveGoodsStateMachineCustomizer implements StateMachineCustomizer<LiveGoodsStatusEnum, LiveGoodsEvent> {

    @Override
    public void customize(StateMachineAccess<LiveGoodsStatusEnum, LiveGoodsEvent> stateMachine) {
        stateMachine.addStateMachineInterceptor(new LiveGoodsStateMachineInterceptor());
    }

    private static class LiveGoodsStateMachineInterceptor extends StateMachineInterceptorAdapter<LiveGoodsStatusEnum, LiveGoodsEvent> {
        @Override
        public Message<LiveGoodsEvent> preEvent(Message<LiveGoodsEvent> message, StateMachine<LiveGoodsStatusEnum, LiveGoodsEvent> stateMachine) {
            LiveGoodsDTO liveGoods = (LiveGoodsDTO) message.getHeaders().get(LiveGoodsConstant.STATE_MACHINE_GOODS);
            if(liveGoods == null){
                throw new ServiceException("商品不存在，状态机恢复失败");
            }
            try {
                //spring状态机是有状态的，所以直播商品使用状态机执行流程时，需要先还原状态机当前状态
                StateMachineContext<LiveGoodsStatusEnum, LiveGoodsEvent> context = new DefaultStateMachineContext<>(liveGoods.getGoodsStatus(), null, null, null, null, stateMachine.getId());
                stateMachine.getStateMachineAccessor().doWithRegion(function -> function.resetStateMachineReactively(context).block());
            }catch (Exception e){
                log.error("直播商品状态机状态恢复异常，商品id：{}, error:{}", liveGoods.getId(), e.getMessage(), e);
                throw new ServiceException("商品状态异常");
            }
            return super.preEvent(message, stateMachine);
        }
    }
}
