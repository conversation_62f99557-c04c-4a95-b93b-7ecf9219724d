package com.bbh.live.statemachine.goods;

import com.bbh.live.dao.dto.livegoods.LiveGoodsDTO;
import com.bbh.live.dao.dto.livegoods.LiveGoodsPutAwayDTO;

/**
 * <AUTHOR> dsy
 * @Date: 2024/7/25
 * @Description:
 */
public interface IGoodsStateMachineManager<T> {

    /**
     * 完善商品
     * @param liveGoods
     * @param context 商品完善所需上下文，可以是前端传来，也可以自己组装 目前是 {@link LiveGoodsPutAwayDTO}
     */
    void complete(LiveGoodsDTO liveGoods, T context);

    /**
     * 重复完善商品
     * @param liveGoods
     * @param context 商品完善所需上下文，可以是前端传来，也可以自己组装
     */
    /*void repeatComplete(LiveGoodsDTO liveGoods, T context);*/

    /**
     * 上架商品
     * @param liveGoods
     */
    void putAway(LiveGoodsDTO liveGoods);

    /**
     * 竞拍商品
     * @param liveGoods
     */
    void auction(LiveGoodsDTO liveGoods);

    /**
     * 竞拍成功 成交
     * @param liveGoods
     * @param context 商品成交所需上下文，可以是前端传来，也可以自己组装
     */
    void trade(LiveGoodsDTO liveGoods, T context);

    /**
     * 竞拍失败
     * @param liveGoods
     * @param context 商品流拍所需上下文，可以是前端传来，也可以自己组装
     */
    void auctionFail(LiveGoodsDTO liveGoods, T context);

    /**
     * 下架 撤回
     * @param liveGoods
     */
    void putAwayOff(LiveGoodsDTO liveGoods);

    /**
     *  流拍商品重新上架
     * @param liveGoods
     */
    void abortiveAuctionGoodsReShelve(LiveGoodsDTO liveGoods);

    /**
     * 传送或议价成功
     * @param liveGoods
     */
    void transferOrBidSuccess(LiveGoodsDTO liveGoods, T context);
}
