package com.bbh.live.statemachine.goods;

import com.bbh.enums.LiveGoodsStatusEnum;
import com.bbh.live.statemachine.StateMachineIdConstants;
import com.bbh.live.statemachine.event.LiveGoodsEvent;
import com.bbh.live.statemachine.goods.action.LiveGoodsActions;
import com.bbh.live.statemachine.goods.guard.LiveGoodsGuards;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.BeanFactory;
import org.springframework.beans.factory.BeanFactoryAware;
import org.springframework.context.annotation.Configuration;
import org.springframework.statemachine.config.EnableStateMachineFactory;
import org.springframework.statemachine.config.EnumStateMachineConfigurerAdapter;
import org.springframework.statemachine.config.builders.StateMachineConfigurationConfigurer;
import org.springframework.statemachine.config.builders.StateMachineStateConfigurer;
import org.springframework.statemachine.config.builders.StateMachineTransitionConfigurer;

import java.util.EnumSet;

/**
 * <AUTHOR> dsy
 * @Date: 2024/7/24
 * @Description:
 *
 *
 *                                                | ------------主动撤回----------------------|
 *                                                |                    |                    |
 *                                                ↓                    |                    |
 *                                            +-------+    上架     +-------+   开始竞拍  +-------+  无人出价    +-------+
 *                                            | 待上架 |----------->| 上架中 |----------->| 竞拍中 |----------->| 已流拍 |
 *                                            +-------+            +-------+           +-------+            +-------+
 *                                               |↑                    |                   |      重新上架        |
 *                                               ||-------------------|------------------- | ------------------- |
 *                                               |                    |           最高价    |                     |
 *                                               |                    |          --------- |                     |
 *                                               |                    |          ↓                               |
 *                                               |     传送|议价 成交   |     +-------+          传送|议价 成交      |
 *                                               ------------------------> | 已成交 | <---------------------------|
 *                                                                         +-------+
 *
 *
 */
@Configuration
@EnableStateMachineFactory(name = "liveGoodsStateMachineFactory")
public class LiveGoodsStateMachineConfig extends EnumStateMachineConfigurerAdapter<LiveGoodsStatusEnum, LiveGoodsEvent> implements BeanFactoryAware {

    private BeanFactory beanFactory;

    @Override
    public void configure(StateMachineConfigurationConfigurer<LiveGoodsStatusEnum, LiveGoodsEvent> config) throws Exception {
        config
                .withConfiguration()
                .machineId(StateMachineIdConstants.LIVE_GOODS_STATEMACHINE_ID)
                .beanFactory(beanFactory);
    }

    //定义初始状态
    @Override
    public void configure(StateMachineStateConfigurer<LiveGoodsStatusEnum, LiveGoodsEvent> states) throws Exception {
        states
                .withStates()
                .initial(LiveGoodsStatusEnum.WAIT_PUT_AWAY)
                .states(EnumSet.allOf(LiveGoodsStatusEnum.class));
    }

    @Override
    public void configure(StateMachineTransitionConfigurer<LiveGoodsStatusEnum, LiveGoodsEvent> transitions)
            throws Exception {
        LiveGoodsGuards liveGoodsGuards = new LiveGoodsGuards();
        LiveGoodsActions liveGoodsActions = new LiveGoodsActions();
        transitions
                /**  主流程  */
                /** 商品上架 */
                .withExternal()
                    .source(LiveGoodsStatusEnum.WAIT_PUT_AWAY)
                    .target(LiveGoodsStatusEnum.PUT_AWAY)
                    .event(LiveGoodsEvent.PUT_AWAY)
                    .guard(liveGoodsGuards::putAwayGuard)
                    .action(liveGoodsActions::putAwayAction)
                .and()
                /** 开始竞拍 */
                .withExternal()
                    .source(LiveGoodsStatusEnum.PUT_AWAY)
                    .target(LiveGoodsStatusEnum.AUCTION)
                    .event(LiveGoodsEvent.AUCTION)
                    .guard(liveGoodsGuards::auctionGuard)
                    .action(liveGoodsActions::auctionAction)
                .and()
                /** 流拍 */
                .withExternal()
                    .source(LiveGoodsStatusEnum.AUCTION)
                    .target(LiveGoodsStatusEnum.ABORTIVE_AUCTION)
                    .event(LiveGoodsEvent.AUCTION_FAIL)
                    .guard(liveGoodsGuards::abortiveAuctionGuard)
                    .action(liveGoodsActions::abortiveAuctionAction)
                .and()
                /** 成交 */
                .withExternal()
                    .source(LiveGoodsStatusEnum.AUCTION)
                    .target(LiveGoodsStatusEnum.TRADED)
                    .event(LiveGoodsEvent.AUCTION_SUCCESS)
                    .guard(liveGoodsGuards::tradeGuard)
                    .action(liveGoodsActions::tradeAction)
                .and()
                /**
                 * 旁支流程
                 */
                /** 商品下架撤回，上架中和竞拍中可撤回商品 撤回到待上架列表 */
                .withExternal()
                    .source(LiveGoodsStatusEnum.PUT_AWAY)
                    .target(LiveGoodsStatusEnum.WAIT_PUT_AWAY)
                    .event(LiveGoodsEvent.RETURN_WAIT_PUT_AWAY)
                    .guard(liveGoodsGuards::returnWaitPutAwayGuard)
                    .action(liveGoodsActions::returnWaitPutAwayAction)
                .and()
                .withExternal()
                    .source(LiveGoodsStatusEnum.AUCTION)
                    .target(LiveGoodsStatusEnum.WAIT_PUT_AWAY)
                    .event(LiveGoodsEvent.RETURN_WAIT_PUT_AWAY)
                    .guard(liveGoodsGuards::returnWaitPutAwayGuard)
                    .action(liveGoodsActions::returnWaitPutAwayAction)
                .and()
                /** 待上架商品直接传送或议价后成交 **/
                .withExternal()
                    .source(LiveGoodsStatusEnum.WAIT_PUT_AWAY)
                    .target(LiveGoodsStatusEnum.TRADED)
                    .event(LiveGoodsEvent.TRANSFER_OR_BID_SUCCESS)
                    .guard(liveGoodsGuards::transferOrBidSuccessGuard)
                    .action(liveGoodsActions::transferOrBidSuccessAction)
                .and()
                /** 上架中商品直接传送或议价后成交 **/
                .withExternal()
                    .source(LiveGoodsStatusEnum.PUT_AWAY)
                    .target(LiveGoodsStatusEnum.TRADED)
                    .event(LiveGoodsEvent.TRANSFER_OR_BID_SUCCESS)
                    .guard(liveGoodsGuards::transferOrBidSuccessGuard)
                    .action(liveGoodsActions::transferOrBidSuccessAction)
                .and()
                /** 流拍商品直接传送或议价后成交 **/
                .withExternal()
                    .source(LiveGoodsStatusEnum.ABORTIVE_AUCTION)
                    .target(LiveGoodsStatusEnum.TRADED)
                    .event(LiveGoodsEvent.TRANSFER_OR_BID_SUCCESS)
                    .guard(liveGoodsGuards::transferOrBidSuccessGuard)
                    .action(liveGoodsActions::transferOrBidSuccessAction)
                .and()
                /** 流拍商品重新上架 */
                .withExternal()
                    .source(LiveGoodsStatusEnum.ABORTIVE_AUCTION)
                    .target(LiveGoodsStatusEnum.WAIT_PUT_AWAY)
                    .event(LiveGoodsEvent.RETURN_WAIT_PUT_AWAY)
                    .guard(liveGoodsGuards::returnWaitPutAwayGuard)
                    .action(liveGoodsActions::returnWaitPutAwayAction);

        /** 内部状态流转 */
        /** 完善商品 */
        transitions
                .withInternal()
                .source(LiveGoodsStatusEnum.WAIT_PUT_AWAY)
                .event(LiveGoodsEvent.COMPLETE)
                .guard(liveGoodsGuards::completeGuard)
                .action(liveGoodsActions::completeAction)
                ;
        /** 一口价商品 上架中修改商品价格 */
        transitions
                .withInternal()
                .source(LiveGoodsStatusEnum.PUT_AWAY)
                .event(LiveGoodsEvent.COMPLETE)
                .guard(liveGoodsGuards::completeGuard)
                .action(liveGoodsActions::completeAction)
        ;
    }

    @Override
    public void setBeanFactory(BeanFactory beanFactory) throws BeansException {
        this.beanFactory = beanFactory;
    }
}
