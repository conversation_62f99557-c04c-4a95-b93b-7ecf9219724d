package com.bbh.live.statemachine.goods;

import cn.hutool.core.exceptions.ExceptionUtil;
import com.bbh.enums.LiveGoodsStatusEnum;
import com.bbh.exception.ServiceException;
import com.bbh.live.constant.LiveGoodsConstant;
import com.bbh.live.dao.dto.livegoods.LiveGoodsDTO;
import com.bbh.live.statemachine.event.LiveGoodsEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.messaging.Message;
import org.springframework.statemachine.StateContext;
import org.springframework.statemachine.StateMachine;
import org.springframework.statemachine.StateMachineEventResult;
import org.springframework.statemachine.state.State;
import org.springframework.util.ObjectUtils;

import java.util.Map;
import java.util.function.BiConsumer;

/**
 * <AUTHOR> dsy
 * @Date: 2024/7/29
 * @Description:
 */
@Slf4j
public class StateMachineUtils {

    public static boolean exec(BiConsumer<LiveGoodsDTO, Object> consumer, StateContext<LiveGoodsStatusEnum, LiveGoodsEvent> context){
        Map<Object, Object> variables = context.getStateMachine().getExtendedState().getVariables();

        //如果已有异常信息，直接返回不执行
        if(variables.containsKey(LiveGoodsConstant.STATE_MACHINE_EXCEPTION)){
            return false;
        }
        try {
            LiveGoodsDTO liveGoods = getLiveGoods(context);
            Object busContext = context.getMessage().getHeaders().get(LiveGoodsConstant.STATE_MACHINE_CONTEXT);
            consumer.accept(liveGoods, busContext);
            return true;
        }catch (Throwable e){
            String causeMessage = e instanceof ServiceException ? e.getMessage() : ExceptionUtil.getRootCauseMessage(e);
            log.error("直播商品状态流转异常：{}", causeMessage, e);
            variables.put(LiveGoodsConstant.STATE_MACHINE_EXCEPTION, causeMessage);
            return false;
        }
    }


    private static LiveGoodsDTO getLiveGoods(StateContext<LiveGoodsStatusEnum, LiveGoodsEvent> stateContext){
        return (LiveGoodsDTO) stateContext.getMessage().getHeaders().get(LiveGoodsConstant.STATE_MACHINE_GOODS);
    }


    public static void liveGoodsStateMachineResultCheck(StateMachine<LiveGoodsStatusEnum, LiveGoodsEvent> stateMachine, StateMachineEventResult<LiveGoodsStatusEnum, LiveGoodsEvent> stateMachineEventResult){
        //执行成功
        if(StateMachineEventResult.ResultType.ACCEPTED.equals(stateMachineEventResult.getResultType())){
            return;
        }
        //执行失败，检查状态是否正确
        State<LiveGoodsStatusEnum, LiveGoodsEvent> currentStatus = stateMachine.getState();
        Message<LiveGoodsEvent> message = stateMachineEventResult.getMessage();
        stateMachine.getTransitions().stream()
                .filter(transition -> transition.getTrigger() != null)
                /** 判断当前商品状态可流转的选择，比如：商品当前状态为上架状态，可流转到竞拍状态，或者成交状态，或者待上架状态。在LiveGoodsStateMachineConfig中配置 */
                .filter(transition -> org.springframework.statemachine.support.StateMachineUtils.containsAtleastOne(transition.getSource().getIds(), currentStatus.getIds()))
                /** 判断当前商品状态可流转的事件，比如：商品当前状态为上架状态，只有
                 * AUCTION事件才可流转到竞拍状态，T
                 * TRANSFER_OR_BID_SUCCESS事件转到成交状态，
                 * RETURN_WAIT_PUT_AWAY事件转到待上架状态。
                 * 在LiveGoodsStateMachineConfig中配置 */
                .filter(transition -> ObjectUtils.nullSafeEquals(transition.getTrigger().getEvent(), message.getPayload()))
                .findFirst()
                .ifPresentOrElse(transition -> {}, () -> {
                    /** 没有可流转的节点，抛出异常 */
                    throw new ServiceException("商品" + liveGoodsStatusMsg(currentStatus.getId()) + ",不能" + liveGoodsEventMsg(message.getPayload()) + ",请刷新列表后重试");
                });

    }

    private static String liveGoodsStatusMsg(LiveGoodsStatusEnum status){
        return switch (status){
            case WAIT_PUT_AWAY -> "待上架";
            case PUT_AWAY -> "上架中";
            case AUCTION -> "竞拍中";
            case ABORTIVE_AUCTION -> "已流拍";
            case TRADED -> "已成交";
            default -> "状态异常";
        };
    }

    private static String liveGoodsEventMsg(LiveGoodsEvent event){
        return switch (event){
            case COMPLETE -> "完善商品";
            case RETURN_WAIT_PUT_AWAY -> "撤回或上架";
            case PUT_AWAY -> "上架讲解";
            case AUCTION -> "开始竞拍";
            case AUCTION_FAIL -> "竞拍失败";
            case AUCTION_SUCCESS -> "竞拍成功";
            case TRANSFER_OR_BID_SUCCESS -> "成交";
        };
    }
}


