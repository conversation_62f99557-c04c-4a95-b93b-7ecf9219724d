package com.bbh.live.statemachine.goods.guard;

import cn.hutool.extra.spring.SpringUtil;
import com.bbh.enums.LiveGoodsStatusEnum;
import com.bbh.live.service.livegoods.transfer.LiveGoodsTransferManager;
import com.bbh.live.statemachine.event.LiveGoodsEvent;
import com.bbh.live.statemachine.goods.StateMachineUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.statemachine.StateContext;

/**
 * <AUTHOR> dsy
 * @Date: 2024/7/25
 * @Description: 直播商品状态流转前置校验, 返回false流程终止
 */
@Slf4j
public class LiveGoodsGuards {

    private LiveGoodsTransferManager liveGoodsTransferManager = SpringUtil.getBean(LiveGoodsTransferManager.class);

    public boolean completeGuard(StateContext<LiveGoodsStatusEnum, LiveGoodsEvent> stateContext) {
        return StateMachineUtils.exec((liveGoods, context) ->
                liveGoodsTransferManager.check(LiveGoodsEvent.COMPLETE, liveGoods, context),
                stateContext);
    }

    /*public boolean repeatCompleteGuard(StateContext<LiveGoodsStatusEnum, LiveGoodsEvent> stateContext) {
        return StateMachineUtils.exec((liveGoods, context) ->
                        liveGoodsTransferManager.check(LiveGoodsEvent.REPEAT_COMPLETE, liveGoods, context),
                stateContext);
    }*/

    public boolean putAwayGuard(StateContext<LiveGoodsStatusEnum, LiveGoodsEvent> stateContext) {
        return StateMachineUtils.exec((liveGoods, context) ->
                liveGoodsTransferManager.check(LiveGoodsEvent.PUT_AWAY, liveGoods, context),
                stateContext);
    }

    public boolean auctionGuard(StateContext<LiveGoodsStatusEnum, LiveGoodsEvent> stateContext) {
        return StateMachineUtils.exec((liveGoods, context) ->
                liveGoodsTransferManager.check(LiveGoodsEvent.AUCTION, liveGoods, context),
                stateContext);
    }

    public boolean abortiveAuctionGuard(StateContext<LiveGoodsStatusEnum, LiveGoodsEvent> stateContext) {
        return StateMachineUtils.exec((liveGoods, context) ->
                liveGoodsTransferManager.check(LiveGoodsEvent.AUCTION_FAIL, liveGoods, context),
                stateContext);
    }


    public boolean tradeGuard(StateContext<LiveGoodsStatusEnum, LiveGoodsEvent> stateContext) {
        return StateMachineUtils.exec((liveGoods, context) ->
                liveGoodsTransferManager.check(LiveGoodsEvent.AUCTION_SUCCESS, liveGoods, context),
                stateContext);
    }

    public boolean returnWaitPutAwayGuard(StateContext<LiveGoodsStatusEnum, LiveGoodsEvent> stateContext) {
        return StateMachineUtils.exec((liveGoods, context) ->
                liveGoodsTransferManager.check(LiveGoodsEvent.RETURN_WAIT_PUT_AWAY, liveGoods, context),
                stateContext);
    }

    public boolean transferOrBidSuccessGuard(StateContext<LiveGoodsStatusEnum, LiveGoodsEvent> stateContext) {
        return StateMachineUtils.exec((liveGoods, context) ->
                liveGoodsTransferManager.check(LiveGoodsEvent.TRANSFER_OR_BID_SUCCESS, liveGoods, context),
                stateContext);
    }
}
