package com.bbh.live.config;

import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.BlockAttackInnerInterceptor;
import com.bbh.config.CustomPaginationInnerInterceptor;
import com.bbh.interceptor.SqlLogInterceptor;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2023/12/20 15:13
 */
@Configuration
@MapperScan(basePackages = {"com.bbh.live.*.mapper"}, nameGenerator = LiveBeanNameGenerator.class)
public class MybatisPlusConfig {
    @Bean
    public MybatisPlusInterceptor mybatisPlusInterceptor() {
        MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();
        // 防全表更新与删除插件
        interceptor.addInnerInterceptor(new BlockAttackInnerInterceptor());
        // 如果配置多个插件,切记分页最后添加
        interceptor.addInnerInterceptor(new CustomPaginationInnerInterceptor(DbType.MYSQL));
        // 数据变更记录插件
        interceptor.addInnerInterceptor(dataChangelogMybatisInterceptor());
        return interceptor;
    }

    @Bean
    public SqlLogInterceptor sqlLogInterceptor() {
        return new SqlLogInterceptor();
    }

    @Bean
    public DataChangelogMybatisInterceptor dataChangelogMybatisInterceptor() {
        DataChangelogMybatisInterceptor interceptor = new DataChangelogMybatisInterceptor();
        interceptor.setTableName("live_goods", "live_room");
        return interceptor;
    }

}
