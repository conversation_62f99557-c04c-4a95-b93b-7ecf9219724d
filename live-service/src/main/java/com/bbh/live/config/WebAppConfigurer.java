package com.bbh.live.config;

import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * <AUTHOR> dsy
 * @Date: 2024/7/29
 * @Description:
 */
@Component
@AllArgsConstructor
public class WebAppConfigurer implements WebMvcConfigurer {

    private final LiveRoomHandlerInterceptor liveRoomHandlerInterceptor;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(liveRoomHandlerInterceptor)
                // 买手的商品相关
                .addPathPatterns("/buyer/liveGoods/**")
                // 导播的商品相关
                .addPathPatterns("/director/liveGoods/**")
                // 传送要校验保证金
                .addPathPatterns("/goodsTransfer/add")
                ;
    }
}
