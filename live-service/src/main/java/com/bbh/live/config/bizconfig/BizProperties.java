package com.bbh.live.config.bizconfig;

import org.springframework.core.annotation.AliasFor;
import org.springframework.stereotype.Component;

import java.lang.annotation.*;

/**
 *
 * 对应的biz_key为{@code live_test_a} <br>
 * 首先获取{@code BizProperties} 的 {@code value}<br>
 * 其次把字段转换成小写加驼峰的格式，再前面拼接注解的value，最终就是biz_key，如:<br>
 * {@link com.bbh.live.config.LiveBizProperties }
 * 其中testA对应的biz_key就是live_test_a <br>
 *
 * <AUTHOR>
 */
@Target({ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Inherited
@Component
public @interface BizProperties {

    @AliasFor("prefix")
    String value() default "";

    @AliasFor("value")
    String prefix() default "";

}
