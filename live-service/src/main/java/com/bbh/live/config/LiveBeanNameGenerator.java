package com.bbh.live.config;

import cn.hutool.core.util.StrUtil;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.beans.factory.support.BeanNameGenerator;
import org.springframework.context.annotation.AnnotationBeanNameGenerator;
import org.springframework.util.ClassUtils;

import java.beans.Introspector;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/9/25 15:09
 * @description
 */
public class LiveBeanNameGenerator extends AnnotationBeanNameGenerator implements BeanNameGenerator {
    private final static String BEAN_PREFIX = "live";

    @Override
    protected @NotNull String buildDefaultBeanName(BeanDefinition definition) {
        String shortClassName = ClassUtils.getShortName(Objects.requireNonNull(definition.getBeanClassName()));

        String suffix = "Impl";
        if (shortClassName.endsWith(suffix)){
            shortClassName = shortClassName.substring(0, shortClassName.length() - suffix.length());
        }
        if(StrUtil.startWithIgnoreCase(shortClassName, BEAN_PREFIX)){
            return Introspector.decapitalize(shortClassName);
        }else {
            return BEAN_PREFIX + shortClassName;
        }
    }
}
