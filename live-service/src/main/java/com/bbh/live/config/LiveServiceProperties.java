package com.bbh.live.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 直播服务接口相关配置
 *
 * <AUTHOR>
 */
@ConfigurationProperties(prefix = "bbh.live-service")
@Data
@RefreshScope
public final class LiveServiceProperties {

    /**
     * 直播服务的地址
     */
    private String liveServiceUrl;

    private String serviceCenterUrl;

    private Integer timeOut = 30000;

    private String bbhMerchantNo;

    private String salt;

    /** 普通商品订单支付回调地址 */
    private String orderPayNotifyUrl;

    /** 虚拟商品订单支付回调地址 */
    private String virtualOrderPayNotifyUrl;

    /**
     * 直播间消息的倒计时时长
     */
    private Integer messageCountdownTime = 120;

    /**
     * 商品的默认竞拍时长 单位秒
     */
    private Integer defaultAuctionDuration = 30;

    /**
     * 商品的默认加价幅度
     */
    private Integer defaultIncreasePrice = 100;

    /**
     * 拍品录像回放缓冲时间
     * 前后5分钟
     */
    private Integer goodsVideoCatchTime = 5;

    /**
     * 试播客服席位id集合
     */
    private List<Long> liveRoomTestSeatIdList = new ArrayList<>();

    /** 用户进入直播间通知消息的发送时间间隔控制: seconds */
    private Integer userEnterLiveRoomMsgInterval = 60;

    /** 直播强制关播前的倒计时: seconds */
    private Integer countdownBeforeLiveEnd = 180;

    /** 启用模拟支付，开启后不再往中台创建订单，直接成功 */
    private Boolean enableMockPay = false;

    /** 是否校验采购权限 */
    private Boolean checkBuyerRightsPermission = true;

    /** 是否校验销售权限 */
    private Boolean checkSellerRightsPermission = true;

    /** 虚拟支付，使用公共的商户号  */
    private String platMerchantNo = "0000029711";

    /** 出价要求的最低保证金 */
    private BigDecimal minBidDeposit = new BigDecimal(100);

    /** 默认的普通用户的分贝抵扣数量 */
    private Integer defaultFenbeiDeductNum = 50;

    /** 默认的直播间背景图 */
    private String defaultLiveRoomDuringCoverImgUrl = "https://bbh-live.oss-cn-beijing.aliyuncs.com/live-room-bg.jpg";

    /** 默认的自定义表情Tab图标 */
    private String defaultCustomMemeTabIcon = "https://bang-file.oss-cn-shanghai.aliyuncs.com/htb/htb-icon-v4/custom-meme-class-icon.png";

    public String getOrderPayNotifyUrl() {
        return orderPayNotifyUrl != null ? orderPayNotifyUrl : liveServiceUrl + "/order/pay/notify";
    }

    public String getVirtualOrderPayNotifyUrl() {
        return virtualOrderPayNotifyUrl != null ? virtualOrderPayNotifyUrl : liveServiceUrl + "/virtualGoods/payCallback";
    }

    /**
     * 秒杀的持续时间，单位毫秒
     */
    private Long secKillDuration = 1000L;

    /** 商品清单是否使用wgt */
    private Boolean ifSwitchWgtGoodsList = false;

    /** 买家申请取消成交，商家自动通过的超时时间，单位秒: 24小时 */
    private Integer goodsBuyerCancelApplyAutoTimeoutDuration = 24 * 60 * 60;

    /** 是否发送进入聊天室消息 */
    private Boolean ifSendUserEnterMsg = true;

    /** 同步云展弹框倒计时秒数 */
    private Integer syncSceSeconds = 10;

    /** 全局是否关闭同步云展弹框标记 */
    private Boolean syncSceFlag = false;

    /** 自动同步基础分钟数 */
    private Integer autoSyncScePutawayTime = 10;

    /**
     * 同步新云展标签
     */
    private String   sceGoodsLabels;

    /**
     * 进入直播间发的系统公告 等待时间（秒）
     */
    private Long systemNoticeWaitSeconds = 5 * 60L;

}
