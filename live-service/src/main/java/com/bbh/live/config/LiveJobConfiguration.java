package com.bbh.live.config;

import com.bbh.live.service.buyer.vip.BuyerVipService;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.FilterType;
import org.springframework.context.annotation.Import;

/**
 * <AUTHOR>
 * @date 2024/9/26 09:50
 * @description job 使用的spring配置
 */
@ComponentScan(basePackages = {"com.bbh.live.config", "com.bbh.live.service.msg", "com.bbh.live.core", "com.bbh.live.dao.service"}, nameGenerator = LiveBeanNameGenerator.class)
@ComponentScan(basePackages = {"com.bbh.live.service.buyer.vip"}, nameGenerator = LiveBeanNameGenerator.class,
        includeFilters = {@ComponentScan.Filter(type = FilterType.ASSIGNABLE_TYPE, classes = {
                BuyerVipService.class
        })
}, useDefaultFilters = false)
@EnableConfigurationProperties({LiveServiceProperties.class})
@Import(MybatisPlusConfig.class)
public class LiveJobConfiguration {
}
