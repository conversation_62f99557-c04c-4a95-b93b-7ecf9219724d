package com.bbh.live.config;

import lombok.Data;

import java.util.List;

/**
 *
 * <AUTHOR>
 */
@FunctionalInterface
public interface ChangelogCommiter {

    void commit(CommitResult commitResult);

    @Data
    public static class CommitResult {
        private String operation;
        private Boolean recordStatus;
        private String tableName;
        private List<ChangedData> changedData;
        private Long cost;

        @Data
        public static class ChangedData {
            private String pkColumnName;
            private Object pkColumnVal;
            private List<OriginalColumnData> originalColumnDatas;
            private List<UpdatedColumn> updatedColumns;

            @Data
            public static class OriginalColumnData {
                private String columnName;
                private String originalValue;
                private String updateValue;
            }

            @Data
            public static class UpdatedColumn {
                private String columnName;
                private String originalValue;
                private String updateValue;
            }
        }
    }

}
