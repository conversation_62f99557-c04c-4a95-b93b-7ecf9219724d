package com.bbh.live.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 直播服务接口相关配置
 */
@ConfigurationProperties(prefix = "bbh.cloud-exhibition")
@Data
@RefreshScope
public final class CloudExhibitionProperties {


    /** 买家申请取消成交，商家自动通过的超时时间，单位秒: 24小时 */
    private Integer goodsBuyerCancelApplyAutoTimeoutDuration = 24 * 60 * 60;


}
