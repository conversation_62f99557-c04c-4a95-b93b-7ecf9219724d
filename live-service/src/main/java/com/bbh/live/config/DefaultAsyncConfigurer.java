package com.bbh.live.config;

import com.bbh.live.thread.ThreadPoolManager;
import com.bbh.util.LogExUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.aop.interceptor.AsyncUncaughtExceptionHandler;
import org.springframework.scheduling.annotation.AsyncConfigurer;
import org.springframework.stereotype.Component;

import java.util.concurrent.Executor;

/**
 * 配置默认的async线程池
 * <AUTHOR>
 */
@Slf4j
@Component
public class DefaultAsyncConfigurer implements AsyncConfigurer {

    @Override
    public Executor getAsyncExecutor() {
        return ThreadPoolManager.getGlobalBizExecutor();
    }

    /**
     * The {@link AsyncUncaughtExceptionHandler} instance to be used
     * when an exception is thrown during an asynchronous method execution
     * with {@code void} return type.
     */
    @Override
    public AsyncUncaughtExceptionHandler getAsyncUncaughtExceptionHandler() {
        return (throwable, method, objects) -> {
            LogExUtil.errorLog("异步任务执行异常，异常信息：" + throwable.getMessage(), throwable);
            log.error("异步任务执行异常，异常信息：", throwable);
        };
    }
}
