package com.bbh.live.config;

import cn.hutool.json.JSONArray;
import com.bbh.live.config.bizconfig.BizProperties;
import lombok.Data;

import static com.bbh.live.core.msg.ImProperties.LIVE_ROOM_CHAT_ROOM_FULL_INFO;

/**
 * 直播业务配置<br>
 * 所有配置存储于 global_biz_config 表中
 *
 * <AUTHOR>
 */
@Data
@BizProperties("live")
public class LiveBizProperties {

    /**
     * 感兴趣-备注-快捷选择
     */
    private JSONArray subscribeFastRemarkList;

    /**
     * 首页轮播图
     */
    private JSONArray homeCarouselImgs;

    /**
     *  再见列表每个直播间默认展示条数
     */
    private Integer auctionFailListDisplaySize = 5;

    /**
     *  再见列表消息滚动栏内容
     */
    private String auctionFailListRemark;

    /**
     * 全局的聊天室ID
     */
    private String globalChatroomId = LIVE_ROOM_CHAT_ROOM_FULL_INFO;

    /**
     * 进入直播间发的系统公告
     */
    private String systemNotice;

    /**
     * 反馈的选项列表
     */
    private JSONArray feedbackOptionList = new JSONArray(0);

    /**
     * 拍卖商品加价幅度选项
     */
    private JSONArray goodsAuctionIncreasePrices;

    /**
     * 拍卖商品拍卖时长选项
     */
    private JSONArray goodsAuctionDuration;
}
