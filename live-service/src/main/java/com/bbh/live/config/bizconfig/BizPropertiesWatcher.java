package com.bbh.live.config.bizconfig;

import cn.hutool.core.util.StrUtil;
import com.bbh.live.dao.service.GlobalBizConfigService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 业务配置监听器，自动返回最新的数据
 * <AUTHOR>
 */
@Aspect
@Component
@Slf4j
public class BizPropertiesWatcher {

    @Resource
    private GlobalBizConfigService globalBizConfigService;

    /**
     * 字段缓存，用于存储类及其字段的映射关系。
     * 键为类对象，值为该类的字段名到Field对象的映射。
     */
    private static final Map<Class<?>, Map<String, Field>> FIELD_CACHE = new ConcurrentHashMap<>();

    /**
     * 前缀缓存，用于存储类及其对应的前缀。
     * 键为类对象，值为该类的前缀字符串。
     */
    private static final Map<Class<?>, String> PREFIX_CACHE = new ConcurrentHashMap<>();

    /**
     * getter方法的标准前缀。
     * 用于构造或识别getter方法名。
     */
    private static final String GET_PREFIX = "get";

    @Around("@within(com.bbh.live.config.bizconfig.BizProperties) && (execution(* *.get*(..)))")
    public Object aroundGetSet(ProceedingJoinPoint joinPoint) throws Throwable {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        Object target = joinPoint.getTarget();

        if (method.getName().startsWith(GET_PREFIX)) {
            String prefix = PREFIX_CACHE.computeIfAbsent(target.getClass(),
                    clazz -> clazz.getAnnotation(BizProperties.class).value());

            // 裁剪出字段名称
            String fieldName = method.getName().substring(3);
            // 首字母小写
            fieldName = StrUtil.lowerFirst(fieldName);
            // 反射获取字段
            Map<String, Field> fieldMap = FIELD_CACHE.computeIfAbsent(target.getClass(),
                    clazz -> Arrays.stream(clazz.getDeclaredFields())
                            .collect(Collectors.toMap(Field::getName, Function.identity())));
            Field field = fieldMap.get(fieldName);
            if (field != null) {
                field.setAccessible(true);
                // 驼峰转小写+下划线
                String fieldKey = StrUtil.toUnderlineCase(fieldName);
                // 拼接成完整的key
                String key = prefix + BizConfigConstant.SEPARATOR + fieldKey;
                // 获取值后自动注入
                Object value = globalBizConfigService.get(key, field.getType());
                // 处理返回结果
                return wrapValue(value, field, target);
            }
        }

        Object result = null;
        try {
            result = joinPoint.proceed();
        } catch (Throwable throwable) {
            log.error("监听业务配置异常", throwable);
        }
        return result;
    }

    /**
     * 包装字段值，处理null值和字符串特殊情况。
     *
     * @param value  需要包装的值
     * @param field  与值相关联的字段
     * @param target 字段所属的目标对象
     * @return 包装后的值
     */
    private Object wrapValue(Object value, Field field, Object target) {
        if (value == null) {
            try {
                // 如果值为null，尝试获取字段的默认值
                return field.get(target);
            } catch (IllegalAccessException e) {
                // 如果无法访问字段，记录错误并返回null
                log.error("解析业务配置异常", e);
                return null;
            }
        }
        // 字符串要特殊处理下，去掉首尾的双引号
        if (value instanceof String) {
            return trimContent((String) value);
        }
        // 对于非null且非字符串的值，直接返回
        return value;
    }

    /**
     * 去除字符串首尾的双引号。
     *
     * @param content 需要处理的字符串内容
     * @return 处理后的字符串
     */
    private String trimContent(String content) {
        if (StrUtil.isBlank(content)) {
            // 如果字符串为空或只包含空白字符，直接返回
            return content;
        }
        // 使用正则表达式移除字符串首尾的双引号
        return content.replaceAll("^\"|\"$", "");
    }



}
