package com.bbh.live.config.bizconfig;

import cn.hutool.core.util.StrUtil;
import com.bbh.live.dao.service.GlobalBizConfigService;
import lombok.AllArgsConstructor;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.config.BeanPostProcessor;

import java.lang.reflect.Field;

/**
 * 实现自动注入业务配置 <br>
 * 因业务调整，需实时查询，因此通过 {@link BizPropertiesWatcher} 实现自动查询，不再由启动时进行初始化
 *
 * <AUTHOR>
 */
//@ConditionalOnBean(GlobalBizConfigService.class)
//@Component
@AllArgsConstructor
@Deprecated
public class BizPropertiesProcessor implements BeanPostProcessor {

    private static final Logger log = LoggerFactory.getLogger(BizPropertiesProcessor.class);
    private final GlobalBizConfigService globalBizConfigService;

    @Override
    public Object postProcessBeforeInitialization(Object bean, @NotNull String beanName) throws BeansException {
        Class<?> beanClass = bean.getClass();
        BizProperties annotation = beanClass.getAnnotation(BizProperties.class);

        if (annotation != null) {
            String prefix = annotation.value();
            Field[] fields = beanClass.getDeclaredFields();

            for (Field field : fields) {
                String fieldName = field.getName();
                // 驼峰转小写+下划线
                String fieldKey = StrUtil.toUnderlineCase(fieldName);
                // 拼接成完整的key
                String key = prefix + BizConfigConstant.SEPARATOR + fieldKey;
                // 从缓存获取值后自动注入
                Object object = globalBizConfigService.get(key, field.getType());
                if (object != null) {
                    try {
                        field.setAccessible(true);
                        field.set(bean, object);
                    } catch (IllegalAccessException e) {
                        log.error("初始化业务配置异常: {}", key, e);
                    }
                }
            }
        }
        return bean;
    }

}
