package com.bbh.live.config.bizconfig;

import org.springframework.stereotype.Component;

import java.lang.annotation.*;

/**
 *
 * 还未实现，参考 {@link org.springframework.beans.factory.annotation.Value}
 *
 * <AUTHOR>
 */
@Target({ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Inherited
@Component
@Deprecated
public @interface BizValue {

    String value() default "";

    String prefix() default "";

}
