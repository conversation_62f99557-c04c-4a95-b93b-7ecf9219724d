package com.bbh.live.config;

import cn.hutool.core.util.StrUtil;
import com.bbh.config.filter.RepeatedlyRequestWrapper;
import com.bbh.live.service.room.context.LiveRoomContextHolder;
import com.bbh.util.ParamsUtil;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import java.nio.charset.StandardCharsets;

/**
 * <AUTHOR> dsy
 * @Date: 2024/7/29
 * @Description: 直播间拦截器 用于初始化直播间上下文 在业务处理时，需要判断当前直播间是否开启
 */
@Component
public class LiveRoomHandlerInterceptor implements HandlerInterceptor {

    private static final String ROOM_ID = "live_room_id";

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        RepeatedlyRequestWrapper requestWrapper;
        if(request instanceof RepeatedlyRequestWrapper){
            requestWrapper = (RepeatedlyRequestWrapper) request;
        } else {
            requestWrapper = new RepeatedlyRequestWrapper(request, response);
        }
        String requestBody = new String(requestWrapper.getBody(), StandardCharsets.UTF_8);
        if(StrUtil.isNotBlank(requestBody)){
            Long liveRoomId = ParamsUtil.getValueFromJsonStr(requestBody, ROOM_ID, Long.class);
            if(liveRoomId != null) {
                LiveRoomContextHolder.initRoomContext(liveRoomId);
            }
        }
        return HandlerInterceptor.super.preHandle(requestWrapper, response, handler);
    }

    @Override
    public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler, ModelAndView modelAndView) throws Exception {
        HandlerInterceptor.super.postHandle(request, response, handler, modelAndView);
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        LiveRoomContextHolder.clearRoom();
        HandlerInterceptor.super.afterCompletion(request, response, handler, ex);
    }
}
