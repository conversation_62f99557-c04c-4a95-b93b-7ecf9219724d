package com.bbh.live.service.buyer.orgmap.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.bbh.live.dao.mapper.OrgGeoMapper;
import com.bbh.live.service.buyer.orgmap.GeoService;
import com.bbh.live.service.buyer.orgmap.geo.GeoSearchParams;
import com.bbh.live.service.buyer.orgmap.geo.GeoUnit;
import com.bbh.live.service.buyer.orgmap.vo.OrgDistanceVO;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/10/9 10:16
 * @description
 */
@Service
@AllArgsConstructor
public class MysqlGeoServiceImpl implements GeoService {

    private final OrgGeoMapper orgGeoMapper;

    @Override
    public double getDistance(GeoPosition position1, GeoPosition position2) {
        return orgGeoMapper.getDistance(position1, position2);
    }

    @Override
    public Map<Long, Double> searchWithDistance(GeoSearchParams args) {
        List<OrgDistanceVO> distanceVOList = orgGeoMapper.searchOrgsWithDistance(args);
        if(CollectionUtil.isEmpty(distanceVOList)){
            return new LinkedHashMap<>(0);
        }
        Map<Long, Double> result = new LinkedHashMap<>(64);
        GeoUnit unit = args.getUnit();
        distanceVOList.forEach(distanceVO -> {
            double distance = distanceVO.getDistance();
            if(unit != null){
                distance = unit.covert(distance);
            }
            result.put(distanceVO.getOrgId(), distance);
        });
        return result;
    }
}
