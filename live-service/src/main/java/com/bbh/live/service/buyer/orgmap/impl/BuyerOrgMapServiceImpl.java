package com.bbh.live.service.buyer.orgmap.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.bbh.base.Sort;
import com.bbh.live.controller.req.OrgMapSearchReq;
import com.bbh.live.dao.service.IGlobalOrganizationService;
import com.bbh.live.enums.BuyerVipTypeEnum;
import com.bbh.live.service.buyer.orgmap.BuyerOrgMapService;
import com.bbh.live.service.buyer.orgmap.GeoService;
import com.bbh.live.service.buyer.orgmap.geo.GeoSearchParams;
import com.bbh.live.service.buyer.orgmap.geo.GeoUnit;
import com.bbh.live.service.buyer.orgmap.vo.OrgAddressInfoVO;
import com.bbh.live.service.buyer.orgmap.vo.OrgPositionVO;
import com.bbh.live.service.buyer.vip.BuyerVipService;
import com.bbh.live.service.buyer.vip.vo.UserBuyerVipInfoVO;
import com.bbh.model.GlobalOrganization;
import com.bbh.secure.AuthUtil;
import com.bbh.util.EncryptUtil;
import com.bbh.util.ParamsUtil;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2024/10/9 13:44
 * @description
 */
@Service
public class BuyerOrgMapServiceImpl implements BuyerOrgMapService {

    @Resource
    private GeoService geoService;
    @Resource
    private BuyerVipService buyerVipService;
    @Resource
    private IGlobalOrganizationService globalOrganizationService;

    @Override
    public List<OrgAddressInfoVO> getNearbyOrgSortedByDistanceAsc(OrgMapSearchReq searchReq) {
        //判断是否vip 非vip 30公里 vip看配置
        UserBuyerVipInfoVO vipInfo = buyerVipService.getUserBuyerVipInfoBySeatId(AuthUtil.getSeatId());
        double radius = vipInfo.getBuyerVipType() == BuyerVipTypeEnum.VIP ? vipInfo.getVipConfig().getNearShopDistance() : 30000;
        GeoSearchParams geoSearchArgs = new GeoSearchParams(searchReq.getLongitude().doubleValue(), searchReq.getLatitude().doubleValue())
                .setRadius(radius)
                .setUnit(GeoUnit.METERS)
                .setOrder(Sort.Direction.ASC);

        // 根据商家名搜索，忽略距离限制
        if(StrUtil.isNotBlank(searchReq.getKeywords())){
            geoSearchArgs.setKeywords(searchReq.getKeywords()).setRadius(null);
        }
        // 商家id -> 距离
        Map<Long, Double> distanceMap = geoService.searchWithDistance(geoSearchArgs);
        if(CollectionUtil.isEmpty(distanceMap)){
            return List.of();
        }
        // 移除自己的商家
        distanceMap.remove(AuthUtil.getOrgId());
        if(distanceMap.isEmpty()){
            return new ArrayList<>();
        }

        List<OrgAddressInfoVO> orgAddressInfoVOList = globalOrganizationService.getOrgAddressInfoByIdList(distanceMap.keySet());
        for (OrgAddressInfoVO orgAddressInfoVO : orgAddressInfoVOList) {
            // 解析省市区
            parseAddressDesc(orgAddressInfoVO);
            // 距离处理
            Double distance = distanceMap.get(orgAddressInfoVO.getId());
            if(distance != null && distance >= 0){
                orgAddressInfoVO.setDistanceDesc(distanceFormat(distance, null));
                orgAddressInfoVO.setDistance(distance);
            }
            // 店铺客服id
            orgAddressInfoVO.setCustomerServiceId("bbh_" + orgAddressInfoVO.getId() + "_ry_" + orgAddressInfoVO.getSeatId());
            orgAddressInfoVO.setOrgId(EncryptUtil.encrypt(String.valueOf(orgAddressInfoVO.getId())));
            orgAddressInfoVO.setOriginalOrgId(String.valueOf(orgAddressInfoVO.getId()));
        }
        // 排序
        orgAddressInfoVOList.sort(Comparator.comparing(OrgAddressInfoVO::getDistance));
        return orgAddressInfoVOList;
    }

    private void parseAddressDesc(OrgAddressInfoVO orgAddressInfoVO) {
        String addressDesc = orgAddressInfoVO.getAddressDesc();
        if(StrUtil.isBlank(addressDesc)){
            return;
        }
        String[] addressArr = addressDesc.split("\\|");
        try {
            orgAddressInfoVO.setProvince(addressArr[0]);
            orgAddressInfoVO.setCity(addressArr[1]);
            orgAddressInfoVO.setRegion(addressArr[2]);
        }catch (Exception ignore){}
    }

    private String distanceFormat(double distance, GeoUnit unit) {
        if (unit == null) {
            double kM1 = 1000D;
            if (distance > 0 && distance < kM1) {
                unit = GeoUnit.METERS;
            } else {
                unit = GeoUnit.KILOMETERS;
            }
        }
        return NumberUtil.round(unit.covert(distance), 0, RoundingMode.UP) + unit.toString();
    }

    @Override
    public List<OrgPositionVO> getAllOrgWithDistance(OrgMapSearchReq searchReq) {
        // 当前用户可见范围
        UserBuyerVipInfoVO vipInfo = buyerVipService.getUserBuyerVipInfoBySeatId(AuthUtil.getSeatId());
        double radius = vipInfo.getBuyerVipType() == BuyerVipTypeEnum.VIP ? vipInfo.getVipConfig().getNearShopDistance() : 30000;

        GeoSearchParams geoSearchArgs = new GeoSearchParams(searchReq.getLongitude().doubleValue(), searchReq.getLatitude().doubleValue())
                .setUnit(GeoUnit.METERS)
                .setOrder(Sort.Direction.ASC);
        // 附近商家距离
        Map<Long, Double> distanceMap = geoService.searchWithDistance(geoSearchArgs);

//        List<GlobalOrganization> globalOrganizations = globalOrganizationService.lambdaQuery().in(GlobalOrganization::getId, distanceMap.keySet())
//                .select(GlobalOrganization::getId, GlobalOrganization::getName, GlobalOrganization::getAddressCoordinate)
//                .list();
        List<GlobalOrganization> globalOrganizations = globalOrganizationService.getMapOrg(distanceMap.keySet());


        if(CollectionUtil.isEmpty(globalOrganizations)){
            return List.of();
        }
        List<OrgPositionVO> result = new ArrayList<>();
        for (GlobalOrganization globalOrganization : globalOrganizations) {
            Double distanceMeters = distanceMap.get(globalOrganization.getId());
            OrgPositionVO orgPositionVO = new OrgPositionVO()
                    .setId(globalOrganization.getId())
                    .setOrgId(EncryptUtil.encrypt(String.valueOf(globalOrganization.getId())))
                    .setOrgName(globalOrganization.getName())
                    // 距离
                    .setDistance(distanceFormat(distanceMeters, null))
                    // 是否可点击
                    .setIfClick(distanceMeters <= radius)
                    // 纬度
                    .setAddrLatitude(ParamsUtil.getValueFromJsonStr((String) globalOrganization.getAddressCoordinate(), "lat", BigDecimal.class))
                    // 经度
                    .setAddrLongitude(ParamsUtil.getValueFromJsonStr((String) globalOrganization.getAddressCoordinate(), "lng", BigDecimal.class));
            result.add(orgPositionVO);
        }
        return result;
    }

    @Override
    public OrgAddressInfoVO getOrgBusinessCard(OrgMapSearchReq searchReq) {

        List<OrgAddressInfoVO> orgAddressInfoByIdList = globalOrganizationService.getOrgAddressInfoByIdList(Set.of(searchReq.getOrgId()));
        if (CollectionUtil.isEmpty(orgAddressInfoByIdList)){
            return new OrgAddressInfoVO();
        }
        OrgAddressInfoVO first = orgAddressInfoByIdList.getFirst();
        // 解析省市区
        parseAddressDesc(first);
        // 距离处理
        if(first.getLat() != null && first.getLng() != null){
            GeoService.GeoPosition myPosition = new GeoService.GeoPosition(searchReq.getLongitude().doubleValue(), searchReq.getLatitude().doubleValue());
            GeoService.GeoPosition targetOrgPosition = new GeoService.GeoPosition(first.getLng(), first.getLat());
            double distance = geoService.getDistance(myPosition, targetOrgPosition);
            first.setDistanceDesc(distanceFormat(distance, null));
            first.setDistance(distance);
        }

        // 店铺客服id
        first.setCustomerServiceId("bbh_" + first.getId() + "_ry_" + first.getSeatId());
        first.setOrgId(EncryptUtil.encrypt(String.valueOf(first.getId())));
        first.setOriginalOrgId(String.valueOf(first.getId()));
        return first;
    }
}
