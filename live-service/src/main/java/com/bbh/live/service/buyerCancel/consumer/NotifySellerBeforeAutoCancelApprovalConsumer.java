package com.bbh.live.service.buyerCancel.consumer;

import cn.hutool.core.util.CharsetUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.bbh.enums.LiveGoodsBuyerCancelRecordStatusEnum;
import com.bbh.exception.ServiceException;
import com.bbh.live.dao.mapper.LiveGoodsBuyerCancelRecordMapper;
import com.bbh.live.service.buyerCancel.BuyerCancelRecordService;
import com.bbh.live.service.msg.MsgService;
import com.bbh.live.service.msg.push.AppMessagePushService;
import com.bbh.live.service.msg.push.LivePushTypeEnum;
import com.bbh.model.LiveGoodsBuyerCancelRecord;
import com.bbh.service.mq.constants.MqConstant;
import com.bbh.service.mq.service.CoreMqListener;
import com.rabbitmq.client.Channel;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 买手取消成交后，商家超时为操作，就会自动通过，在自动通过前1小时，发系统推送
 * <AUTHOR>
 */
@AllArgsConstructor
@Slf4j
@Component
public class NotifySellerBeforeAutoCancelApprovalConsumer implements CoreMqListener {

    private final LiveGoodsBuyerCancelRecordMapper liveGoodsBuyerCancelRecordMapper;
    private final BuyerCancelRecordService buyerCancelRecordService;
    private final AppMessagePushService appMessagePushService;
    private final MsgService msgService;

    @RabbitListener(queuesToDeclare = @Queue(name = MqConstant.LIVE_BUYER_CANCEL_REVIEW_TIMEOUT_PUSH, durable = "true"))
    @Override
    public void handle(Message message, Channel channel) {
        try {
            String json = StrUtil.str(message.getBody(), CharsetUtil.UTF_8);
            log.info("主动取消 超时消费前推送  ===> {}", json);

            // 空消息直接确认
            if (StrUtil.isBlank(json)) {
                log.error("消息内容为空");
                ack(message, channel);
                return;
            }

            // JSON解析异常处理
            JSONObject jsonObject;
            try {
                jsonObject = JSONUtil.parseObj(json);
            } catch (Exception e) {
                log.error("JSON解析失败, message: {}", json, e);
                ack(message, channel); // JSON解析失败的消息直接确认，避免死循环
                return;
            }

            // 拿到id
            Long recordId = jsonObject.getLong("id");
            if (recordId == null) {
                log.error("记录ID为空, message: {}", json);
                ack(message, channel);
                return;
            }

            // 查一下记录
            LiveGoodsBuyerCancelRecord record = liveGoodsBuyerCancelRecordMapper.selectById(recordId);
            if (record == null) {
                // 不存在，直接ack
                ack(message, channel);
                return;
            }

            // 判断是否已经处理
            if (record.getCancelStatus() == LiveGoodsBuyerCancelRecordStatusEnum.PLATFORM_REVIEWING) {
                ack(message, channel);
                return;
            }

            // 查一下有没有发过
            if (record.getIfNotified() != null && record.getIfNotified()) {
                // 已经发过了，直接ack
                ack(message, channel);
                return;
            }

            // 没发过就发
            msgService.notifySellerBuyerCancelApproval(recordId, record.getSellerOrgId(), LivePushTypeEnum.NOTIFY_SELLER_BEFORE_AUTO_CANCEL_APPROVAL, Map.of("id", record.getId()));

            // 更新发送状态
            LambdaUpdateWrapper<LiveGoodsBuyerCancelRecord> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(LiveGoodsBuyerCancelRecord::getId, recordId)
                    .set(LiveGoodsBuyerCancelRecord::getIfNotified, true);
            liveGoodsBuyerCancelRecordMapper.update(updateWrapper);

            log.info("主动取消 超时消费成功 ");
        } catch (ServiceException e) {
            log.info("业务异常，仍然ACK，因为：{}", e.getMessage());
            ack(message, channel);
        }  catch (Exception e) {
            log.error("消息处理发生未预期异常", e);
            nack(message, channel);
        }
    }

    private void nack(Message message, Channel channel) {
        try {
            channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, true);
        } catch (Exception e) {
            log.error("消息确认失败", e);
        }
    }

    private void ack(Message message, Channel channel) {
        try {
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
        } catch (Exception e) {
            log.error("消息确认失败", e);
        }
    }
}
