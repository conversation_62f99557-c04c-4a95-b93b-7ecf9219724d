package com.bbh.live.service.user.permission;

import com.bbh.model.GlobalOrgRole;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/9/20 16:25
 * @description erp权限
 */
public class ErpPermissionCheckServiceImpl extends AbstractPermissionCheckService {

    @Override
    protected Integer getPermissionIfAll(GlobalOrgRole globalOrgRole) {
        return globalOrgRole.getErpPermissionIfAll();
    }

    @Override
    protected List<String> getPermissionCodeList(GlobalOrgRole globalOrgRole) {
        return globalOrgRole.getErpPermissionCodeList();
    }
}
