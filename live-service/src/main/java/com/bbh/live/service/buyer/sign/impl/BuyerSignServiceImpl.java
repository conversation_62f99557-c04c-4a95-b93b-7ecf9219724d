package com.bbh.live.service.buyer.sign.impl;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.bbh.exception.ServiceException;
import com.bbh.live.dao.service.*;
import com.bbh.live.enums.BuyerVipTypeEnum;
import com.bbh.live.service.buyer.sign.BuyerSignService;
import com.bbh.live.service.buyer.sign.SignCacheManager;
import com.bbh.live.service.buyer.sign.vo.SignAwardFenBeiVO;
import com.bbh.live.service.buyer.sign.vo.SignInfoVo;
import com.bbh.live.service.buyer.vip.BuyerVipService;
import com.bbh.live.service.buyer.vip.vo.UserBuyerVipInfoVO;
import com.bbh.model.GlobalOrganization;
import com.bbh.model.GlobalSignRecord;
import com.bbh.model.GlobalSignSet;
import com.bbh.model.VipBuyerCard;
import com.bbh.secure.AuthUtil;
import lombok.AllArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;

/**
 * <AUTHOR> dsy
 * @Date: 2024/8/15
 * @Description:
 */
@Service
@AllArgsConstructor
public class BuyerSignServiceImpl implements BuyerSignService {

    private static final Logger log = LoggerFactory.getLogger(BuyerSignServiceImpl.class);
    private final BuyerVipService buyerVipService;
    private final VipBuyerCardService vipBuyerCardService;
    private final GlobalSignRecordService globalSignRecordService;
    private final GlobalSignSetService globalSignSetService;
    private final GlobalFenbeiDetailService globalFenbeiDetailService;
    private final IGlobalOrganizationService globalOrganizationService;
    private final SignCacheManager signCacheManager;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public SignAwardFenBeiVO sign(Date now, Long buyerSeatId) {

        //检查今日是否已签到
        if(signCacheManager.isSign(now, buyerSeatId)){
            log.error("今日已签到，用户：{}, 时间：{}", buyerSeatId, DateUtil.format(now, DatePattern.NORM_DATETIME_PATTERN));
            throw new ServiceException("今日已签到");
        }

        //连续签到天数
        int consecutiveSignDaysThisWeek = signCacheManager.getConsecutiveSignDaysThisWeek(now, buyerSeatId);

        // 当天签到的基本奖励
        AtomicInteger baseFenbei = new AtomicInteger();
        // 最终得到的分贝奖励
        AtomicInteger awardsFenbei = new AtomicInteger();
        //连续签到对应的分贝奖励
        globalSignSetService.lambdaQuery().eq(GlobalSignSet::getTimes, consecutiveSignDaysThisWeek).
                oneOpt().ifPresent(globalSignSet -> {
                    baseFenbei.set(globalSignSet.getFenbeiReward());
                    awardsFenbei.set(globalSignSet.getFenbeiReward());
                });


        AtomicReference<Integer> fullMothAwardFenbei = new AtomicReference<>(0);
        //每月满签奖励
        if(DateUtil.isLastDayOfMonth(now) && signCacheManager.isSignEveryDayForMonthBeforeToday(now, buyerSeatId)){
            globalSignSetService.lambdaQuery().eq(GlobalSignSet::getTimes, 31)
                    .oneOpt()
                    .ifPresent(globalSignSet -> fullMothAwardFenbei.set(globalSignSet.getFenbeiReward()));
        }
        awardsFenbei.set(awardsFenbei.get() + fullMothAwardFenbei.get());

        // 获取商户每日签到人数限制
        var orgId = AuthUtil.getOrgId();
        GlobalOrganization organization = globalOrganizationService.getById(orgId);
        Integer dailySignInLimit = organization.getDailySignInLimit();

        // 商家每日签到分贝奖励上限（保留原有逻辑）
        //globalBizProperties.getOrgSignRewardFbLimitDaily();
        int orgSignRewardFbUpperLimitDaily = Integer.MAX_VALUE;
        int orgSignRewardToday = globalSignRecordService.lambdaQuery()
                .eq(GlobalSignRecord::getOrgId, orgId)
                .between(GlobalSignRecord::getCreatedAt, DateUtil.beginOfDay(now), DateUtil.endOfDay(now))
                .select(GlobalSignRecord::getFenbeiReward)
                .list()
                .stream()
                .mapToInt(GlobalSignRecord::getFenbeiReward)
                .sum();

        // 计算可获得的分贝数量（保留原有逻辑）
        int canAwardFenBei = orgSignRewardFbUpperLimitDaily - orgSignRewardToday;
        if(awardsFenbei.get() > canAwardFenBei){
            awardsFenbei.set(canAwardFenBei);
        }

        // 检查今日已获得分贝的签到人数
        long todayRewardCount = globalSignRecordService.lambdaQuery()
                .eq(GlobalSignRecord::getOrgId, orgId)
                .gt(GlobalSignRecord::getFenbeiReward, 0)
                .between(GlobalSignRecord::getCreatedAt, DateUtil.beginOfDay(now), DateUtil.endOfDay(now))
                .count();

        // 如果已经超过每日限制人数，则不给分贝奖励
        if (dailySignInLimit > 0 && todayRewardCount >= dailySignInLimit) {
            awardsFenbei.set(0);
        }

        UserBuyerVipInfoVO buyerVipInfo = buyerVipService.getUserBuyerVipInfoBySeatId(buyerSeatId);
        // 计算VIP额外奖励（保留原有逻辑）
        processVipExtraReward(buyerVipInfo, awardsFenbei, baseFenbei, canAwardFenBei);

        //插入签到记录,分贝明细，余额变更
        insertRecord(now, awardsFenbei, consecutiveSignDaysThisWeek);

        //插入签到记录
        signCacheManager.signIn(now, buyerSeatId);

        int tomorrowAwardFenBei = calculateTomorrowAwardFenBei(consecutiveSignDaysThisWeek, buyerVipInfo, now, fullMothAwardFenbei.get());
        tomorrowAwardFenBei = Math.min(tomorrowAwardFenBei, orgSignRewardFbUpperLimitDaily);
        return new SignAwardFenBeiVO(awardsFenbei.get(), tomorrowAwardFenBei);
    }

    private Integer calculateTomorrowAwardFenBei(int consecutiveSignDaysThisWeek, UserBuyerVipInfoVO buyerVipInfo, Date now, Integer fullMothAwardFenBei) {
        consecutiveSignDaysThisWeek = consecutiveSignDaysThisWeek == 7 ? 1 : consecutiveSignDaysThisWeek + 1;

        AtomicInteger tomorrowAwardFenBei = new AtomicInteger();
        // 基础奖励
        globalSignSetService.lambdaQuery().eq(GlobalSignSet::getTimes, consecutiveSignDaysThisWeek).
                oneOpt().ifPresent(globalSignSet -> tomorrowAwardFenBei.set(globalSignSet.getFenbeiReward()));

        // VIP倍数奖励
        if(buyerVipInfo.getBuyerVipType() == BuyerVipTypeEnum.VIP){
            tomorrowAwardFenBei.set(tomorrowAwardFenBei.get() * buyerVipInfo.getVipConfig().getSignExtraFenbei());
        }

        Date tomorrow = DateUtil.offsetDay(now, 1);
        // 是否满勤
        if(DateUtil.isLastDayOfMonth(tomorrow) && signCacheManager.isSignEveryDayForMonthBeforeToday(tomorrow, buyerVipInfo.getSeatId())){
            tomorrowAwardFenBei.set(tomorrowAwardFenBei.get() + fullMothAwardFenBei);
        }
        return tomorrowAwardFenBei.get();
    }

    private void insertRecord(Date now, AtomicInteger awardsFenbei, int consecutiveSignDaysThisWeek){
        //插入签到记录
        GlobalSignRecord globalSignRecord = new GlobalSignRecord();
        globalSignRecord.setFenbeiReward(awardsFenbei.get());
        globalSignRecord.setConsecutiveDays(consecutiveSignDaysThisWeek);
        globalSignRecord.setCreatedAt(now);
        globalSignRecordService.save(globalSignRecord);

        //账户分贝余额变更
        GlobalOrganization organization = globalOrganizationService.lambdaQuery()
                .eq(GlobalOrganization::getId, AuthUtil.getOrgId())
                .select(GlobalOrganization::getId, GlobalOrganization::getFenbei)
                .one();

        //插入分贝变更明细
        globalFenbeiDetailService.saveSignFenbeiDetail(awardsFenbei.intValue(), organization.getFenbei(), consecutiveSignDaysThisWeek);

        organization.setFenbei(organization.getFenbei() + awardsFenbei.get());
        globalOrganizationService.updateById(organization);
    }

    /**
     * 处理vip额外奖励
     * @param buyerVipInfo 用户席位id
     * @param awardsFenBei 已获得的分贝
     * @param baseFenBei 连续签到基础分贝
     * @param canAwardFenBei 可获得的分贝
     */
    private void processVipExtraReward(UserBuyerVipInfoVO buyerVipInfo, AtomicInteger awardsFenBei, AtomicInteger baseFenBei, int canAwardFenBei) {
        //计算vip额外奖励的分贝数量
        AtomicInteger vipExtraFenBei = new AtomicInteger();
        if(buyerVipInfo.getBuyerVipType() != BuyerVipTypeEnum.VIP){
            return;
        }
        // vip可额外获得的分贝
        vipExtraFenBei.set(baseFenBei.get() * (buyerVipInfo.getVipConfig().getSignExtraFenbei() - 1));

        // 实际可获得的分贝数量
        vipExtraFenBei.set(Math.min(vipExtraFenBei.get(), canAwardFenBei - awardsFenBei.get()));

        if(vipExtraFenBei.get() > 0) {
            awardsFenBei.set(awardsFenBei.get() + vipExtraFenBei.get());
            vipBuyerCardService.lambdaUpdate().eq(VipBuyerCard::getId, buyerVipInfo.getId())
                    .setIncrBy(VipBuyerCard::getTotalGetFenbei, vipExtraFenBei.get())
                    .update();
        }
    }

    @Override
    public SignInfoVo signInfo(Date date, Long buyerSeatId) {
        List<Integer> signDayThisMonthList = signCacheManager.signDayThisMonth(date, buyerSeatId);
        int signDaysThisWeek = signCacheManager.signDayThisWeek(date, buyerSeatId).size();

        int awardsFenbeiThisMonth = globalSignRecordService.lambdaQuery().eq(GlobalSignRecord::getCreateSeatId, buyerSeatId)
                .eq(GlobalSignRecord::getOrgId, AuthUtil.getOrgId())
                .between(GlobalSignRecord::getCreatedAt, DateUtil.beginOfMonth(date), DateUtil.endOfMonth(date))
                .list()
                .stream()
                .mapToInt(GlobalSignRecord::getFenbeiReward)
                .sum();
        return new SignInfoVo(signCacheManager.isSign(date, buyerSeatId), signDaysThisWeek, signDayThisMonthList.size(), awardsFenbeiThisMonth, signDayThisMonthList);
    }

    @Override
    public boolean isSign(Date date, Long buyerSeatId) {
        return signCacheManager.isSign(date, buyerSeatId);
    }
}
