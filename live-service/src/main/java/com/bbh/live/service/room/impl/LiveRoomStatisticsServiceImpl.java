package com.bbh.live.service.room.impl;

import com.bbh.enums.LiveRoomStreamStatusEnum;
import com.bbh.live.dao.dto.LiveRoomSaleStatisticsDTO;
import com.bbh.live.dao.dto.RoomIdDTO;
import com.bbh.live.dao.dto.vo.LiveRoomSaleInfo;
import com.bbh.live.dao.dto.vo.LiveRoomSaleStatisticsInfo;
import com.bbh.live.dao.service.LiveGoodsService;
import com.bbh.live.dao.service.LiveRoomService;
import com.bbh.live.service.room.LiveRoomCacheService;
import com.bbh.live.service.room.LiveRoomStatisticsService;
import com.bbh.live.util.LivePermissionChecker;
import com.bbh.model.LiveRoom;
import com.bbh.util.AssertUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;

/**
 * <AUTHOR> dsy
 * @Date: 2024/9/12
 * @Description:
 */
@Slf4j
@Service
@AllArgsConstructor
public class LiveRoomStatisticsServiceImpl implements LiveRoomStatisticsService {

    private final LiveRoomService liveRoomService;
    private final LiveRoomCacheService liveRoomCacheService;
    private final LiveGoodsService liveGoodsService;


    @Override
    public LiveRoomSaleInfo liveSaleInfo(RoomIdDTO roomId) {
        LiveRoom liveRoom = liveRoomService.lambdaQuery().eq(LiveRoom::getId, roomId.getRoomId())
                .select(LiveRoom::getId,
                        LiveRoom::getRoomName,
                        LiveRoom::getStartAt,
                        LiveRoom::getEndAt,
                        LiveRoom::getGoodsCount,
                        LiveRoom::getSoldCount,
                        LiveRoom::getSoldAmount
                )
                .one();
        AssertUtil.assertNotNull(liveRoom, "直播间不存在");

        // 直播间销售数据
        LiveRoomSaleInfo saleInfo = new LiveRoomSaleInfo()
                .setRoomName(liveRoom.getRoomName())
                .setStartAt(liveRoom.getStartAt());

        if(liveRoom.getEndAt() != null && liveRoom.getEndAt().before(new Date()) && liveRoom.getStreamStatus() == LiveRoomStreamStatusEnum.OFF){
            // 已结束的直播从数据库取销售数据
            saleInfo.setGoodsCount(liveRoom.getGoodsCount())
                    .setSoldCount(liveRoom.getSoldCount())
                    .setSoldAmount(liveRoom.getSoldAmount());
        } else {
            // 正在直播的从缓存中取实时数据
            saleInfo.setSoldAmount(liveRoomCacheService.getGoodsSoldAmount(liveRoom.getId()))
                    .setGoodsCount(liveRoomCacheService.getGoodsCount(liveRoom.getId()))
                    .setSoldCount(liveRoomCacheService.getGoodsTradeCount(liveRoom.getId()).intValue());
        }


        // 直播成交商品数据
        var soldGoodsList = liveGoodsService.getSoldGoodsListByLiveRoom(liveRoom.getId());
        saleInfo.setSaleGoodsList(soldGoodsList);

        if(saleInfo.getSoldCount() != soldGoodsList.size()){
            log.warn("直播成交商品数据与数据库实际售出商品数据不一致，直播间id：{}，实时统计数量：{}, 数据库售出商品数量：{}", liveRoom.getId(), saleInfo.getSoldCount(), soldGoodsList.size());
            saleInfo.setSoldCount(soldGoodsList.size());
        }
        return saleInfo;
    }

    @Override
    public LiveRoomSaleStatisticsInfo realTimeSaleInfo(RoomIdDTO roomId) {
        AssertUtil.assertTrue(liveRoomService.lambdaQuery().eq(LiveRoom::getId, roomId.getRoomId()).exists(), "直播间不存在");

        LivePermissionChecker.assertDirector(roomId.getRoomId());

        LiveRoomSaleStatisticsDTO liveRoomSaleStatisticsDTO = liveRoomService.getLiveRoomSaleStatisticsInfo(roomId.getRoomId());
        LiveRoomSaleStatisticsInfo statisticsInfo = new LiveRoomSaleStatisticsInfo()
                .setSoldAmount(liveRoomSaleStatisticsDTO.getSoldAmount())
                .setSoldCount(liveRoomSaleStatisticsDTO.getSoldCount())
                .setAbortiveAmount(liveRoomSaleStatisticsDTO.getAbortiveAmount())
                .setAbortiveCount(liveRoomSaleStatisticsDTO.getAbortiveCount())
                .setAccumulatePutawayAmount(liveRoomSaleStatisticsDTO.getAccumulatePutawayAmount())
                .setAccumulatePutawayCount(liveRoomSaleStatisticsDTO.getAccumulatePutawayCount());

        // 客单价
        if(liveRoomSaleStatisticsDTO.getCustomerCount() != 0){
            BigDecimal perCustomerTransactionAmount = liveRoomSaleStatisticsDTO.getSoldAmount()
                    .divide(new BigDecimal(liveRoomSaleStatisticsDTO.getCustomerCount()), 2, RoundingMode.HALF_UP);
            statisticsInfo.setPerCustomerTransactionAmount(perCustomerTransactionAmount);
        } else {
            statisticsInfo.setPerCustomerTransactionAmount(BigDecimal.ZERO);
        }
        // 成交商品均价
        if(liveRoomSaleStatisticsDTO.getSoldCount() != 0){
            BigDecimal avgSoldAmount = liveRoomSaleStatisticsDTO.getAccumulatePutawayAmount()
                    .divide(new BigDecimal(liveRoomSaleStatisticsDTO.getSoldCount()), 2, RoundingMode.HALF_UP);
            statisticsInfo.setAvgSoldAmount(avgSoldAmount);
        } else {
            statisticsInfo.setAvgSoldAmount(BigDecimal.ZERO);
        }
        return statisticsInfo;
    }
}
