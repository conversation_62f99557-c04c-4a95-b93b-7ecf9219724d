package com.bbh.live.service.livegoods.impl;

import cn.hutool.json.JSONUtil;
import com.bbh.enums.ErpGoodsSaleStatusEnum;
import com.bbh.enums.LiveGoodsStatusEnum;
import com.bbh.enums.LiveGoodsTradeTypeEnum;
import com.bbh.exception.ServiceException;
import com.bbh.live.dao.dto.livegoods.LiveGoodsDTO;
import com.bbh.live.dao.service.LiveGoodsService;
import com.bbh.live.service.livegoods.LiveGoodsDetailService;
import com.bbh.model.LiveGoods;
import com.bbh.util.AssertUtil;
import com.bbh.util.EnvironmentUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR> dsy
 * @Date: 2024/7/26
 * @Description:
 */
@Slf4j
@Service
public class LiveGoodsDetailServiceImpl extends AbstractLiveGoodsService implements LiveGoodsDetailService {

    @Override
    public LiveGoodsDTO getLiveGoodsDetailInfo(Long liveGoodsId) {
        LiveGoodsDTO liveGoodsDetailInfo = liveGoodsService.getLiveGoodsDetailInfo(liveGoodsId);
        if (liveGoodsDetailInfo == null) {
            return null;
        }
        richLiveGoods(List.of(liveGoodsDetailInfo));
        return liveGoodsDetailInfo;
    }

    @Override
    public LiveGoodsDTO getLiveGoodsSimpleDetailInfo(Long liveGoodsId) {
        return liveGoodsService.getLiveGoodsDetailInfo(liveGoodsId);
    }

    /**
     * 根据id查询直播商品详情
     *
     * @param liveGoodsId
     * @param globalGoodsId
     * @return
     */
    @Override
    public LiveGoodsDTO getLiveGoodsDetailInfo(Long liveGoodsId, Long globalGoodsId) {
        LiveGoodsDTO liveGoodsDetailInfo;
        if (globalGoodsId == null) {
            liveGoodsDetailInfo = liveGoodsMapper.getLiveGoodsDetailInfo(liveGoodsId, null);
        } else {
            liveGoodsDetailInfo = liveGoodsMapper.getLiveGoodsDetailInfoByGlobalGoodsId(globalGoodsId);
        }
        if (liveGoodsDetailInfo == null) {
            return null;
        }
        richLiveGoods(List.of(liveGoodsDetailInfo));
        return liveGoodsDetailInfo;
    }

    @Override
    public LiveGoodsDTO getCheckedGoodsInfo(Long globalGoodsId) {
        return liveGoodsMapper.getLiveGoodsDetailInfoByGlobalGoodsId(globalGoodsId);
    }

    @Override
    public void updateLiveGoods(LiveGoodsDTO liveGoodsDTO) {
        LiveGoods liveGoods = new LiveGoods();
        BeanUtils.copyProperties(liveGoodsDTO, liveGoods);
        liveGoods.setGoodsName(liveGoodsDTO.getGlobalGoodsName());
        liveGoods.setGoodsQuality(liveGoodsDTO.getQuality());
        try {
            String jsonStr = JSONUtil.toJsonStr(liveGoodsDTO.getImgUrlList());
            liveGoods.setGoodsImgUrlList(jsonStr);
        } catch (Exception e) {
            log.error("更新商品冗余信息失败，转换商品图片失败", e);
            liveGoods.setGoodsImgUrlList(null);
        }
        liveGoods.setGoodsDescription(liveGoodsDTO.getGlobalGoodsDescription());
        // 一口价，如果是零元，改成0.01
        if (liveGoodsDTO.getTradeType() == LiveGoodsTradeTypeEnum.SEC_KILL
                && liveGoodsDTO.getStartPrice() != null
                && liveGoodsDTO.getStartPrice().compareTo(BigDecimal.ZERO) <= 0) {
            liveGoodsDTO.setStartPrice(BigDecimal.valueOf(0.01));
        }
        liveGoodsService.updateById(liveGoods);
    }

    @Override
    public LiveGoodsService getLiveGoodsService() {
        return liveGoodsService;
    }

    @Override
    public LiveGoodsDTO getNextWaitPutAwayLiveGoods(LiveGoodsDTO liveGoodsDTO) {
        Optional<LiveGoods> liveGoodsOptional = liveGoodsService.lambdaQuery()
                .eq(LiveGoods::getLiveRoomId, liveGoodsDTO.getLiveRoomId())
                .eq(LiveGoods::getGoodsStatus, LiveGoodsStatusEnum.WAIT_PUT_AWAY)
                .orderByAsc(LiveGoods::getSort)
                .last("limit 1")
                .select(LiveGoods::getId)
                .oneOpt();

        return liveGoodsOptional.map(liveGoods -> this.getLiveGoodsDetailInfo(liveGoods.getId())).orElse(null);
    }

    @Override
    public void checkLiveGoodsCanAddToCashierDesk(LiveGoodsDTO liveGoods, boolean ifLock) {
        if (EnvironmentUtil.hasProfile("local")) {
            return;
        }

        AssertUtil.assertNotNull(liveGoods, "商品不存在");
        //检查商品是否处于可交易状态
        //判断erp状态
        if(liveGoods.getErpIfLocked() || liveGoods.getErpPlaceOrderStatus() || liveGoods.getErpSaleStatus().equals(ErpGoodsSaleStatusEnum.SOLD_OUT.getType())){
            throw new ServiceException("商品已售出");
        }

        //满足条件，可加入收银台。当前在事务内提前加锁
        if(ifLock && TransactionSynchronizationManager.isSynchronizationActive()){
            liveGoodsService.lambdaQuery().eq(LiveGoods::getId, liveGoods.getId()).select(LiveGoods::getId).last("for update");
        }

        //判断商品状态
        AssertUtil.assertFalse(liveGoods.getGoodsStatus().equals(LiveGoodsStatusEnum.TRADED), "商品已售出");
    }
}
