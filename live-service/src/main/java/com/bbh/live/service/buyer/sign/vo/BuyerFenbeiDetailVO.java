package com.bbh.live.service.buyer.sign.vo;

import com.bbh.enums.GlobalFenbeiDetailTypeEnum;
import com.bbh.model.GlobalFenbeiDetail;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/9/24 11:09
 * @description
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
public class BuyerFenbeiDetailVO {

    private Integer changeFenbeiNum;

    private Date createdAt;

    private String event;

    private GlobalFenbeiDetailTypeEnum type;

    private String reason;

    public static BuyerFenbeiDetailVO build(GlobalFenbeiDetail globalFenbeiDetail){
        return new BuyerFenbeiDetailVO(globalFenbeiDetail.getChangeNumber(), globalFenbeiDetail.getCreatedAt(),
                globalFenbeiDetail.getEvent(), globalFenbeiDetail.getType(), globalFenbeiDetail.getReason());
    }
}
