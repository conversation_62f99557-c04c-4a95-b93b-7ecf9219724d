package com.bbh.live.service.room.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bbh.base.PageBase;
import com.bbh.enums.LiveOrgFilterModeEnum;
import com.bbh.enums.LiveOrgFilterSourceTypeEnum;
import com.bbh.exception.ServiceException;
import com.bbh.live.dao.dto.LiveFilterSearchUserDTO;
import com.bbh.live.dao.dto.LiveOrgFilterDTO;
import com.bbh.live.dao.dto.QuerySimpleUserDTO;
import com.bbh.live.dao.dto.SimpleUserInfoDTO;
import com.bbh.live.dao.dto.vo.LiveOrgFilterVO;
import com.bbh.live.dao.mapper.LiveRoomMapper;
import com.bbh.live.dao.service.GlobalOrgSeatService;
import com.bbh.live.dao.service.IGlobalOrganizationService;
import com.bbh.live.dao.service.LiveOrgFilterService;
import com.bbh.live.dao.service.LiveRoomService;
import com.bbh.live.enums.BuyerVipTypeEnum;
import com.bbh.live.service.buyer.vip.BuyerVipService;
import com.bbh.live.service.buyer.vip.vo.UserBuyerVipInfoVO;
import com.bbh.live.service.msg.MsgService;
import com.bbh.live.service.room.LiveOrgFilterBizService;
import com.bbh.model.GlobalOrgSeat;
import com.bbh.model.GlobalOrganization;
import com.bbh.model.LiveOrgFilter;
import com.bbh.model.LiveRoom;
import com.bbh.secure.AuthUtil;
import com.bbh.util.AssertUtil;
import com.bbh.vo.AuthUser;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.function.BiConsumer;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/9/27 09:47
 * @description
 */
@Service
@AllArgsConstructor
public class LiveOrgFilterBizServiceImpl implements LiveOrgFilterBizService {

    private final GlobalOrgSeatService globalOrgSeatService;
    private final LiveOrgFilterService liveOrgFilterService;
    private final LiveRoomService liveRoomService;
    private final MsgService msgService;
    private final BuyerVipService buyerVipService;
    private final IGlobalOrganizationService globalOrganizationService;
    private final LiveRoomMapper liveRoomMapper;

    @Override
    public IPage<LiveOrgFilterVO> pageListUser(PageBase pageBase, AuthUser user, LiveOrgFilterSourceTypeEnum liveOrgFilterSourceTypeEnum) {
        return liveOrgFilterService.pageListUser(pageBase, user, liveOrgFilterSourceTypeEnum);
    }

    @Override
    public IPage<LiveOrgFilterVO> searchUser(AuthUser authUser, LiveFilterSearchUserDTO dto) {
        String searchKey = dto.getSearchKey();
        if (StringUtils.isBlank(searchKey)) {
            throw new ServiceException("搜索词不能为空");
        }
        Long orgId = authUser.getOrgId();

        // 查询席位与关联的会员信息
        IPage<SimpleUserInfoDTO> userPageList = globalOrgSeatService.selectPageWithVip(
                new Page<>(dto.getCurrentPage(), dto.getPerPage()),
                // 根据拍号精确搜索
                new QuerySimpleUserDTO().setAuctionCode(searchKey)
        );
        // 没查到结果直接返回
        if (CollectionUtil.isEmpty(userPageList.getRecords())) {
            return Page.of(dto.getCurrentPage(), dto.getPerPage());
        }

        // 查询黑名单信息，用于检查每个用户是否被当前商户拉黑
        List<LiveOrgFilter> liveOrgFilters = liveOrgFilterService.getByOrgId(orgId, LiveOrgFilterSourceTypeEnum.MERCHANT_BLOCK_USER);
        Map<Long, LiveOrgFilter> byUserId = liveOrgFilters.stream().collect(Collectors.toMap(LiveOrgFilter::getUserId, x -> x, (x1, x2) -> x1));

        // 查到结果进行二次组装
        return convertUserToFilterVO(userPageList, (user, vo) -> {
            LiveOrgFilter liveOrgFilter = byUserId.get(vo.getUserId());
            if (liveOrgFilter != null) {
                vo.setId(liveOrgFilter.getId());
                vo.setHas(true);
            }else {
                vo.setId(null);
                vo.setHas(false);
            }
        });
    }

    /**
     * 将SimpleUserInfoDTO页面对象转换为LiveOrgFilterVO页面对象。
     *
     * @param userPageList 包含SimpleUserInfoDTO对象的IPage实例
     * @param biConsumer   可选的BiConsumer，用于对转换后的每个LiveOrgFilterVO对象进行额外处理
     * @return 包含转换后LiveOrgFilterVO对象的IPage实例
     */
    private IPage<LiveOrgFilterVO> convertUserToFilterVO(IPage<SimpleUserInfoDTO> userPageList, BiConsumer<SimpleUserInfoDTO, LiveOrgFilterVO> biConsumer) {
        IPage<LiveOrgFilterVO> resultPage = new Page<>();
        resultPage.setSize(userPageList.getSize());
        resultPage.setCurrent(userPageList.getCurrent());
        resultPage.setTotal(userPageList.getTotal());
        resultPage.setPages(userPageList.getPages());
        resultPage.setRecords(userPageList.getRecords().stream().map(user -> {
            LiveOrgFilterVO vo = new LiveOrgFilterVO();
            BeanUtil.copyProperties(user, vo);
            vo.setUserName(user.getSeatName());

            // 如果提供了biConsumer，则应用额外的处理
            if (biConsumer != null) {
                biConsumer.accept(user, vo);
            }

            return vo;
        }).toList());
        return resultPage;
    }

    @Override
    public IPage<LiveOrgFilterVO> pageListOrg(PageBase pageBase, AuthUser user, LiveOrgFilterSourceTypeEnum liveOrgFilterSourceTypeEnum) {
        return liveOrgFilterService.pageListOrg(pageBase, user, liveOrgFilterSourceTypeEnum);
    }

    @Override
    public IPage<LiveOrgFilterVO> searchOrg(AuthUser user, LiveFilterSearchUserDTO dto) {
        return liveOrgFilterService.searchOrg(user, dto);
    }

    @Override
    public void addUser(AuthUser user, LiveOrgFilterDTO dto) {
        LiveRoom liveRoom = liveRoomService.getById(dto.getLiveRoomId());
        if (liveRoom == null) {
            throw new ServiceException("直播间不存在");
        }
        Long orgId = user.getOrgId();
        Long seatId = dto.getSeatId();
        GlobalOrgSeat byId = globalOrgSeatService.getById(seatId);

        LambdaQueryWrapper<LiveOrgFilter> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(LiveOrgFilter::getOrgId, orgId)
                .eq(LiveOrgFilter::getSourceType, LiveOrgFilterSourceTypeEnum.MERCHANT_BLOCK_USER)
                .eq(LiveOrgFilter::getFilterMode, LiveOrgFilterModeEnum.BLACKLIST)
                .eq(LiveOrgFilter::getUserId, byId.getUserId());
        if (liveOrgFilterService.count(queryWrapper) > 0) {
            throw new ServiceException("该用户已在名单中");
        }
        LiveOrgFilter liveOrgFilter = new LiveOrgFilter();
        liveOrgFilter.setOrgId(orgId);
        liveOrgFilter.setSeatId(byId.getId());
        liveOrgFilter.setUserId(byId.getUserId());
        liveOrgFilter.setUserName(byId.getName());
        liveOrgFilter.setSourceType(LiveOrgFilterSourceTypeEnum.MERCHANT_BLOCK_USER);
        liveOrgFilter.setFilterMode(LiveOrgFilterModeEnum.BLACKLIST);
        liveOrgFilter.setRemark(dto.getRemark());
        liveOrgFilter.setCreateUserId(user.getUserId());
        liveOrgFilter.setCreateSeatId(user.getSeatId());
        liveOrgFilter.setCreateUserName(globalOrgSeatService.getSeatName(AuthUtil.getSeatId()));
        liveOrgFilterService.save(liveOrgFilter);

        // 发送提出直播间消息
        msgService.letUserOutNow(dto.getLiveRoomId(), byId.getUserId(), seatId);
    }


    @Override
    public Long addOrg(AuthUser user, LiveOrgFilterDTO dto) {
        Long orgId = dto.getOrgId();
        GlobalOrgSeat seat = globalOrgSeatService.getById(user.getSeatId());
        GlobalOrganization organization = globalOrganizationService.getById(orgId);
        if (organization == null) {
            throw new ServiceException("商家不存在");
        }

        // 检查是否有年费会员拉黑商家的权限
        UserBuyerVipInfoVO vipInfo = buyerVipService.getUserBuyerVipInfoBySeatId(seat.getId());
        AssertUtil.assertTrue(vipInfo != null && vipInfo.getBuyerVipType() == BuyerVipTypeEnum.VIP && Boolean.TRUE.equals(vipInfo.getIsAnnualFeeVip()), "仅年费会员可拉黑商家");

        long count = liveOrgFilterService.count(new LambdaQueryWrapper<LiveOrgFilter>().eq(LiveOrgFilter::getOrgId, orgId)
                .eq(LiveOrgFilter::getUserId, user.getUserId())
                .eq(LiveOrgFilter::getSourceType, LiveOrgFilterSourceTypeEnum.USER_BLOCK_MERCHANT)
                .eq(LiveOrgFilter::getFilterMode, LiveOrgFilterModeEnum.BLACKLIST));
        if (count > 0) {
            throw new ServiceException("商家已经在名单中");
        }
        LiveOrgFilter liveOrgFilter = new LiveOrgFilter();
        liveOrgFilter.setOrgId(orgId);
        liveOrgFilter.setUserId(user.getUserId());
        liveOrgFilter.setSeatId(user.getSeatId());
        liveOrgFilter.setUserName(seat.getName());
        liveOrgFilter.setSourceType(LiveOrgFilterSourceTypeEnum.USER_BLOCK_MERCHANT);
        liveOrgFilter.setFilterMode(LiveOrgFilterModeEnum.BLACKLIST);
        liveOrgFilter.setRemark(dto.getRemark());
        liveOrgFilter.setCreatedAt(new Date());
        liveOrgFilter.setCreateUserId(user.getUserId());
        liveOrgFilter.setCreateSeatId(user.getSeatId());
        liveOrgFilter.setCreateUserName(globalOrgSeatService.getSeatName(AuthUtil.getSeatId()));
        liveOrgFilterService.save(liveOrgFilter);
        return liveOrgFilter.getId();
    }

    @Override
    public void remove(Long userId, Long orgId, Long id) {
        liveOrgFilterService.update(new LambdaUpdateWrapper<LiveOrgFilter>()
                .set(LiveOrgFilter::getDeletedAt, LocalDateTime.now())
                .eq(orgId!=null,LiveOrgFilter::getOrgId,orgId)
                .eq(userId!=null,LiveOrgFilter::getUserId,userId)
                .eq(LiveOrgFilter::getId, id));
    }

    /**
     * 获取被屏蔽的商家的所有直播间id
     *
     * @param user 当前登录用户
     * @return 直播间id列表
     */
    @Override
    public List<Long> getFilteredLiveRoomIdList(AuthUser user) {
        IPage<LiveOrgFilterVO> page = liveOrgFilterService.pageListOrg(new PageBase(1, -1), user, LiveOrgFilterSourceTypeEnum.USER_BLOCK_MERCHANT);
        if (CollUtil.isEmpty(page.getRecords())) {
            return List.of();
        }
        List<LiveOrgFilterVO> records = page.getRecords();
        List<Long> orgIdList = records.stream().map(LiveOrgFilterVO::getOrgId).distinct().toList();
        List<LiveRoom> liveRoomList = liveRoomMapper.selectList(Wrappers.lambdaQuery(LiveRoom.class).select(LiveRoom::getId).in(LiveRoom::getOrgId, orgIdList));
        return liveRoomList.stream().map(LiveRoom::getId).toList();
    }
}
