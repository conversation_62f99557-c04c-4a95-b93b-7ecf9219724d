package com.bbh.live.service.msg.dto;

import com.bbh.live.core.msg.MsgType;
import com.bbh.live.service.msg.dto.base.BaseMsg;
import com.bbh.live.service.msg.dto.base.BaseSeat;
import com.bbh.live.service.msg.dto.base.IMsg;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 表情包消息
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class CommonMemeMsgDTO extends BaseMsg implements IMsg {

    private Long liveRoomId;

    /**
     * Emoji图片地址
     */
    private String emojiUrl;

    /**
     * 表情名称
     */
    private String memeName;

    /**
     * 表情ID
     */
    private Long memeId;

    private BaseSeat user;

    /**
     * 每个消息对应一个类型
     *
     * @return {@link MsgType}
     */
    @Override
    public String type() {
        return MsgType.MEME;
    }
}
