package com.bbh.live.service.order.dto;

import com.bbh.live.dao.dto.CreateOrderDTO;
import com.bbh.live.dao.dto.livegoods.LiveGoodsDTO;
import com.bbh.live.service.order.impl.FenbeiCalculator;
import com.bbh.model.GlobalOrder;
import com.bbh.model.GlobalOrderItem;
import com.bbh.model.GlobalOrderSub;
import com.bbh.vo.AuthUser;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 准备订单用的数据传输类
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class PreparedOrderDTO {

    /**
     * 当前登录用户
     */
    private AuthUser user;

    /**
     * 统一当前时间
     */
    private Date now;

    /**
     * 调用请求时的入参，目前包含支付方式、发货方式等，用于创建订单时一并返回
     */
    private CreateOrderDTO createOrderParam;

    /**
     * 重新查询补充数据后的商品列表
     */
    private List<LiveGoodsDTO> liveGoodsList;

    /**
     * 组装生成好的订单项列表
     */
    private List<GlobalOrderItem> orderItemList;

    /**
     * 组装好的商户订单列表
     */
    private List<GlobalOrderSub> orderSubList;

    /**
     * 组装好的总订单
     */
    private GlobalOrder globalOrder;

    /**
     * 将订单项按商户分组后的map <br>
     * key是商户id，value是商品订单
     */
    private Map<Long, List<GlobalOrderItem>> orderItemMap;

    /**
     * 线上支付的分贝计算结果
     */
    private FenbeiCalculator.OnlineResult onlineResult;

    /**
     * 线下支付的扣除分贝计算结果
     */
    private FenbeiCalculator.OfflineResult offlineResult;

}
