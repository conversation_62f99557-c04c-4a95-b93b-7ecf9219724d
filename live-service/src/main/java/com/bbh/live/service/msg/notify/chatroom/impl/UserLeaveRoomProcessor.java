package com.bbh.live.service.msg.notify.chatroom.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.bbh.live.dao.dto.ChatroomStatusSyncDTO;
import com.bbh.live.dao.service.LiveRoomEntryRecordService;
import com.bbh.live.service.msg.notify.chatroom.Status0SubProcessor;
import com.bbh.live.service.msg.notify.chatroom.enums.ChatroomActionType;
import com.bbh.live.service.room.LiveRoomCacheService;
import com.bbh.model.LiveRoomEntryRecord;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * 用户离开聊天室
 * <AUTHOR>
 */
@Service
@AllArgsConstructor
@Slf4j
public class UserLeaveRoomProcessor implements Status0SubProcessor {

    private final LiveRoomEntryRecordService liveRoomEntryRecordService;
    private final LiveRoomCacheService liveRoomCacheService;

    @Override
    public void process(ChatroomStatusSyncDTO dto, Long liveRoomId) {
        var userList = dto.parseUserInfo();
        if (userList == null) {
            log.info("userList is null, chatRoomId: {}", dto.getChatRoomId());
            return;
        }

        userList.forEach(user -> updateEntry(liveRoomId, user.getSeatId(), user.getOrgId()));
    }

    private void updateEntry(Long liveRoomId, Long seatId, Long orgId) {
        // 检查有没有未处理的数据
        List<LiveRoomEntryRecord> entryRecordList = liveRoomEntryRecordService.list(Wrappers.lambdaQuery(LiveRoomEntryRecord.class)
                .eq(LiveRoomEntryRecord::getSeatId, seatId)
                .eq(LiveRoomEntryRecord::getLiveRoomId, liveRoomId)
                .isNull(LiveRoomEntryRecord::getLeaveAt)
        );
        if (CollUtil.isEmpty(entryRecordList)) {
            return;
        }

        for (LiveRoomEntryRecord entryRecord : entryRecordList) {
            Date now = new Date();
            entryRecord.setLeaveAt(now);
            entryRecord.setDuration((int) DateUtil.between(entryRecord.getJoinAt(), now, DateUnit.SECOND));
            liveRoomEntryRecordService.updateById(entryRecord);
        }

        // 更新实时人数缓存
        liveRoomCacheService.incrementRealtimeCount(liveRoomId, -1);
        liveRoomCacheService.removeRoomUser(liveRoomId, seatId);
    }

    @Override
    public boolean canHandle(ChatroomActionType type) {
        return type == ChatroomActionType.LEAVE;
    }
}
