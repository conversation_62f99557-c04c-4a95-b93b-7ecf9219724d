package com.bbh.live.service.msg.notify.chatroom;

import com.bbh.live.dao.dto.ChatroomStatusSyncDTO;
import com.bbh.live.service.msg.notify.chatroom.enums.ChatroomActionType;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 状态0的子处理工厂
 * <AUTHOR>
 */
@Service
@AllArgsConstructor
@Slf4j
public class Status0SubProcessorFactory {

    private final List<Status0SubProcessor> processors;

    public Status0SubProcessor getProcessor(ChatroomStatusSyncDTO dto) {
        ChatroomActionType chatroomActionType = ChatroomActionType.fromValue(dto.getType());
        return processors.stream()
                .filter(p -> p.canHandle(chatroomActionType))
                .findFirst()
                .orElseGet(() -> {
                    log.info("融云回调不支持的类型:{}. 融云消息：{}", dto.getType(), dto);
                    return null;
                });
    }

}
