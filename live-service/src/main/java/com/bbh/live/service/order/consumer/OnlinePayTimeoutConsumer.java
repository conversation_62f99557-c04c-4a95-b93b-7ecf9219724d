package com.bbh.live.service.order.consumer;

import cn.hutool.core.util.CharsetUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.bbh.enums.GlobalOrderStatusEnum;
import com.bbh.live.dao.dto.CancelPayDTO;
import com.bbh.live.dao.service.GlobalOrderService;
import com.bbh.live.service.order.OrderV2Service;
import com.bbh.model.GlobalOrder;
import com.bbh.service.mq.constants.MqConstant;
import com.bbh.service.mq.service.CoreMqListener;
import com.bbh.util.LogExUtil;
import com.rabbitmq.client.Channel;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;

import java.io.IOException;

/**
 * 在线支付超时任务
 * <AUTHOR>
 */
@Component
@Slf4j
@AllArgsConstructor
public class OnlinePayTimeoutConsumer implements CoreMqListener {

    private final OrderV2Service orderV2Service;
    private final GlobalOrderService globalOrderService;

    @RabbitListener(queuesToDeclare = @Queue(name = MqConstant.LIVE_PAY_CLOSE_DELAY, durable = "true"))
    @Override
    public void handle(Message message, Channel channel) {
        String json = StrUtil.str(message.getBody(), CharsetUtil.UTF_8);
        LogExUtil.infoLog("=============> 在线支付超时队列，消息内容:" + json + " <=============");
        try {
            JSONObject body = null;
            try {
                body = JSONUtil.parseObj(json);
            } catch (Exception e) {
                LogExUtil.errorLog("在线支付超时队列出错，非法的消息内容:" + json + " ", e);
                return;
            }

            GlobalOrder globalOrder;
            String orderNo = body.getStr("orderNo");
            if (StrUtil.isBlank(orderNo)) {
                Long orderId = body.getLong("orderId");
                globalOrder = globalOrderService.getOne(Wrappers.lambdaQuery(GlobalOrder.class).eq(GlobalOrder::getId, orderId));
            } else {
                globalOrder = globalOrderService.getOne(Wrappers.lambdaQuery(GlobalOrder.class).eq(GlobalOrder::getOrderNo, orderNo));
            }

            if (globalOrder == null) {
                // 订单不存在，大概率是线下转账订单已被删除，因此不需要error告警
                LogExUtil.warnLog("在线支付超时队列中断，订单不存在:" + json + " ");
                return;
            }

            // 检查订单状态是否待支付，如果不是就直接中断
            if (!globalOrder.getOrderStatus().equals(GlobalOrderStatusEnum.TO_BE_PAID)) {
                LogExUtil.warnLog("在线支付超时队列中断，订单不是待支付:" + json + " + 订单状态:" + globalOrder.getOrderStatus().getCode());
                return;
            }

            CancelPayDTO cancelPayDTO = new CancelPayDTO();
            cancelPayDTO.setIfAuto(true);
            cancelPayDTO.setOrderNo(orderNo);
            try {
                orderV2Service.cancelPay(cancelPayDTO);
            } catch (Exception e) {
                LogExUtil.errorLog("在线支付超时队列出错，订单号:" + orderNo + " ", e);
                return;
            }
        } catch (Exception e) {
            LogExUtil.errorLog("在线支付超时队列出错，消息内容:" + json + " ", e);
        } finally {
            try {
                channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
            } catch (IOException ioException) {
                LogExUtil.errorLog("Failed to ACK message", ioException);
            }
        }
        LogExUtil.infoLog("=============> 在线支付超时任务队列执行成功，消息内容:" + json + " <=============");
    }
}
