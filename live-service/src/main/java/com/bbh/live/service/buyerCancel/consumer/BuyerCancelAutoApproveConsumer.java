package com.bbh.live.service.buyerCancel.consumer;

import cn.hutool.core.util.CharsetUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.bbh.enums.LiveGoodsBuyerCancelRecordStatusEnum;
import com.bbh.exception.ServiceException;
import com.bbh.live.dao.mapper.LiveGoodsBuyerCancelRecordMapper;
import com.bbh.live.service.buyerCancel.BuyerCancelRecordService;
import com.bbh.live.service.buyerCancel.dto.request.SellerCancelRecordApproveRequest;
import com.bbh.model.LiveGoodsBuyerCancelRecord;
import com.bbh.service.mq.constants.MqConstant;
import com.bbh.service.mq.service.CoreMqListener;
import com.rabbitmq.client.Channel;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;

/**
 * 买手取消成交后，商家超时未操作，则自动通过，进入平台审核
 * <AUTHOR>
 */
@Component
@Slf4j
@AllArgsConstructor
public class BuyerCancelAutoApproveConsumer implements CoreMqListener {

    private final LiveGoodsBuyerCancelRecordMapper liveGoodsBuyerCancelRecordMapper;
    private final BuyerCancelRecordService buyerCancelRecordService;

    @RabbitListener(queuesToDeclare = @Queue(name = MqConstant.LIVE_BUYER_CANCEL_REVIEW_TIMEOUT, durable = "true"))
    @Override
    public void handle(Message message, Channel channel) {
        try {
            String json = StrUtil.str(message.getBody(), CharsetUtil.UTF_8);
            log.info("主动取消 超时消费 ===> {}", json);

            // 空消息直接确认
            if (StrUtil.isBlank(json)) {
                log.error("消息内容为空");
                ack(message, channel);
                return;
            }

            // JSON解析异常处理
            JSONObject jsonObject;
            try {
                jsonObject = JSONUtil.parseObj(json);
            } catch (Exception e) {
                log.error("JSON解析失败, message: {}", json, e);
                ack(message, channel); // JSON解析失败的消息直接确认，避免死循环
                return;
            }

            // 拿到id
            Long recordId = jsonObject.getLong("id");
            if (recordId == null) {
                log.error("记录ID为空, message: {}", json);
                ack(message, channel);
                return;
            }

            // 查一下记录
            LiveGoodsBuyerCancelRecord record = liveGoodsBuyerCancelRecordMapper.selectById(recordId);
            if (record == null) {
                // 不存在，直接ack
                ack(message, channel);
                return;
            }

            // 判断是否已经处理
            if (record.getCancelStatus() == LiveGoodsBuyerCancelRecordStatusEnum.PLATFORM_REVIEWING) {
                ack(message, channel);
                return;
            }

            // 走通过流程
            SellerCancelRecordApproveRequest request = new SellerCancelRecordApproveRequest();
            request.setIfDeduct(true);
            request.setOrderId(recordId);
            request.setIfTimeout(true);
            buyerCancelRecordService.sellerApprove(request);
            ack(message, channel);
        } catch (ServiceException e) {
            log.info("业务异常，仍然ACK，因为：{}", e.getMessage());
            ack(message, channel);
        } catch (Exception e) {
            log.error("消息处理发生未预期异常", e);
            nack(message, channel);
        }
    }

    private void nack(Message message, Channel channel) {
        try {
            channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, true);
        } catch (Exception e) {
            log.error("消息确认失败", e);
        }
    }

    private void ack(Message message, Channel channel) {
        try {
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
        } catch (Exception e) {
            log.error("消息确认失败", e);
        }
    }
}
