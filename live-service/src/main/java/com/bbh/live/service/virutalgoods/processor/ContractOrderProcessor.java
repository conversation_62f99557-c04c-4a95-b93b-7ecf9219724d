package com.bbh.live.service.virutalgoods.processor;

import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.bbh.enums.AuditTypeEnum;
import com.bbh.enums.ResponseCode;
import com.bbh.exception.ServiceException;
import com.bbh.feign.IDepositApiClient;
import com.bbh.live.dao.mapper.GlobalOrganizationMapper;
import com.bbh.live.dao.service.GlobalOrgSeatService;
import com.bbh.model.GlobalOrderOtherGoodsDic;
import com.bbh.model.GlobalOrganization;
import com.bbh.model.GlobalVirtualGoodsOrder;
import com.bbh.service.deposit.dto.RechargeDepositDTO;
import com.bbh.util.LogExUtil;
import com.bbh.vo.Result;
import jakarta.annotation.Resource;
import lombok.AllArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 签约缴费
 * <AUTHOR>
 */
@Service
@AllArgsConstructor
public class ContractOrderProcessor implements IVirtualOrderProcessor {

    private final Logger logger = LoggerFactory.getLogger(ContractOrderProcessor.class);

    private final GlobalOrganizationMapper globalOrganizationMapper;
    private final GlobalOrgSeatService globalOrgSeatService;

    @Resource
    private IDepositApiClient depositApiClient;

    @SuppressWarnings("all")
    @Override
    public Result process(VirtualOrderProcessContext context) {
        GlobalVirtualGoodsOrder order = context.getOrder();
        GlobalOrderOtherGoodsDic goodsInfo = context.getGoods();

        // 查org的签约信息
        GlobalOrganization organization = globalOrganizationMapper.selectById(order.getOrgId());

        // 调用充值保证金接口
        RechargeDepositDTO param = new RechargeDepositDTO();
        param.setDeposit(order.getOrderPrice());
        param.setOsn(order.getOrderNo());
        param.setInOrgId(order.getOrgId());
        param.setCreateId(context.getOrder().getBuyerSeatId());
        param.setCreateName(globalOrgSeatService.getSeatName(context.getOrder().getBuyerSeatId()));
        // 固定是卖家充值
        param.setInType(AuditTypeEnum.SELLER);
        Result result = depositApiClient.recharge(param);
        if (result == null) {
            LogExUtil.errorLog("充值保证金失败", new ServiceException("充值保证金失败"), logger);
            throw new ServiceException("充值保证金失败");
        }
        if (result.getCode() != ResponseCode.SUCCESS.getCode()) {
            LogExUtil.errorLog(result.getMsg(), new ServiceException("充值保证金失败: " + result.getMsg()), logger);
            throw new ServiceException("充值保证金失败: " + result.getMsg());
        }

        // 默认当成未签约，那就在当前时间基础上加一年
        Date offsetFrom = new Date();
        // 如果是签约状态且合约未到期，并且当前的商家保证金余额不是0（解约之后就是0），则在原合约结束时间基础上延期
        if (organization.getIfOrgContract() == 1
                && organization.getOrgContractEndTime() != null
                && organization.getOrgContractEndTime().after(new Date())
                && organization.getSellerDeposit().compareTo(BigDecimal.ZERO) > 0
        ) {
            offsetFrom = organization.getOrgContractEndTime();
        }
        globalOrganizationMapper.update(Wrappers.lambdaUpdate(GlobalOrganization.class)
                .eq(GlobalOrganization::getId, order.getOrgId())
                .set(GlobalOrganization::getOrgContractEndTime, DateUtil.offset(offsetFrom, DateField.YEAR, 1)
                ));

        return Result.ok();
    }
}
