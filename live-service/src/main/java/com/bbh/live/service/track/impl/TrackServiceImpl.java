package com.bbh.live.service.track.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.servlet.JakartaServletUtil;
import com.bbh.live.dao.service.GlobalOrgSeatService;
import com.bbh.live.dao.service.IGlobalUserService;
import com.bbh.live.dao.service.LiveGoodsClickRecordService;
import com.bbh.live.dao.service.VipBuyerClickRecordService;
import com.bbh.live.service.buyer.vip.BuyerVipService;
import com.bbh.live.service.buyer.vip.vo.UserBuyerVipInfoVO;
import com.bbh.live.service.track.TrackService;
import com.bbh.model.LiveGoodsClickRecord;
import com.bbh.model.VipBuyerClickRecord;
import com.bbh.secure.AuthUtil;
import com.bbh.vo.AuthUser;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * 埋点服务
 * <AUTHOR>
 */
@AllArgsConstructor
@Slf4j
@Service
public class TrackServiceImpl implements TrackService {


    @Resource
    private HttpServletRequest httpServletRequest;

    private final LiveGoodsClickRecordService liveGoodsClickRecordService;
    private final VipBuyerClickRecordService  vipBuyerClickRecordService;
    private final BuyerVipService buyerVipService;
    private final GlobalOrgSeatService globalOrgSeatService;
    private final IGlobalUserService globalUserService;

    /**
     * 点击商品
     *
     * @param liveGoodsId   直播商品id
     * @param globalGoodsId ERP商品id
     * @param liveRoomId    直播间id
     * @param roomOrgId     直播间的商户id
     */
    @Override
    public void clickGoods(Long liveGoodsId, Long globalGoodsId, Long liveRoomId, Long roomOrgId) {
        try{
            AuthUser user = AuthUtil.getUser(null);
            if(user == null){
                return;
            }
            LiveGoodsClickRecord clickRecord = new LiveGoodsClickRecord();
            clickRecord.setLiveGoodsId(liveGoodsId);
            clickRecord.setGlobalGoodsId(globalGoodsId);
            clickRecord.setLiveRoomId(liveRoomId);
            clickRecord.setOrgId(roomOrgId);
            clickRecord.setCreatedAt(new Date());
            clickRecord.setCreateSeatId(user.getSeatId());
            clickRecord.setCreateUserId(user.getUserId());
            if (httpServletRequest != null) {
                String ip = JakartaServletUtil.getClientIP(httpServletRequest);
                clickRecord.setIpAddress(ip);
                clickRecord.setDeviceInfo(httpServletRequest.getHeader("User-Agent"));
            }
            liveGoodsClickRecordService.save(clickRecord);
        }catch (Exception e){
            log.error("记录商品点击记录异常:{}", e.getMessage(), e);
        }
    }

    @Override
    public void clickVip(VipBuyerClickRecord vipBuyerClickRecord) {
        AuthUser user = AuthUtil.getUser();
        vipBuyerClickRecord.setCreatedAt(new Date());
        vipBuyerClickRecord.setUserId(user.getUserId().intValue());
        vipBuyerClickRecord.setSeatId(user.getSeatId());
        
        // 默认设置空字符串
        vipBuyerClickRecord.setNickName("");
        vipBuyerClickRecord.setUserName("");
        vipBuyerClickRecord.setPhone("");

        // 默认是失败的，等付款成功后再去更新，是否付费成功 0否 1 是（月费vip） 2是（年费vip）
        vipBuyerClickRecord.setPayType(0);

        // 席位信息
        globalOrgSeatService.getOptById(user.getSeatId())
                .ifPresent(seat -> {
                    vipBuyerClickRecord.setNickName(StrUtil.emptyToDefault(seat.getNickName(), ""));
                    vipBuyerClickRecord.setUserName(StrUtil.emptyToDefault(seat.getName(), ""));
                });

        // 会员信息
        globalUserService.getOptById(user.getUserId())
                .ifPresent(u -> {
                    vipBuyerClickRecord.setPhone(StrUtil.emptyToDefault(u.getPhone(), ""));
                });

        // 会员信息
        UserBuyerVipInfoVO vipInfo = buyerVipService.getUserBuyerVipInfoBySeatId(user.getSeatId());
        if (vipInfo != null && vipInfo.getId() != null) {
            vipBuyerClickRecord.setVipBuyerCardId(vipInfo.getId());
        } else {
            vipBuyerClickRecord.setVipBuyerCardId(0L);
        }

        vipBuyerClickRecordService.save(vipBuyerClickRecord);
    }
}
