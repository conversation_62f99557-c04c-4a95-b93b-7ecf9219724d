package com.bbh.live.service.virutalgoods.processor;

import cn.hutool.json.JSONUtil;
import com.bbh.feign.IDepositApiClient;
import com.bbh.feign.dto.SettlePenaltyFeeDTO;
import com.bbh.live.dao.service.GlobalOrgSeatService;
import com.bbh.model.GlobalVirtualGoodsOrder;
import com.bbh.vo.Result;
import jakarta.annotation.Resource;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 违规扣款-补缴
 * <AUTHOR>
 */
@Slf4j
@Service
@AllArgsConstructor
public class LateFeeDepositOrderProcessor implements IVirtualOrderProcessor {

    @Resource
    private IDepositApiClient depositApiClient;

    private final GlobalOrgSeatService globalOrgSeatService;

    @Override
    public Result process(VirtualOrderProcessContext context) {
        // 补缴不是固定的商品金额
        GlobalVirtualGoodsOrder order = context.getOrder();
        String extraData = order.getExtraData();
        if (!JSONUtil.isTypeJSON(extraData)) {
            log.error("非法的补缴数据:{}", extraData);
            return Result.fail("非法的补缴数据");
        }

        // 解析出参数
        SettlePenaltyFeeDTO param = JSONUtil.toBean(extraData, SettlePenaltyFeeDTO.class);

        // 直接调补偿金的接口
        param.setDeposit(order.getOrderPrice());
        param.setOrderNo(order.getOrderNo());
        param.setInOrgId(order.getOrgId());
        param.setCreateId(context.getOrder().getBuyerSeatId());
        param.setCreateName(globalOrgSeatService.getSeatName(context.getOrder().getBuyerSeatId()));
        return depositApiClient.settlePenaltyFee(param);
    }
}
