package com.bbh.live.service.livegoods.context;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2024/12/2 14:47
 * @description
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class AuctionFailLiveGoodsContext extends LiveGoodsContext {

    /**
     * 是否成交结算异常后转的流拍
     */
    private final boolean ifAbnormal;

    public AuctionFailLiveGoodsContext(Long liveGoodsId, boolean ifAbnormal) {
        super.liveGoodsId = liveGoodsId;
        this.ifAbnormal = ifAbnormal;
    }

}
