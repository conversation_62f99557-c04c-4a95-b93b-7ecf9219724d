package com.bbh.live.service.buyer.vip.vo;

import com.bbh.enums.CreateFromEnum;
import com.bbh.live.enums.BuyerVipTypeEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * vip用户买家信息
 *
 * <AUTHOR>
 * @create 2023/7/25 15:15
 * @date 2023/07/25
 */
@Data
public class UserBuyerVipInfoVO {

    /**
     * id
     */
    private Long id;

    /**
     * uid
     */
    private Long seatId;

    /**
     * 店信息id
     */
    private Long orgId;

    /**
     * 经验值
     */
    private Integer exp;

    /**
     * 距离下一等级所需经验值
     */
    private Integer nextLevelNeedExp;

    /**
     * vip结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date timeVipEnd;

    /**
     * 年费是贵宾
     */
    private Boolean isAnnualFeeVip;

    /**
     * 是否是vip
     */
    private Boolean isVip;

    /**
     * 窥探出价人 使用 次数
     */
    private Integer peepBuyerUsedTimes;
    /**
     * 抽奖 使用 次数
     */
    private Integer lotteryUsedTimes;

    /**
     * 修改昵称 使用 次数
     */
    private Integer modifyNicknameUsedTimes;
    /**
     * 售后服务 使用 次数
     */
    private Integer afterSaleServiceUsedTimes;


    /**
     * 窥探出价人 未使用 次数
     */
    private Integer peepBuyerUnUsedTimes;
    /**
     * 抽奖 未使用 次数
     */
    private Integer lotteryUnUsedTimes;

    /**
     * 修改昵称 未使用 次数
     */
    private Integer modifyNicknameUnUsedTimes;
    /**
     * 售后服务 未使用 次数
     */
    private Integer afterSaleServiceUnUsedTimes;


    /**
     * 省钱
     */
    private BigDecimal saveMoney;

    /**
     * vip等级
     */
    private Integer vipLevel = -1;

    /**
     * 0 不是
     * 1 是 没有过期
     * 2 是 但是过期了
     */
    private BuyerVipTypeEnum buyerVipType;

    /**
     * 该有 的 VIP 权益
     */
    private BuyerVipConfigVO vipConfig;

    /**
     * 未使用VIP 权益次数
     */
    private Integer unUseAllVipTimes;

    /**
     * 是否是 保证金用户
     * 0 不是
     * 1 是
     * 2 过期
     */
    private Integer buyerVipMargin;

    /***
     * 年费VIP潜水功能 1潜水 0不潜水 默认0
     */
    private Boolean underwater;

    /**
     * 累计已省分贝
     */
    private Long saveFenbei;


    /**
     * 累计获得分贝
     */
    private Long totalGetFenbei;


    /**
     * 是否要弹出临近过期弹框
     */
    private Boolean nearlyTimeVipEndPop;

    /**
     * 邀请码
     */
    private String vipInviteCode;

    /**
     * 非VIP地图可查看距离
     */
    private Integer unVipNearShopDistance;

    /**
     * 今日是否登录
     */
    private Boolean isSignToday;

    /**
     * 线下支付扣除分贝总数
     */
    private Integer offlineFbDeductNum = 0;

    /**
     * 线下支付扣除分贝次数
     */
    private Integer offlineFbDeductTimes = 0;

    /**
     * 创建来源
     */
    private CreateFromEnum createFrom = CreateFromEnum.APP;

    /** 兼容处理，前端都对接的createdFrom，改成createFrom导致出错了 */
    private CreateFromEnum createdFrom = CreateFromEnum.APP;

    public CreateFromEnum getCreatedFrom() {
        return createFrom;
    }
}
