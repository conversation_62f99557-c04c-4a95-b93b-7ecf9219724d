package com.bbh.live.service.msg;

import cn.hutool.json.JSONUtil;
import com.bbh.live.core.msg.MsgDTO;
import com.bbh.live.dao.mapper.LiveImSendLogMapper;
import com.bbh.model.LiveImSendLog;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 消息日志
 * <AUTHOR>
 */
@Slf4j
@AllArgsConstructor
@Service
public class MsgLogService {

    private final LiveImSendLogMapper liveImSendLogMapper;

    /**
     * 记录发送日志
     */
    public void log(MsgDTO msgDTO) {
        LiveImSendLog sendLog = new LiveImSendLog();
        sendLog.setMsgType(msgDTO.getMsgType());

        // For chatroomId
        Set<?> chatRoomIds = msgDTO.getChatRoomIds();
        if (chatRoomIds != null && !chatRoomIds.isEmpty()) {
            sendLog.setChatroomId(chatRoomIds.stream()
                    .map(Object::toString)
                    .collect(Collectors.joining(",")));
        } else {
            sendLog.setChatroomId(null);
        }

        // For liveRoomId
        Set<?> liveRoomIds = msgDTO.getLiveRoomIds();
        if (liveRoomIds != null && !liveRoomIds.isEmpty()) {
            sendLog.setLiveRoomId(liveRoomIds.stream()
                    .filter(id -> id instanceof Long)
                    .map(id -> (Long) id)
                    .findFirst()
                    .orElse(null));
        } else {
            sendLog.setLiveRoomId(null);
        }
        sendLog.setContent(JSONUtil.toJsonStr(msgDTO.getData()));
        sendLog.setMsgType(msgDTO.getMsgType());
        sendLog.setCreatedAt(new Date());

        liveImSendLogMapper.insert(sendLog);
    }

}
