package com.bbh.live.service.buyer;

import com.bbh.base.ListBase;
import com.bbh.live.controller.req.FenbeiDetailReq;
import com.bbh.live.dao.dto.PresentedFenbeiDTO;
import com.bbh.live.dao.dto.vo.BuyerFenbeiInfo;
import com.bbh.live.service.buyer.sign.vo.BuyerFenbeiDetailVO;

/**
 * <AUTHOR>
 * @date 2024/9/24 10:01
 * @description
 */
public interface BuyerFenbeiService {

    /**
     * 获取用户分贝信息
     * @param seatId
     * @return
     */
    BuyerFenbeiInfo getBuyerFenbeiInfo(Long seatId);

    /**
     * 赠送分贝
     * @param presentedFenbei 赠送分贝对象
     */
    void presentedFenbei(PresentedFenbeiDTO presentedFenbei);

    /**
     * 分贝明细记录列表
     * @param fenbeiDetailReq
     * @return
     */
    ListBase<BuyerFenbeiDetailVO> getFenbeiDetail(FenbeiDetailReq fenbeiDetailReq);

    /**
     * 购买分贝
     * @param virtualOrderId 虚拟订单ID
     */
    void buyFenbei(Long virtualOrderId);
}
