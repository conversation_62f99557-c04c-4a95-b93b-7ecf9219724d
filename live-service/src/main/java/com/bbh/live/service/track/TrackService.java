package com.bbh.live.service.track;

import com.bbh.model.VipBuyerClickRecord;

/**
 * 埋点
 * <AUTHOR>
 */
public interface TrackService {

    /**
     * 点击商品
     * @param liveGoodsId       直播商品id
     * @param globalGoodsId     ERP商品id
     * @param liveRoomId        直播间id
     * @param roomOrgId         直播间的商户id
     */
    void clickGoods(Long liveGoodsId, Long globalGoodsId, Long liveRoomId, Long roomOrgId);

    /**
     * 点击VIP买家，埋点
     * @param vipBuyerClickRecord VIP买家点击记录
     */
    void clickVip(VipBuyerClickRecord vipBuyerClickRecord);
}
