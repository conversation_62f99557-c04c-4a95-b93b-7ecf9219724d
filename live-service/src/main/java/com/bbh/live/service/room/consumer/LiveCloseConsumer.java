package com.bbh.live.service.room.consumer;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import com.bbh.live.dao.mapper.LiveRoomMapper;
import com.bbh.live.service.director.DirectorLiveGoodsOpService;
import com.bbh.live.service.room.LiveStreamService;
import com.bbh.model.LiveRoom;
import com.bbh.service.mq.constants.MqConstant;
import com.rabbitmq.client.Channel;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * 中断直播
 * <AUTHOR>
 */
@Component
@Slf4j
public class LiveCloseConsumer extends AbstractRoomConsumer {

    @Resource
    private LiveStreamService liveStreamService;
    @Resource
    private LiveRoomMapper liveRoomMapper;

    @Resource
    private DirectorLiveGoodsOpService directorLiveGoodsOpService;

    @Override
    protected String getConsumerName() {
        return "直播间关闭倒计时队列";
    }

    @Override
    protected void processLiveRoom(LiveRoom liveRoom, String body) {
        // 检查结束时间是否在1分钟内，如果确实到了结束，再走业务处理
        var seconds = DateUtil.between(liveRoom.getEndAt(), new Date(), DateUnit.SECOND);
        if (seconds < 60) {
            log.info("{} 直播间id={}, 开始关播", getConsumerName(), liveRoom.getId());
            // 走关播回调的逻辑，避免重复关播
            // 最新需求：直播间到点后，不强制关播，列表上看不到，也滑不到，主播正常播，已经在直播间的正常看，导播不能再上架
//            liveStreamService.stopLiveByStreamCallback(liveRoom.getId());
            //处理同步云展
//            directorLiveGoodsOpService.batchSyncToCeByLiveEnd(liveRoom.getId());
        }
    }

    @RabbitListener(queuesToDeclare = @Queue(name = MqConstant.LIVE_CLOSE_DELAY, durable = "true"))
    @Override
    public void handle(Message message, Channel channel) {
        super.handle(message, channel);
    }
}
