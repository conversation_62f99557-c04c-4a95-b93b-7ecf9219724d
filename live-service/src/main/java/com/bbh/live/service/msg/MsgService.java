package com.bbh.live.service.msg;

import com.bbh.live.core.msg.MsgDTO;
import com.bbh.live.dao.dto.BargainGoodsDTO;
import com.bbh.live.enums.LiveRoomEnhancedStatusEnum;
import com.bbh.live.service.livegoods.cache.info.LiveGoodsAuctionBidInfo;
import com.bbh.live.service.msg.dto.base.BaseGoods;
import com.bbh.live.service.msg.dto.base.BaseSeat;
import com.bbh.live.service.msg.dto.base.IMsg;
import com.bbh.live.service.msg.dto.base.SourceType;
import com.bbh.live.service.msg.push.LivePushTypeEnum;
import com.bbh.model.GlobalOrderSub;
import com.bbh.model.LiveRoom;
import org.jetbrains.annotations.NotNull;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 统一发送消息
 *
 * <AUTHOR>
 */
public interface MsgService {

    /**
     * 组装融云消息
     * @param t                 消息体
     * @param context           上下文，包含发送人uid、发送人席位id、房间id
     * @return                  融云消息体
     * @param <T>               实现{@link IMsg}的类
     */
    <T extends IMsg> MsgDTO<T> buildMsgDTO(T t, MsgContext context);

    /**
     * 用户预约商品的消息提醒
     *
     * @param liveRoomId  直播间id
     * @param liveGoodsId 商品id
     */
    void goodsSubscribe(Long liveRoomId, Long liveGoodsId);

    /**
     *
     * @param liveRoomId
     * @param liveGoodsId
     */
    void goodsSubscribeClose(Long liveRoomId, Long liveGoodsId);

    /**
     * 商品竞拍成功提醒
     * @param liveRoomId 直播间id
     * @param liveGoodsId 商品id
     * @param buyerSeatId 买手席位id
     * @param belongPrice 成交价
     */
    void goodsAuctionSuccess(@NotNull Long liveRoomId, Long liveGoodsId, Long buyerSeatId, BigDecimal belongPrice);

    /**
     * 传送消息，导播传送给买手
     * @param liveRoomId 直播间id
     * @param buyerSeatId 买手席位
     * @param liveGoodsTransferId 传送内容ID
     */
    void goodsTransfer(@NotNull Long liveRoomId, Long buyerSeatId, Long liveGoodsTransferId);

    /**
     * 取消传送/传送关闭
     * @param liveRoomId 直播间id
     * @param buyerSeatId 买手席位
     * @param liveGoodsId 商品id
     */
    void goodsTransferClose(@NotNull Long liveRoomId, Long buyerSeatId, Long liveGoodsId);

    /**
     * 传送消息 <br>
     * 导播发起传送，用户同意
     * @param liveRoomId    直播间ID
     * @param liveGoodsId   商品ID
     */
    void goodsTransferAgreed(@NotNull Long liveRoomId, Long liveGoodsId, Long tradeSeatId);

    /**
     * 商品开启竞拍
     * @param liveRoomId    直播间ID
     * @param liveGoodsId   商品ID
     */
    void goodsAuctionStart(@NotNull Long liveRoomId, @NotNull Long liveGoodsId, Date auctionEndAt);

    /**
     * 商品流拍
     * @param liveRoomId    直播间ID
     * @param liveGoodsId   商品ID
     */
    void goodsAbortedAuction(@NotNull Long liveRoomId, Long liveGoodsId);

    /**
     * 商品上架讲解
     * @param liveRoomId    直播间ID
     * @param liveGoodsId   商品ID
     */
    void goodsPutAway(@NotNull Long liveRoomId, Long liveGoodsId);

    /**
     * 商品撤回
     * @param liveRoomId    直播间ID
     * @param liveGoodsId   商品ID
     */
    void goodsRevoke(@NotNull Long liveRoomId, Long liveGoodsId);

    /**
     * 用户发起议价
     * @param liveRoomId    直播间ID
     * @param liveGoodsId   商品ID
     * @param buyerSeatId   买手席位
     */
    void userBargain(@NotNull Long liveRoomId, Long liveGoodsId, Long buyerSeatId, BigDecimal bargainPrice);

    /**
     * 用户议价成功，导播通知用户
     * @param bargainGoodsDTO   议价信息
     */
    void userBargainAgreed(BargainGoodsDTO bargainGoodsDTO);


    /**
     * 导播拒绝用户议价 通知用户
     * @param bargainGoodsDTO    议价信息
     */
    void userBargainReject(BargainGoodsDTO bargainGoodsDTO);

    /**
     * 用户请求讲解商品
     * @param liveRoomId    直播间ID
     * @param liveGoodsId   商品ID
     * @param buyerSeatId   买手席位
     */
    void userAskForExplanation(@NotNull Long liveRoomId, Long liveGoodsId, Long buyerSeatId);

    /**
     * 用户出价成功
     *
     * @param bargainGoodsDTO    竞价信息
     * @param auctionEndAt   竞拍结束时间
     * @param biddenList     所有出价列表
     */
    void userBidSuccess(@NotNull BargainGoodsDTO bargainGoodsDTO, Date auctionEndAt, List<LiveGoodsAuctionBidInfo> biddenList);

    /**
     * 直播结束
     * @param liveRoom    直播间
     * @param status       直播状态
     */
    void liveEnd(@NotNull LiveRoom liveRoom, LiveRoomEnhancedStatusEnum status);

    /**
     * 开始直播
     * @param liveRoomId    直播间ID
     */
    void liveStart(@NotNull Long liveRoomId);

    /**
     * 直播间公告变更通知
     * @param liveRoomId 直播间ID
     * @param content 公告内容
     */
    void roomNoticeChanged(@NotNull Long liveRoomId, @NotNull String content);

    /**
     * 用户进入直播间
     *
     * @param liveRoomId 直播间ID
     * @param userId     用户ID
     * @param seatId     席位ID
     */
    void userEnterRoom(@NotNull Long liveRoomId, Long userId, @NotNull Long seatId);

    /**
     * 普通文本消息
     *
     * @param liveRoomId   直播间ID
     * @param content      文本内容
     * @param senderSeatId 发送者席位
     * @param sourceType   消息来源
     */
    void text(@NotNull Long liveRoomId, @NotNull String content, @NotNull Long senderSeatId, SourceType sourceType);

    /**
     * 表情包
     *
     * @param liveRoomId   直播间ID
     * @param memeId       表情ID
     * @param senderSeatId 发送者席位
     * @param sourceType   消息来源
     */
    void meme(@NotNull Long liveRoomId, @NotNull Long memeId, @NotNull Long senderSeatId, SourceType sourceType);

    BaseSeat retrieveSeatInfo(@NotNull Long seatId);

    BaseGoods retrieveGoodsInfo(@NotNull Long liveGoodsId);

    /**
     * 开启试播消息
     * @param room    直播间id
     */
    void sendTestBroadcastBeginMsg(LiveRoom room);

    /**
     * 结束试播消息
     * @param room    直播间id
     */
    void sendTestBroadcastEndMsg(LiveRoom room);

    /**
     * 发送平台公告，这个消息实际由后台发送
     * @param liveRoomId    直播间id
     * @param content       内容
     */
    void sendPlatformNotice(Long liveRoomId, String content);

    /**
     * 发送系统公告
     * @param liveRoomId    直播间id
     * @param content       内容
     */
    void sendSystemNotice(Long liveRoomId, String content);

    /**
     * 更新商品信息
     * @param liveRoomId    直播间id
     * @param liveGoodsId   商品id
     */
    void goodsUpdate(Long liveRoomId, Long liveGoodsId);

    /**
     * 新增商品信息
     * @param liveRoomId    直播间id
     * @param liveGoodsId   商品id
     */
    void goodsInsert(Long liveRoomId, List<Long> liveGoodsId);

    /**
     * 删除商品
     *
     * @param liveRoomId  直播间id
     * @param liveGoodsId 商品id
     */
    void goodsDelete(Long liveRoomId, List<Long> liveGoodsId);

    /**
     * 商家拉黑用户，让用户直接离开直播间
     * @param liveRoomId    直播间ID
     * @param userId        用户ID
     * @param seatId        席位ID
     */
    void letUserOutNow(Long liveRoomId, Long userId, Long seatId);

    /**
     * 开始直播倒计时
     * <AUTHOR>
     */
    void startLiveCountdown(Long liveRoomId, Long remainingTime);

    /**
     * 关闭直播倒计时
     * <AUTHOR>
     */
    void closeLiveCountdown(Long liveRoomId);

    /**
     * 订单商品发货提醒
     */
    void deliveryOrderGoods(List<GlobalOrderSub> orderSubList);

    /**
     * 线下转账审核失败
     */
    void offlineTransferFailed(Long orderId, Long buyerOrgId, Long buyerSeatId);

    /**
     * 关注了商家
     *
     * @param seatId          谁关注
     * @param liveRoomId      在哪个直播间
     * @param beAttendedOrgId 被关注的商家
     */
    void orgAttention(Long seatId, Long liveRoomId, Long beAttendedOrgId);

    /**
     * 买家主动取消成交，卖家 APP推送
     *
     * @param applyOrderId 取消成交记录id
     * @param sellerOrgId  卖家机构
     * @param pushType  消息推送类型
     * @param params 消息推送参数
     */
    void notifySellerBuyerCancelApproval(Long applyOrderId, Long sellerOrgId, LivePushTypeEnum pushType, Map<String, Object> params);

    /**
     * 买家主动取消成交，买家 APP推送
     * @param applyOrderId 取消成交记录id
     * @param buyerOrgId 买家店铺id
     * @param buyerSeatId 买家席位id
     * @param pushType 消息推送类型
     * @param params 消息推送参数
     */
    void notifyBuyerCancelApproval(Long applyOrderId, Long buyerOrgId, Long buyerSeatId, LivePushTypeEnum pushType, Map<String, Object> params);
}

