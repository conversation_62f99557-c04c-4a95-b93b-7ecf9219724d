package com.bbh.live.service.user;

import com.bbh.live.dao.dto.AggregatedUserDTO;
import com.bbh.live.dao.dto.SimpleUserInfoDTO;

/**
 * 用户信息
 * <AUTHOR>
 */
public interface UserInfoService {

    /**
     * 当前登录用户的信息
     * @return 完整的用户信息
     */
    AggregatedUserDTO current();

    /**
     *
     * @param seatId
     * @return
     */
    SimpleUserInfoDTO getSimpleInfoBySeatId(Long seatId);

}
