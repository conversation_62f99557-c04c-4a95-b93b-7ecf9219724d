package com.bbh.live.service.msg.dto;

import com.bbh.live.core.msg.MsgType;
import com.bbh.live.service.msg.dto.base.BaseAuctionLimitMsg;
import com.bbh.live.service.msg.dto.base.BaseGoods;
import com.bbh.live.service.msg.dto.base.IGodViewMsg;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 流拍
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
public class GoodsAuctionAbortedMsgDTO extends BaseAuctionLimitMsg implements IGodViewMsg {

    @Override
    public String type() {
        return MsgType.AUCTION_ABORTED;
    }

    private BaseGoods goods;

    private List<?> godViewList;

    private Integer tradeType;

    @Override
    public void setGodViewList(List<?> godViewList) {
        this.godViewList = godViewList;
    }
}
