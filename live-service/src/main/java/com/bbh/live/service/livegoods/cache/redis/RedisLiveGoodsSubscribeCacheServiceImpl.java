package com.bbh.live.service.livegoods.cache.redis;

import com.bbh.live.service.livegoods.cache.LiveGoodsCacheConstant;
import com.bbh.live.service.livegoods.cache.LiveGoodsSubscribeCacheService;
import org.redisson.api.RBitSet;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR> dsy
 * @Date: 2024/9/10
 * @Description: 缓存看播订阅商品信息，用于看播刚进直播间时展示。已关闭/已查看的商品不展示 并且只展示商品状态是上架中或竞拍中的
 *
 *      所有商品订阅信息缓存，包括已关闭已查看 bitset。
 *        key ：live_goods_subscribe:${liveRoomId}:${liveGoodsId}
 */
@Component
public class RedisLiveGoodsSubscribeCacheServiceImpl extends BaseRedisLiveGoodsCache implements LiveGoodsSubscribeCacheService {

    private static final ConcurrentHashMap<Long, Map<Long, RBitSet>> subscribeMap = new ConcurrentHashMap<>(64);

    @Override
    public void subscribeLiveGoods(Long liveRoomId, Long liveGoodsId, Long buyerSeatId) {
        getSubscribeBitSet(liveRoomId, liveGoodsId).set(buyerSeatId);
    }

    @Override
    public boolean isSubscribe(Long liveRoomId, Long liveGoodsId, Long buyerSeatId) {
        return getSubscribeBitSet(liveRoomId, liveGoodsId).get(buyerSeatId);
    }

    @Override
    public void cancelSubscribeLiveGoods(Long liveRoomId, Long liveGoodsId, Long buyerSeatId) {
        getSubscribeBitSet(liveRoomId, liveGoodsId).clear(buyerSeatId);
    }

    private RBitSet getSubscribeBitSet(Long liveRoomId, Long liveGoodsId){
        String key = builderKey(LiveGoodsCacheConstant.LIVE_GOODS_SUBSCRIBE_KEY, liveRoomId + ":" + liveGoodsId);
        return subscribeMap.computeIfAbsent(liveRoomId, k -> new ConcurrentHashMap<>(32))
                .computeIfAbsent(liveGoodsId, k -> {
                    RBitSet bitSet = redisson.getBitSet(key);
                    bitSet.expireAsync(Duration.ofSeconds(super.getLiveRoomEndLeft(liveRoomId)));
                    return bitSet;
                });
    }

    @Override
    public void clearAll(Long liveRoomId) {
        Map<Long, RBitSet> bitSetMap = subscribeMap.remove(liveRoomId);
        if (bitSetMap != null) {
            bitSetMap.forEach((k, v) -> v.delete());
        }
    }
}
