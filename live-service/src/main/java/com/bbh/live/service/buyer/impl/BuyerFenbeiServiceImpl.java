package com.bbh.live.service.buyer.impl;

import com.bbh.base.ListBase;
import com.bbh.base.Page;
import com.bbh.live.controller.req.FenbeiDetailReq;
import com.bbh.live.dao.dto.PresentedFenbeiDTO;
import com.bbh.live.dao.dto.vo.BuyerFenbeiInfo;
import com.bbh.live.dao.service.*;
import com.bbh.live.enums.BuyerVipTypeEnum;
import com.bbh.live.service.buyer.BuyerFenbeiService;
import com.bbh.live.service.buyer.sign.vo.BuyerFenbeiDetailVO;
import com.bbh.live.service.buyer.vip.BuyerVipService;
import com.bbh.live.service.buyer.vip.vo.UserBuyerVipInfoVO;
import com.bbh.live.util.PageUtils;
import com.bbh.model.*;
import com.bbh.secure.AuthUtil;
import com.bbh.util.AssertUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/9/24 10:02
 * @description
 */
@Service
@Slf4j
@AllArgsConstructor
public class BuyerFenbeiServiceImpl implements BuyerFenbeiService {

    private final BuyerVipService buyerVipService;
    private final GlobalOrgSeatService globalOrgSeatService;
    private final IGlobalOrganizationService globalOrganizationService;
    private final VipBuyerCardService vipBuyerCardService;
    private final GlobalFenbeiDetailService globalFenbeiDetailService;
    private final GlobalVirtualGoodsOrderService globalVirtualGoodsOrderService;

    @Override
    public BuyerFenbeiInfo getBuyerFenbeiInfo(Long orgId) {
        GlobalOrganization one = globalOrganizationService.lambdaQuery().eq(GlobalOrganization::getId, orgId).select(GlobalOrganization::getFenbei).one();
        return new BuyerFenbeiInfo().setFenbeiNum(one.getFenbei());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void presentedFenbei(PresentedFenbeiDTO presentedFenbei) {
        // 接收人席位id
        Long receiverSeatId = presentedFenbei.getReceiverSeatId();
        // 赠送的分贝数量
        Integer presentedFenbeiNum = presentedFenbei.getPresentedFenbeiNum();

        GlobalOrgSeat receiver = globalOrgSeatService.lambdaQuery().eq(GlobalOrgSeat::getId, receiverSeatId).one();
        AssertUtil.assertNotNull(receiver, "接收人不存在");

        Long presentedSeatId = presentedFenbei.getPresentedSeatId();
        GlobalOrgSeat presenter = globalOrgSeatService.lambdaQuery().eq(GlobalOrgSeat::getId, presentedSeatId).one();
        AssertUtil.assertNotNull(presenter, "转赠人不存在");

        UserBuyerVipInfoVO vipInfo = buyerVipService.getUserBuyerVipInfoBySeatId(presentedSeatId);
        AssertUtil.assertTrue(vipInfo.getBuyerVipType() == BuyerVipTypeEnum.VIP, "接收人必须是VIP");

        // 余额校验
        GlobalOrganization presentedOrganization = globalOrganizationService.lambdaQuery().eq(GlobalOrganization::getId, presentedFenbei.getPresentedOrgId())
                .select(GlobalOrganization::getId, GlobalOrganization::getFenbei).one();

        AssertUtil.assertTrue(presentedOrganization.getFenbei() >= presentedFenbeiNum, "账户余额不足");

        // 根据vip等级计算折扣后接收到的分贝数量 接收到的分贝数量 = 赠送分贝数量 * （1 - vip等级折扣）
        Integer receivedFenbeiNum = new BigDecimal(presentedFenbeiNum).multiply(BigDecimal.ONE.subtract(vipInfo.getVipConfig().getCanReceiveFenbei())).setScale(0, RoundingMode.FLOOR).intValue();

        if(log.isDebugEnabled()){
            log.debug("分贝赠送：转赠人：{}，接收人：{}，赠送分贝：{}，接收分贝：{}", presenter.getAuctionCode() + "-" + presenter.getShowName(),
                    receiver.getAuctionCode() + "-" + receiver.getShowName(), presentedFenbeiNum, receivedFenbeiNum);
        }
        // 转赠分贝
        globalOrganizationService.presentedFenbei(presenter, receiver, presentedFenbeiNum, receivedFenbeiNum);

        // 增加vip获得分贝
        vipBuyerCardService.lambdaUpdate().eq(VipBuyerCard::getSeatId, receiverSeatId)
                .setIncrBy(VipBuyerCard::getTotalGetFenbei, receivedFenbeiNum)
                .update();
    }

    @Override
    public ListBase<BuyerFenbeiDetailVO> getFenbeiDetail(FenbeiDetailReq fenbeiDetailReq) {
        Page<GlobalFenbeiDetail> fenbeiDetailPage = globalFenbeiDetailService.lambdaQuery()
                //.eq(GlobalFenbeiDetail::getCreateSeatId, fenbeiDetailReq.getSeatId())
                .eq(GlobalFenbeiDetail::getOrgId, AuthUtil.getOrgId())
                .orderByDesc(GlobalFenbeiDetail::getCreatedAt)
                .page(PageUtils.getPage(fenbeiDetailReq, GlobalFenbeiDetail.class));

        List<BuyerFenbeiDetailVO> detailVOList = fenbeiDetailPage.getRecords().stream().map(BuyerFenbeiDetailVO::build).toList();
        return new ListBase<>(detailVOList, fenbeiDetailPage.getTotal(), fenbeiDetailPage.getCurrent(), fenbeiDetailPage.getSize());
    }

    /**
     * 购买分贝
     *
     * @param virtualOrderId 虚拟订单ID
     */
    @Override
    public void buyFenbei(Long virtualOrderId) {
        GlobalVirtualGoodsOrder order = globalVirtualGoodsOrderService.getById(virtualOrderId);
        AssertUtil.assertNotNull(order, "订单不存在");

        // 分贝数量
        Integer fbNumber = order.getGoodsNumber();

        var buyerVipInfo = buyerVipService.getUserBuyerVipInfoBySeatId(order.getBuyerSeatId());
        globalOrganizationService
                .lambdaUpdate()
                .eq(GlobalOrganization::getId, order.getBuyerOrgId())
                .setIncrBy(GlobalOrganization::getFenbei, fbNumber)
                .update();

        // 分贝变更明细
        globalFenbeiDetailService.savePayFenbeiDetail(order, globalOrganizationService.getOrgFenbeiNumber(buyerVipInfo.getOrgId()));
    }
}
