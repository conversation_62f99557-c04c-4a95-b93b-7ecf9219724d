package com.bbh.live.service.room.context;

import com.bbh.enums.LiveRoomStreamStatusEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * <AUTHOR> dsy
 * @Date: 2024/7/29
 * @Description:
 */
@Data
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class LiveRoomContext {

    /**
     * 直播间id
     */
    private Long roomId;

    /**
     * 直播间名称
     */
    private String roomName;

    /**
     * 直播间状态: 00-关闭, 10-开启
     */
    private LiveRoomStreamStatusEnum streamStatus;

    /**
     * 设置的开播时间
     */
    private Date startAt;

    /**
     * 设置的关播时间
     */
    private Date endAt;

    /**
     * 主播id
     */
    private Long anchorUserId;

    /**
     * 直播间保证金购买力
     */
    private Integer depositRate;

    /**
     * 竞拍时间追加阈值，竞拍时间低于这个阈值，有人出价则追加时间<br>
     * 单位:(s)
     */
    private Integer increaseSurplusTime;

    /**
     *  竞拍时间追加的秒数<br>
     *  单位:(s)
     */
    private Integer increaseTime;

    /**
     * 即拍即上剩余次数
     */
    private Integer offhandRemainTimes;

}
