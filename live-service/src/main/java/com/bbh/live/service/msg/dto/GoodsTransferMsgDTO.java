package com.bbh.live.service.msg.dto;

import cn.hutool.core.text.CharPool;
import com.bbh.live.core.msg.MsgType;
import com.bbh.live.service.msg.dto.base.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.function.Consumer;

/**
 * 商品传送-用户收到的卡片弹窗消息
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
public class GoodsTransferMsgDTO extends BaseMsg implements IMultiExpiringMsg, IMsgStoreProcess {


    /**
     * 直播间ID
     */
    private Long liveRoomId;

    /**
     * 卖家商户ID
     */
    private Long sellerOrgId;

    /**
     * 传送价格
     */
    private BigDecimal transferPrice;

    /**
     * 过期时间
     */
    private Date expiredAt;

    /**
     * 剩余时间
     */
    private Integer remainTime;

    private BaseSeat user;

    private BaseGoods goods;

    private Integer tradeType;

    private Consumer<Void> handleExpiryConsumer;

    public static String buildBuyerKey(Long targetSeatId) {
        return MsgType.TRANSFER + CharPool.COLON + "buyer" + CharPool.COLON + targetSeatId;
    }

    public static String buildDirectorKey(Long liveRoomId) {
        return MsgType.TRANSFER + CharPool.COLON + "director" + CharPool.COLON + liveRoomId;
    }

    /**
     * 每个消息对应一个类型
     *
     * @return {@link MsgType}
     */
    @Override
    public String type() {
        return MsgType.TRANSFER;
    }

    @Override
    public Date expiredAt() {
        return this.expiredAt;
    }

    @Override
    public List<String> uniqueKeys() {
        return List.of(buildBuyerKey(user.getSeatId()), buildDirectorKey(liveRoomId));
    }
}
