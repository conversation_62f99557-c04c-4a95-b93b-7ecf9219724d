package com.bbh.live.service.buyerCancel.converter;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bbh.base.ListBase;
import com.bbh.enums.GlobalOrderTypeEnum;
import com.bbh.enums.LiveGoodsBuyerCancelRecordStatusEnum;
import com.bbh.live.service.buyerCancel.dto.LiveGoodsBuyerCancelRecordDetailDTO;
import com.bbh.live.service.buyerCancel.dto.response.BuyerCancelRecordDetailVO;
import com.bbh.live.service.buyerCancel.dto.response.BuyerCancelRecordListVO;
import com.bbh.model.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;

// DTO与实体类的转换器
@Service
@Slf4j
public class BuyerCancelRecordConverter {

    public BuyerCancelRecordDetailVO toTransactionInfo(LiveGoods liveGoods, ErpGoods erpGoods, GlobalOrganization sellerOrg, GlobalOrgSeat buyerSeat) {
        BuyerCancelRecordDetailVO vo = new BuyerCancelRecordDetailVO();

        BuyerCancelRecordDetailVO.OrgInfo sellerOrgInfo = new BuyerCancelRecordDetailVO.OrgInfo();
        sellerOrgInfo.setId(sellerOrg.getId());
        sellerOrgInfo.setName(sellerOrg.getName());
        sellerOrgInfo.setLogoUrl(sellerOrg.getLogoUrl());
        vo.setSellerOrgInfo(sellerOrgInfo);

        BuyerCancelRecordDetailVO.GoodsInfo goodsInfo = new BuyerCancelRecordDetailVO.GoodsInfo();
        goodsInfo.setId(erpGoods.getId());
        goodsInfo.setName(liveGoods.getGoodsName());
        goodsInfo.setImgUrlList(CollUtil.getFirst(erpGoods.getImgUrlList()));
        goodsInfo.setQuality(erpGoods.getQuality());
        goodsInfo.setPeerPrice(erpGoods.getPeerPrice());
        goodsInfo.setCreatedAt(erpGoods.getCreatedAt());
        goodsInfo.setImgDetailUrlList(erpGoods.getImgUrlList());
        goodsInfo.setGoodsPrice(liveGoods.getSellPrice());
        goodsInfo.setBizType(GlobalOrderTypeEnum.LIVE);
        goodsInfo.setBizGoodsId(liveGoods.getId());
        goodsInfo.setBizId(liveGoods.getLiveRoomId());
        goodsInfo.setLiveGoodsId(liveGoods.getId());
        goodsInfo.setLiveRoomId(liveGoods.getLiveRoomId());
        goodsInfo.setLiveGoodsCode(liveGoods.getLiveGoodsCode().toString());
        goodsInfo.setBelongName(buyerSeat.getName());
        goodsInfo.setBelongNickName(buyerSeat.getNickName());
        goodsInfo.setBelongShowName(buyerSeat.getShowName());
        goodsInfo.setBelongAuctionCode(buyerSeat.getAuctionCode());
        goodsInfo.setTradeType(liveGoods.getTradeType().getCode());
        goodsInfo.setBelongType(liveGoods.getBelongType().getCode());
        goodsInfo.setLiveVideoUrl(liveGoods.getLiveVideoUrl());
        vo.setGoodsInfo(goodsInfo);

        return vo;
    }

    public BuyerCancelRecordDetailVO toTransactionInfo(SceGoods sceGoods, ErpGoods erpGoods, GlobalOrganization sellerOrg, GlobalOrgSeat buyerSeat) {
        BuyerCancelRecordDetailVO vo = new BuyerCancelRecordDetailVO();

        BuyerCancelRecordDetailVO.OrgInfo sellerOrgInfo = new BuyerCancelRecordDetailVO.OrgInfo();
        sellerOrgInfo.setId(sellerOrg.getId());
        sellerOrgInfo.setName(sellerOrg.getName());
        sellerOrgInfo.setLogoUrl(sellerOrg.getLogoUrl());
        vo.setSellerOrgInfo(sellerOrgInfo);

        BuyerCancelRecordDetailVO.GoodsInfo goodsInfo = new BuyerCancelRecordDetailVO.GoodsInfo();
        goodsInfo.setId(erpGoods.getId());
        goodsInfo.setName(erpGoods.getName());
        goodsInfo.setImgUrlList(CollUtil.getFirst(erpGoods.getImgUrlList()));
        goodsInfo.setQuality(erpGoods.getQuality());
        goodsInfo.setPeerPrice(erpGoods.getPeerPrice());
        goodsInfo.setCreatedAt(erpGoods.getCreatedAt());
        goodsInfo.setImgDetailUrlList(erpGoods.getImgUrlList());
        //todo
        goodsInfo.setGoodsPrice(sceGoods.getSellPrice());
        goodsInfo.setBizGoodsId(sceGoods.getId());
        goodsInfo.setBizType(GlobalOrderTypeEnum.CE);

        goodsInfo.setBelongName(buyerSeat.getName());
        goodsInfo.setBelongNickName(buyerSeat.getNickName());
        goodsInfo.setBelongShowName(buyerSeat.getShowName());
        goodsInfo.setBelongAuctionCode(buyerSeat.getAuctionCode());
        goodsInfo.setBelongType(sceGoods.getBelongType().getCode());
        goodsInfo.setVideoUrlList(erpGoods.getVideoUrlList());
        vo.setGoodsInfo(goodsInfo);

        return vo;
    }


    public ListBase<BuyerCancelRecordListVO> toRecordList(Page<LiveGoodsBuyerCancelRecordDetailDTO> pageResult, boolean isSeller) {
        if (pageResult == null || CollUtil.isEmpty(pageResult.getRecords())) {
            return ListBase.of();
        }

        List<BuyerCancelRecordListVO> records = pageResult.getRecords().stream().map(x -> {
            BuyerCancelRecordListVO vo = new BuyerCancelRecordListVO();

            // 记录信息
            vo.setRecordInfo(x);

            // 商品信息
            BuyerCancelRecordDetailVO.GoodsInfo goodsInfo = new BuyerCancelRecordDetailVO.GoodsInfo();
            goodsInfo.setId(x.getId());
            goodsInfo.setName(x.getGoodsName());
            List<String> imgUrlList = JSONUtil.toList(x.getImgUrlList(), String.class);
            goodsInfo.setImgUrlList(CollUtil.getFirst(imgUrlList));
            goodsInfo.setQuality(x.getQuality());
            goodsInfo.setPeerPrice(x.getPeerPrice());
            goodsInfo.setCreatedAt(x.getGlobalCreatedAt());
            goodsInfo.setImgDetailUrlList(imgUrlList);
            goodsInfo.setGoodsPrice(x.getSellPrice());
            goodsInfo.setBizGoodsId(x.getLiveGoodsId());
            goodsInfo.setBizId(x.getLiveRoomId());
            goodsInfo.setLiveGoodsId(x.getLiveGoodsId());
            goodsInfo.setLiveRoomId(x.getLiveRoomId());
            goodsInfo.setLiveGoodsCode(x.getLiveGoodsCode().toString());
            goodsInfo.setBelongName(x.getBelongName());
            goodsInfo.setBelongNickName(x.getBelongNickName());
            goodsInfo.setBelongShowName(StrUtil.join("-", List.of(x.getBelongAuctionCode(), x.getBelongShowName())));
            goodsInfo.setBelongAuctionCode(x.getBelongAuctionCode());
            goodsInfo.setTradeType(x.getTradeType());
            goodsInfo.setBelongType(x.getBelongType());
            goodsInfo.setLiveVideoUrl(x.getLiveVideoUrl());
            vo.setGoodsInfo(goodsInfo);

            // 卖家商户信息
            BuyerCancelRecordDetailVO.OrgInfo sellerOrgInfo = new BuyerCancelRecordDetailVO.OrgInfo();
            sellerOrgInfo.setId(x.getSellerOrgId());
            sellerOrgInfo.setName(x.getSellerOrgName());
            sellerOrgInfo.setLogoUrl(x.getBuyerLogoUrl());
            vo.setSellerOrgInfo(sellerOrgInfo);

            // 买家商户信息
            BuyerCancelRecordDetailVO.OrgInfo buyerOrgInfo = new BuyerCancelRecordDetailVO.OrgInfo();
            buyerOrgInfo.setId(x.getBuyerOrgId());
            buyerOrgInfo.setName(x.getBuyerOrgName());
            buyerOrgInfo.setLogoUrl(x.getBuyerLogoUrl());
            vo.setBuyerOrgInfo(buyerOrgInfo);

            // 买家席位信息
            BuyerCancelRecordDetailVO.SeatInfo buyerSeatInfo = new BuyerCancelRecordDetailVO.SeatInfo();
            buyerSeatInfo.setId(x.getBelongSeatId());
            buyerSeatInfo.setName(x.getBelongShowName());
            buyerSeatInfo.setAvatar(x.getBelongAvatar());
            vo.setBuyerSeatInfo(buyerSeatInfo);

            // bizGoods信息
            BuyerCancelRecordDetailVO.BizGoodsInfo bizGoodsInfo = new BuyerCancelRecordDetailVO.BizGoodsInfo();
            bizGoodsInfo.setBizGoodsId(x.getLiveGoodsId());
            bizGoodsInfo.setBizId(x.getLiveRoomId());
            if (x.getLiveGoodsCode() != null) {
                bizGoodsInfo.setLiveGoodsCode(x.getLiveGoodsCode().toString());
            }
            vo.setBizGoods(bizGoodsInfo);

            // 直播间信息
            BuyerCancelRecordDetailVO.LiveRoomInfo liveRoomInfo = new BuyerCancelRecordDetailVO.LiveRoomInfo();
            liveRoomInfo.setId(x.getLiveRoomId());
            liveRoomInfo.setRoomName(x.getLiveRoomName());
            liveRoomInfo.setStart(x.getLiveRoomStartAt());
            vo.setLiveRoomInfo(liveRoomInfo);

            // id
            vo.setId(x.getId());

            // 拼接描述信息
            if (x.getCancelStatus() == LiveGoodsBuyerCancelRecordStatusEnum.COMPLETED) {
                String doneDesc = "";
                if (x.getIfDeduct()) {
                    // 扣除金额
                    BigDecimal deductAmount = x.getDeductDepositAmount().setScale(0, RoundingMode.DOWN);
                    // 补偿给商家的金额
                    BigDecimal compensateToSellerAmount = x.getCompensateSellerAmount().setScale(0, RoundingMode.DOWN);

                    // 销售列表上显示“补偿50保证金”
                    // 采购列表显示“扣除50保证金”
                    doneDesc = StrUtil.format("{}{}保证金", isSeller ? "补偿" : "扣除", isSeller ? compensateToSellerAmount : deductAmount);
                } else {
                    doneDesc = "无需扣保证金";
                }
                vo.setDoneDesc(doneDesc);
            }

            return vo;
        }).toList();

        ListBase<BuyerCancelRecordListVO> result = new ListBase<>();
        result.setRecords(records);
        result.setTotal(pageResult.getTotal());
        result.setCurrentPage(pageResult.getCurrent());
        result.setPerPage(pageResult.getSize());
        return result;
    }

    /**
     * 采购 显示的状态
     *
     * @param detail
     * @return
     */
    public BuyerCancelRecordDetailVO.StatusInfo toBuyerStatus(LiveGoodsBuyerCancelRecordDetailDTO detail) {
        BuyerCancelRecordDetailVO.StatusInfo statusInfo = new BuyerCancelRecordDetailVO.StatusInfo();
        statusInfo.setStatus(detail.getCancelStatus().getCode());
        switch (detail.getCancelStatus()) {
            case WAITING_SELLER:
                statusInfo.setCalcTitle("待商家处理");
                statusInfo.setCancelTitle(DateUtil.formatDateTime(detail.getTimeoutAt()));
                statusInfo.setCancelDesc("前商家未处理，将自动同意取消并扣除保证金");
                break;
            case PLATFORM_REVIEWING:
                statusInfo.setCalcTitle("待平台处理");
                statusInfo.setCancelTitle("平台审核中");
                statusInfo.setCancelDesc("商家已同意，并要求扣除保证金");
                break;
            default:
                statusInfo.setCalcTitle("已完成");
                // 根据是否扣除保证金，显示不同的内容
                if (detail.getIfDeduct()) {
                    BigDecimal amount = detail.getDeductDepositAmount().setScale(0, RoundingMode.DOWN);
                    statusInfo.setCancelTitle(StrUtil.format("判定：扣除{}元保证金", amount));
                    statusInfo.setCancelDesc("商家已同意，并要求扣除保证金");
                } else {
                    statusInfo.setCancelTitle("无需扣除保证金");
                    statusInfo.setCancelDesc("商家已直接同意");
                }
        }
        statusInfo.setCalcDesc(StrUtil.format("{} 发起申请", DateUtil.formatDateTime(detail.getBuyerApplyAt())));

        return statusInfo;
    }

    /**
     * 销售 显示的状态
     *
     * @param detail
     * @return
     */
    public BuyerCancelRecordDetailVO.StatusInfo toSellerStatus(LiveGoodsBuyerCancelRecordDetailDTO detail) {
        BuyerCancelRecordDetailVO.StatusInfo statusInfo = new BuyerCancelRecordDetailVO.StatusInfo();
        statusInfo.setStatus(detail.getCancelStatus().getCode());
        switch (detail.getCancelStatus()) {
            case WAITING_SELLER:
                statusInfo.setCalcTitle("待处理");
                statusInfo.setCancelTitle(DateUtil.formatDateTime(detail.getTimeoutAt()));
                statusInfo.setCancelDesc("前未处理，将自动同意取消并对您补偿");
                break;
            case PLATFORM_REVIEWING:
                statusInfo.setCalcTitle("已同意，待平台处理");
                statusInfo.setCancelTitle("平台审核中");
                statusInfo.setCancelDesc("您已同意，并要求进行补偿");
                break;
            default:
                statusInfo.setCalcTitle("已完成");
                // 根据是否扣除保证金，显示不同的内容
                if (detail.getIfDeduct()) {
                    BigDecimal amount = detail.getCompensateSellerAmount().setScale(0, RoundingMode.DOWN);
                    statusInfo.setCancelTitle(StrUtil.format("判定：补偿您{}元", amount));
                    statusInfo.setCancelDesc("您已同意，并要求进行补偿");
                } else {
                    statusInfo.setCancelTitle("无需补偿");
                    statusInfo.setCancelDesc("您已直接同意");
                }
        }
        statusInfo.setCalcDesc(StrUtil.format("{} 发起申请", DateUtil.formatDateTime(detail.getBuyerApplyAt())));

        return statusInfo;
    }

}
