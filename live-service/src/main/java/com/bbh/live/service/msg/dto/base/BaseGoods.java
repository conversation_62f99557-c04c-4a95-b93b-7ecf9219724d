package com.bbh.live.service.msg.dto.base;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 通用商品信息
 * <AUTHOR>
 */
@Data
public class BaseGoods {

    /**
     * 商品清单ID
     */
    private Long liveGoodsId;

    /**
     * ERP货品ID
     */
    private Long globalGoodsId;

    /**
     * 交易类型
     */
    private Integer tradeType;

    /**
     * 商品名称
     */
    private String globalGoodsName;

    /**
     * 商品图
     */
    private List<String> imgUrlList;

    /**
     * 成色
     */
    private String quality;

    // 起拍价、加价幅度、竞拍时长
    private BigDecimal startPrice;
    private BigDecimal increasePrice;
    private Integer auctionDuration;

    /**
     * 商品状态
     */
    private Integer goodsStatus;

    /**
     * 成交价
     */
    private BigDecimal sellPrice;

    /**
     * 商品拍号
     */
    private String liveGoodsCode;


    private String directorRemark;

}
