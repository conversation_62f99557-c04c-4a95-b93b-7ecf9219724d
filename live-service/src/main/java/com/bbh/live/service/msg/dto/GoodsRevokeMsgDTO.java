package com.bbh.live.service.msg.dto;

import com.bbh.live.core.msg.MsgType;
import com.bbh.live.service.msg.dto.base.BaseAuctionLimitMsg;
import com.bbh.live.service.msg.dto.base.BaseGoods;
import com.bbh.live.service.msg.dto.base.IGodViewMsg;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 商品撤回
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class GoodsRevokeMsgDTO extends BaseAuctionLimitMsg implements IGodViewMsg {

    private BaseGoods goods;

    private Integer tradeType;

    /**
     * 每个消息对应一个类型
     *
     * @return {@link MsgType}
     */
    @Override
    public String type() {
        return MsgType.REVOKE;
    }

    private List<?> godViewList;

    @Override
    public void setGodViewList(List<?> godViewList) {
        this.godViewList = godViewList;
    }

}
