package com.bbh.live.service.msg.consumer;

import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.bbh.live.constant.DelayQueueTopics;
import com.bbh.live.dao.mapper.LiveGoodsTransferMapper;
import com.bbh.live.handler.queue.DelayJob;
import com.bbh.live.handler.queue.consumer.AbstractMessageConsumer;
import com.bbh.live.service.msg.MsgService;
import com.bbh.live.service.msg.dto.GoodsTransferMsgDTO;
import com.bbh.model.LiveGoodsTransfer;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * 商品传送到期 消费
 * @param <T> 传送的消息JSON
 * <AUTHOR>
 */
@Service
@Slf4j
@AllArgsConstructor
public class GoodsTransferConsumer extends AbstractMessageConsumer<String> {

    private final LiveGoodsTransferMapper liveGoodsTransferMapper;

    /**
     * 定义topic
     */
    @Override
    public String topic() {
        return DelayQueueTopics.LIVE_TRANSFER_TOPIC;
    }

    /**
     * 消费
     *
     * @param delayJob 传送消息JSON
     */
    @Override
    public void consume(DelayJob<String> delayJob) {
        log.info("GoodsTransferConsumer consume: {}", delayJob);
        GoodsTransferMsgDTO transferMsg = JSONUtil.toBean(delayJob.getData(), GoodsTransferMsgDTO.class);
        log.info("GoodsTransferConsumer consume: {}", transferMsg);

        // 获取必要的信息
        Long liveRoomId = transferMsg.getLiveRoomId();
        Long liveGoodsId = transferMsg.getGoods().getLiveGoodsId();
        Long seatId = transferMsg.getUser().getSeatId();

        // 发送传送关闭的消息
        SpringUtil.getBean(MsgService.class).goodsTransferClose(liveRoomId, seatId, liveGoodsId);

        // 更新传送状态
        LambdaUpdateWrapper<LiveGoodsTransfer> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        lambdaUpdateWrapper.eq(LiveGoodsTransfer::getLiveGoodsId, liveGoodsId);
        lambdaUpdateWrapper.eq(LiveGoodsTransfer::getTargetSeatId, seatId);
        lambdaUpdateWrapper.set(LiveGoodsTransfer::getIfHandled, true);
        lambdaUpdateWrapper.set(LiveGoodsTransfer::getExpireAt, new Date());
        liveGoodsTransferMapper.update(lambdaUpdateWrapper);
    }
}
