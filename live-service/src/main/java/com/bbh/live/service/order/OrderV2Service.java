package com.bbh.live.service.order;

import com.bbh.live.dao.dto.*;
import com.bbh.live.dao.dto.vo.CreateOrderV2VO;

/**
 * 订单处理Service
 * <AUTHOR>
 */
public interface OrderV2Service {

    /**
     * 准备订单信息，将商品id根据直播场次进行分组后，计算商品总额、买手服务费
     * @param prepareOrderDTO   勾选的商品id
     * @return  返回准备页面需要的数据
     */
    PrepareOrderVO prepareOrderInfoV2(PrepareOrderDTO prepareOrderDTO);

    /**
     * 创建订单，返回调起支付参数
     *
     * @param createOrderV2DTO 创建订单参数
     * @return 调起支付参数
     */
    CreateOrderV2VO createOrderV2(CreateOrderV2DTO createOrderV2DTO);

    /**
     * 取消支付，把商品退回到直播的购物车
     * @param cancelPayDTO 取消支付参数
     */
    void cancelPay(CancelPayDTO cancelPayDTO);

    /**
     * 支付成功回调，更新订单状态, 返还保证金
     */
    String paySuccessCallBack(SignDTO signDTO);

    /**
     * 单独的调起支付接口
     *
     * @param payDTO 流水号
     * @return 调起支付参数
     */
    Object pay(PayDTO payDTO);

    /**
     * 支付成功处理
     * @param outTradeNo    订单流水号
     */
    void paySuccess(String outTradeNo);

    /**
     * 检查支付是否成功
     *
     * @param orderId 订单ID
     * @return 支付是否成功: true/false
     */
    Boolean checkPaySuccess(Long orderId);

    /**
     * 线下转账
     * @param offlineTransferDTO 线下转账参数
     */
    void offlineTransfer(OfflineTransferDTO offlineTransferDTO);

    /**
     * 订单状态数量分组
     * @return  订单状态数量
     */
    OrderStatusCountGroupDTO getStatusCountGroup(OrderCenterDTO orderCenterDTO);

    /**
     * 消息未读数量
     * @return  消息未读数量
     */
    OrderStatusCountGroupDTO getUnreadMessage(OrderCenterDTO orderCenterDTO);
}
