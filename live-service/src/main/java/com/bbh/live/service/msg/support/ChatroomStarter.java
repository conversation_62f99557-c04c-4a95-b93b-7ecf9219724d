package com.bbh.live.service.msg.support;

import com.bbh.live.core.msg.ImProperties;
import com.bbh.live.core.msg.ImService;
import lombok.AllArgsConstructor;
import org.jetbrains.annotations.NotNull;
import org.springframework.boot.context.event.ApplicationStartedEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024/9/26 10:52
 * @description
 */
@Component
@AllArgsConstructor
public class ChatroomStarter implements ApplicationListener<ApplicationStartedEvent> {

    private final ImService imService;
    private final ImProperties imProperties;
    @Override
    public void onApplicationEvent(@NotNull ApplicationStartedEvent event) {
        if(imProperties.getAppKey() == null || imProperties.getAppSecret() == null){
            return;
        }
        imService.createChatRoomKeepLive(ImProperties.LIVE_ROOM_CHAT_ROOM_FULL_INFO);
    }
}
