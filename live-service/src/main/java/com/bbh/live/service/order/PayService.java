package com.bbh.live.service.order;

import com.bbh.live.service.order.dto.OrderBuilderContext;
import com.bbh.live.service.order.dto.PreparedOrderDTO;
import com.bbh.model.GlobalVirtualGoodsOrder;

import java.util.Date;
import java.util.List;

/**
 * 支付
 * <AUTHOR>
 */
public interface PayService {

    /**
     * 普通订单的调起支付，支持线上线下两种方式
     * @param context           订单上下文
     * @param preparedOrderDTO  准备好的订单数据
     * @return  支付参数
     */
    Object orderPay(OrderBuilderContext context, PreparedOrderDTO preparedOrderDTO);

    /**
     * 普通订单的线上支付，支持线上线下两种方式
     * @param context           订单上下文
     * @param preparedOrderDTO  准备好的订单数据
     * @return  支付参数
     */
    Object orderOnlinePay(OrderBuilderContext context, PreparedOrderDTO preparedOrderDTO);

    /**
     * 虚拟订单的线上线上支付
     * @param context           订单上下文
     * @param order             虚拟订单
     * @return  支付参数
     */
    Object virtualOrderOnlinePay(OrderBuilderContext context, GlobalVirtualGoodsOrder order);

    /**
     * 普通订单的线下支付，支持线上线下两种方式
     * @param context           订单上下文
     * @param preparedOrderDTO  准备好的订单数据
     * @return  支付参数
     */
    Object orderOfflinePay(OrderBuilderContext context, PreparedOrderDTO preparedOrderDTO);

    /**
     * 普通订单的关闭支付
     * @param outTradeNo    订单流水号
     */
    void orderClosePay(String outTradeNo);

    boolean getOrderPaidInfoStatus(String outTradeNo);

    /**
     * 线下转账上传凭证
     * @param outTradeNo        订单流水号
     * @param offlineTransferImgList    凭证图片
     * @return  上传结果
     */
    Object offlinePayUpload(String outTradeNo, List<String> offlineTransferImgList);

    /**
     * 获取线下转账结果
     * @param outTradeNo        订单流水号
     * @param orderCreateAt     订单创建时间
     * @return  线下转账结果
     */
    Object getOfflinePaidInfo(String outTradeNo, Date orderCreateAt);
}
