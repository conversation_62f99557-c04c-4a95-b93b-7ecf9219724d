package com.bbh.live.service.room.context;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.bbh.enums.LiveRoomStreamStatusEnum;
import com.bbh.exception.ServiceException;
import com.bbh.live.dao.service.LiveRoomService;
import com.bbh.model.LiveRoom;

/**
 * <AUTHOR> dsy
 * @Date: 2024/7/29
 * @Description:
 */
public class LiveRoomContextHolder {

    private static final ThreadLocal<LiveRoomContext> ROOM_CONTEXT = new ThreadLocal<>();

    private static LiveRoomService liveRoomService = null;

    public static void initRoomContext(Long roomId) {
        if(liveRoomService == null){
            liveRoomService = SpringUtil.getBean(LiveRoomService.class);
        }

        LiveRoom liveRoom = liveRoomService.lambdaQuery()
                .eq(LiveRoom::getId, roomId)
                .select(LiveRoom::getRoomName, LiveRoom::getStartAt, LiveRoom::getEndAt, LiveRoom::getAnchorUserId,
                        LiveRoom::getIncreaseSurplusTime, LiveRoom::getIncreaseTime, LiveRoom::getStreamStatus, LiveRoom::getDepositRate,
                        LiveRoom::getIfTestBroadcast, LiveRoom::getOffhandRemainTimes)
                .one();
        if(liveRoom == null){
            throw new ServiceException("直播间不存在");
        }
        LiveRoomContext liveRoomContext = new LiveRoomContext();
        liveRoomContext.setRoomId(roomId).setRoomName(liveRoom.getRoomName())
                .setStartAt(liveRoom.getStartAt())
                .setEndAt(liveRoom.getEndAt())
                .setAnchorUserId(liveRoom.getAnchorUserId())
                .setIncreaseSurplusTime(liveRoom.getIncreaseSurplusTime())
                .setIncreaseTime(liveRoom.getIncreaseTime())
                .setStreamStatus(liveRoom.getStreamStatus())
                .setDepositRate(liveRoom.getDepositRate())
                .setOffhandRemainTimes(liveRoom.getOffhandRemainTimes() == null ? 0 : liveRoom.getOffhandRemainTimes())
                ;
        ROOM_CONTEXT.set(liveRoomContext);
    }

    public static Long getRoomId() {
        return ROOM_CONTEXT.get().getRoomId();
    }

    /**
     * 直播间是否已开始
     * @return
     */
    public static boolean liveRoomIsStarted() {
        LiveRoomContext liveRoomContext = ROOM_CONTEXT.get();
        return liveRoomContext.getStartAt().before(DateUtil.date());
    }

    /**
     * 直播间是否还没开始
     * @return
     */
    public static boolean liveRoomIsBeforeStart() {
        LiveRoomContext liveRoomContext = ROOM_CONTEXT.get();
        return liveRoomContext.getStartAt().after(DateUtil.date());
    }

    /**
     * 直播间是否已结束：包含结束时间和直播流的双重判断
     * @return
     */
    public static boolean liveRoomIsFinished() {
        LiveRoomContext liveRoomContext = ROOM_CONTEXT.get();
        return liveRoomContext.getEndAt().before(DateUtil.date()) && LiveRoomStreamStatusEnum.OFF.equals(liveRoomContext.getStreamStatus());
    }

    /**
     * 直播间是否进行中
     * @return
     */
    public static boolean liveRoomIsUnderway() {
        LiveRoomContext liveRoomContext = ROOM_CONTEXT.get();
        DateTime now = DateUtil.date();
        return liveRoomContext.getStartAt().before(now) && LiveRoomStreamStatusEnum.ON.equals(liveRoomContext.getStreamStatus());
    }

    /**
     * 直播间是否到了结束时间
     */
    public static boolean liveRoomIsEnd() {
        LiveRoomContext liveRoomContext = ROOM_CONTEXT.get();
        DateTime now = DateUtil.date();
        return liveRoomContext.getEndAt().before(now);
    }

    /**
     * 直播间是否正在试播
     * @return
     */
    public static boolean liveRoomIsTestBroadcast(){
        // 开播前就是试播
        return SpringUtil.getBean(LiveRoomService.class).ifLiveRoomTestBroadcast(ROOM_CONTEXT.get().getStartAt());
    }

    public static Integer getLiveRoomDepositRate(){
        return ROOM_CONTEXT.get().getDepositRate();
    }

    public static LiveRoomContext getLiveRoomContext() {
        return ROOM_CONTEXT.get();
    }

    public static void setLiveRoomContext(LiveRoomContext liveRoomContext) {
        ROOM_CONTEXT.set(liveRoomContext);
    }


    public static void clearRoom() {
        ROOM_CONTEXT.remove();
    }


}
