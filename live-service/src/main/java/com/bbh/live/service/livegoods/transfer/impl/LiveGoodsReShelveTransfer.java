package com.bbh.live.service.livegoods.transfer.impl;

import com.bbh.enums.ErpGoodsSaleStatusEnum;
import com.bbh.enums.LiveGoodsStatusEnum;
import com.bbh.live.constant.DelayQueueTopics;
import com.bbh.live.dao.dto.livegoods.LiveGoodsDTO;
import com.bbh.live.handler.queue.DelayJob;
import com.bbh.live.service.livegoods.context.LiveGoodsContext;
import com.bbh.util.AssertUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> dsy
 * @Date: 2024/7/29
 * @Description: 流派商品重新上架| 上架商品，竞拍商品撤回   回到待上架列表第一条
 */
@Slf4j
@Service("liveGoodsReShelveTransfer")
public class LiveGoodsReShelveTransfer extends AbstractLiveGoodsTransfer<LiveGoodsContext>{

    @Override
    protected void doCheck(LiveGoodsDTO liveGoods, LiveGoodsContext context) {
        //上架讲解和竞拍中都可主动下架，竞拍中下架必须无人出价才行
        LiveGoodsStatusEnum goodsStatus = liveGoods.getGoodsStatus();
        if (goodsStatus == LiveGoodsStatusEnum.AUCTION){
            //正在竞拍的信息
            Boolean revoke = liveGoodsCacheManager.getLiveGoodsAuctionCache().setAuctionGoodsBeginRevoke(liveGoods.getLiveRoomId(), liveGoods.getId());
            AssertUtil.assertTrue(revoke, "当前商品已有人出价，不可撤回");
        }
    }

    @Override
    protected void doAction(LiveGoodsDTO liveGoods, LiveGoodsContext context) {
        Date now = new Date();
        // 商品当前的状态
        LiveGoodsStatusEnum goodsStatus = liveGoods.getGoodsStatus();
        switch (goodsStatus){
            case ABORTIVE_AUCTION:
                // 已流拍 -> 重新上架
                abortiveAuctionGoodsReturn(liveGoods);
                // 修改回放地址，下面会更新
                liveGoods.setLiveVideoUrl("");
                break;
            case AUCTION:
            case PUT_AWAY:
                // 已上架 -> 撤回
                revocationGoodsReturn(liveGoods, now);
                break;
            default:
                break;
        }

        //商品回到待上架列表
        liveGoods.setGoodsStatus(LiveGoodsStatusEnum.WAIT_PUT_AWAY);
        liveGoodsDetailService.updateLiveGoods(liveGoods);

        //排第一位
        liveGoodsListService.updateLiveGoodsSortAsWaitPutAwayFirst(liveGoods.getId());
    }


    /**
     * 流拍商品处理
     */
    private void abortiveAuctionGoodsReturn(LiveGoodsDTO liveGoods){
        // 流拍数量减一
        liveRoomCacheService.incrementUnsoldCount(liveGoods.getLiveRoomId(), -1);
    }


    /**
     * 撤回商品处理
     * @param liveGoods
     */
    private void revocationGoodsReturn(LiveGoodsDTO liveGoods, Date now){

        LiveGoodsStatusEnum goodsStatus = liveGoods.getGoodsStatus();
        //商品撤回，更新结束时间
        liveGoods.setEndAt(now);

        //竞拍中商品
        if(goodsStatus == LiveGoodsStatusEnum.AUCTION){
            //实际竞拍时长
            liveGoods.setActualAuctionDuration(super.actualAuctionTime(liveGoods));

            //停止延时任务
            DelayJob<Long> delayJob = new DelayJob<>();
            delayJob.setTopic(DelayQueueTopics.AUCTION_TOPIC);
            delayJob.setJobId(liveGoods.getLiveRoomId().toString());
            delayJob.setData(liveGoods.getId());
            delayQueueManager.remove(delayJob);
        }

        //发送消息 提示商品被撤回
        msgService.goodsRevoke(liveGoods.getLiveRoomId(), liveGoods.getId());

        // 如果还ERP的失效商品，要删除看播端列表中的商品
        // 商品已锁单,商品已开单,商品已售出
        if (liveGoods.getErpIfLocked()
                || liveGoods.getErpPlaceOrderStatus()
                || liveGoods.getErpSaleStatus().equals(ErpGoodsSaleStatusEnum.SOLD_OUT.getType())
                || liveGoods.getGoodsStatus().equals(LiveGoodsStatusEnum.TRADED)
        ){
            msgService.goodsDelete(liveGoods.getLiveRoomId(), List.of(liveGoods.getId()));
        }

    }
}
