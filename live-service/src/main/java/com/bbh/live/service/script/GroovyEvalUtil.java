package com.bbh.live.service.script;

import groovy.lang.Binding;
import groovy.lang.GroovyClassLoader;
import groovy.lang.GroovyShell;
import groovy.lang.GroovySystem;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @date 2024/12/3 14:17
 * @description
 */
public class GroovyEvalUtil {

    public static Object groovyExec(String groovyText, Binding binding, ClassLoader parentClassLoader) {
        GroovyShell shell = new GroovyShell(parentClassLoader, binding);
        try {
            StringBuilder script = new StringBuilder();
            script.append("import java.util.*;\n");
            script.append("import java.lang.*;\n");
            script.append("import cn.hutool.extra.spring.SpringUtil;\n");
            script.append("import com.bbh.model.*;\n");
            script.append("import cn.hutool.core.date.*;\n");
            script.append(groovyText);
            Object value = shell.evaluate(script.toString());

            value = value == null ? StringUtils.EMPTY : value.toString();

            return value;
        } finally {
            try {
                if(shell.getClassLoader() != null){
                    GroovyClassLoader groovyClassLoader = shell.getClassLoader();
                    for(Class<?> c : groovyClassLoader.getLoadedClasses()) {
                        GroovySystem.getMetaClassRegistry().removeMetaClass(c);
                    }
                    groovyClassLoader.clearCache();
                    groovyClassLoader.close();
                }
            }catch (Throwable ignore){}
        }
    }
}
