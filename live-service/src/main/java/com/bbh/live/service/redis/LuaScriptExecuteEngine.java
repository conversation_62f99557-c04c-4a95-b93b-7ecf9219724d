package com.bbh.live.service.redis;

import cn.hutool.core.io.IoUtil;
import cn.hutool.core.io.LineHandler;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.bbh.util.AssertUtil;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.data.redis.core.script.RedisScript;

import java.io.IOException;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 */
public class LuaScriptExecuteEngine {

    private static final ConcurrentHashMap<String, RedisScript<?>> SCRIPT_CACHE = new ConcurrentHashMap<>(16);
    private static final ConcurrentHashMap<String, String> SCRIPT_STRING_CACHE = new ConcurrentHashMap<>(16);
    private static final String LUA_SCRIPT_LOCATION = "classpath:lua/*.lua";
    private static final StringRedisTemplate stringRedisTemplate;

    /**
     * 枚举code与文件名保持一致
     * @param luaScript
     * @return
     */
    @SuppressWarnings("unchecked")
    public static <T> T execScipt(LuaScript luaScript, Class<T> resultType, List<String> keys, Object... objects) {
        String script = SCRIPT_STRING_CACHE.get(luaScript.getCode());
        AssertUtil.assertFalse(StrUtil.isBlank(script), "lua脚本不存在，名称：" + luaScript.getCode());

        RedisScript<T> redisScript = (RedisScript<T>) LuaScriptExecuteEngine.SCRIPT_CACHE
                .computeIfAbsent(script, s -> new DefaultRedisScript<>(script, resultType));
        return stringRedisTemplate.execute(redisScript, keys, objects);
    }

    public static void reload(){
        load();
        SCRIPT_CACHE.clear();
    }

    private static void load(){
        PathMatchingResourcePatternResolver pmr = new PathMatchingResourcePatternResolver(LuaScript.class.getClassLoader());
        try {
            Resource[] resources = pmr.getResources(LUA_SCRIPT_LOCATION);
            for (Resource resource : resources) {
                StringBuilder sb = new StringBuilder();
                IoUtil.readUtf8Lines(resource.getInputStream(), (LineHandler) line -> {
                    if(!line.startsWith("---")){
                        sb.append(line);
                    }
                });
                SCRIPT_STRING_CACHE.put(Objects.requireNonNull(resource.getFilename()).split("\\.")[0], sb.toString());
            }
        } catch (IOException e) {
            throw new RuntimeException("lua脚本加载失败", e);
        }
    }

    static {
        load();
        stringRedisTemplate = SpringUtil.getBean("stringRedisTemplate", StringRedisTemplate.class);
    }
}
