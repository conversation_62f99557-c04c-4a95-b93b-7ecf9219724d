package com.bbh.live.service.buyer.vip;

import cn.hutool.extra.spring.SpringUtil;
import com.bbh.live.service.buyer.vip.vo.BuyerVipConfigVO;
import com.bbh.model.VipBuyerConfig;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/8/15 11:12
 */
public class BuyerVipUtil {

    public static BuyerVipConfigVO getBuyerVipConfig(Integer exps, boolean ifYear) {
        BuyerVipService buyerVipService = SpringUtil.getBean(BuyerVipService.class);
        List<VipBuyerConfig> vipBuyerConfigs = buyerVipService.initBuyerVip(ifYear);

        BuyerVipConfigVO buyerVip0 = new BuyerVipConfigVO();
        BuyerVipConfigVO currentVip = null;

        for (VipBuyerConfig config : vipBuyerConfigs) {
            Integer nextLevelExp = config.getNextLevelExp();
            Integer needExp = config.getNeedExp();

            if (needExp <= exps && nextLevelExp > exps) {
                currentVip = new BuyerVipConfigVO();
                setVipConfig(currentVip, config);
                break;
            }

            if (needExp == 0) {
                setVipConfig(buyerVip0, config);
            }
        }
        return currentVip != null ? currentVip : buyerVip0;
    }

    private static void setVipConfig(BuyerVipConfigVO vipConfig, VipBuyerConfig config) {
        vipConfig.setVipLevel(config.getVipLevel());
        vipConfig.setNeedExp(config.getNeedExp());
        vipConfig.setVipMedal(config.getVipMedal());
        vipConfig.setNearShopDistance(config.getNearShopDistance());
        vipConfig.setSignExtraFenbei(config.getSignExtraFenbei());
        vipConfig.setCanReceiveFenbei(config.getCanReceiveFenbei());
        vipConfig.setGoldCustomerService(config.getGoldCustomerService());
        vipConfig.setPeepBuyerNum(config.getPeepBuyerNum());
        vipConfig.setLotteryNum(config.getLotteryNum());
        vipConfig.setModifyNicknameNum(config.getModifyNicknameNum());
        vipConfig.setSaleAfterServiceNum(config.getSaleAfterServiceNum());
        vipConfig.setFenbeiMallDiscount(config.getFenbeiMallDiscount());
        vipConfig.setNextLevelExp(config.getNextLevelExp());
        vipConfig.setOfflineFbDeductNum(config.getOfflineFbDeductNum());
        vipConfig.setSecKillRandomWeight(config.getSecKillRandomWeight());
    }

    public static Integer getBuyerVipLevel(Integer exps, boolean ifYear) {
        BuyerVipService buyerVipService = SpringUtil.getBean(BuyerVipService.class);
        List<VipBuyerConfig> vipBuyerConfigs = buyerVipService.initBuyerVip(ifYear);

        for (VipBuyerConfig config : vipBuyerConfigs) {
            Integer nextLevelExp = config.getNextLevelExp();
            Integer needExp = config.getNeedExp();

            if (needExp <= exps && nextLevelExp > exps) {
                return config.getVipLevel();
            }
        }
        return 0;
    }
}
