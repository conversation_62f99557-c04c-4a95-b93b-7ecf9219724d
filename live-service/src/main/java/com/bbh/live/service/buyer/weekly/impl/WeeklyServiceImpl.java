package com.bbh.live.service.buyer.weekly.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.bbh.exception.ServiceException;
import com.bbh.live.dao.dto.QueryWeeklyDTO;
import com.bbh.live.dao.dto.QueryWeeksDTO;
import com.bbh.live.dao.dto.vo.*;
import com.bbh.live.dao.mapper.*;
import com.bbh.live.service.buyer.vip.BuyerVipService;
import com.bbh.live.service.buyer.vip.vo.UserBuyerVipInfoVO;
import com.bbh.live.service.buyer.weekly.WeeklyService;
import com.bbh.model.*;
import com.bbh.secure.AuthUtil;
import com.bbh.util.AssertUtil;
import com.bbh.vo.AuthUser;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 行情周报 Service
 *
 * <AUTHOR>
 */
@Service
@Slf4j
@AllArgsConstructor
public class WeeklyServiceImpl implements WeeklyService {

    private final VipWeeklyMapper vipWeeklyMapper;
    private final VipWeeklySpuMapper vipWeeklySpuMapper;
    private final VipWeeklySkuGoodsMapper vipWeeklySkuGoodsMapper;
    private final VipWeeklySpuGoodsMapper vipWeeklySpuGoodsMapper;
    private final VipWeeklySpuDetailMapper vipWeeklySpuDetailMapper;
    private final BuyerVipService buyerVipService;
    private final VipBuyerPayRecordMapper vipBuyerPayRecordMapper;

    /**
     * 查询行情周报
     *
     * @param query 查询参数
     * @return 分页后的周报列表
     */
    @Override
    public WeeklyVO getWeekly(QueryWeeklyDTO query) {
        // 校验会员权限，获取会员充值记录
        List<VipBuyerPayRecord> vipBuyerPayRecordList = validVipAndReturnPayRecordList();

        // 没有充值记录的不给看
        if (CollUtil.isEmpty(vipBuyerPayRecordList)) {
            return new WeeklyVO();
        }

        WeeklyVO result = new WeeklyVO();
        VipWeekly vipWeekly = null;

        // 是否查询历史或本周
        if (query.getIfHistory()) {
            // 历史查询：获取指定年月周的周报
            vipWeekly = selectWeekly(query.getYear(), query.getMonth(), query.getWeek(), vipBuyerPayRecordList);
            result = buildWeeklyVO(vipWeekly);
        } else {
            // 非历史查询：获取当前月的周报
            List<VipWeekly> weeklyList = selectWeeklyMonth(query.getYear(), query.getMonth(), vipBuyerPayRecordList);
            // 获取最新的周报
            vipWeekly = weeklyList.stream().max(Comparator.comparing(VipWeekly::getDayStart)).orElse(null);
        }

        // 如果在会员时间范围内没有找到周报，则获取最新的周报
        if (vipWeekly == null && CollUtil.isNotEmpty(vipBuyerPayRecordList)) {
            vipWeekly = getLatestWeekly();
        }

        // 如果仍然没有找到周报，构建一个空的周报VO
        if (vipWeekly == null) {
            result = buildWeeklyVO(query.getYear(), query.getMonth(), query.getWeek());
        } else {
            result = buildWeeklyVO(vipWeekly);
        }

        // 设置下次周报更新时间
        result.setSendDay(calcNextSendDay());

        // 周报ID是否存在
        if (result.getId() == null) {
            // 不存在直接返回
            return result;
        }

        // 存在，再获取关联的SPU列表
        List<WeeklySpuVO> weeklySpuVOList = selectWeeklySpuListByWeeklyId(result.getId()).stream().map(x -> {
            WeeklySpuVO vo = new WeeklySpuVO();
            BeanUtil.copyProperties(x, vo);

            // 处理封面图片
            vo.setCoverImg(CollUtil.getFirst(x.getLogos()));

            return vo;
        }).toList();
        result.setSpuList(weeklySpuVOList);

        // 再获取关联的SKU列表
        List<WeeklySkuGoodsVO> skuGoodsVOList = selectWeeklySkuList(result.getId()).stream().map(x -> {
            WeeklySkuGoodsVO vo = new WeeklySkuGoodsVO();
            BeanUtil.copyProperties(x, vo);
            // 封面图片
            vo.setCoverImgMax(x.getCoverImg());
            return vo;
        }).toList();
        result.setSkuList(skuGoodsVOList);

        return result;
    }

    private VipWeekly getLatestWeekly() {
        return vipWeeklyMapper.selectOne(new LambdaQueryWrapper<VipWeekly>()
                .lt(VipWeekly::getDayEnd, new Date())
                .orderByDesc(VipWeekly::getDayEnd)
                .last("LIMIT 1"));
    }
    /**
     * 查询历史周报的筛选：年月周
     *
     * @param query 查询参数
     * @return 年、月、周列表
     */
    @Override
    public List<WeeksVO> getWeeks(QueryWeeksDTO query) {
        // 校验会员权限，获取会员充值记录
        List<VipBuyerPayRecord> vipBuyerPayRecordList = validVipAndReturnPayRecordList();

        // 即使没有充值记录，也返回当前的年月周
        return selectDistinctWeekly(query.getYear(), query.getMonth(), vipBuyerPayRecordList);
    }


    /**
     * 查询SPU的最近成交
     *
     * @param weeklySpuId spu的id
     * @return 最近成交记录
     */
    @Override
    public WeeklySpuVO getWeeklySpu(Long weeklySpuId) {
        AuthUser authUser = AuthUtil.getUser();

        WeeklySpuVO weeklySpuVO = selectWeeklySpuListByWeeklySpuId(weeklySpuId);
        AssertUtil.assertTrue(weeklySpuVO != null, "未查询到SPU");

        // 商品列表，根据品级排序
        List<WeeklySpuGoodsVO> spuGoodsVOList = selectAllByWeeklySpuId(weeklySpuId);
        weeklySpuVO.setSpuGoodsList(spuGoodsVOList);
        // 处理封面图
        if (CollUtil.isNotEmpty(weeklySpuVO.getLogos())) {
            weeklySpuVO.setCoverImg(CollUtil.getFirst(weeklySpuVO.getLogos()));
        }
        // 获取spu详情
        VipWeeklySpuDetail detail = vipWeeklySpuDetailMapper.selectOne(Wrappers.lambdaQuery(VipWeeklySpuDetail.class)
                .eq(VipWeeklySpuDetail::getWeeklyId, weeklySpuVO.getWeeklyId())
                .eq(VipWeeklySpuDetail::getWeeklySpuId, weeklySpuId)
                .eq(VipWeeklySpuDetail::getSeatId, authUser.getSeatId())
        );
        if (detail == null || detail.getResult() == null) {
            return weeklySpuVO;
        }
        // 涨跌平
        switch (detail.getResult()) {
            case UP -> weeklySpuVO.setUpFlag(true);
            case DOWN -> weeklySpuVO.setDownFlag(true);
            case MIDDLE -> weeklySpuVO.setMiddleFlag(true);
        }
        return weeklySpuVO;
    }

    /**
     * 校验会员权限，返回会员充值记录
     * @return  会员充值记录
     */
    private List<VipBuyerPayRecord> validVipAndReturnPayRecordList() {
        AuthUser authUser = AuthUtil.getUser();
        UserBuyerVipInfoVO vip = buyerVipService.getUserBuyerVipInfoBySeatId(authUser.getSeatId());
        if(!vip.getIsVip()){
            return List.of();
        }

        // 检查会员充值记录
        List<VipBuyerPayRecord> vipBuyerPayRecordList = vipBuyerPayRecordMapper.selectList(Wrappers
                .lambdaQuery(VipBuyerPayRecord.class)
                .eq(VipBuyerPayRecord::getVipBuyerCardId, vip.getId())
        );
        return vipBuyerPayRecordList == null ? List.of() : vipBuyerPayRecordList;
    }

    /**
     * 构建返回值
     */
    private WeeklyVO buildWeeklyVO(VipWeekly vipWeekly) {
        WeeklyVO weeklyVO = new WeeklyVO();
        BeanUtil.copyProperties(vipWeekly, weeklyVO);

        // 如果全都是null，要给当前日期
        if (weeklyVO.getYear() == null || weeklyVO.getMonth() == null || weeklyVO.getWeek() == null) {
            Date now = new Date();
            weeklyVO.setYear(DateUtil.year(now));
            weeklyVO.setMonth(DateUtil.month(now) + 1);
            weeklyVO.setWeek(DateUtil.weekOfMonth(now));
            weeklyVO.setSendDay(calcNextSendDay());
        }

        return weeklyVO;
    }

    /**
     * 构建返回值
     */
    private WeeklyVO buildWeeklyVO(Integer year, Integer month, Integer week) {
        Date now = new Date();
        WeeklyVO weeklyVO = new WeeklyVO();
        weeklyVO.setYear(Objects.requireNonNullElse(year, DateUtil.year(now)));
        weeklyVO.setMonth(Objects.requireNonNullElse(month, DateUtil.month(now) + 1));
        weeklyVO.setWeek(Objects.requireNonNullElse(week, DateUtil.weekOfMonth(now)));
        weeklyVO.setSendDay(calcNextSendDay());
        return weeklyVO;
    }

    /**
     * 计算下次发送日期，并格式化：M月d号
     * @return 下次发送日期
     */
    private String calcNextSendDay() {
        // 获取下个周一的日期
        DateTime nextWeek = DateUtil.nextWeek();
        DateTime dateTime = DateUtil.beginOfWeek(nextWeek);
        String formatted = DateUtil.format(dateTime, "M月d号");
        log.info("{}", formatted);
        return formatted;
    }

    /**
     * 根据条件查询某一周的周报
     * @param year          年
     * @param month         月
     * @param week          周
     * @param vipRecords    会员充值记录
     * @return              周报信息
     */
    private VipWeekly selectWeekly(int year, int month, int week, List<VipBuyerPayRecord> vipRecords) {
        return vipWeeklyMapper.selectOne(new LambdaQueryWrapper<VipWeekly>()
                .eq(VipWeekly::getYear, year)
                .eq(VipWeekly::getMonth, month)
                .eq(VipWeekly::getWeek, week)
                .lt(VipWeekly::getDayStart, new Date())
                .and(CollUtil.isNotEmpty(vipRecords), wrapper -> vipRecords.forEach(item ->
                        wrapper.or(w -> w
                                .le(VipWeekly::getDayStart, item.getEndTime())
                                .ge(VipWeekly::getDayEnd, item.getStartTime())
                        )
                ))
                .orderByDesc(VipWeekly::getId)
                .last("limit 1")
        );
    }

    private List<VipWeekly> selectWeeklyAll(List<VipBuyerPayRecord> vipRecords) {
        return vipWeeklyMapper.selectList(new LambdaQueryWrapper<VipWeekly>()
                .lt(VipWeekly::getDayEnd, new Date())
                .orderByDesc(VipWeekly::getYear)
                .and(CollUtil.isNotEmpty(vipRecords), wrapper -> vipRecords.forEach(item ->
                        wrapper.or(w -> w
                                .le(VipWeekly::getDayStart, item.getEndTime())
                                .ge(VipWeekly::getDayEnd, item.getStartTime())
                        )
                ))
        );
    }

    /**
     * 按月查询行情周报
     * @param year          年
     * @param month         月
     * @param vipRecords    会员充值记录
     * @return              某月的周报列表
     */
    private List<VipWeekly> selectWeeklyMonth(Integer year, Integer month, List<VipBuyerPayRecord> vipRecords) {
        return vipWeeklyMapper.selectList(new LambdaQueryWrapper<VipWeekly>()
                .eq(year != null, VipWeekly::getYear, year)
                .eq(month != null, VipWeekly::getMonth, month)
                .lt(VipWeekly::getDayStart, new Date())
                .and(CollUtil.isNotEmpty(vipRecords), wrapper -> vipRecords.forEach(item ->
                        wrapper.or(w -> w
                                .le(VipWeekly::getDayStart, item.getEndTime())
                                .ge(VipWeekly::getDayEnd, item.getStartTime())
                        )
                ))
        );
    }

    /**
     * 查询周报中的SPU列表
     * @param weeklyId 周报ID
     * @return SPU列表
     */
    private List<VipWeeklySpu> selectWeeklySpuListByWeeklyId(Long weeklyId) {
        return vipWeeklySpuMapper.selectList(new LambdaQueryWrapper<VipWeeklySpu>()
                .eq(VipWeeklySpu::getWeeklyId, weeklyId)
                .orderByAsc(VipWeeklySpu::getSort)
        );
    }

    /**
     * 查询周报中的SKU商品列表
     * @param weeklyId  周报ID
     * @return SKU列表
     */
    private List<VipWeeklySkuGoods> selectWeeklySkuList(Long weeklyId) {
        LambdaQueryWrapper<VipWeeklySkuGoods> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(VipWeeklySkuGoods::getWeeklyId, weeklyId)
                .orderByAsc(VipWeeklySkuGoods::getSort);
        return vipWeeklySkuGoodsMapper.selectList(queryWrapper);
    }

    /**
     * 查询获取会员期间，特定的所有周报的时间单元列表 <br>
     * 如果年、月都为空，说明要查询的是年份列表 <br>
     * 如果有年份但没有月份，说明要根据年份查询月份列表 <br>
     * 如果年月都不为空，说明要根据年月查询周列表
     * @param year          年
     * @param month         月
     * @param vipRecords    会员充值记录
     * @return              时间单元列表
     */
    private List<WeeksVO> selectDistinctWeekly(Integer year, Integer month, List<VipBuyerPayRecord> vipRecords) {
        List<VipWeekly> result;

        if (CollUtil.isEmpty(vipRecords)) {
            // 如果没有充值记录，直接返回当前时间对应的数据
            return getCurrentTimeWeeks(year, month);
        }

        LambdaQueryWrapper<VipWeekly> queryWrapper = new LambdaQueryWrapper<>();

        if (year == null && month == null) {
            // 查询所有年份
            queryWrapper.select(VipWeekly::getYear);
        } else if (year != null && month == null) {
            // 查询特定年份的所有月份
            queryWrapper.select(VipWeekly::getMonth)
                    .eq(VipWeekly::getYear, year);
        } else if (year != null) {
            // 查询特定年月的所有周
            queryWrapper.select(VipWeekly::getWeek)
                    .eq(VipWeekly::getYear, year)
                    .eq(VipWeekly::getMonth, month);
        } else {
            throw new ServiceException("参数有误，不支持的条件");
        }

        // 设置通用查询条件
        queryWrapper
                // 确保周报结束日期在当前日期之前
                .lt(VipWeekly::getDayEnd, new Date())
                .and(CollUtil.isNotEmpty(vipRecords), wrapper -> vipRecords.forEach(item ->
                        wrapper.or(w -> w
                                .le(VipWeekly::getDayStart, item.getEndTime())
                                .ge(VipWeekly::getDayEnd, item.getStartTime())
                        )
                ))
                // 根据查询类型设置排序
                .orderByDesc(year == null ? VipWeekly::getYear :
                        month == null ? VipWeekly::getMonth :
                                VipWeekly::getWeek);

        // 执行查询
        result = vipWeeklyMapper.selectList(queryWrapper);

        // 如果没有找到匹配的周报且存在VIP记录，则查找最新的周报
        if (result.isEmpty() && CollUtil.isNotEmpty(vipRecords)) {
            VipWeekly latestReport = getLatestWeekly();
            if (latestReport != null) {
                // 如果找到最新周报，使用它作为结果
                result = List.of(latestReport);
            }
        }

        // 完全没有周报，就给默认的时间
        if (CollUtil.isEmpty(result)) {
            return getCurrentTimeWeeks(year, month);
        }

        // 将查询结果转换为WeeksVO对象列表
        return result.stream()
                .distinct()  // 去重
                .map(vipWeekly -> {
                    WeeksVO weeksVO = new WeeksVO();
                    weeksVO.setYear(vipWeekly.getYear());
                    if (year != null) {
                        weeksVO.setMonth(vipWeekly.getMonth());
                    }
                    if (year != null && month != null) {
                        weeksVO.setWeek(vipWeekly.getWeek());
                    }
                    return weeksVO;
                })
                .collect(Collectors.toList());
    }

    /**
     * 获取当前时间对应的年月周数据
     */
    private List<WeeksVO> getCurrentTimeWeeks(Integer year, Integer month) {
        Calendar calendar = Calendar.getInstance();
        WeeksVO weeksVO = new WeeksVO();

        if (year == null && month == null) {
            // 只返回当前年份
            weeksVO.setYear(calendar.get(Calendar.YEAR));
        } else if (year != null && month == null) {
            // 返回指定年份的当前月份
            weeksVO.setYear(year);
            weeksVO.setMonth(calendar.get(Calendar.MONTH) + 1);
        } else if (year != null) {
            // 返回指定年月的当前周
            weeksVO.setYear(year);
            weeksVO.setMonth(month);
            weeksVO.setWeek(calendar.get(Calendar.WEEK_OF_MONTH));
        }

        return List.of(weeksVO);
    }

    private WeeklySpuVO selectWeeklySpuListByWeeklySpuId(Long weeklySpuId) {
        return vipWeeklySpuMapper.selectByWeeklySpuId(weeklySpuId);
    }

    private List<WeeklySpuGoodsVO> selectAllByWeeklySpuId(Long weeklySpuId) {
        return vipWeeklySpuGoodsMapper.selectAllByWeeklySpuId(weeklySpuId);
    }

    @Override
    public Long getWeeklyCount(Long vipId) {
        // 会员充值记录
        List<VipBuyerPayRecord> vipBuyerPayRecordList = vipBuyerPayRecordMapper.selectList(Wrappers
                .lambdaQuery(VipBuyerPayRecord.class)
                .eq(VipBuyerPayRecord::getVipBuyerCardId, vipId)
                .lt(VipBuyerPayRecord::getStartTime, new Date())
        );
        if (CollectionUtil.isEmpty(vipBuyerPayRecordList)) {
            return 0L;
        }

        LambdaQueryWrapper<VipWeekly> queryWrapper = Wrappers.lambdaQuery(VipWeekly.class)
                .lt(VipWeekly::getDayEnd, new Date())
                .and(CollUtil.isNotEmpty(vipBuyerPayRecordList), wrapper -> vipBuyerPayRecordList.forEach(item ->
                        wrapper.or(w -> w
                                .le(VipWeekly::getDayStart, item.getEndTime())
                                .ge(VipWeekly::getDayEnd, item.getStartTime())
                        )
                ));
        return vipWeeklyMapper.selectCount(queryWrapper);
    }
}
