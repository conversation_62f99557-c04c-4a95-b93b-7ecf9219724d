package com.bbh.live.service.buyer.sign.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR> dsy
 * @Date: 2024/8/16
 * @Description:
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
public class SignInfoVo {

    /** 今日是否已签到 */
    private boolean todayIsSign;

    /** 本周签到天数 */
    private Integer signDaysThisWeek;

    /** 本月签到天数 */
    private Integer signDaysThisMonth;

    /** 本月签到获得的分贝奖励 */
    private Integer awardFenbeiThisMonth;

    /** 本月签到的日期 */
    private List<Integer> signDayThisMonthList;
}
