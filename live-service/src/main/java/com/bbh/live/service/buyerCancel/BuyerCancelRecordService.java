package com.bbh.live.service.buyerCancel;

import com.baomidou.mybatisplus.extension.service.IService;
import com.bbh.base.ListBase;
import com.bbh.base.PageQuery;
import com.bbh.enums.GlobalOrderTypeEnum;
import com.bbh.live.service.buyerCancel.dto.request.BuyerCancelRecordQueryRequest;
import com.bbh.live.service.buyerCancel.dto.request.BuyerCancelRecordSubmitRequest;
import com.bbh.live.service.buyerCancel.dto.request.SellerCancelRecordApproveRequest;
import com.bbh.live.service.buyerCancel.dto.response.BuyerCancelRecordDetailVO;
import com.bbh.live.service.buyerCancel.dto.response.BuyerCancelRecordListVO;
import com.bbh.model.LiveGoodsBuyerCancelRecord;
import com.bbh.service.deposit.enums.CodeEnum;

public interface BuyerCancelRecordService extends IService<LiveGoodsBuyerCancelRecord> {

    /**
     * 买家提交审核
     *
     * @return
     */
    LiveGoodsBuyerCancelRecord submit(BuyerCancelRecordSubmitRequest request);

    /**
     * 买家撤销审核
     */
    void revoke(Long recordId);

    /**
     * 买家查看审核详情
     */
    BuyerCancelRecordDetailVO getRecordDetail(Long recordId, boolean ifBuyer);

    /**
     * 买家审核列表
     */
    ListBase<BuyerCancelRecordListVO> getRecordList(BuyerCancelRecordQueryRequest request, PageQuery pageQuery);

    /**
     * 卖家审核通过
     */
    void sellerApprove(SellerCancelRecordApproveRequest request);

    /**
     * 获取商品成交信息
     */
    BuyerCancelRecordDetailVO getTransactionInfo(Long liveGoodsId);



}
