package com.bbh.live.service.buyer.vip.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 买家贵宾配置
 *
 * <AUTHOR>
 * @create 2023/7/25 14:26
 * @date 2023/07/26
 */
@Data
public class BuyerVipConfigVO implements Serializable {

	/**
	 * vip水平
	 */
	private Integer vipLevel;
	/**
	 * 当前等级所需成长值
	 */
	private Integer needExp;
	/**
	 * VIP徽章
	 */
	private Integer vipMedal;
	/**
	 * 可查看附近商家距离(默认100km)
	 */
	private Integer nearShopDistance = 100000;
	/**
	 * 签到额外分贝，2表示2倍，3表示3倍
	 */
	private Integer signExtraFenbei = 2;
	/**
	 * 接收分贝损耗比例
	 */
	private BigDecimal canReceiveFenbei = BigDecimal.valueOf(0.15);
	/**
	 * 金牌服务
	 */
	private Integer goldCustomerService;
	/**
	 * 偷看买家num
	 */
	private Integer peepBuyerNum;
	/**
	 * 彩票num
	 */
	private Integer lotteryNum;
	/**
	 * 修改昵称num
	 */
	private Integer modifyNicknameNum;
	/**
	 * 销售后服务num
	 */
	private Integer saleAfterServiceNum;
	/**
	 *  分贝 折扣
	 */
	private BigDecimal fenbeiMallDiscount;
	/**
	 * 下一等级所需成长值
	 */
	private Integer nextLevelExp;

	/**
	 * 线下支付分贝扣除数量
	 */
	private Integer offlineFbDeductNum;

	/** 随机权重 */
	private Integer secKillRandomWeight;

}
