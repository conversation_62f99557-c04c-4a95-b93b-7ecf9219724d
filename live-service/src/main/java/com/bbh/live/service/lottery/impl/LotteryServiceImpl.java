package com.bbh.live.service.lottery.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.lang.WeightRandom;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.bbh.base.ListBase;
import com.bbh.enums.FenbeiDetailEventEnum;
import com.bbh.enums.GlobalFenbeiDetailOperateSourceEnum;
import com.bbh.enums.GlobalLotteryWinningRecordProcessStatusEnum;
import com.bbh.exception.ServiceException;
import com.bbh.live.dao.dto.LotteryCreateDTO;
import com.bbh.live.dao.dto.LotteryPrizeItemDTO;
import com.bbh.live.dao.dto.LotteryWinningRecordDTO;
import com.bbh.live.dao.dto.LotteryWinningRecordPO;
import com.bbh.live.dao.dto.vo.BuyerFenbeiInfo;
import com.bbh.live.dao.dto.vo.LotteryWheelVO;
import com.bbh.live.dao.mapper.GlobalLotteryWheelPrizeMapper;
import com.bbh.live.dao.mapper.GlobalUserMapper;
import com.bbh.live.dao.mapper.VipBuyerConfigMapper;
import com.bbh.live.dao.service.*;
import com.bbh.live.service.buyer.BuyerFenbeiService;
import com.bbh.live.service.buyer.vip.BuyerVipService;
import com.bbh.live.service.buyer.vip.vo.UserBuyerVipInfoVO;
import com.bbh.live.service.lottery.LotteryService;
import com.bbh.live.service.user.UserInfoService;
import com.bbh.model.*;
import com.bbh.secure.AuthUtil;
import com.bbh.service.lock.HtbLockService;
import com.bbh.service.lock.bean.HtbLock;
import com.bbh.util.AssertUtil;
import com.bbh.vo.AuthUser;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 转盘抽奖
 * <AUTHOR>
 */
@Service
@AllArgsConstructor
@Slf4j
public class LotteryServiceImpl implements LotteryService {

    /** 默认获取中奖记录条数 */
    private static final int DEFAULT_RECORD_LIMIT = 10;
    /** 中奖公告模板 */
    private static final String WINNING_NOTICE_TEMPLATE = "恭喜{}获得{}";

    private final GlobalLotteryWheelService globalLotteryWheelService;
    private final GlobalLotteryWheelPrizeService globalLotteryWheelPrizeService;
    private final GlobalLotteryWheelPrizeMapper globalLotteryWheelPrizeMapper;
    private final GlobalLotteryWinningRecordService globalLotteryWinningRecordService;
    private final BuyerVipService buyerVipService;
    private final BuyerFenbeiService buyerFenbeiService;
    private final UserInfoService userInfoService;
    private final GlobalUserMapper globalUserMapper;
    private final PrizeResultManager prizeResultManager;
    private final IGlobalOrganizationService globalOrganizationService;
    private final GlobalFenbeiDetailService globalFenbeiDetailService;
    private final VipBuyerConfigMapper vipBuyerConfigMapper;
    private final VipBuyerCardService vipBuyerCardService;
    private final VipBuyerLotteryLogService vipBuyerLotteryLogService;
    private final HtbLockService htbLockService;

    /**
     * 创建抽奖
     *
     * @param createLotteryDTO 抽奖次数
     * @return 抽中的奖品
     */
    @Transactional
    @Override
    public List<LotteryPrizeItemDTO> createLottery(LotteryCreateDTO createLotteryDTO) {
        var user = AuthUtil.getUser();
        // 检查是否会员
        UserBuyerVipInfoVO vipInfo = getVipInfo(user);
        // 检查分贝余额和剩余次数是否充足
        checkTimesAndFenbei(user, vipInfo, createLotteryDTO);

        // 执行批量抽奖
        return drawBatchLottery(createLotteryDTO, user, vipInfo);
    }

    /**
     * 批量抽奖处理
     * @param createLotteryDTO 抽奖创建DTO
     * @param user 当前用户
     * @param vipInfo 用户VIP信息
     * @return 抽中的奖品列表
     */
    @Transactional
    public List<LotteryPrizeItemDTO> drawBatchLottery(LotteryCreateDTO createLotteryDTO, AuthUser user, UserBuyerVipInfoVO vipInfo) {
        int times = createLotteryDTO.getTimes();
        Long wheelId = createLotteryDTO.getWheelId();

        HtbLock lock = htbLockService.getLock("lottery-wheel-" + wheelId);
        if (!lock.tryLock(5, TimeUnit.SECONDS)) {
            throw new ServiceException("系统繁忙，请稍后再试");
        }

        try {
            // 获取当前转盘的所有奖品列表
            List<LotteryPrizeItemDTO> prizeItems = globalLotteryWheelService.findPrizeList(wheelId);

            // 批量抽奖
            List<LotteryPrizeItemDTO> selectedPrizes = selectBatchPrizes(prizeItems, times);

            // 批量更新库存
            updateBatchStock(selectedPrizes);

            // 批量保存中奖记录
            saveBatchWinningRecords(selectedPrizes, user);

            // 扣除用户抽奖次数或分贝余额
            decrementUserLotteryTimes(user, vipInfo, times, selectedPrizes);

            // 批量处理奖品结果
            processBatchPrizeResults(selectedPrizes, user);

            return selectedPrizes;
        } finally {
            lock.unlock();
        }
    }

    /**
     * 批量选择奖品
     * @param prizeItems 可选奖品列表
     * @param times 抽奖次数
     * @return 选中的奖品列表
     */
    private List<LotteryPrizeItemDTO> selectBatchPrizes(List<LotteryPrizeItemDTO> prizeItems, int times) {
        List<LotteryPrizeItemDTO> selectedPrizes = new ArrayList<>();
        for (int i = 0; i < times; i++) {
            LotteryPrizeItemDTO prize = selectSinglePrize(prizeItems);
            if (prize != null) {
                selectedPrizes.add(prize);
                // 更新临时库存，确保下次选择时考虑到已选择的奖品
                updateTempStock(prizeItems, prize);
            }
        }
        return selectedPrizes;
    }

    /**
     * 选择单个奖品
     * @param prizeItems 可选奖品列表
     * @return 选中的单个奖品，如果没有可用奖品则返回null
     */
    private LotteryPrizeItemDTO selectSinglePrize(List<LotteryPrizeItemDTO> prizeItems) {
        // 过滤出有库存的奖品，并创建权重对象列表
        List<WeightRandom.WeightObj<LotteryPrizeItemDTO>> weightObjs = prizeItems.stream()
                .filter(item -> !item.getQuantityLimited() || item.getStockCount() > 0)
                .map(item -> new WeightRandom.WeightObj<>(item, item.getWeight().doubleValue()))
                .collect(Collectors.toList());

        if (weightObjs.isEmpty()) {
            // 没有可用奖品
            return null;
        }

        // 使用权重随机选择一个奖品
        WeightRandom<LotteryPrizeItemDTO> weightRandom = RandomUtil.weightRandom(weightObjs);
        return weightRandom.next();
    }

    /**
     * 更新临时库存
     * @param prizeItems 奖品列表
     * @param selectedPrize 被选中的奖品
     */
    private void updateTempStock(List<LotteryPrizeItemDTO> prizeItems, LotteryPrizeItemDTO selectedPrize) {
        prizeItems.stream()
                .filter(item -> item.getPrizeId().equals(selectedPrize.getPrizeId()))
                .findFirst()
                .ifPresent(item -> {
                    if (item.getQuantityLimited()) {
                        item.setStockCount(item.getStockCount() - 1);
                    }
                });
    }

    /**
     * 批量更新奖品库存
     * @param selectedPrizes 被选中的奖品列表
     */
    @Transactional
    public void updateBatchStock(List<LotteryPrizeItemDTO> selectedPrizes) {
        // 统计每种奖品被选中的次数
        Map<Long, Long> prizeStockChanges = selectedPrizes.stream()
                .filter(LotteryPrizeItemDTO::getQuantityLimited)
                .collect(Collectors.groupingBy(LotteryPrizeItemDTO::getWheelPrizeId, Collectors.counting()));

        // 批量更新数据库中的库存
        for (Map.Entry<Long, Long> entry : prizeStockChanges.entrySet()) {
            Long wheelPrizeId = entry.getKey();
            Long count = entry.getValue();
            // 无库存上限的不处理
            if (count == null) {
                continue;
            }

            globalLotteryWheelPrizeMapper.update(
                    Wrappers.lambdaUpdate(GlobalLotteryWheelPrize.class)
                            .eq(GlobalLotteryWheelPrize::getId, wheelPrizeId)
                            .setDecrBy(GlobalLotteryWheelPrize::getStockCount, count)
            );
        }
    }

    /**
     * 批量保存中奖记录
     * @param selectedPrizes 被选中的奖品列表
     * @param user 当前用户
     */
    private void saveBatchWinningRecords(List<LotteryPrizeItemDTO> selectedPrizes, AuthUser user) {
        List<GlobalLotteryWinningRecord> records = selectedPrizes.stream()
                .map(prize -> {
                    GlobalLotteryWinningRecord record = new GlobalLotteryWinningRecord();
                    record.setWheelId(prize.getWheelId());
                    record.setPrizeId(prize.getPrizeId());
                    record.setPrizeType(prize.getType());
                    record.setProcessStatus(GlobalLotteryWinningRecordProcessStatusEnum.PENDING);
                    record.setSeatId(user.getSeatId());
                    record.setUserId(user.getUserId());
                    record.setOrgId(user.getOrgId());
                    return record;
                })
                .collect(Collectors.toList());

        // 批量保存中奖记录
        globalLotteryWinningRecordService.saveBatch(records);

        // 设置中奖记录ID到选中的奖品中
        for (int i = 0; i < selectedPrizes.size(); i++) {
            selectedPrizes.get(i).setWinningRecordId(records.get(i).getId());
        }
    }

    /**
     * 批量处理奖品结果
     * @param selectedPrizes 被选中的奖品列表
     * @param user 当前用户
     */
    private void processBatchPrizeResults(List<LotteryPrizeItemDTO> selectedPrizes, AuthUser user) {
        for (LotteryPrizeItemDTO prize : selectedPrizes) {
            prizeResultManager.processPrizeResult(user.getUserId(), user.getSeatId(), prize);
        }
    }

    /**
     * 检查剩余次数和分贝余额是否足够
     * @param user          当前用户信息
     * @param vipInfo       会员信息
     * @param createDTO     抽奖信息
     */
    private void checkTimesAndFenbei(AuthUser user, UserBuyerVipInfoVO vipInfo, LotteryCreateDTO createDTO) {
        // 先获取本场转盘的信息
        GlobalLotteryWheel globalLotteryWheel = globalLotteryWheelService.getById(createDTO.getWheelId());
        AssertUtil.assertNotNull(globalLotteryWheel, "转盘不存在，无法抽奖");
        AssertUtil.assertTrue(globalLotteryWheel.getEnabled(), "转盘已关闭，无法抽奖");

        // 判断本次抽奖是多少次，判断会员的剩余抽奖次数是否足够
        Integer times = createDTO.getTimes();
        Integer unUsedTimes = vipInfo.getLotteryUnUsedTimes();

        // 如果剩余次数不足当前次数，就直接使用分贝抽奖，需要在检查分贝够不够
        if (times > unUsedTimes) {
            checkFenbeiBalance(user, globalLotteryWheel, times);
        }
    }

    /**
     * 检查用户的分贝余额是否足够支付抽奖费用
     * @param user 当前用户
     * @param globalLotteryWheel 全局转盘信息
     * @param times 抽奖次数
     */
    private void checkFenbeiBalance(AuthUser user, GlobalLotteryWheel globalLotteryWheel, int times) {
        // 获取用户的分贝信息
        BuyerFenbeiInfo buyerFenbeiInfo = buyerFenbeiService.getBuyerFenbeiInfo(user.getOrgId());
        int fenbeiBalance = buyerFenbeiInfo.getFenbeiNum();

        // 计算本次抽奖所需的费用
        long cost = calculateCost(globalLotteryWheel, times);

        // 如果余额不足，抛出异常
        if (cost > fenbeiBalance) {
            throw new ServiceException("您的分贝余额不足，无法使用该功能");
        }
    }

    /**
     * 根据抽奖次数计算所需的分贝费用
     * @param globalLotteryWheel 全局转盘信息
     * @param times 抽奖次数
     * @return 所需的分贝费用
     */
    private long calculateCost(GlobalLotteryWheel globalLotteryWheel, int times) {
        return switch (times) {
            case 1 -> globalLotteryWheel.getSingleDrawCost();
            case 5 -> globalLotteryWheel.getFiveDrawsCost();
            case 10 -> globalLotteryWheel.getTenDrawsCost();
            default -> throw new ServiceException("抽奖次数非法，警告一次");
        };
    }

    /**
     * 获取当前会员信息
     */
    private UserBuyerVipInfoVO getVipInfo(AuthUser user) {
        return buyerVipService.getUserBuyerVipInfoBySeatId(user.getSeatId());
    }

    /**
     * 扣除用户抽奖次数或分贝余额
     * @param user 当前用户
     * @param vipInfo 用户VIP信息
     * @param times 抽奖次数
     * @param prizeItemList 中奖结果
     */
    private void decrementUserLotteryTimes(AuthUser user, UserBuyerVipInfoVO vipInfo, Integer times, List<LotteryPrizeItemDTO> prizeItemList) {
        // 用户信息
        var userInfo = userInfoService.getSimpleInfoBySeatId(user.getSeatId());
        // 用户手机号
        var globalUser = globalUserMapper.selectById(user.getUserId());

        // 获取当前转盘信息
        GlobalLotteryWheel globalLotteryWheel = globalLotteryWheelService.getById(prizeItemList.getFirst().getWheelId());
        // 如果剩余次数充足，这里直接拿结果去扣减即可，因为调用时已经做了拦截，不支持混合消耗的情况
        Integer unUsedTimes = vipInfo.getLotteryUnUsedTimes();
        if (unUsedTimes >= times) {
            log.info("用户 {} 在席位 {} 有剩余免费抽奖次数，直接扣减即可", user.getUserId(), user.getSeatId());
            // 减少免费剩余次数，记录免费衰减记录，一个奖品记一次
            // 只有剩余次数充足了才会记录会员的使用日志
            var logList = prizeItemList.stream().map(prizeItem -> {
                VipBuyerLotteryLog vipBuyerLotteryLog = new VipBuyerLotteryLog();
                vipBuyerLotteryLog.setUserId(user.getUserId());
                vipBuyerLotteryLog.setSeatId(user.getSeatId());
                vipBuyerLotteryLog.setOwnName(prizeItem.getName());
                vipBuyerLotteryLog.setSeatName(userInfo.getSeatName());
                vipBuyerLotteryLog.setSeatNickName(userInfo.getShowName());
                vipBuyerLotteryLog.setUserPhone(globalUser.getPurePhone());
                vipBuyerLotteryLog.setVipBuyerCardId(vipInfo.getId());
                vipBuyerLotteryLog.setActiveTurntableWinId(prizeItem.getWinningRecordId());
                return vipBuyerLotteryLog;
            }).collect(Collectors.toList());
            buyerVipService.updateUseTimesBySeatId(user.getSeatId(), logList);
        }
        // 次数不足时，再去扣分贝
        else {
            log.info("用户 {} 在席位 {} 剩余免费抽奖次数不足{}次，需要扣除分贝", user.getUserId(), user.getSeatId(), times);
            // 计算本次扣除的分贝数量
            long cost = calculateCost(globalLotteryWheel, times);
            // 查询商户
            var org = globalOrganizationService.getById(user.getOrgId());
            // 扣除分贝余额
            globalOrganizationService.lambdaUpdate()
                    .eq(GlobalOrganization::getId, user.getOrgId())
                    .setDecrBy(GlobalOrganization::getFenbei, cost)
                    .update();
            log.info("扣除{}的分贝余额：{}：{}", org.getId(), org.getFenbei(), cost);
            // 分贝明细
            var globalFenbeiDetail = new GlobalFenbeiDetail();
            globalFenbeiDetail.setAccountBalance(org.getFenbei() - (int)cost);
            globalFenbeiDetail.setChangeNumber((int)cost);
            // 转盘抽奖
            FenbeiDetailEventEnum eventEnum = FenbeiDetailEventEnum.LOTTERY_DEDUCT;
            globalFenbeiDetail.setType(eventEnum.getType());
            globalFenbeiDetail.setEvent(eventEnum.getEvent());
            globalFenbeiDetail.setEventDescribe(eventEnum.getEventDescribe(times));
            globalFenbeiDetail.setOperateSource(GlobalFenbeiDetailOperateSourceEnum.APP_BBH);
            globalFenbeiDetailService.save(globalFenbeiDetail);
        }
    }

    /**
     * 获取抽奖转盘信息
     * 包括转盘配置、剩余抽奖次数、奖品列表和中奖公告等
     *
     * @return 转盘信息VO对象
     */
    @Override
    public LotteryWheelVO getLottery() {
        // 获取当前用户信息
        var user = AuthUtil.getUser();
        // 获取用户VIP信息
        UserBuyerVipInfoVO vipInfo = buyerVipService.getUserBuyerVipInfoBySeatId(user.getSeatId());

        // 构建基础转盘信息
        LotteryWheelVO lotteryWheel = buildBaseLotteryWheel(vipInfo);

        // 获取当前时间段内可用的转盘配置
        GlobalLotteryWheel availableWheel = globalLotteryWheelService.findAvailableWheel();
        if (availableWheel == null) {
            return lotteryWheel;
        }

        // 填充转盘详细信息
        fillWheelDetails(lotteryWheel, availableWheel);

        return lotteryWheel;
    }

    /**
     * 构建基础转盘信息
     * 包括剩余次数、VIP免费次数和默认中奖公告
     *
     * @param vipInfo 用户VIP信息
     * @return 基础转盘信息
     */
    private LotteryWheelVO buildBaseLotteryWheel(UserBuyerVipInfoVO vipInfo) {
        LotteryWheelVO lotteryWheel = new LotteryWheelVO();

        // 设置剩余抽奖次数，非VIP用户默认为0
        lotteryWheel.setRemainingCount(
                Optional.ofNullable(vipInfo)
                        // 如果是有效会员，使用实际剩余次数
                        .filter(info -> Boolean.TRUE.equals(info.getIsVip()))
                        .map(UserBuyerVipInfoVO::getLotteryUnUsedTimes)
                        // 如果不是会员或会员过期，使用VIP0的默认次数
                        .orElseGet(this::getDefaultVipLotteryNum)
        );

        // 设置VIP等级对应的免费抽奖次数
        lotteryWheel.setVipFreeTimes(getVipFreeTimes(vipInfo));

        // 设置中奖公告（无特定转盘时显示所有中奖记录）
        lotteryWheel.setNoticeList(getWinningNotices(null));

        // 设置空奖品列表
        lotteryWheel.setPrizeList(Collections.emptyList());

        return lotteryWheel;
    }

    /**
     * 填充转盘详细信息
     * 包括转盘配置、奖品列表和对应的中奖公告
     *
     * @param lotteryWheel 待填充的转盘对象
     * @param availableWheel 可用的转盘配置
     */
    private void fillWheelDetails(LotteryWheelVO lotteryWheel, GlobalLotteryWheel availableWheel) {
        // 复制转盘基础配置属性
        BeanUtil.copyProperties(availableWheel, lotteryWheel);
        lotteryWheel.setWheelId(availableWheel.getId());

        // 设置当前转盘的奖品列表
        List<LotteryPrizeItemDTO> prizeList = globalLotteryWheelService.findPrizeList(availableWheel.getId());
        lotteryWheel.setPrizeList(prizeList);

        // 更新当前转盘的中奖公告
        lotteryWheel.setNoticeList(getWinningNotices(availableWheel.getId()));
    }

    /**
     * 获取中奖公告列表
     *
     * @param wheelId 转盘ID，为null时获取所有转盘的中奖记录
     * @return 格式化后的中奖公告列表
     */
    private List<String> getWinningNotices(Long wheelId) {
        List<LotteryWinningRecordDTO> winningRecords = globalLotteryWinningRecordService
                .findListByWheelIdAndSeatId(wheelId, null, true, "limit " + DEFAULT_RECORD_LIMIT);

        return winningRecords.stream()
                .map(this::formatWinningNotice)
                .toList();
    }

    /**
     * 格式化单条中奖公告
     * 优先使用昵称，昵称为空时使用用户名
     *
     * @param record 中奖记录
     * @return 格式化后的中奖公告
     */
    private String formatWinningNotice(LotteryWinningRecordDTO record) {
        String displayName = Optional.ofNullable(record.getSeatNickName()).orElse(record.getSeatName());
        return StrUtil.format(WINNING_NOTICE_TEMPLATE, displayName, record.getName());
    }

    /**
     * 获取用户VIP等级对应的免费抽奖次数
     * 非VIP用户返回最低等级会员的抽奖次数
     *
     * @param vipInfo 用户VIP信息
     * @return 免费抽奖次数
     */
    private Integer getVipFreeTimes(UserBuyerVipInfoVO vipInfo) {
        return Optional.ofNullable(vipInfo)
                // 如果是会员，使用当前等级的次数
                .filter(UserBuyerVipInfoVO::getIsVip)
                .map(info -> info.getVipConfig().getLotteryNum())
                // 如果不是会员，查询VIP0等级的次数
                .orElseGet(this::getDefaultVipLotteryNum);
    }

    /**
     * 获取最低等级会员的默认抽奖次数
     * 用于非VIP用户显示升级后可获得的抽奖次数
     *
     * @return 默认抽奖次数，配置异常时返回1
     */
    private Integer getDefaultVipLotteryNum() {
        return vipBuyerConfigMapper.selectList(Wrappers.lambdaQuery(VipBuyerConfig.class)
                    .eq(VipBuyerConfig::getIfYearly, false)
                    .orderByAsc(VipBuyerConfig::getNeedExp)
                    .last("limit 1")
                )
                .stream()
                .findFirst()
                .map(VipBuyerConfig::getLotteryNum)
                .orElse(1);
    }

    /**
     * 我的中奖结果
     */
    @Override
    public ListBase<LotteryWinningRecordDTO> getWinningRecordList(LotteryWinningRecordPO pageBase) {
        var user = AuthUtil.getUser();
        var page = globalLotteryWinningRecordService.findPageByWheelIdAndSeatId(pageBase, pageBase.getWheelId(), user.getSeatId(), false);
        return new ListBase<>(page.getRecords(), page.getTotal(), page.getCurrent(), page.getSize());
    }
}
