package com.bbh.live.service.organization.resource;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import com.bbh.base.ListBase;
import com.bbh.live.dao.dto.OrgVipResourcePO;
import com.bbh.live.dao.service.GlobalAuctionPremiumNumberService;
import com.bbh.live.dao.service.GlobalOrgSeatService;
import com.bbh.live.dao.service.VipBuyerBindLogService;
import com.bbh.live.dao.service.VipBuyerCardService;
import com.bbh.live.enums.BuyerVipTypeEnum;
import com.bbh.live.service.buyer.vip.BuyerVipUtil;
import com.bbh.live.service.organization.dto.OrgAuctionNumberResourcesDTO;
import com.bbh.live.service.organization.dto.OrgVipResourcesDTO;
import com.bbh.model.GlobalAuctionPremiumNumber;
import com.bbh.model.GlobalOrgSeat;
import com.bbh.model.VipBuyerCard;
import com.bbh.util.AssertUtil;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/9/20 08:52
 * @description
 */
@Service
@AllArgsConstructor
public class OrgResourcesServiceImpl implements OrgResourcesService {

    private final GlobalAuctionPremiumNumberService globalAuctionPremiumNumberService;
    private final GlobalOrgSeatService globalOrgSeatService;
    private final VipBuyerCardService vipBuyerCardService;
    private final VipBuyerBindLogService vipBuyerBindLogService;

    @Override
    public ListBase<OrgAuctionNumberResourcesDTO> getAuctionNumberResources(Long orgId) {
        // 指定店铺下的所有靓号
        List<OrgAuctionNumberResourcesDTO> auctionNumberResources = globalAuctionPremiumNumberService.getAuctionNumberResources(orgId);
        return new ListBase<>(auctionNumberResources, (long) auctionNumberResources.size(), null, null);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void bindAuctionNumber(String auctionNumber, Long seatId) {
        // 之前绑定的靓号解绑
        GlobalAuctionPremiumNumber beforePremiumNumber = globalAuctionPremiumNumberService.lambdaQuery().eq(GlobalAuctionPremiumNumber::getSeatId, seatId).one();
        if (beforePremiumNumber != null) {
            this.unbindAuctionNumber(beforePremiumNumber.getNumber());
        }

        // 更新靓号所属席位
        boolean update = globalAuctionPremiumNumberService.lambdaUpdate()
                .eq(GlobalAuctionPremiumNumber::getNumber, auctionNumber)
                .set(GlobalAuctionPremiumNumber::getSeatId, seatId)
                .update();
        AssertUtil.assertTrue(update, "拍号不存在，绑定失败");
        // 同步靓号到席位上
        GlobalOrgSeat globalOrgSeat = globalOrgSeatService.lambdaQuery().eq(GlobalOrgSeat::getId, seatId)
                .select(GlobalOrgSeat::getId, GlobalOrgSeat::getAuctionCode, GlobalOrgSeat::getOriginalAuctionCode)
                .one();
        globalOrgSeat.setOriginalAuctionCode(globalOrgSeat.getAuctionCode());
        globalOrgSeat.setAuctionCode(auctionNumber.toString());
        globalOrgSeatService.updateById(globalOrgSeat);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void unbindAuctionNumber(String auctionNumber) {
        GlobalAuctionPremiumNumber globalAuctionPremiumNumber = globalAuctionPremiumNumberService.lambdaQuery()
                .eq(GlobalAuctionPremiumNumber::getNumber, auctionNumber)
                .select(GlobalAuctionPremiumNumber::getSeatId)
                .one();
        AssertUtil.assertNotNull(globalAuctionPremiumNumber, "拍号不存在");
        // 解绑靓号上都席位
        globalAuctionPremiumNumberService.lambdaUpdate()
                .eq(GlobalAuctionPremiumNumber::getNumber, auctionNumber)
                .set(GlobalAuctionPremiumNumber::getSeatId, null)
                .update();

        // 删除席位上的店铺靓号
        Long seatId = globalAuctionPremiumNumber.getSeatId();
        if (seatId != null && seatId > 0L) {
            GlobalOrgSeat globalOrgSeat = globalOrgSeatService.lambdaQuery().eq(GlobalOrgSeat::getId, seatId)
                    .select(GlobalOrgSeat::getId, GlobalOrgSeat::getAuctionCode, GlobalOrgSeat::getOriginalAuctionCode)
                    .one();
            globalOrgSeatService.lambdaUpdate().eq(GlobalOrgSeat::getId, seatId)
                    .set(GlobalOrgSeat::getAuctionCode, globalOrgSeat.getOriginalAuctionCode())
                    .set(GlobalOrgSeat::getOriginalAuctionCode, null)
                    .update();
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void changeBindAuctionNumber(String auctionNumber, Long newSeatId) {
        this.unbindAuctionNumber(auctionNumber);
        this.bindAuctionNumber(auctionNumber, newSeatId);
    }

    @Override
    public ListBase<OrgVipResourcesDTO> getVipResources(Long orgId) {
        List<OrgVipResourcesDTO> result = new ArrayList<>();
        List<OrgVipResourcePO> orgVipResources = vipBuyerCardService.getOrgVipResources(orgId);
        if (CollectionUtil.isEmpty(orgVipResources)) {
            return new ListBase<>(result, 0L, null, null);
        }

        for (OrgVipResourcePO orgVipResource : orgVipResources) {
            Integer vipLevel = BuyerVipUtil.getBuyerVipLevel(orgVipResource.getExp(), Boolean.TRUE.equals(orgVipResource.getIfAnnualFeeVip()));
            BuyerVipTypeEnum vipStatus = new Date().before(DateUtil.endOfDay(orgVipResource.getVipEndTime())) ? BuyerVipTypeEnum.VIP : BuyerVipTypeEnum.VIP_EXPIRED;
            OrgVipResourcesDTO orgVipResourcesDTO = new OrgVipResourcesDTO()
                    .setVipId(orgVipResource.getVipId())
                    .setVipLevel(vipLevel)
                    .setIfAnnualFeeVip(Boolean.TRUE.equals(orgVipResource.getIfAnnualFeeVip()))
                    .setVipStatus(vipStatus)
                    .setVipEndTime(orgVipResource.getVipEndTime())
                    .setSeatId(orgVipResource.getSeatId())
                    .setSeatName(orgVipResource.getSeatName())
                    .setAvatar(orgVipResource.getAvatar())
                    .setCreatedFrom(orgVipResource.getCreateFrom());
            result.add(orgVipResourcesDTO);
        }
        return new ListBase<>(result, (long) result.size(), null, null);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void bindVip(Long vipId, Long seatId) {

        VipBuyerCard beforeBindVip = vipBuyerCardService.lambdaQuery().eq(VipBuyerCard::getSeatId, seatId)
                .select(VipBuyerCard::getId).one();
        if (beforeBindVip != null) {
            // 解除原来绑定的VIP
            vipBuyerCardService.lambdaUpdate().eq(VipBuyerCard::getId, beforeBindVip.getId())
                    .set(VipBuyerCard::getSeatId, null).update();
            // 记录解绑日志
            vipBuyerBindLogService.saveVipBuyerBindLog(seatId, null, beforeBindVip.getId());
        }

        // 新VIP
        VipBuyerCard newVipInfo = vipBuyerCardService.lambdaQuery().eq(VipBuyerCard::getId, vipId)
                .select(VipBuyerCard::getId, VipBuyerCard::getSeatId).one();
        AssertUtil.assertNotNull(newVipInfo, "绑定失败，新VIP不存在");
        // 绑定新的VIP
        vipBuyerCardService.lambdaUpdate().eq(VipBuyerCard::getId, vipId)
                .set(VipBuyerCard::getSeatId, seatId)
                .update();
        vipBuyerBindLogService.saveVipBuyerBindLog(newVipInfo.getSeatId(), seatId, newVipInfo.getId());
    }

    @Override
    public void unbindVip(Long vipId) {
        VipBuyerCard oldVip = vipBuyerCardService.getById(vipId);
        if(oldVip == null){
            return;
        }
        vipBuyerCardService.lambdaUpdate().eq(VipBuyerCard::getId, vipId)
                .set(VipBuyerCard::getSeatId, null)
                .update();
        vipBuyerBindLogService.saveVipBuyerBindLog(oldVip.getSeatId(), null, vipId);
    }
}
