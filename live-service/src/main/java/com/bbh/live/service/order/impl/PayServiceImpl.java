package com.bbh.live.service.order.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.bbh.enums.GlobalPayTypeEnum;
import com.bbh.exception.ServiceException;
import com.bbh.live.config.LiveServiceProperties;
import com.bbh.live.dao.mapper.GlobalOrgSettlementBankMapper;
import com.bbh.live.service.order.PayService;
import com.bbh.live.service.order.dto.OrderBuilderContext;
import com.bbh.live.service.order.dto.PreparedOrderDTO;
import com.bbh.model.GlobalOrder;
import com.bbh.model.GlobalOrderSub;
import com.bbh.model.GlobalOrgSettlementBank;
import com.bbh.model.GlobalVirtualGoodsOrder;
import com.bbh.service.alipay.service.AlipayService;
import com.bbh.service.pay.PayCenterService;
import com.bbh.service.pay.bean.PayRequest;
import com.bbh.service.pay.config.PayCenterProperties;
import com.bbh.service.pay.dto.*;
import com.bbh.service.pay.enums.MethodEnum;
import com.bbh.service.pay.enums.PayModeEnum;
import com.bbh.service.pay.enums.PayTypeEnum;
import com.bbh.service.pay.enums.TransTypeEnum;
import com.bbh.util.OrderNoGenerator;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Comparator;
import java.util.Date;
import java.util.List;

/**
 * 支付
 * <AUTHOR>
 */
@Service
@AllArgsConstructor
@Slf4j
public class PayServiceImpl implements PayService {

    private final PayCenterService payCenterService;
    private final PayCenterProperties payCenterProperties;
    private final GlobalOrgSettlementBankMapper globalOrgSettlementBankMapper;
    private final LiveServiceProperties liveServiceProperties;
    private final AlipayService alipayService;

    @SuppressWarnings("all")
    @Override
    public Object orderPay(OrderBuilderContext context, PreparedOrderDTO preparedOrderDTO) {
        return switch (context.getPayType()) {
            // 线下支付
            case OFFLINE -> orderOfflinePay(context, preparedOrderDTO);
            // 线上支付
            default -> orderOnlinePay(context, preparedOrderDTO);
        };
    }

    @SuppressWarnings("all")
    @Override
    public Object orderOnlinePay(OrderBuilderContext context, PreparedOrderDTO preparedOrderDTO) {
        GlobalOrder order = preparedOrderDTO.getGlobalOrder();

        if (CollUtil.isEmpty(context.getOrgSettlementBankList())) {
            List<GlobalOrgSettlementBank> orgSettlementBankList = globalOrgSettlementBankMapper.selectList(Wrappers.lambdaQuery(GlobalOrgSettlementBank.class)
                    .in(GlobalOrgSettlementBank::getOrgId, preparedOrderDTO.getOrderSubList().stream().map(GlobalOrderSub::getSellerOrgId).toList()));
            context.setOrgSettlementBankList(orgSettlementBankList == null ? List.of() : orgSettlementBankList);
        }

        List<PayDTO.BizDataDTO> bizDataList = preparedOrderDTO.getOrderSubList().stream().map(x -> {
            PayDTO.BizDataDTO bizDataDTO = new PayDTO.BizDataDTO();
            bizDataDTO.setOrderParentNo(x.getOrderSubNo());
            bizDataDTO.setOpTotalAmount(x.getNeedPayAmount().multiply(new BigDecimal(100)).longValue());
            context.getOrgSettlementBankList()
                    .stream()
                    // 必须取对应商户的进件信息
                    .filter(bank -> bank.getOrgId().equals(x.getSellerOrgId()))
                    // 数据有时有重复，按id倒序取第一个
                    .sorted(Comparator.comparing(GlobalOrgSettlementBank::getId).reversed())
                    .findFirst()
                    .ifPresentOrElse(bank -> {
                        bizDataDTO.setPlatMerchantNo(bank.getPlatMerchantNo());
                    },() -> {throw new ServiceException("商家结算账户信息为空");});
            return bizDataDTO;
        }).toList();

        return commonOnlinePay(
                context,
                context.getPaymentTradeNo(),
                "购买直播商品",
                order.getNeedPayAmount(),
                liveServiceProperties.getOrderPayNotifyUrl(),
                bizDataList
        );
    }

    @Override
    public Object virtualOrderOnlinePay(OrderBuilderContext context, GlobalVirtualGoodsOrder order) {
        PayDTO.BizDataDTO bizDataDTO = new PayDTO.BizDataDTO();
        bizDataDTO.setOrderParentNo(OrderNoGenerator.generateIdNumber(OrderNoGenerator.Service.GLOBAL, OrderNoGenerator.Function.VIRTUAL_GOODS, OrderNoGenerator.Business.ORDER, true));
        bizDataDTO.setPlatMerchantNo(liveServiceProperties.getPlatMerchantNo());
        bizDataDTO.setOpTotalAmount(order.getOrderPrice().multiply(new BigDecimal(100)).longValue());

        return commonOnlinePay(
                context,
                context.getPaymentTradeNo(),
                order.getGoodsName() + "*" + order.getGoodsNumber(),
                order.getOrderPrice(),
                liveServiceProperties.getVirtualOrderPayNotifyUrl(),
                List.of(bizDataDTO)
        );
    }

    private Object commonOnlinePay(OrderBuilderContext context, String outTradeNo, String subject, BigDecimal totalAmount, String notifyUrl, List<PayDTO.BizDataDTO> bizDataList) {
        MethodEnum methodEnum = MethodEnum.PAY;
        PayDTO payDTO = new PayDTO();

        // 用code换阿里云的userId
        if (context.getPayType() == GlobalPayTypeEnum.ALIPAY) {
            var userId = alipayService.getUserIdByCode(context.getAliPayUserId());
            payDTO.setAliPayUserId(userId);
        } else if (context.getPayType() == GlobalPayTypeEnum.WECHAT) {
            // 微信的暂时不支持
            payDTO.setWxOpenId("");
        }

        // 时间戳
        payDTO.setTimestamp(System.currentTimeMillis() + "");
        // 流水号
        payDTO.setOutTradeNo(outTradeNo);
        // 订单标题
        payDTO.setSubject(subject);
        // 总金额，单位分
        payDTO.setTotalAmount(totalAmount.multiply(new BigDecimal(100)).longValue());
        // 支付类型
        payDTO.setPayType(PayTypeEnum.JN);
        // 支付模式
        payDTO.setPayMode(convertPayMode(context.getPayType()));
        // 异步回调地址
        payDTO.setNotifyUrl(notifyUrl);
        // 同步回调地址
        payDTO.setAsyncNotify("");
        // 订单数据
        payDTO.setBizData(bizDataList);

        PayRequest<PayDTO> payRequest = new PayRequest<>();
        payRequest.setData(payDTO);

        var result = payCenterService.handle(methodEnum, payRequest);
        // 判断是否成功，成功返回result内的参数，否则原样返回
        if ("00000".equals(result.getCode())) {
            return result.getResult();
        } else {
            return result;
        }
    }

    /**
     * 线下转账
     * <br>
     * 返回值:
     * <p>
     * {
     * "code": "00000",
     * "message": "成功",
     * "result": {
     * "orderAcctNo": "0100101401000001093751256", //订单子账号(打款账号)
     * "acctName": "黄抱皇剧窃愚搜岔叨所滑卷妖保证金",//账户名称
     * "bankNo": "************",//开户行号
     * "bankName": "江南农村商业银行" //开户行名称
     * }
     * }
     * </p>
     */
    @Override
    @SuppressWarnings("all")
    public Object orderOfflinePay(OrderBuilderContext context, PreparedOrderDTO preparedOrderDTO) {
        MethodEnum methodEnum = MethodEnum.OFFLINE_PAY;

        // 商户结算账号
        if (CollUtil.isEmpty(context.getOrgSettlementBankList())) {
            List<Long> sellerOrgIdList = preparedOrderDTO.getOrderSubList().stream().map(GlobalOrderSub::getSellerOrgId).toList();
            List<GlobalOrgSettlementBank> orgSettlementBankList = globalOrgSettlementBankMapper.selectList(Wrappers
                    .lambdaQuery(GlobalOrgSettlementBank.class)
                    .in(GlobalOrgSettlementBank::getOrgId, sellerOrgIdList)
            );
            context.setOrgSettlementBankList(orgSettlementBankList == null ? List.of() : orgSettlementBankList);
        }

        OfflinePayDTO offlinePayDTO = new OfflinePayDTO();
        // 帮帮虎中台商户号
        offlinePayDTO.setBbhMerchantNo(payCenterProperties.getBbhMerchantNo());
        // 回调地址
        offlinePayDTO.setNotifyUrl(liveServiceProperties.getOrderPayNotifyUrl());
        // 支付流水号
        offlinePayDTO.setMerchantSeqNo(context.getPaymentTradeNo());
        // 支付时间
        offlinePayDTO.setOrderTime(DateUtil.format(new Date(), DatePattern.PURE_DATETIME_PATTERN));
        // 交易金额（分）
        offlinePayDTO.setTransAmt(preparedOrderDTO.getGlobalOrder().getNeedPayAmount().multiply(new BigDecimal(100)).longValue());
        // 子订单信息
        offlinePayDTO.setSubOrderInfo(preparedOrderDTO.getOrderSubList().stream().map(x -> {
            SubOrderDTO bizDataDTO = new SubOrderDTO();
            // 子订单号
            bizDataDTO.setSubMerchantSeqNo(x.getOrderSubNo());
            // 子订单金额，金额分
            bizDataDTO.setSubOrderAmt(x.getNeedPayAmount().multiply(new BigDecimal(100)).longValue());
            // 用结算账户的银行卡信息
            context.getOrgSettlementBankList()
                    .stream()
                    // 必须取对应商户的进件信息
                    .filter(bank -> bank.getOrgId().equals(x.getSellerOrgId()))
                    // 数据有时有重复，按id倒序取第一个
                    .sorted(Comparator.comparing(GlobalOrgSettlementBank::getId).reversed())
                    .findFirst()
                    .ifPresentOrElse(bank -> {
                        // 第三方平台商户号 = 平台分配的商户号
                        bizDataDTO.setPtMerchantId(bank.getPlatMerchantNo());
                        bizDataDTO.setPayeeName(bank.getAccountName());
                        bizDataDTO.setPayeeCard(bank.getCardNo());
                        bizDataDTO.setRevBankName(bank.getBankName());
                        bizDataDTO.setRevBankId(bank.getBankNo());
                    },()->{
                        throw new ServiceException("商家结算账户信息为空");
                    });
            return bizDataDTO;
        }).toList());

        PayRequest<OfflinePayDTO> payRequest = new PayRequest<>();
        payRequest.setData(offlinePayDTO);
        return payCenterService.handle(methodEnum, payRequest);
    }

    @Override
    public void orderClosePay(String outTradeNo) {
        MethodEnum methodEnum = MethodEnum.CLOSE;

        PayRequest<TradeNoDTO> payRequest = new PayRequest<>();
        TradeNoDTO tradeNoDTO = new TradeNoDTO();
        tradeNoDTO.setOutNo(outTradeNo);
        payRequest.setData(tradeNoDTO);
        payCenterService.handle(methodEnum, payRequest);
    }

    @Override
    public boolean getOrderPaidInfoStatus(String outTradeNo) {
        MethodEnum methodEnum = MethodEnum.ORDER_INFO;

        PayRequest<TradeNoDTO> payRequest = new PayRequest<>();
        TradeNoDTO tradeNoDTO =  new TradeNoDTO();
        tradeNoDTO.setOutNo(outTradeNo);
        tradeNoDTO.setTransType(TransTypeEnum.PAY.getCode());
        payRequest.setData(tradeNoDTO);
        try{
            // 支付失败会抛异常
            var payResponse = payCenterService.handle(methodEnum, payRequest);
            return "00000".equals(payResponse.getCode());
        } catch (Throwable throwable){
            throw new ServiceException("获取支付结果异常: " + throwable.getMessage());
        }
    }

    /**
     * 线下转账上传凭证
     *
     * @param outTradeNo             订单流水号
     * @param offlineTransferImgList 凭证图片
     * @return 上传结果
     */
    @Override
    public Object offlinePayUpload(String outTradeNo, List<String> offlineTransferImgList) {
        MethodEnum methodEnum = MethodEnum.ONLINE_PAY_UPLOADING;

        // 调中台的上传凭证
        PayRequest<OnlinePayUploadingDTO> payRequest = new PayRequest<>();
        OnlinePayUploadingDTO onlinePayUploadingDTO = new OnlinePayUploadingDTO();
        onlinePayUploadingDTO.setOutTradeNo(outTradeNo);
        onlinePayUploadingDTO.setUploadVoucherImg(CollUtil.join(offlineTransferImgList, ","));
        payRequest.setData(onlinePayUploadingDTO);
        return payCenterService.handle(methodEnum, payRequest);
    }

    /**
     * 获取线下转账结果
     *
     * @param outTradeNo    订单流水号
     * @param orderCreateAt 订单创建时间
     * @return 线下转账结果
     */
    @Override
    public Object getOfflinePaidInfo(String outTradeNo, Date orderCreateAt) {
        MethodEnum methodEnum = MethodEnum.OFFLINE_INFO;

        OfflinePayInfoDTO dto = new OfflinePayInfoDTO();
        dto.setOutTradeNo(outTradeNo);
        dto.setStartDate(DateUtil.format(orderCreateAt, DatePattern.PURE_DATETIME_PATTERN));
        dto.setEndDate(DateUtil.format(new Date(), DatePattern.PURE_DATETIME_PATTERN));

        PayRequest<OfflinePayInfoDTO> pa = new PayRequest<>();
        pa.setData(dto);
        return payCenterService.handle(methodEnum, pa);
    }

    private PayTypeEnum convertPayType(GlobalPayTypeEnum payType) {
        return switch (payType) {
            case ALIPAY -> PayTypeEnum.ZFB;
            case WECHAT -> PayTypeEnum.WX;
            default -> null;
        };
    }

    private PayModeEnum convertPayMode(GlobalPayTypeEnum payType) {
        return switch (payType) {
            case ALIPAY -> PayModeEnum.ZFB;
            case WECHAT -> PayModeEnum.WX;
            default -> null;
        };
    }
}
