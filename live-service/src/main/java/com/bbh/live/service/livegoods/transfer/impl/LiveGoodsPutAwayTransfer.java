package com.bbh.live.service.livegoods.transfer.impl;

import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.bbh.enums.LiveGoodsStatusEnum;
import com.bbh.enums.LiveGoodsTradeTypeEnum;
import com.bbh.exception.ServiceException;
import com.bbh.live.dao.dto.GoodsTransferDTO;
import com.bbh.live.dao.dto.livegoods.LiveGoodsDTO;
import com.bbh.live.service.livegoods.LiveGoodsTransferBizService;
import com.bbh.live.service.livegoods.context.LiveGoodsContext;
import com.bbh.live.service.room.context.LiveRoomContextHolder;
import com.bbh.model.LiveGoods;
import com.bbh.service.mq.enums.MqTopicEnum;
import com.bbh.service.mq.service.CoreMqService;
import com.bbh.util.AssertUtil;
import com.bbh.util.EnvironmentUtil;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 商品上架-状态机流转
 * <AUTHOR> dsy
 * @Date: 2024/7/29
 * @Description:
 */
@Service("liveGoodsPutAwayTransfer")
public class LiveGoodsPutAwayTransfer extends AbstractLiveGoodsTransfer<LiveGoodsContext> {

    @Resource
    private LiveGoodsTransferBizService liveGoodsTransferBizService;
    @Resource
    private CoreMqService coreMqService;

    @Override
    protected void doCheck(LiveGoodsDTO liveGoods, LiveGoodsContext context) {
        super.liveGoodsAuctionCanTransferCheck(liveGoods);

        if (EnvironmentUtil.hasProfile("local")) {
            return;
        }

        // 检查结束时间，超出时间后禁止上架，但是已上架的商品，允许继续竞拍
        AssertUtil.assertFalse(LiveRoomContextHolder.liveRoomIsEnd(), "已超出结束时间，无法上架商品");

        //检查是否有已上架和竞拍中的商品
        Wrapper<LiveGoods> query = Wrappers.lambdaQuery(LiveGoods.class)
                .eq(LiveGoods::getLiveRoomId, liveGoods.getLiveRoomId())
                .in(LiveGoods::getGoodsStatus, LiveGoodsStatusEnum.PUT_AWAY, LiveGoodsStatusEnum.AUCTION);
        AssertUtil.assertFalse(liveGoodsDetailService.getLiveGoodsService().exists(query), "直播间已有商品处于上架或竞拍中");

        //传送中的商品无法上架讲解
        GoodsTransferDTO transferDTO = new GoodsTransferDTO();
        transferDTO.setLiveGoodsId(liveGoods.getId()).setLiveRoomId(liveGoods.getLiveRoomId());
        try {
            liveGoodsTransferBizService.timesAndStatusCheck(transferDTO);
        }catch (Exception e){
            throw new ServiceException(e.getMessage() + ", 无法上架");
        }
    }

    @Override
    protected void doAction(LiveGoodsDTO liveGoods, LiveGoodsContext context) {
        liveGoods.setGoodsStatus(LiveGoodsStatusEnum.PUT_AWAY);
        liveGoods.setPutawayAt(new Date());
        //可能重新上架，这里结束时间重置
        liveGoods.setEndAt(null);
        // 如果是一口价且起拍价金额为0，则改为0.01
        if (liveGoods.getTradeType() == LiveGoodsTradeTypeEnum.SEC_KILL && liveGoods.getStartPrice() != null && liveGoods.getStartPrice().compareTo(BigDecimal.ZERO) <= 0) {
            liveGoods.setStartPrice(BigDecimal.valueOf(0.01));
        }
        liveGoodsDetailService.updateLiveGoods(liveGoods);

        //发送商品上架消息
        msgService.goodsPutAway(liveGoods.getLiveRoomId(), liveGoods.getId());

        // 启动上架的延迟队列，目前是延迟5分钟，用于获取商品回放
        long delayMillis = 5 * 60 * 1000;
        JSONObject jsonObject = new JSONObject().set("liveGoodsId", liveGoods.getId());
        coreMqService.send(MqTopicEnum.LIVE_GOODS_PUTAWAY, jsonObject.toString(), delayMillis);
    }
}
