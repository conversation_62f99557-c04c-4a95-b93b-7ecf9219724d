package com.bbh.live.service.lottery;

import com.bbh.base.ListBase;
import com.bbh.live.dao.dto.LotteryCreateDTO;
import com.bbh.live.dao.dto.LotteryPrizeItemDTO;
import com.bbh.live.dao.dto.LotteryWinningRecordDTO;
import com.bbh.live.dao.dto.LotteryWinningRecordPO;
import com.bbh.live.dao.dto.vo.LotteryWheelVO;

import java.util.List;

/**
 * 转盘抽奖
 * <AUTHOR>
 */
public interface LotteryService {

    /**
     * 创建抽奖
     * @param createLotteryDTO  抽奖次数
     * @return  抽中的奖品
     */
    List<LotteryPrizeItemDTO> createLottery(LotteryCreateDTO createLotteryDTO);

    /**
     * 获取抽奖页面信息
     */
    LotteryWheelVO getLottery();

    /**
     * 我的中奖结果
     */
    ListBase<LotteryWinningRecordDTO> getWinningRecordList(LotteryWinningRecordPO pageBase);
}
