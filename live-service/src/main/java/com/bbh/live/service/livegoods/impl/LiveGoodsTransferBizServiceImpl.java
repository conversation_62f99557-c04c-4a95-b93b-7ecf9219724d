package com.bbh.live.service.livegoods.impl;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bbh.enums.LiveGoodsStatusEnum;
import com.bbh.enums.LiveGoodsTradeTypeEnum;
import com.bbh.exception.ServiceException;
import com.bbh.live.constant.ProjectConstant;
import com.bbh.live.dao.dto.GoodsTransferDTO;
import com.bbh.live.dao.dto.vo.AuctionBidVO;
import com.bbh.live.dao.service.*;
import com.bbh.live.service.livegoods.LiveGoodsTransferBizService;
import com.bbh.live.service.msg.MsgService;
import com.bbh.live.service.permission.PermissionService;
import com.bbh.live.util.LivePermissionChecker;
import com.bbh.model.*;
import com.bbh.secure.AuthUtil;
import com.bbh.util.AssertUtil;
import com.bbh.util.EnvironmentUtil;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/9/27 09:29
 * @description
 */
@Service
@AllArgsConstructor
public class LiveGoodsTransferBizServiceImpl implements LiveGoodsTransferBizService {

    private final LiveRoomService liveRoomService;
    private final LiveGoodsService liveGoodsService;
    private final ErpGoodsService erpGoodsService;
    private final GlobalOrgSeatService globalOrgSeatService;
    private final LiveGoodsTransferService liveGoodsTransferService;
    private final MsgService msgService;
    private final PermissionService permissionService;


    @Override
    public AuctionBidVO goodsTransfer(GoodsTransferDTO goodsTransferDTO) {
        // 验证商品转出模型的合法性
        goodsTransferDTO.goodsTransferModelCheck();
        // 导播权限校验
        LivePermissionChecker.assertDirector(goodsTransferDTO.getLiveRoomId());
        // 如果直播到时间后，禁止传送
        LiveRoom liveRoom = liveRoomService.getById(goodsTransferDTO.getLiveRoomId());
        AssertUtil.assertFalse(liveRoom.getEndAt().before(DateUtil.date()), "已超出结束时间，不能传送商品");

        var globalOrgSeat = globalOrgSeatService.lambdaQuery().eq(GlobalOrgSeat::getId, goodsTransferDTO.getTargetSeatId()).select(GlobalOrgSeat::getOrgId, GlobalOrgSeat::getUserId).one();
        AssertUtil.assertNotNull(globalOrgSeat, "目标用户不存在");
        goodsTransferDTO.setTargetUserId(globalOrgSeat.getUserId());
        goodsTransferDTO.setTargetOrgId(globalOrgSeat.getOrgId());

        // 获取商品信息
        LambdaQueryWrapper<LiveGoods> liveGoodsLqw = new LambdaQueryWrapper<>();
        liveGoodsLqw.eq(LiveGoods::getId, goodsTransferDTO.getLiveGoodsId())
                .eq(LiveGoods::getLiveRoomId, goodsTransferDTO.getLiveRoomId())
                .eq(LiveGoods::getOrgId, AuthUtil.getOrgId());
        LiveGoods liveGoods = liveGoodsService.getOne(liveGoodsLqw);
        AssertUtil.assertNotNull(liveGoods, "商品不存在");

        if (liveGoods.getTradeType() == LiveGoodsTradeTypeEnum.AUCTION) {
            AssertUtil.assertTrue(goodsTransferDTO.getTransferPrice().compareTo(BigDecimal.ZERO) > 0, "传送价格必须大于0");
            AssertUtil.assertTrue(goodsTransferDTO.getTransferPrice().compareTo(new BigDecimal("99999999")) <= 0, "金额过大，不能超过99999999元");
        }

        // 进行业务逻辑检查
        this.goodsTransferLogicCheck(goodsTransferDTO, liveGoods);
        // 时效检查 状态检查 不能 一货多传
        this.timesAndStatusCheck(goodsTransferDTO);

        // 如果是一口价商品，且金额是零元，强制修改为0.01
        BigDecimal transferPrice = goodsTransferDTO.getTransferPrice();
        if (liveGoods.getTradeType() == LiveGoodsTradeTypeEnum.SEC_KILL && transferPrice != null && transferPrice.compareTo(BigDecimal.ZERO) <= 0) {
            transferPrice = BigDecimal.valueOf(0.01);
        }

        if (!EnvironmentUtil.isProfile("local")) {
            // 保证金检查
            var depositResult = permissionService.checkBidPermission(
                    goodsTransferDTO.getTargetOrgId(),
                    transferPrice,
                    goodsTransferDTO.getTargetSeatId(),
                    goodsTransferDTO.getLiveGoodsId(),
                    "发起传送"
            );
            if (depositResult != null && !depositResult.getSuccess()) {
                if (depositResult.getIfNeedAuth()) {
                    throw new ServiceException("对方尚未认证");
                }
                if (depositResult.getIfNeedDepositCharge()) {
                    throw new ServiceException("对方保证金不足");
                }
            }
        }

        // 获取当前时间
        DateTime date = DateUtil.date();
        LiveGoodsTransfer liveGoodsTransfer = new LiveGoodsTransfer();
        liveGoodsTransfer.setLiveRoomId(goodsTransferDTO.getLiveRoomId());
        liveGoodsTransfer.setLiveGoodsId(goodsTransferDTO.getLiveGoodsId());
        liveGoodsTransfer.setTransferPrice(transferPrice);
        liveGoodsTransfer.setTargetSeatId(goodsTransferDTO.getTargetSeatId());
        liveGoodsTransfer.setTargetUserId(goodsTransferDTO.getTargetUserId());
        liveGoodsTransfer.setTargetOrgId(goodsTransferDTO.getTargetOrgId());

        // 设置转出记录的过期时间，为当前时间后两分钟
        liveGoodsTransfer.setExpireAt(DateUtil.offsetSecond(date, 120));
        liveGoodsTransfer.setIfHandled(false);
        liveGoodsTransferService.save(liveGoodsTransfer);

        // 推送传送消息
        msgService.goodsTransfer(liveGoodsTransfer.getLiveRoomId(), goodsTransferDTO.getTargetSeatId(), liveGoodsTransfer.getId());

        return new AuctionBidVO().setSuccess(true);
    }

    /**
     * 对商品传递逻辑进行校验。
     * 该方法接收一个商品转移DTO对象作为参数，用于描述商品的传递细节。
     * 方法的主要作用是根据传入的商品转移信息，执行相应的逻辑校验，确保转移操作符合业务规则。
     * 具体的校验逻辑未在代码中直接体现，因此注释主要描述了方法的目的和功能。
     *
     * @param goodsTransferDTO 商品转移的DTO（数据传输对象），包含了商品转移的相关信息
     * @param liveGoods
     */
    public void goodsTransferLogicCheck(GoodsTransferDTO goodsTransferDTO, LiveGoods liveGoods) {
        Long liveRoomId = goodsTransferDTO.getLiveRoomId();
        Long liveGoodsId = goodsTransferDTO.getLiveGoodsId();

        Long targetSeatId = goodsTransferDTO.getTargetSeatId();

        AssertUtil.assertFalse(AuthUtil.getOrgId().equals(goodsTransferDTO.getTargetOrgId()), "不能传送到本机构");

        LambdaQueryWrapper<LiveRoom> liveRoomLqw = new LambdaQueryWrapper<>();
        liveRoomLqw.eq(LiveRoom::getId, liveRoomId);
        long count = liveRoomService.count(liveRoomLqw);
        AssertUtil.assertTrue(count > 0, "直播间不存在");

        // 验证商品 1.锁单  2.是否竞拍  3.是否已经成交  不能传送
        AssertUtil.assertNotNull(liveGoods, "商品不存在");
        AssertUtil.assertFalse(liveGoods.getGoodsStatus() == LiveGoodsStatusEnum.TRADED, "商品已成交，无法传送");

        LambdaQueryWrapper<ErpGoods> erpGoodsLqw = new LambdaQueryWrapper<>();
        erpGoodsLqw.eq(ErpGoods::getId, liveGoods.getGlobalGoodsId())
                .select(ErpGoods::getSaleStatus, ErpGoods::getIfLocked);
        ErpGoods erpGoods = erpGoodsService.getOne(erpGoodsLqw, false);
        AssertUtil.assertTrue(erpGoods != null, "商品不存在");
        // 判断是否卖出
        AssertUtil.assertTrue(Objects.requireNonNull(erpGoods).getSaleStatus() != ProjectConstant.ErpGoodsSaleStatus.SOLD_OUT, "商品已卖出");
        // 判断是否锁单
        AssertUtil.assertTrue(erpGoods.getIfLocked() != ProjectConstant.ErpGoodsLockStatus.LOCK, "商品已锁单");

        // 判断用户 是否存在
        LambdaQueryWrapper<GlobalOrgSeat> globalOrgSeatLqw = new LambdaQueryWrapper<>();
        globalOrgSeatLqw.eq(GlobalOrgSeat::getId, targetSeatId);
        long globalOrgSeatCount = globalOrgSeatService.count(globalOrgSeatLqw);
        AssertUtil.assertTrue(globalOrgSeatCount > 0, "用户不存在");
    }

    public void timesAndStatusCheck(GoodsTransferDTO goodsTransferDTO) {
        LambdaQueryWrapper<LiveGoodsTransfer> liveGoodsTransferLqw = new LambdaQueryWrapper<>();
        liveGoodsTransferLqw.eq(LiveGoodsTransfer::getLiveGoodsId, goodsTransferDTO.getLiveGoodsId())
                .eq(LiveGoodsTransfer::getLiveRoomId, goodsTransferDTO.getLiveRoomId())
                .eq(LiveGoodsTransfer::getIfHandled, false)
                .gt(LiveGoodsTransfer::getExpireAt, DateUtil.date())
                .last("limit 1")
                .select(LiveGoodsTransfer::getId, LiveGoodsTransfer::getExpireAt);
        LiveGoodsTransfer liveGoodsTransfer = liveGoodsTransferService.getOne(liveGoodsTransferLqw, false);
        AssertUtil.assertNull(liveGoodsTransfer, "商品已经传送给其他用户");
    }

    @Override
    public void cancel(GoodsTransferDTO goodsTransferDTO) {
        Date now = new Date();
        // 当前商品的传送记录
        LiveGoodsTransfer liveGoodsTransfer = this.getLiveGoodsCurrentTransferRecord(goodsTransferDTO.getLiveRoomId(), goodsTransferDTO.getLiveGoodsId());
        AssertUtil.assertNotNull(liveGoodsTransfer, "当前商品没有传送记录");
        // 取消传送- 修改过期时间
        liveGoodsTransfer.setExpireAt(now);
        liveGoodsTransfer.setIfHandled(true);
        liveGoodsTransferService.updateById(liveGoodsTransfer);

        //发im消息关闭传送卡片
        msgService.goodsTransferClose(liveGoodsTransfer.getLiveRoomId(), liveGoodsTransfer.getTargetSeatId(), liveGoodsTransfer.getLiveGoodsId());
    }

    /**
     * 根据直播间ID和商品ID获取商品传送记录
     * @param liveRoomId
     * @param liveGoodsId
     * @return
     */
    public LiveGoodsTransfer getLiveGoodsCurrentTransferRecord(Long liveRoomId, Long liveGoodsId) {
        Date now = new Date();
        List<LiveGoodsTransfer> list = liveGoodsTransferService.lambdaQuery()
                .eq(LiveGoodsTransfer::getLiveGoodsId, liveGoodsId)
                .eq(LiveGoodsTransfer::getLiveRoomId, liveRoomId)
                .eq(LiveGoodsTransfer::getIfHandled, false)
                .gt(LiveGoodsTransfer::getExpireAt, now)
                .list();

        AssertUtil.assertFalse(list.size() > 1, "商品传送记录存在多个");
        return list.size() == 1 ? list.getFirst() : null;
    }
}
