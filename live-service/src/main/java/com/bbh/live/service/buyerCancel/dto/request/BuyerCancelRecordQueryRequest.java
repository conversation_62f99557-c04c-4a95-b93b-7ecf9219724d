package com.bbh.live.service.buyerCancel.dto.request;

import com.bbh.enums.GlobalOrderTypeEnum;
import com.bbh.enums.LiveGoodsBuyerCancelRecordStatusEnum;
import com.bbh.live.service.buyerCancel.dto.BuyerCancelBiz;
import lombok.Data;

/**
 * 买家取消成交记录查询请求对象
 *
 * <AUTHOR>
 * @since 2025-01-10
 */
@Data
public class BuyerCancelRecordQueryRequest implements BuyerCancelBiz {

    /**
     * 取消状态
     */
    private LiveGoodsBuyerCancelRecordStatusEnum status;

    /**
     * 个人/店铺，true=个人，false=店铺
     */
    private Boolean ifMy = true;

    /**
     * 买家身份，true=买家，false=卖家
     */
    private Boolean ifBuyer = true;

    /**
     * 记录ID
     */
    private Long recordId;

    private GlobalOrderTypeEnum bizType;

    @Override
    public GlobalOrderTypeEnum getBizType() {
        return this.bizType;
    }

    @Override
    public void setBizType(GlobalOrderTypeEnum bizType) {
        this.bizType=bizType;
    }

}
