package com.bbh.live.service.msg.dto;

import com.bbh.live.core.msg.MsgType;
import com.bbh.live.service.msg.dto.base.BaseGoods;
import com.bbh.live.service.msg.dto.base.BaseMsg;
import com.bbh.live.service.msg.dto.base.IMsg;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 商品预约-用户收到的卡片弹窗消息 <br>
 * 一个用户一套消息
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
public class GoodsSubscribeCloseMsgDTO extends BaseMsg implements IMsg{

    /**
     * 直播间id，通过它跳转到对应的直播间
     */
    private Long liveRoomId;

    /**
     * 预约的商品
     */
    private BaseGoods goods;


    /**
     * 每个消息对应一个类型
     *
     * @return {@link MsgType}
     */
    @Override
    public String type() {
        return MsgType.GOODS_SUBSCRIBE_CLOSED;
    }

}
