package com.bbh.live.service.msg.notify.chatroom.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 聊天室状态
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum ChatroomStatus {
    ACTIVE_CALL(0),
    AUTO_KICKED_OUT(1),
    USER_BANNED(2),
    AUTO_DESTROYED(3);

    private final int value;

    public static ChatroomStatus fromValue(int value) {
        for (ChatroomStatus status : values()) {
            if (status.getValue() == value) {
                return status;
            }
        }
        throw new IllegalArgumentException("不支持的状态: " + value);
    }
}