package com.bbh.live.service.lottery.strategy;

import com.bbh.enums.GlobalLotteryPrizePoolTypeEnum;
import com.bbh.live.dao.dto.LotteryPrizeItemDTO;

/**
 * 奖品处理策略
 * <AUTHOR>
 */
public interface PrizeProcessStrategy {

    /**
     * 匹配
     * @param type 匹配的类型
     * @return 是否匹配
     */
    boolean match(GlobalLotteryPrizePoolTypeEnum type);

    /**
     * 处理奖品
     * @param userId 用户ID
     * @param seatId 用户座位ID
     * @param prizeItem 奖品
     */
    void processPrize(Long userId, Long seatId, LotteryPrizeItemDTO prizeItem);

}
