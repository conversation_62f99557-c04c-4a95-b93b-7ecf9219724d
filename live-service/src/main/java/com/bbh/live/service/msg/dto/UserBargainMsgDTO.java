package com.bbh.live.service.msg.dto;

import com.bbh.live.core.msg.MsgType;
import com.bbh.live.service.msg.dto.base.BaseGoods;
import com.bbh.live.service.msg.dto.base.BaseMsg;
import com.bbh.live.service.msg.dto.base.BaseSeat;
import com.bbh.live.service.msg.dto.base.IMsg;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <AUTHOR> dsy
 * @Date: 2024/9/2
 * @Description:
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
public class UserBargainMsgDTO extends BaseMsg implements IMsg {

    @Override
    public String type() {
        return MsgType.BARGAIN;
    }

    /**
     * 直播间id
     */
    private Long liveRoomId;

    /**
     * 商品信息
     */
    private BaseGoods goods;

    /**
     * 用户信息
     */
    private BaseSeat user;

    /**
     * 议价金额
     */
    private BigDecimal bargainPrice;

    private Integer tradeType;
}
