package com.bbh.live.service.msg.dto;

import com.bbh.live.core.msg.MsgType;
import com.bbh.live.service.msg.dto.base.BaseMsg;
import com.bbh.live.service.msg.dto.base.IMsg;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 商品删除消息
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class GoodsDeleteMsgDTO extends BaseMsg implements IMsg {

    private Long liveRoomId;

    private List<Long> liveGoodsIdList;

    /**
     * 每个消息对应一个类型
     *
     * @return {@link MsgType}
     */
    @Override
    public String type() {
        return MsgType.GOODS_DELETE;
    }
}
