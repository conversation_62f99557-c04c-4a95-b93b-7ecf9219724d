package com.bbh.live.service.virutalgoods.processor;

import com.bbh.model.GlobalOrderOtherGoodsDic;
import com.bbh.model.GlobalOrgSeat;
import com.bbh.vo.Result;

/**
 * 虚拟订单处理器
 * <AUTHOR> dsy
 * @Date: 2024/9/12
 * @Description:
 */
public interface IVirtualOrderProcessor {

    /**
     * 前置处理方法，用于拦截器校验
     * @param buyerSeat     买手信息
     * @param virtualGoods  虚拟商品信息
     * @return 是否允许继续执行
     */
    default boolean preProcess(GlobalOrgSeat buyerSeat, GlobalOrderOtherGoodsDic virtualGoods) {
        return true;
    }

    /**
     * 处理订单支付成功回调
     * @param context
     * @return
     */
    Result process(VirtualOrderProcessContext context);
}
