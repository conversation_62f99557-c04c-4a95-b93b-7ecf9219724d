package com.bbh.live.service.livegoods.cache.info;

import com.bbh.enums.LiveGoodsTradeTypeEnum;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR> dsy
 * @Date: 2024/7/26
 * @Description:
 */
@Data
@Accessors(chain = true)
public class LiveGoodsAuctionCacheObj {

    private BigDecimal currentPrice;

    private BigDecimal increasePrice;

    private Long auctionEndTime;

    private Long buyerSeatId;

    private String ifSettle;

    private String ifRevoke;

    private Integer tradeType;

    public static LiveGoodsAuctionCacheObj transfer2AuctionCacheObj(LiveGoodsAuctionInfo liveGoodsAuctionInfo) {
        Long auctionEndTimestamp = liveGoodsAuctionInfo.getAuctionEndTime().getTime();
        return new LiveGoodsAuctionCacheObj()
                .setTradeType(liveGoodsAuctionInfo.getTradeType().getCode())
                .setCurrentPrice(liveGoodsAuctionInfo.getCurrentPrice())
                .setIncreasePrice(liveGoodsAuctionInfo.getIncreasePrice())
                .setAuctionEndTime(auctionEndTimestamp)
                .setBuyerSeatId(liveGoodsAuctionInfo.getBuyerSeatId())
                .setIfSettle(Boolean.TRUE.equals(liveGoodsAuctionInfo.getIfSettle()) ? "1" : "0")
                .setIfRevoke(Boolean.TRUE.equals(liveGoodsAuctionInfo.getIfRevoke()) ? "1" : "0");
    }

    public static LiveGoodsAuctionInfo transfer2AuctionInfo(LiveGoodsAuctionCacheObj liveGoodsAuctionCacheObj) {
        return new LiveGoodsAuctionInfo()
                .setTradeType(liveGoodsAuctionCacheObj.getTradeType() == 0 ? LiveGoodsTradeTypeEnum.AUCTION : LiveGoodsTradeTypeEnum.SEC_KILL)
                .setCurrentPrice(liveGoodsAuctionCacheObj.getCurrentPrice())
                .setIncreasePrice(liveGoodsAuctionCacheObj.getIncreasePrice())
                .setAuctionEndTime(new Date(liveGoodsAuctionCacheObj.getAuctionEndTime()))
                .setBuyerSeatId(liveGoodsAuctionCacheObj.getBuyerSeatId())
                .setIfSettle("1".equals(liveGoodsAuctionCacheObj.getIfSettle()))
                .setIfRevoke("1".equals(liveGoodsAuctionCacheObj.getIfRevoke()));
    }
}
