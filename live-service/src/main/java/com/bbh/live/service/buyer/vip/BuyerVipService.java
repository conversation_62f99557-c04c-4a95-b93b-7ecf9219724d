package com.bbh.live.service.buyer.vip;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.bbh.enums.GlobalOrgTypeEnum;
import com.bbh.enums.VipBuyerChangeLogStatusEnum;
import com.bbh.enums.VipBuyerExpLogExpTypeEnum;
import com.bbh.live.constant.ProjectConstant;
import com.bbh.live.dao.mapper.GlobalOrderItemMapper;
import com.bbh.live.dao.mapper.GlobalOrderMapper;
import com.bbh.live.dao.service.*;
import com.bbh.live.enums.BuyerVipEventEnum;
import com.bbh.live.enums.BuyerVipTypeEnum;
import com.bbh.live.service.buyer.sign.BuyerSignService;
import com.bbh.live.service.buyer.vip.vo.BuyerVipConfigVO;
import com.bbh.live.service.buyer.vip.vo.BuyerVipMsgTransferVO;
import com.bbh.live.service.buyer.vip.vo.UserBuyerVipInfoVO;
import com.bbh.live.service.vipdeduction.GlobalVipDeductionService;
import com.bbh.model.*;
import com.bbh.service.RedisService;
import com.bbh.util.AssertUtil;
import com.bbh.util.LogExUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2024/8/15 11:00
 */
@Service
@RequiredArgsConstructor
public class BuyerVipService {
    private final VipBuyerCardService vipBuyerCardService;
    private final VipBuyerConfigService vipBuyerConfigService;
    private final RedisService redisService;
    private final VipBuyerExpLogService vipBuyerExpLogService;
    private final VipBuyerChangeNicknameLogService vipBuyerChangeNicknameLogService;
    private final VipBuyerPeepLogService vipBuyerPeepLogService;
    private final VipBuyerLotteryLogService vipBuyerLotteryLogService;
    private final VipBuyerRefundFenbeiService vipBuyerRefundFenbeiService;
    private final GlobalOrderMapper globalOrderMapper;
    private final GlobalOrderItemMapper globalOrderItemMapper;
    private final GlobalOrgSeatService globalOrgSeatService;
    private final VipBuyerPayRecordService vipBuyerPayRecordService;
    private final GlobalVirtualGoodsOrderService globalVirtualGoodsOrderService;
    private final IGlobalOrganizationService organizationService;
    private final GlobalVipDeductionService globalVipDeductionService;

    @Value("${htb.annual_vip_deduction:6188}")
    private BigDecimal annualVipDeduction;

    public UserBuyerVipInfoVO getUserBuyerVipInfoBySeatId(Long seatId) {
        return this.getUserBuyerVipInfoBySeatIdOrVipId(seatId, null);
    }

    public UserBuyerVipInfoVO getUserBuyerVipInfoByVipId(Long vipId) {
        return this.getUserBuyerVipInfoBySeatIdOrVipId(null, vipId);
    }

    /**
     * 根据座位ID获取用户买家VIP信息 此方法主要用于获取用户的VIP状态及其相关信息，包括未使用的权益次数等 如果用户不是VIP，则返回默认的非VIP状态信息 如果用户是VIP，则根据其VIP等级返回相应的信息和权益使用情况
     *
     * @param seatId 座位ID，用于识别用户
     * @return 用户买家VIP信息对象，包含VIP状态、等级、权益使用情况等
     */
    public UserBuyerVipInfoVO getUserBuyerVipInfoBySeatIdOrVipId(Long seatId, Long vipId) {
        UserBuyerVipInfoVO userBuyerVipInfo;
        if (vipId != null) {
            userBuyerVipInfo = vipBuyerCardService.getUserBuyerVipInfoByVipId(vipId);
        } else {
            userBuyerVipInfo = vipBuyerCardService.getUserBuyerVipInfoBySeatId(seatId);
        }

        // 处理未找到的情况
        if (ObjUtil.isNull(userBuyerVipInfo)) {
            return vipBuyerCardService.getDefaultVipInfoBySeatId(seatId);
        }
        boolean ifYearVip = Boolean.TRUE.equals(userBuyerVipInfo.getIsAnnualFeeVip());

        Date nowDate = DateUtil.beginOfDay(new Date());
        Date timeVipEnd = DateUtil.endOfDay(userBuyerVipInfo.getTimeVipEnd());
        boolean after = timeVipEnd.after(nowDate);
        if (after) {
            userBuyerVipInfo.setBuyerVipType(BuyerVipTypeEnum.VIP);
            userBuyerVipInfo.setIsVip(true);
            // 没有过期但是临近过期十天提示框
            long daysRemaining = DateUtil.betweenDay(nowDate, timeVipEnd, true);
            if (daysRemaining <= ProjectConstant.BuyerVip.VIP_EXPIRE_WARN_DAY) {
                userBuyerVipInfo.setNearlyTimeVipEndPop(true);
            }
        } else {
            userBuyerVipInfo.setIsVip(false);
            userBuyerVipInfo.setBuyerVipType(BuyerVipTypeEnum.VIP_EXPIRED);
        }
        BuyerVipConfigVO buyerVipConfig = BuyerVipUtil.getBuyerVipConfig(userBuyerVipInfo.getExp(), ifYearVip);
        userBuyerVipInfo.setVipLevel(buyerVipConfig.getVipLevel());
        userBuyerVipInfo.setVipConfig(buyerVipConfig);
        userBuyerVipInfo.setNextLevelNeedExp(buyerVipConfig.getNextLevelExp() - userBuyerVipInfo.getExp());
        // 计算剩余次数
        calculateUnusedTimes(userBuyerVipInfo, buyerVipConfig);
        // 今日是否已登陆
        userBuyerVipInfo.setIsSignToday(SpringUtil.getBean(BuyerSignService.class).isSign(new Date(), userBuyerVipInfo.getSeatId()));
        return userBuyerVipInfo;
    }

    /**
     * 根据用户ID列表获取用户买家VIP信息 此方法旨在为每个用户提供相应的VIP状态和相关信息，包括未使用的次数等 对于没有VIP信息的用户，将提供默认的VIP信息
     *
     * @param seatIdList 用户ID列表，用于查询对应的VIP信息
     * @return 返回一个Map，其中键是用户ID，值是用户的VIP信息对象
     */
    public Map<Long, UserBuyerVipInfoVO> getUserBuyerVipInfoByUidList(List<Long> seatIdList) {
        Map<Long, UserBuyerVipInfoVO> resultMap = new HashMap<>(8);
        List<UserBuyerVipInfoVO> userBuyerVipInfoList = vipBuyerCardService.getUserBuyerVipInfoBySeatIdList(seatIdList);
        List<Long> hasUidList = userBuyerVipInfoList.stream().map(UserBuyerVipInfoVO::getSeatId).toList();
        seatIdList.removeAll(hasUidList);
        if (!seatIdList.isEmpty()) {
            UserBuyerVipInfoVO defaultInfo = new UserBuyerVipInfoVO();
            defaultInfo.setBuyerVipType(BuyerVipTypeEnum.NO_VIP);
            defaultInfo.setIsAnnualFeeVip(false);
            defaultInfo.setAfterSaleServiceUnUsedTimes(0);
            defaultInfo.setLotteryUnUsedTimes(0);
            defaultInfo.setModifyNicknameUnUsedTimes(0);
            defaultInfo.setPeepBuyerUnUsedTimes(0);
            defaultInfo.setUnUseAllVipTimes(0);
            BuyerVipConfigVO buyerVipConfig = BuyerVipUtil.getBuyerVipConfig(0, false);
            defaultInfo.setUnVipNearShopDistance(buyerVipConfig.getNearShopDistance());
            defaultInfo.setVipConfig(buyerVipConfig);
            seatIdList.forEach(uid -> resultMap.put(uid, defaultInfo));
        }

        for (UserBuyerVipInfoVO userBuyerVipInfo : userBuyerVipInfoList) {
            boolean ifYearVip = Boolean.TRUE.equals(userBuyerVipInfo.getIsAnnualFeeVip());
            boolean isVipActive = userBuyerVipInfo.getTimeVipEnd().after(new Date());
            userBuyerVipInfo.setBuyerVipType(isVipActive ? BuyerVipTypeEnum.VIP : BuyerVipTypeEnum.VIP_EXPIRED);
            userBuyerVipInfo.setVipLevel(BuyerVipUtil.getBuyerVipLevel(userBuyerVipInfo.getExp(), Boolean.TRUE.equals(userBuyerVipInfo.getIsAnnualFeeVip())));
            BuyerVipConfigVO buyerVipConfig = BuyerVipUtil.getBuyerVipConfig(userBuyerVipInfo.getExp(), ifYearVip);
            userBuyerVipInfo.setVipConfig(buyerVipConfig);
            userBuyerVipInfo.setNextLevelNeedExp(buyerVipConfig.getNextLevelExp() - userBuyerVipInfo.getExp());
            calculateUnusedTimes(userBuyerVipInfo, buyerVipConfig);
            resultMap.put(userBuyerVipInfo.getSeatId(), userBuyerVipInfo);
        }
        return resultMap;
    }

    /**
     * 发送融云消息 - 获取买手vip信息
     *
     * @param seatId
     * @return
     */
    public BuyerVipMsgTransferVO getBuyerVipMsgTransferVO(Long seatId) {
        var vipBuyerCard = vipBuyerCardService.lambdaQuery().eq(VipBuyerCard::getSeatId, seatId).select(VipBuyerCard::getExp, VipBuyerCard::getIsAnnualFeeVip, VipBuyerCard::getTimeVipEnd).one();
        if (vipBuyerCard == null) {
            return new BuyerVipMsgTransferVO(0L, seatId, BuyerVipTypeEnum.NO_VIP, 0, false);
        } else {
            Date nowDate = DateUtil.beginOfDay(new Date());
            Date timeVipEnd = DateUtil.endOfDay(vipBuyerCard.getTimeVipEnd());

            boolean isAnnualFeeVip = Boolean.TRUE.equals(vipBuyerCard.getIsAnnualFeeVip());
            return new BuyerVipMsgTransferVO(vipBuyerCard.getId(), seatId, timeVipEnd.after(nowDate) ? BuyerVipTypeEnum.VIP : BuyerVipTypeEnum.VIP_EXPIRED, BuyerVipUtil.getBuyerVipLevel(vipBuyerCard.getExp(), isAnnualFeeVip), isAnnualFeeVip);
        }
    }

    /**
     * 根据用户席位ID更新经验值
     *
     * @param vipId   座席ID
     * @param exp     经验值
     * @param vipEnum 会员事件枚举
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateExpByVipId(Long vipId, Integer exp, BuyerVipEventEnum vipEnum) {
        // 首先确保获取到的用户信息不为空
        UserBuyerVipInfoVO userBuyerVipInfoByUid = vipBuyerCardService.getUserBuyerVipInfoByVipId(vipId);
        if (ObjectUtil.isNull(userBuyerVipInfoByUid) || exp <= 0) {
            return;
        }
        LambdaUpdateWrapper<VipBuyerCard> vipBuyerCardLuw = new LambdaUpdateWrapper<>();
        vipBuyerCardLuw.eq(VipBuyerCard::getId, userBuyerVipInfoByUid.getId()).setIncrBy(VipBuyerCard::getExp, vipEnum.getFlag() ? exp : -exp);

        // 解约保证金直接删除vip信息
        if (BuyerVipEventEnum.MARGIN_BREAK == vipEnum) {
            vipBuyerCardService.remove(vipBuyerCardLuw);
        } else {
            vipBuyerCardService.update(vipBuyerCardLuw);
        }

        // 记录日志
        vipBuyerExpLogService.insertVipExpChangeLog(userBuyerVipInfoByUid, exp, vipEnum);
    }

    /**
     * 根据座位ID更新使用次数
     *
     * @param seatId 座位ID
     * @param t      更新内容，可以是日志对象 (修改昵称|查看出价人) 或日志对象列表(抽奖)
     * @return 更新是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public <T> boolean updateUseTimesBySeatId(Long seatId, T t) {
        String timesType;
        try {
            if (t instanceof VipBuyerChangeNicknameLog entity) {
                // 修改 昵称 次数
                timesType = BuyerVipEventEnum.MODIFY_NICKNAME.getCode();
                vipBuyerChangeNicknameLogService.save(entity);
                return vipBuyerCardService.updateTimesBySeatId(seatId, timesType, 1);
            } else if (t instanceof VipBuyerPeepLog entity) {
                // 窥探 日志
                timesType = BuyerVipEventEnum.PEEP_BUYER.getCode();
                vipBuyerPeepLogService.save(entity);
                return vipBuyerCardService.updateTimesBySeatId(seatId, timesType, 1);
            } else if (t instanceof List list && !list.isEmpty()) {
                // 批量 修改 抽奖 日志
                Object firstElement = list.getFirst();
                if (firstElement instanceof VipBuyerLotteryLog) {
                    timesType = BuyerVipEventEnum.LOTTERY.getCode();
                    vipBuyerLotteryLogService.saveBatch(list);
                    return vipBuyerCardService.updateTimesBySeatId(seatId, timesType, list.size());
                } else if (firstElement instanceof VipBuyerRefundFenbei) {
                    // 批量 售后 分贝
                    timesType = BuyerVipEventEnum.SALE_AFTER_SERVICE.getCode();
                    vipBuyerRefundFenbeiService.saveBatch(list);
                    BigDecimal sum = BigDecimal.ZERO;
                    for (Object o : list) {
                        VipBuyerRefundFenbei vipBuyerRefundFenbei = (VipBuyerRefundFenbei) o;
                        sum = NumberUtil.add(sum, vipBuyerRefundFenbei.getRefundFenbei());
                    }
                    return vipBuyerCardService.updateTimesBySeatIdAndFenbei(seatId, timesType, list.size(), sum.intValue());
                }
            }
        } catch (Exception e) {
            LogExUtil.errorLog("updateUseTimesBySeatId异常 " + t.getClass() + " : ", e);
            return false;
        }
        return false;
    }

    /**
     * 根据买家经验值获取买家VIP配置信息 此方法封装了调用BuyerVipUtil类的getBuyerVipConfig方法的过程，以便在不同地方根据经验值获取对应的VIP配置
     *
     * @param exp 买家的经验值，用于确定买家的VIP等级和相关配置
     * @return 返回一个BuyerVipConfigVO对象，包含买家的VIP配置信息如果找不到对应的配置，可能返回null
     */
    public BuyerVipConfigVO getBuyerVipConfig(Integer exp, boolean ifYear) {
        return BuyerVipUtil.getBuyerVipConfig(exp, ifYear);
    }

    /**
     * 根据买家的经验值获取买家的VIP等级
     *
     * @param exps 买家的经验值，用于计算VIP等级
     * @return 返回计算得到的买家VIP等级
     */
    public Integer getBuyerVipLevel(Integer exps) {
        return BuyerVipUtil.getBuyerVipLevel(exps, false);
    }

    private void calculateUnusedTimes(UserBuyerVipInfoVO userBuyerVipInfo, BuyerVipConfigVO buyerVipConfig) {
        int totalUnused = 0;
        int afterSaleServiceUnused = Math.max(0, buyerVipConfig.getSaleAfterServiceNum() - userBuyerVipInfo.getAfterSaleServiceUsedTimes());
        userBuyerVipInfo.setAfterSaleServiceUnUsedTimes(afterSaleServiceUnused);
        totalUnused += (afterSaleServiceUnused > 0 ? 1 : 0);
        int peepBuyerUnused = Math.max(0, buyerVipConfig.getPeepBuyerNum() - userBuyerVipInfo.getPeepBuyerUsedTimes());
        userBuyerVipInfo.setPeepBuyerUnUsedTimes(peepBuyerUnused);
        totalUnused += (peepBuyerUnused > 0 ? 1 : 0);
        int modifyNicknameUnused = Math.max(0, buyerVipConfig.getModifyNicknameNum() - userBuyerVipInfo.getModifyNicknameUsedTimes());
        userBuyerVipInfo.setModifyNicknameUnUsedTimes(modifyNicknameUnused);
        totalUnused += (modifyNicknameUnused > 0 ? 1 : 0);
        int lotteryUnused = Math.max(0, buyerVipConfig.getLotteryNum() - userBuyerVipInfo.getLotteryUsedTimes());
        userBuyerVipInfo.setLotteryUnUsedTimes(lotteryUnused);
        totalUnused += (lotteryUnused > 0 ? 1 : 0);
        userBuyerVipInfo.setUnUseAllVipTimes(totalUnused);
    }

    public List<VipBuyerConfig> initBuyerVip(boolean ifYear) {
        List list = redisService.get("initBuyerVip:ifYear:" + ifYear, List.class);
        if (CollUtil.isEmpty(list)) {
            LambdaUpdateWrapper<VipBuyerConfig> vipBuyerConfigLuw = new LambdaUpdateWrapper<>();
            vipBuyerConfigLuw.eq(VipBuyerConfig::getIfYearly, ifYear);
            list = vipBuyerConfigService.list(vipBuyerConfigLuw);
            if (CollUtil.isNotEmpty(list)) {
                redisService.set("initBuyerVip:ifYear:" + ifYear, list, 86400);
            }
        }
        return list;
    }

    /**
     * 买家会员购买商品，增加成长值
     *
     * @param orderNo 订单号
     */
    public void increaseExpAfterPaySuccess(String orderNo) {
        var order = globalOrderMapper.selectOne(new LambdaQueryWrapper<GlobalOrder>().eq(GlobalOrder::getOrderNo, orderNo));
        AssertUtil.assertNotNull(order, "订单不存在");

        // 获取付款人
        var seatId = order.getBuyerSeatId();
        UserBuyerVipInfoVO vipInfo = getUserBuyerVipInfoBySeatId(seatId);

        if (vipInfo.getBuyerVipType() != BuyerVipTypeEnum.VIP) {
            return;
        }

        // 买家会员购买商品，增加成长值
        VipBuyerCard vipBuyerCard = vipBuyerCardService.getOne(new LambdaQueryWrapper<VipBuyerCard>().eq(VipBuyerCard::getSeatId, seatId));
        List<VipBuyerExpLog> vipBuyerExpLogList = new ArrayList<>();
        int totalExp = 0;
        BigDecimal fenbeiDeductionAmount = BigDecimal.ZERO;
        int fenbeiDeductionCount = 0;
        List<GlobalOrderItem> orderItemList = globalOrderItemMapper.selectList(new LambdaQueryWrapper<GlobalOrderItem>().eq(GlobalOrderItem::getGlobalOrderId, order.getId()));
        for (GlobalOrderItem orderDetail : orderItemList) {
            // 计算分贝抵扣金额
            fenbeiDeductionAmount = fenbeiDeductionAmount.add(orderDetail.getFenbeiDeductionAmount());
            // 分贝抵扣数量
            fenbeiDeductionCount += orderDetail.getFenbeiDeductionCount();

            // 满1000加1成长值，叠加每满3000加1
            if ((orderDetail.getRealPayedAmount().compareTo(BigDecimal.valueOf(1000)) < 0)) {
                continue;
            }
            int exp = 1;
            exp = exp + orderDetail.getRealPayedAmount().divide(BigDecimal.valueOf(3000), 0, RoundingMode.FLOOR).intValue();
            VipBuyerExpLog vipBuyerExpLog = new VipBuyerExpLog();
            vipBuyerExpLog.setSeatId(seatId);
            vipBuyerExpLog.setUpdateExp(exp);
            vipBuyerExpLog.setOldExp(vipBuyerCard.getExp() + totalExp);
            vipBuyerExpLog.setNewExp(vipBuyerExpLog.getOldExp() + vipBuyerExpLog.getUpdateExp());
            vipBuyerExpLog.setExpType(VipBuyerExpLogExpTypeEnum.NEW_OLD);
            vipBuyerExpLog.setAddOrSub(VipBuyerChangeLogStatusEnum.ADD);
            vipBuyerExpLog.setExpDesc("购买商品");
            vipBuyerExpLog.setOrgId(orderDetail.getSellerOrgId());
            vipBuyerExpLog.setVipBuyerCardId(vipBuyerCard.getId());
            vipBuyerExpLogList.add(vipBuyerExpLog);
            totalExp += exp;
        }
        // 批量保存经验值明细
        vipBuyerExpLogService.saveBatch(vipBuyerExpLogList);
        // 更新当前用户的经验值、累计节省金额和累计使用分贝
        if (totalExp > 0 || fenbeiDeductionCount > 0 || fenbeiDeductionAmount.compareTo(BigDecimal.ZERO) > 0) {
            vipBuyerCardService.update(new LambdaUpdateWrapper<VipBuyerCard>().setIncrBy(totalExp > 0, VipBuyerCard::getExp, totalExp).setIncrBy(fenbeiDeductionCount > 0, VipBuyerCard::getSaveFenbei, fenbeiDeductionCount).setIncrBy(fenbeiDeductionAmount.compareTo(BigDecimal.ZERO) > 0, VipBuyerCard::getSaveMoney, fenbeiDeductionAmount).eq(VipBuyerCard::getSeatId, seatId));
        }
    }

    @Transactional
    public Long activateAndRenewVipCard(Long orderId) {
        Date vipStartAt;
        VipBuyerCard vipBuyerCard;

        GlobalVirtualGoodsOrder order = globalVirtualGoodsOrderService.getById(orderId);
        AssertUtil.assertNotNull(order, "订单不存在");

        String extraData = order.getExtraData();
        // 获取商家是否微商
        GlobalOrganization buyerOrg = organizationService.getById(order.getBuyerOrgId());
        // 微商没有vipId兜底查一下以前买过没
        if (StrUtil.isBlank(extraData) && Objects.equals(buyerOrg.getType(), GlobalOrgTypeEnum.MICRO_BUSINESS.getType())) {
            VipBuyerCard card = vipBuyerCardService.lambdaQuery().eq(VipBuyerCard::getOrgId, order.getBuyerOrgId()).eq(VipBuyerCard::getSeatId, order.getBuyerSeatId()).orderByDesc(VipBuyerCard::getId).last("limit 1").one();
            if (card != null) {
                extraData = card.getId().toString();
                globalVirtualGoodsOrderService.lambdaUpdate().set(GlobalVirtualGoodsOrder::getExtraData, extraData).eq(GlobalVirtualGoodsOrder::getId, orderId).update();
            }
        }

        Long vipBuyerCardId = StrUtil.isBlank(extraData) ? null : Long.parseLong(extraData);
        String userName = globalOrgSeatService.lambdaQuery().eq(GlobalOrgSeat::getId, order.getBuyerSeatId()).select(GlobalOrgSeat::getShowName).one().getShowName();
        if (vipBuyerCardId == null) {
            // 开通新会员
            vipBuyerCard = vipBuyerCardService.createNewVipCard(order, userName);
            // 绑定到订单购买人
            // 查询当前购买人是否有会员
            UserBuyerVipInfoVO vipInfoBySeatId = getUserBuyerVipInfoBySeatId(order.getBuyerSeatId());
            if (vipInfoBySeatId == null || vipInfoBySeatId.getBuyerVipType() == BuyerVipTypeEnum.NO_VIP) {
                vipBuyerCard.setSeatId(order.getBuyerSeatId());
            }
            vipStartAt = vipBuyerCard.getTimeVipStart();
        } else {
            // 续费老会员
            vipBuyerCard = vipBuyerCardService.getById(vipBuyerCardId);
            Date timeVipEnd = vipBuyerCard.getTimeVipEnd();
            // 会员续费
            vipBuyerCardService.renewVipCard(order, vipBuyerCard);
            // 本次开通开始时间，如果过期-续费是今天，如果没过期续费是之前vip到期时间
            vipStartAt = timeVipEnd.before(new Date()) ? vipBuyerCard.getTimeVipStart() : timeVipEnd;
        }

        // 冗余支付时间=当前时间
        vipBuyerCard.setPayTime(new Date());
        // 续费时间
        vipBuyerCard.setOpenTime(new Date());
        // 保存或更新
        vipBuyerCardService.saveOrUpdate(vipBuyerCard);
        // 保存支付记录
        Long payId = vipBuyerPayRecordService.insertVipBuyerPayRecord(order, vipStartAt, vipBuyerCard, userName);
        // 开通年费会员赠送666成长值
        if (order.getGoodsNumber() == 12) {
            updateExpByVipId(vipBuyerCard.getId(), 666, BuyerVipEventEnum.ANNUAL_FEE_VIP_EXP);
            // 需要添加 6188 抵扣金
            GlobalVipDeduction vipDeduction = new GlobalVipDeduction();
            vipDeduction.setOrgId(buyerOrg.getId());
            vipDeduction.setVipDeduction(annualVipDeduction);
            vipDeduction.setEndAt(DateUtil.offsetDay(vipBuyerCard.getPayTime(), 365));
            vipDeduction.setPayAt(vipBuyerCard.getPayTime());
            vipDeduction.setPayId(payId);
            vipDeduction.setUpdateUserId(0L);
            vipDeduction.setUpdatedAt(new Date());
            globalVipDeductionService.save(vipDeduction);
        }
        return vipBuyerCard.getId();
    }
}
