package com.bbh.live.service.test;

import com.bbh.live.constant.DelayQueueTopics;
import com.bbh.live.handler.queue.DelayJob;
import com.bbh.live.handler.queue.consumer.AbstractMessageConsumer;
import com.bbh.model.Fen95Goods;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class TestConsumer extends AbstractMessageConsumer<Fen95Goods> {
    /**
     * 定义topic
     */
    @Override
    public String topic() {
        return DelayQueueTopics.TEST_TOPIC;
    }

    /**
     * 消费
     *
     * @param delayJob 队列内容
     */
    @Override
    public void consume(DelayJob<Fen95Goods> delayJob) {
        log.info("TestConsumer consume: {}", delayJob.getData());
    }
}
