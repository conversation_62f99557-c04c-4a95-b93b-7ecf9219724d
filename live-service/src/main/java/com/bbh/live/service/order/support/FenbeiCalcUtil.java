package com.bbh.live.service.order.support;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;

import com.bbh.model.GlobalOrderItem;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 分贝计算工具类
 *
 * <AUTHOR>
 */
@SuppressWarnings("all")
public class FenbeiCalcUtil {

    /**
     * 支付下单，使用分贝计算（4分贝抵扣1元）
     * 
     * @param belongPrice 单件商品的成交金额
     * @param fenbeiAccount 用户分贝账户余额
     * @return 可抵扣分贝数量
     */
    public static int payUseFenbeiCalc(BigDecimal belongPrice, int fenbeiAccount) {
        // 商品金额大于125元才能使用分贝抵扣
        BigDecimal threshold = new BigDecimal("125");
        if (belongPrice.compareTo(threshold) < 0) {
            return 0;
        }
        // 每件商品抵扣金额上限=0.2%
        BigDecimal maxExchangeRate = new BigDecimal("0.002");
        BigDecimal maxExchange = belongPrice.multiply(maxExchangeRate);
        // 每件商品使用分贝上限=4
        BigDecimal maxUseFenbei = maxExchange.multiply(new BigDecimal("4"));
        // 分贝抵扣的数量
        if (new BigDecimal(fenbeiAccount).compareTo(maxUseFenbei) >= 0) {
            return maxUseFenbei.intValue();
        } else if (new BigDecimal(fenbeiAccount).compareTo(BigDecimal.ZERO) > 0
            && maxUseFenbei.compareTo(new BigDecimal(fenbeiAccount)) > 0) {
            // 分贝不足，使用全部分贝
            return new BigDecimal(fenbeiAccount).intValue();
        } else {
            return 0;
        }
    }

    // 统一的分贝抵扣
    public static FenbeiCalcUtil.FenbeiDeductionResult distributeDeduction(List<GlobalOrderItem> orderItemList,
        int fenbeiBalance, boolean isVip, boolean modifyItems) {
        // 计算通道费总额
        BigDecimal totalChannelAmount =
            orderItemList.stream().map(item -> Optional.ofNullable(item.getChannelAmount()).orElse(BigDecimal.ZERO))
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        // 计算可抵扣的分贝数量（1元 = 4分贝）
        int vipFenbeiCutNumber =
            totalChannelAmount.multiply(BigDecimal.valueOf(4)).setScale(0, RoundingMode.UP).intValue();

        // 先算会员的结果
        FenbeiDeductionResult vipResult =
            distributeVipDeduction(orderItemList, totalChannelAmount, vipFenbeiCutNumber, fenbeiBalance,
                // 只有是会员且需要修改时才修改
                isVip && modifyItems);

        if (isVip) {
            // 如果是会员，直接返回会员的计算结果
            vipResult.setVipMaxFenbeiCount(vipResult.getTotalFenbeiDeductionCount());
            vipResult.setVipMaxFenbeiAmount(vipResult.getTotalFenbeiDeductionAmount());
            return vipResult;
        } else {
            // 如果不是会员，计算非会员的结果，但把会员的可抵扣信息带上
            FenbeiDeductionResult nonVipResult = distributeNonVipDeduction(orderItemList, fenbeiBalance, modifyItems);
            // 把会员可抵扣的信息设置到非会员结果中
            nonVipResult.setVipMaxFenbeiCount(vipResult.getTotalFenbeiDeductionCount());
            nonVipResult.setVipMaxFenbeiAmount(vipResult.getTotalFenbeiDeductionAmount());
            return nonVipResult;
        }
    }

    // 非会员
    public static FenbeiDeductionResult distributeNonVipDeduction(List<GlobalOrderItem> orderItemList,
        int fenbeiBalance, boolean modifyItems) {
        // 计算总订单金额
        BigDecimal totalOrderAmount =
            orderItemList.stream().map(GlobalOrderItem::getNeedPayAmount).reduce(BigDecimal.ZERO, BigDecimal::add);

        // 如果总订单金额小于125，不使用分贝
        if (totalOrderAmount.compareTo(new BigDecimal("125")) < 0 || fenbeiBalance <= 0) {
            return resetItemsAndReturnEmpty(orderItemList, modifyItems);
        }

        // 计算可抵扣的最大金额（0.2%）
        BigDecimal maxDeductionAmount = totalOrderAmount.multiply(new BigDecimal("0.002"));

        // 计算可抵扣的分贝数
        int maxDeductionFenbei =
            maxDeductionAmount.multiply(new BigDecimal("4")).setScale(0, RoundingMode.DOWN).intValue();

        // 实际可用的分贝数
        int usableFenbei = Math.min(maxDeductionFenbei, fenbeiBalance);

        // 按比例均摊分贝抵扣
        BigDecimal totalFenbeiDeductionAmount = new BigDecimal(usableFenbei * 0.25);

        // 如果不修改原始订单项，创建一个副本
        List<GlobalOrderItem> workingList = modifyItems ? orderItemList : new ArrayList<>(orderItemList);

        // 按订单金额降序排列
        workingList.sort(Comparator.comparing(GlobalOrderItem::getNeedPayAmount).reversed());

        int leftFenbeiCutNumber = usableFenbei;
        BigDecimal leftFenbeiDiscountMoney = totalFenbeiDeductionAmount;

        for (int i = 0; i < workingList.size(); i++) {
            GlobalOrderItem item = workingList.get(i);
            int cutFenbeiNumber;
            BigDecimal fenbeiDiscountMoney;

            if (i != workingList.size() - 1) {
                // 按比例分配分贝数量和抵扣金额
                BigDecimal itemRatio = item.getNeedPayAmount().divide(totalOrderAmount, 4, RoundingMode.HALF_UP);
                cutFenbeiNumber =
                    itemRatio.multiply(new BigDecimal(usableFenbei)).setScale(0, RoundingMode.DOWN).intValue();
                fenbeiDiscountMoney = itemRatio.multiply(totalFenbeiDeductionAmount).setScale(2, RoundingMode.HALF_UP);

                leftFenbeiCutNumber -= cutFenbeiNumber;
                leftFenbeiDiscountMoney = leftFenbeiDiscountMoney.subtract(fenbeiDiscountMoney);
            } else {
                // 最后一个订单项分配剩余的所有分贝和抵扣金额
                cutFenbeiNumber = leftFenbeiCutNumber;
                fenbeiDiscountMoney = leftFenbeiDiscountMoney;
            }

            if (modifyItems) {
                item.setFenbeiDeductionCount(cutFenbeiNumber);
                item.setFenbeiDeductionAmount(fenbeiDiscountMoney);
            }
        }

        return new FenbeiDeductionResult().setTotalFenbeiDeductionCount(usableFenbei)
            .setTotalFenbeiDeductionAmount(totalFenbeiDeductionAmount);
    }

    // 辅助方法：重置订单项并返回空结果
    private static FenbeiDeductionResult resetItemsAndReturnEmpty(List<GlobalOrderItem> orderItemList,
        boolean modifyItems) {
        if (modifyItems) {
            orderItemList.forEach(item -> {
                item.setFenbeiDeductionAmount(BigDecimal.ZERO);
                item.setFenbeiDeductionCount(0);
            });
        }
        return FenbeiDeductionResult.empty();
    }

    /**
     * 非会员分贝兑换计算
     * 
     * @param belongPrice 商品价格
     * @param fenbeiAccount 分贝账户余额
     * @return 可兑换的分贝数量
     */
    private static int calculateNonVipFenbeiExchange(Double belongPrice, int fenbeiAccount) {
        // 商品最多兑换
        double maxExchange = belongPrice * 0.002;
        // 商家账号分贝兑换的总金额
        double sumExchangeMoney = fenbeiAccount * 0.25;

        // 分贝抵扣的数量
        if (sumExchangeMoney >= maxExchange) {
            // 分贝足够抵扣全部0.2%
            return (int)Math.floor(maxExchange / 0.25);
        } else if (sumExchangeMoney > 0 && maxExchange > sumExchangeMoney) {
            // 分贝不够单品价格的0.2%
            return (int)Math.floor(sumExchangeMoney / 0.25);
        } else {
            return 0;
        }
    }

    /**
     * 均摊分配VIP分贝抵扣
     *
     * @param orderItemList 订单项列表
     * @param totalChannelAmount 总通道费用
     * @param totalFenbeiDeductCount VIP可抵扣的总分贝数
     * @param orgFenbeiBalance 用户当前分贝余额
     * @param modifyItems 是否修改原始订单项
     * @return 分贝抵扣结果，包含总抵扣分贝数和总抵扣金额
     */
    public static FenbeiDeductionResult distributeVipDeduction(List<GlobalOrderItem> orderItemList,
        BigDecimal totalChannelAmount, int totalFenbeiDeductCount, int orgFenbeiBalance, boolean modifyItems) {
        int useFenbeiNumber = 0; // 实际使用的分贝数
        BigDecimal totalFenbeiDeductionAmount = BigDecimal.ZERO; // 总分贝抵扣金额
        // 如果不修改原始订单项，创建一个副本
        List<GlobalOrderItem> workingList = modifyItems ? orderItemList : new ArrayList<>(orderItemList);

        // 如果分贝余额为0或通道费价格为0，则不使用分贝
        if (orgFenbeiBalance <= 0 || totalChannelAmount.compareTo(BigDecimal.ZERO) == 0) {
            workingList.forEach(item -> {
                if (modifyItems) {
                    item.setFenbeiDeductionAmount(BigDecimal.ZERO);
                    item.setFenbeiDeductionCount(0);
                }
            });
        } else {
            // 按通道费升序排列，确保小额订单项优先分配
            workingList.sort(Comparator.comparing(GlobalOrderItem::getChannelAmount));

            // 如果分贝余额大于等于VIP分贝抵扣数量，全额抵扣
            if (orgFenbeiBalance >= totalFenbeiDeductCount) {
                useFenbeiNumber = totalFenbeiDeductCount;
                int leftFenbeiCutNumber = totalFenbeiDeductCount;
                for (int i = 0; i < workingList.size(); i++) {
                    GlobalOrderItem item = workingList.get(i);
                    BigDecimal fenbeiDeductionAmount = item.getChannelAmount();
                    int cutFenbeiNumber;
                    if (i != workingList.size() - 1) {
                        // 按比例分配分贝数量
                        cutFenbeiNumber = item.getChannelAmount().divide(totalChannelAmount, 3, RoundingMode.FLOOR)
                            .multiply(new BigDecimal(useFenbeiNumber)).intValue();
                        leftFenbeiCutNumber -= cutFenbeiNumber;
                    } else {
                        // 最后一个订单项分配剩余的所有分贝
                        cutFenbeiNumber = leftFenbeiCutNumber;
                    }
                    if (modifyItems) {
                        item.setFenbeiDeductionAmount(fenbeiDeductionAmount);
                        item.setFenbeiDeductionCount(cutFenbeiNumber);
                    }
                    totalFenbeiDeductionAmount = totalFenbeiDeductionAmount.add(fenbeiDeductionAmount);
                }
            } else {
                // 分贝余额不足，按比例部分抵扣
                useFenbeiNumber = orgFenbeiBalance;
                BigDecimal totalFenbeiDiscountMoney = new BigDecimal(orgFenbeiBalance * 0.25); // 1分贝 = 0.25元
                int leftFenbeiCutNumber = orgFenbeiBalance;
                BigDecimal leftFenbeiDiscountMoney = totalFenbeiDiscountMoney;
                for (int i = 0; i < workingList.size(); i++) {
                    GlobalOrderItem item = workingList.get(i);
                    int cutFenbeiNumber;
                    BigDecimal fenbeiDiscountMoney;
                    if (i != workingList.size() - 1) {
                        // 按比例分配分贝数量和抵扣金额
                        cutFenbeiNumber = item.getChannelAmount().divide(totalChannelAmount, 3, RoundingMode.FLOOR)
                            .multiply(new BigDecimal(useFenbeiNumber)).intValue();
                        fenbeiDiscountMoney = item.getChannelAmount().divide(totalChannelAmount, 3, RoundingMode.FLOOR)
                            .multiply(totalFenbeiDiscountMoney).setScale(2, RoundingMode.FLOOR);
                        leftFenbeiCutNumber -= cutFenbeiNumber;
                        leftFenbeiDiscountMoney = leftFenbeiDiscountMoney.subtract(fenbeiDiscountMoney);
                    } else {
                        // 最后一个订单项分配剩余的所有分贝和抵扣金额
                        cutFenbeiNumber = leftFenbeiCutNumber;
                        fenbeiDiscountMoney = leftFenbeiDiscountMoney;
                    }
                    if (modifyItems) {
                        item.setFenbeiDeductionCount(cutFenbeiNumber);
                        item.setFenbeiDeductionAmount(fenbeiDiscountMoney);
                    }
                    totalFenbeiDeductionAmount = totalFenbeiDeductionAmount.add(fenbeiDiscountMoney);
                }
            }
        }
        // 返回总的分贝抵扣数量和总的分贝抵扣金额
        return new FenbeiDeductionResult().setTotalFenbeiDeductionCount(useFenbeiNumber)
            .setTotalFenbeiDeductionAmount(totalFenbeiDeductionAmount);
    }

    /**
     * 分贝抵扣结果类，用于封装分贝抵扣的总计结果
     */
    @Data
    @Accessors(chain = true)
    public static class FenbeiDeductionResult {

        // 总分贝抵扣数量
        private int totalFenbeiDeductionCount;
        // 总分贝抵扣金额
        private BigDecimal totalFenbeiDeductionAmount;

        // // 总年会抵扣数量
        // private int totalVipDeduction = 0;
        // // 总年会抵扣金额
        // private BigDecimal totalVipDeductionAmount;
        /**
         * 会员可抵扣的最大分贝数量
         */
        private Integer vipMaxFenbeiCount = 0;
        /**
         * 会员可抵扣的最大分贝金额
         */
        private BigDecimal vipMaxFenbeiAmount = BigDecimal.ZERO;

        public static FenbeiDeductionResult empty() {
            return new FenbeiDeductionResult().setTotalFenbeiDeductionCount(0)
                .setTotalFenbeiDeductionAmount(BigDecimal.ZERO);
        }

    }

    @Data
    public static class FenbeiGoodsDTO {

        /**
         * 商品的第三方价格
         */
        private BigDecimal thirdPrice;

        /**
         * 分贝折扣金额
         */
        private BigDecimal fenbeiDiscountMoney;

        /**
         * 分贝抵扣数量
         */
        private int fenbeiCutNumber;

    }
}
