package com.bbh.live.service.buyerCancel;

import com.bbh.enums.GlobalOrderTypeEnum;
import com.bbh.exception.ServiceException;
import com.bbh.live.service.buyerCancel.dto.BuyerCancelBiz;
import org.springframework.stereotype.Component;

import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @Date 2025/4/2
 * @description:
 */
@Component
public class BuyerCancelRecordServiceContext {

    private ConcurrentHashMap<GlobalOrderTypeEnum, BuyerCancelRecordService> buyerCancelRecordServiceMap = new ConcurrentHashMap<>();

    public BuyerCancelRecordServiceContext(BuyerCancelRecordServiceImpl buyerCancelRecordService, BuyerCancelRecordServiceSceImpl buyerCancelRecordServiceSceImpl) {
        buyerCancelRecordServiceMap.put(GlobalOrderTypeEnum.LIVE, buyerCancelRecordService);
        buyerCancelRecordServiceMap.put(GlobalOrderTypeEnum.SCE, buyerCancelRecordServiceSceImpl);
    }

    public BuyerCancelRecordService strategy(BuyerCancelBiz buyerCancelBiz) {
        if (buyerCancelBiz == null || buyerCancelBiz.getBizType() == null) {
            return buyerCancelRecordServiceMap.get(GlobalOrderTypeEnum.LIVE);
        }
        return Optional.of(buyerCancelRecordServiceMap.get(buyerCancelBiz.getBizType())).orElseThrow(() -> new ServiceException("业务类型不正确"));
    }


}
