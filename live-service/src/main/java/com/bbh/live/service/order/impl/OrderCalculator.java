package com.bbh.live.service.order.impl;

import cn.hutool.core.collection.CollUtil;
import com.bbh.live.constant.LiveGoodsConstant;
import com.bbh.live.dao.dto.RateTemplateItemDTO;
import com.bbh.live.dao.dto.livegoods.************;
import com.bbh.live.dao.mapper.GlobalOrderItemMapper;
import com.bbh.live.dao.mapper.LiveGoodsMapper;
import com.bbh.live.dao.service.LiveRateTemplateService;
import com.bbh.model.GlobalOrderItem;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Objects;
import java.util.function.Function;

/**
 * 订单计算器
 * <AUTHOR>
 */
@Slf4j
@AllArgsConstructor
@Service
public class OrderCalculator {

    private final LiveRateTemplateService liveRateTemplateService;
    private final GlobalOrderItemMapper globalOrderItemMapper;
    private final LiveGoodsMapper liveGoodsMapper;

    /**
     * 计算买手服务费
     * @param goodsPrice                   商品价格
     * @param liveRoomBuyerServiceRate     直播间配置的买手服务费率字段
     * @return 买手服务费
     */
    public BigDecimal computeBuyerServiceAmount(@NotNull BigDecimal goodsPrice , @NotNull BigDecimal liveRoomBuyerServiceRate) {
        return goodsPrice.multiply(liveRoomBuyerServiceRate).setScale(2, RoundingMode.HALF_UP);
    }

    /**
     * 计算通道费 <br>
     * 保留两位小数  <br>
     * 多余小数四舍五入  <br>
     * 不足1分记为0 <br>
     *
     * @param sellPrice 商品价格
     * @return 通道费
     */
    public BigDecimal computeChannelAmount(BigDecimal sellPrice) {
        return sellPrice.multiply(LiveGoodsConstant.CHANNEL_RATE).setScale(2, RoundingMode.HALF_UP);
    }

    /**
     * 计算通道费合计
     * @param sellPriceList 商品价格列表
     * @return 通道费
     */
    public BigDecimal computeTotalChannelAmount(List<BigDecimal> sellPriceList) {
        // 遍历sellPriceList，计算单个通道费并进行累加
        return sellPriceList.stream().map(this::computeChannelAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /**
     * 匹配卖家服务费率
     *
     * @param rateTemplateItemList 费率单元列表
     * @param payAmount                商品金额
     * @param oneClassifyId        一级分类ID
     * @return 费率
     */
    public BigDecimal matchSellerServiceRate(List<RateTemplateItemDTO> rateTemplateItemList, BigDecimal payAmount, Integer oneClassifyId) {
        if (CollUtil.isEmpty(rateTemplateItemList)) {
            return BigDecimal.ZERO;
        }
        for (RateTemplateItemDTO rateTemplateItem : rateTemplateItemList) {
            // 满足最小值～最大值，且匹配到一级分类ID
            if (Objects.equals(oneClassifyId, rateTemplateItem.getOneClassifyId())
                    && payAmount.compareTo(new BigDecimal(rateTemplateItem.getMinValue())) >= 0
                    && payAmount.compareTo(new BigDecimal(rateTemplateItem.getMaxValue())) < 0) {
                return rateTemplateItem.getRate();
            }
        }
        // 没匹配到就直接零
        return BigDecimal.ZERO;
    }

    /**
     * 计算卖家服务费
     * @param goodsPrice        商品售价
     * @param rate              费率
     * @return  卖家服务费
     */
    public BigDecimal computeSellerServiceAmount(BigDecimal goodsPrice, BigDecimal rate) {
        // 计算卖家服务费，保留2位，四舍五入
        return goodsPrice.multiply(rate).setScale(2, RoundingMode.HALF_UP);
    }

    /**
     * 匹配商品分类ID
     * @param orderItem                 订单商品
     * @param goodsListWithClassify     关联查询了一级分类的商品列表
     * @return  一级分类ID
     */
    public Integer matchClassifyId(GlobalOrderItem orderItem, List<************> goodsListWithClassify, Function<************, Integer> classifyIdGetter) {
        return goodsListWithClassify.stream()
                .filter(goods -> goods.getId().equals(orderItem.getBizGoodsId()))
                .findFirst()
                .map(classifyIdGetter)
                .orElse(null);
    }

    /**
     * 匹配到SPU对应的分类ID
     * @param orderItem                 订单商品
     * @param goodsListWithClassify     关联查询了一级分类的商品列表
     * @return   一级分类ID
     */
    public Integer matchSpuClassifyId(GlobalOrderItem orderItem, List<************> goodsListWithClassify) {
        return matchClassifyId(orderItem, goodsListWithClassify, ************::getOneClassifyId);
    }

    /**
     * 匹配到平台对应的分类ID
     * @param orderItem                 订单商品
     * @param goodsListWithClassify     关联查询了一级分类的商品列表
     * @return   一级分类ID
     */
    public Integer matchPlatformClassifyId(GlobalOrderItem orderItem, List<************> goodsListWithClassify) {
        return matchClassifyId(orderItem, goodsListWithClassify, ************::getPlatformClassifyId);
    }

}
