package com.bbh.live.service.buyer.orgmap.geo;

/**
 * <AUTHOR>
 */

public enum GeoUnit {

    /**
     * 米
     */
    METERS {
        @Override
        public double covert(double value) {
            return value;
        }

        @Override
        public String toString() {
            return "M";
        }
    },

    /**
     * 千米
     */
    KILOMETERS {
        @Override
        public double covert(double value) {
            return value / 1000;
        }

        @Override
        public String toString() {
            return "KM";
        }
    },

    /**
     * 英里
     */
    MILES {
        @Override
        public double covert(double value) {
            return value / 1000 * 0.621371;
        }

        @Override
        public String toString() {
            return "MI";
        }
    },
    ;

    public double covert(double value) {
        return 0;
    }
}