package com.bbh.live.service.room.consumer;

import cn.hutool.core.util.CharsetUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.bbh.exception.ServiceException;
import com.bbh.util.LogExUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;

/**
 * 直播间的消费者
 * <AUTHOR>
 */
public interface IRoomConsumer {

    /**
     * 从消息体中解析出直播间ID
     * @param message 消息体
     * @return  直播间ID
     */
    default Long analyzeLiveRoomId(Message message) throws IllegalArgumentException {
        String json = StrUtil.str(message.getBody(), CharsetUtil.UTF_8);
        return analyzeLiveRoomId(json);
    }

    /**
     * 从JSON字符串中解析出直播间ID
     * 这个方法会尝试将输入的字符串解析为JSON对象，然后提取 liveRoomId 字段
     *
     * @param body JSON格式的字符串
     * @return 解析出的直播间ID
     * @throws IllegalArgumentException 如果JSON解析失败或缺少 liveRoomId 字段
     */
    default Long analyzeLiveRoomId(String body) throws IllegalArgumentException {
        JSONObject json = null;
        try {
            json = JSONUtil.parseObj(body);
        } catch (Exception e) {
            throw new IllegalArgumentException("非法的消息内容: " + body);
        }
        Long liveRoomId = json.getLong("liveRoomId");
        if (liveRoomId == null) {
            throw new IllegalArgumentException("非法的消息内容，缺少直播间ID: " + body);
        }
        return liveRoomId;
    }

}
