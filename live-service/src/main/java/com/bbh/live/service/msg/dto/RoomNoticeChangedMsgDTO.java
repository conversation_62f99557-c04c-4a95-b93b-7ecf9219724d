package com.bbh.live.service.msg.dto;

import com.bbh.live.core.msg.MsgType;
import com.bbh.live.service.msg.dto.base.BaseMsg;
import com.bbh.live.service.msg.dto.base.IMsg;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 直播间公告变更的通知
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
public class RoomNoticeChangedMsgDTO extends BaseMsg implements IMsg {
    /**
     * 每个消息对应一个类型
     *
     * @return {@link MsgType}
     */
    @Override
    public String type() {
        return MsgType.CHANGE_NOTICE;
    }

    private Long liveRoomId;

    /**
     * 新的公告
     */
    private String content;
}
