package com.bbh.live.service.livegoods.cache;

import com.bbh.live.service.livegoods.cache.info.AuctionResult;
import com.bbh.live.service.livegoods.cache.info.LiveGoodsAuctionBidInfo;
import com.bbh.live.service.livegoods.cache.info.LiveGoodsAuctionInfo;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> dsy
 * @Date: 2024/7/27
 * @Description:
 */
public interface LiveGoodsAuctionCacheService {

    /**
     * 添加竞拍商品信息 当前价格，出价人，竞拍结束时间等信息
     *
     * @param liveRoomId
     * @param context
     *
     * @return boolean 更新成功
     */
    boolean insertAuctionLiveGoods(Long liveRoomId, LiveGoodsAuctionInfo context);

    /**
     * 返回竞拍中的商品 当前价格，出价人，竞拍结束时间等信息
     * @param liveRoomId
     * @param liveGoodsId
     * @return
     */
    LiveGoodsAuctionInfo getAuctionLiveGoods(Long liveRoomId, Long liveGoodsId);

    /**
     * 返回竞拍中的商品 当前价格，出价人，竞拍结束时间等信息,如果商品已经过期，则清除缓存，返回null
     * @param liveRoomId
     * @param liveGoodsId
     * @return
     */
    LiveGoodsAuctionInfo getAuctionLiveGoodsBeforeExpire(Long liveRoomId, Long liveGoodsId);

    /**
     * 移除竞拍中商品缓存信息
     * @param liveRoomId
     * @param liveGoodsId
     * @return
     */
    void removeAuctionLiveGoods(Long liveRoomId, Long liveGoodsId);

    /**
     * 清理所有竞拍商品缓存，一般只有一个
     * @param liveRoomId
     */
    void clearAll(Long liveRoomId);

    /**
     * 获取竞拍中商品结束时间
     * @param liveRoomId
     * @param liveGoodsId
     * @return
     */
    Date getAuctionGoodsEndTime(Long liveRoomId, Long liveGoodsId);

    /**
     * 竞拍商品当前价格
     * @param liveRoomId
     * @param liveGoodsId
     * @return
     */
    BigDecimal getAuctionGoodsCurrentPrice(Long liveRoomId, Long liveGoodsId);

    /**
     * 设置竞拍商品结算标记
     * @param liveRoomId
     * @param liveGoodsId
     * @return
     */
    Boolean setAuctionGoodsBeginSettle(Long liveRoomId, Long liveGoodsId);

    /**
     * 设置竞拍商品结算标记
     * @param liveRoomId
     * @param liveGoodsId
     * @return
     */
    Boolean resetAuctionGoodsSettle(Long liveRoomId, Long liveGoodsId);

    /**
     *  设置竞拍商品撤回标记
     * @param liveRoomId
     * @param liveGoodsId
     * @return
     */
    Boolean setAuctionGoodsBeginRevoke(Long liveRoomId, Long liveGoodsId);

    /**
     * 竞拍商品：
     *      更新当前价格。如果需要延长时间
     * 一口价商品：
     *      更新第一个出价人
     * @param liveRoomId
     * @param pre 之前竞拍信息
     * @param cur 当前竞拍信息
     * @return AuctionResult
     */
    AuctionResult auctionBid(Long liveRoomId, LiveGoodsAuctionInfo pre, LiveGoodsAuctionInfo cur);

    /**
     * 竞拍商品出过价的席位ID列表
     *
     * @param liveRoomId  直播间id
     * @param liveGoodsId 商品ID
     * @return 席位ID列表
     */
    List<LiveGoodsAuctionBidInfo> getAuctionGoodsBidInfo(Long liveRoomId, Long liveGoodsId);

    /**
     * 插入竞拍商品中出价人的席位
     *
     * @param liveRoomId  直播间id
     * @param liveGoodsId 商品ID
     * @param seatId      席位ID
     */
    boolean insertLiveGoodsBidInfo(Long liveRoomId, Long liveGoodsId, Long seatId, BigDecimal bidAmount);
}
