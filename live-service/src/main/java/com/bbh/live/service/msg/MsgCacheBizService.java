package com.bbh.live.service.msg;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import com.bbh.live.constant.RedisKey;
import com.bbh.live.service.msg.dto.GoodsSubscribeMsgDTO;
import com.bbh.live.service.msg.dto.GoodsTransferMsgDTO;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 消息缓存
 *
 * <AUTHOR>
 */
@Service
@AllArgsConstructor
@Slf4j
public class MsgCacheBizService implements RedisKey {

    private final MsgCacheService msgCacheService;

    /**
     * 获取直播间内所有商品传送消息
     * @param liveRoomId    直播间ID
     * @return  商品传送消息列表
     */
    public List<GoodsTransferMsgDTO> getGoodsTransferMsgListForDirector(Long liveRoomId) {
        return msgCacheService.getActiveMessages(GoodsTransferMsgDTO.buildDirectorKey(liveRoomId), GoodsTransferMsgDTO.class, true).stream().peek(x -> {
            // 剩余时间=过期时间-当前时间，单位秒
            x.setRemainTime((int)DateUtil.between(x.getExpiredAt(), DateUtil.date(), DateUnit.SECOND));
        }).collect(Collectors.toList());
    }

    /**
     * 获取买手的所有商品传送消息
     * @param buyerSeatId    买手席位ID
     * @return  商品传送消息列表
     */
    public List<GoodsTransferMsgDTO> getGoodsTransferMsgListForBuyer(Long buyerSeatId) {
        return msgCacheService.getActiveMessages(GoodsTransferMsgDTO.buildBuyerKey(buyerSeatId), GoodsTransferMsgDTO.class, true)
                .stream()
                .peek(x -> {
                    // 剩余时间=过期时间-当前时间，单位秒
                    x.setRemainTime((int)DateUtil.between(x.getExpiredAt(), DateUtil.date(), DateUnit.SECOND));
                })
                // 正序排，过期时间晚代表是新发的，新发的在后面
                .sorted(Comparator.comparing(GoodsTransferMsgDTO::getExpiredAt)).collect(Collectors.toList());
    }

    /**
     * 获取商品传送消息
     * @param liveRoomId    直播间ID (可为null)
     * @param buyerSeatId   买手席位ID (可为null)
     * @param liveGoodsId   商品ID
     * @return  商品传送消息
     */
    public GoodsTransferMsgDTO getGoodsTransferMsg(Long liveRoomId, Long buyerSeatId, Long liveGoodsId) {
        List<String> keys = new ArrayList<>();
        if (liveRoomId != null) {
            keys.add(GoodsTransferMsgDTO.buildDirectorKey(liveRoomId));
        }
        if (buyerSeatId != null) {
            keys.add(GoodsTransferMsgDTO.buildBuyerKey(buyerSeatId));
        }

        // 这里会自动清理过期的消息
        List<GoodsTransferMsgDTO> transferMsgList = msgCacheService.getActiveMessagesForMultiKey(keys, GoodsTransferMsgDTO.class);
        if (CollUtil.isEmpty(transferMsgList)) {
            return null;
        }
        // 因为两条消息完全一致，因此随便取第一条即可
        return transferMsgList.stream().filter(x -> {
            return Objects.nonNull(x)
                    && Objects.equals(x.getLiveRoomId(), liveRoomId)
                    && Objects.nonNull(x.getGoods())
                    && Objects.equals(x.getGoods().getLiveGoodsId(), liveGoodsId);
        }).findFirst().orElse(null);
    }

    /**
     * 移除商品传送消息
     * @param liveRoomId    直播间ID (可为null)
     * @param buyerSeatId   买手席位ID (可为null)
     * @param liveGoodsId   商品ID
     */
    public void removeGoodsTransferMsg(Long liveRoomId, Long buyerSeatId, Long liveGoodsId) {
        GoodsTransferMsgDTO goodsTransferMsg = getGoodsTransferMsg(liveRoomId, buyerSeatId, liveGoodsId);
        if (Objects.isNull(goodsTransferMsg)) {
            return;
        }

        String value = goodsTransferMsg.getExpiredAt().getTime() + "";

        // 移除导播的消息
        if (liveRoomId != null) {
            String directorKey = GoodsTransferMsgDTO.buildDirectorKey(liveRoomId);
            msgCacheService.remove(directorKey, value);
        }

        // 移除买手的消息
        if (buyerSeatId != null) {
            String buyerKey = GoodsTransferMsgDTO.buildBuyerKey(buyerSeatId);
            msgCacheService.remove(buyerKey, value);
        }
    }

    /**
     * 获取买手的商品预约消息
     * @param buyerSeatId   买手席位id
     * @return  商品预约消息列表
     */
    public List<GoodsSubscribeMsgDTO> getGoodsSubscribeMsgList(Long buyerSeatId) {
        //return msgCacheService.getActiveMessages(GoodsSubscribeMsgDTO.buildUniqueKey(buyerSeatId), GoodsSubscribeMsgDTO.class, false);
        return null;
    }

    /**
     * 删除用户的商品订阅消息
     * @param buyerSeatId   买手席位id
     * @param liveGoodsId   商品id
     */
    public void removeGoodsSubscribeMsg(Long buyerSeatId, Long liveGoodsId) {
        /*List<GoodsSubscribeMsgDTO> messages = getGoodsSubscribeMsgList(buyerSeatId);
        if (CollUtil.isEmpty(messages)) {
            return;
        }
        messages.stream().filter(x -> Objects.equals(x.getGoods().getLiveGoodsId(), liveGoodsId)).forEach(x -> {
            msgCacheService.remove(GoodsSubscribeMsgDTO.buildUniqueKey(buyerSeatId), buyerSeatId.toString());
        });*/
    }

}
