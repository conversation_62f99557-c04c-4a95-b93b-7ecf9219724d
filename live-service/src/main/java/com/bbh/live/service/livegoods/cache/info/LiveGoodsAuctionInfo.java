package com.bbh.live.service.livegoods.cache.info;

import com.bbh.enums.LiveGoodsTradeTypeEnum;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR> dsy
 * @Date: 2024/7/26
 * @Description:
 */
@Data
@Accessors(chain = true)
public class LiveGoodsAuctionInfo {

    private Long liveGoodsId;

    private BigDecimal currentPrice;

    private BigDecimal increasePrice;

    /**
     * 竞拍结束时间
     */
    private Date auctionEndTime;

    /**
     * 当前最高价席位
     */
    private Long buyerSeatId;

    /**
     * 是否正在结算
     */
    private Boolean ifSettle;

    /**
     * 是否正在撤回商品
     */
    private Boolean ifRevoke;

    /**
     * 贸易方式
     */
    private LiveGoodsTradeTypeEnum tradeType;
}
