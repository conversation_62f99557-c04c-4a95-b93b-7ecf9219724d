package com.bbh.live.service.buyer.vip;

import com.bbh.base.ListBase;
import com.bbh.live.controller.req.VipRightsUseRecordsReq;
import com.bbh.live.service.buyer.vip.vo.VipBuyerWholeTimeUsedVO;

/**
 * <AUTHOR>
 * @date 2024/9/21 16:08
 * @description
 */
public interface BuyerVipQueryService {

    /**
     * 获取买手vip整个时间过程使用累计情况
     * @param vipId
     * @return
     */
    VipBuyerWholeTimeUsedVO getWholeTimeUsedInfo(Long vipId);


    /**
     * VIP权益使用记录
     * @param recordsReq
     * @return 权益使用记录
     */
    ListBase<?> getVipRightsUsedRecords(VipRightsUseRecordsReq recordsReq);
}
