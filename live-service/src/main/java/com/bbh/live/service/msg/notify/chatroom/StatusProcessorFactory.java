package com.bbh.live.service.msg.notify.chatroom;

import com.bbh.exception.ServiceException;
import com.bbh.live.service.msg.notify.chatroom.enums.ChatroomStatus;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 状态处理工厂
 * <AUTHOR>
 */
@Slf4j
@Service
@AllArgsConstructor
public class StatusProcessorFactory {

    private final List<StatusProcessor> processors;

    public StatusProcessor getProcessor(int status) {
        ChatroomStatus chatroomStatus = ChatroomStatus.fromValue(status);
        return processors.stream()
                .filter(p -> p.canHandle(chatroomStatus))
                .findFirst()
                .orElseThrow(() -> new ServiceException("不支持的状态: " + status));
    }

}
