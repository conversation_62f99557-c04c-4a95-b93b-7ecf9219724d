package com.bbh.live.service.msg.dto.base;

import com.bbh.live.service.msg.MsgCacheService;
import org.springframework.data.redis.core.StringRedisTemplate;

/**
 * 消息存储处理接口
 * <AUTHOR>
 */
public interface IMsgStoreProcess {

    /**
     * 前置函数，如果返回true则继续执行，否则直接中断
     * @return true 继续
     */
    default boolean preStore(StringRedisTemplate redisTemplate, MsgCacheService msgCacheService) {
        return true;
    }

    /**
     * 后置函数，消息存储完成后执行
     */
    default void postStore(StringRedisTemplate redisTemplate, MsgCacheService msgCacheService) {}

}
