package com.bbh.live.service.buyer;

import com.bbh.base.ListBase;
import com.bbh.live.controller.req.LiveGoodsQueryReq;
import com.bbh.live.dao.dto.AuctionFailGoodsListDTO;
import com.bbh.live.dao.dto.BuyerLiveGoodsDTO;
import com.bbh.live.dao.dto.livegoods.LiveGoodsDTO;
import com.bbh.live.dao.dto.vo.BuyerLiveGoodsStatisticsDTO;

/**
 * <AUTHOR> dsy
 * @Date: 2024/7/30
 * @Description: 买手端直播商品查询服务
 */
public interface BuyerLiveGoodsQueryService {

    /**
     * 商品详细信息
     *
     * @param liveGoodsId
     * @return BuyerLiveGoodsDTO
     */
    BuyerLiveGoodsDTO getLiveGoodsDetailInfo(Long liveGoodsId);

    /**
     * 校验商品是否有效
     *
     * @param liveGoodsId
     * @return BuyerLiveGoodsDTO
     */
    void checkLiveGoodsValid(Long liveGoodsId);


    /**
     * 商品列表
     *
     * @param queryParam
     * @return
     */
    ListBase<BuyerLiveGoodsDTO> getLiveGoodsList(LiveGoodsQueryReq queryParam);

    /**
     * 商品列表数量统计
     * 待拍商品 = 竞拍商品 + 上架讲解商品 + 待上架商品  （过滤失效商品）
     * 已成交  （不过滤失效商品，无法判断erp状态，可能是erp操作的，也可能是直播成交的，所以只要是成交的就不许失效）
     * 流拍商品 （过滤失效商品）
     * @param queryParam
     * @return
     */
    BuyerLiveGoodsStatisticsDTO getLiveGoodsCount(LiveGoodsQueryReq queryParam);

    /**
     * 再见列表 流拍商品信息
     * @return
     */
    AuctionFailGoodsListDTO getAbortiveAuctionGoodsList();

    /**
     * 捡漏列表 v2
     * @return
     */
    AuctionFailGoodsListDTO getAbortiveAuctionGoodsListV2();

    /**
     * 获取有流拍商品的直播间数量
     *
     * @return Long 有流拍商品的直播间数量
     *         如果没有符合条件的直播间，返回0
     */
    Long getAbortiveAuctionRoomCount();

    /**
     * 获取已选商品信息
     * @param globalGoodsId
     * @return
     */
    LiveGoodsDTO getCheckedGoodsInfo(Long globalGoodsId);
}
