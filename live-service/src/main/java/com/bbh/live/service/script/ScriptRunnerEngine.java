package com.bbh.live.service.script;

import com.bbh.live.controller.req.RunScriptReq;
import groovy.lang.Binding;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024/12/3 14:26
 * @description
 */
@Component
public class ScriptRunnerEngine implements IScriptRunnerEngine, ApplicationContextAware {

    private ApplicationContext applicationContext;

    @Override
    public Object runScript(RunScriptReq runScriptReq) {

        switch (runScriptReq.getType()) {
            case "groovy" :
                Binding binding = new BafBinding(applicationContext);
                ClassLoader parentClassLoader = ScriptRunnerEngine.class.getClassLoader();
                return GroovyEvalUtil.groovyExec(runScriptReq.getSrcCode(), binding, parentClassLoader);
            case "sql" :
                return null;
        }
        return null;
    }

    @Override
    public void setApplicationContext(@NotNull ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }
}
