package com.bbh.live.service.warehouse.impl;

import com.bbh.base.ListBase;
import com.bbh.exception.ServiceException;
import com.bbh.feign.IErpApiClient;
import com.bbh.feign.dto.WarehouseGoodsQueryDTO;
import com.bbh.feign.vo.StorehouseVO;
import com.bbh.feign.vo.WarehouseGoodsVO;
import com.bbh.live.service.warehouse.WarehouseService;
import com.bbh.vo.Result;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 仓库
 * <AUTHOR>
 */
@Service
@Slf4j
@AllArgsConstructor
public class WarehouseServiceImpl implements WarehouseService {

    private final IErpApiClient erpApiClient;
    private final ObjectMapper objectMapper;

    @Override
    public ListBase<StorehouseVO> getStorehouseList() {
        Result result = erpApiClient.getWarehouseStorehousePage(1, 1, 9999);
        List<StorehouseVO> data = getData(result, StorehouseVO.class);
        return new ListBase<>(data, (long) data.size(), null, null);
    }

    @Override
    public ListBase<WarehouseGoodsVO> getWarehouseGoodsList(WarehouseGoodsQueryDTO goodsQueryDTO) {
        // 剔除已售出、已锁单的货品
        goodsQueryDTO.setIf_locked(0);
        goodsQueryDTO.setSale_status(1);
        Result result = erpApiClient.getWarehouseStorehouseGoodsPage(goodsQueryDTO);
        List<WarehouseGoodsVO> dataList = getData(result, WarehouseGoodsVO.class);
        dataList.forEach(data -> data.setCostPrice(null));
        return new ListBase<>(dataList, (long) dataList.size(), null, null);
    }

    @SuppressWarnings("all")
    private <T> List<T> getData(Result result, Class<T> clazz) {
        if(result.getCode() != 200){
            log.error("获取仓库/商品失败: {}", result);
            throw new ServiceException("获取仓库/商品失败");
        }
        List<T> dataList = new ArrayList<>();
        if(result.getData() == null || result.getData() instanceof List<?>){
            return dataList;
        }
        Map<String, List<Map<String, Object>>> map = (Map) result.getData();
        List<Map<String, Object>> records = map.get("records");
        records.forEach(x -> {
            T data = objectMapper.convertValue(x, clazz);
            dataList.add(data);
        });
        return dataList;
    }
}
