package com.bbh.live.service.livegoods.transfer;

import cn.hutool.extra.spring.SpringUtil;
import com.bbh.live.dao.dto.livegoods.LiveGoodsDTO;
import com.bbh.live.service.livegoods.context.LiveGoodsContext;
import com.bbh.live.statemachine.event.LiveGoodsEvent;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> dsy
 * @Date: 2024/7/25
 * @Description:
 */
@Component
public class LiveGoodsTransferManager implements InitializingBean {

    private static final Map<LiveGoodsEvent, LiveGoodsTransfer<LiveGoodsContext>> LIVE_GOODS_TRANSFER_MAP = new HashMap<>();

    /**
     * 商品状态流转前置校验
     * @param event
     * @param goods
     * @param context
     */
    public void check(LiveGoodsEvent event, LiveGoodsDTO goods, Object context){
        LiveGoodsTransferManager.LIVE_GOODS_TRANSFER_MAP.get(event).check(goods, (LiveGoodsContext) context);
    }

    /**
     * 商品状态流转过程中需要的处理逻辑
     * @param event
     * @param goods
     * @param context
     */
    public void action(LiveGoodsEvent event, LiveGoodsDTO goods, Object context){
        LiveGoodsTransferManager.LIVE_GOODS_TRANSFER_MAP.get(event).action(goods, (LiveGoodsContext) context);
    }

    @Override
    @SuppressWarnings({"unchecked"})
    public void afterPropertiesSet() throws Exception {
        /** 完善商品信息 */
        LIVE_GOODS_TRANSFER_MAP.put(LiveGoodsEvent.COMPLETE, SpringUtil.getBean("liveGoodsCompleteTransfer", LiveGoodsTransfer.class));
        /** 商品上架 */
        LIVE_GOODS_TRANSFER_MAP.put(LiveGoodsEvent.PUT_AWAY, SpringUtil.getBean("liveGoodsPutAwayTransfer", LiveGoodsTransfer.class));
        /** 商品竞拍 */
        LIVE_GOODS_TRANSFER_MAP.put(LiveGoodsEvent.AUCTION, SpringUtil.getBean("liveGoodsAuctionTransfer", LiveGoodsTransfer.class));
        /** 商品竞拍成功或者议价传送成功 */
        LIVE_GOODS_TRANSFER_MAP.put(LiveGoodsEvent.AUCTION_SUCCESS, SpringUtil.getBean("liveGoodsTradeTransfer", LiveGoodsTransfer.class));
        LIVE_GOODS_TRANSFER_MAP.put(LiveGoodsEvent.TRANSFER_OR_BID_SUCCESS, SpringUtil.getBean("liveGoodsTradeTransfer", LiveGoodsTransfer.class));
        /** 商品流拍 */
        LIVE_GOODS_TRANSFER_MAP.put(LiveGoodsEvent.AUCTION_FAIL, SpringUtil.getBean("liveGoodsAuctionFailTransfer", LiveGoodsTransfer.class));
        /** 商品重新上架 */
        LIVE_GOODS_TRANSFER_MAP.put(LiveGoodsEvent.RETURN_WAIT_PUT_AWAY, SpringUtil.getBean("liveGoodsReShelveTransfer", LiveGoodsTransfer.class));
    }

    /**
     * 商品状态流转监听器，可添加自己感谢的事件
     */
    public interface Listener {
        /**
         * 商品上架监听
         * 待上架 -> 上架
         * @param goods
         */
        void waitPutAwayTransition(LiveGoodsDTO goods);

        /**
         * 上架商品结束监听
         *   上架 -> 传送成交，撤回
         * @param goods
         */
        void putAwayTransitionEnd(LiveGoodsDTO goods);

        /**
         * 竞拍结束监听
         *   竞拍 -> 流拍，成交，撤回
         * @param goods
         */
        void auctionTransitionEnd(LiveGoodsDTO goods);

        /**
         * 重新上架监听
         *    上架 -> 撤回 -> 待上架
         *    竞拍中 -> 撤回 -> 待上架
         *    流拍 -> 待上架
         * @param liveGoods
         */
        void reShelveTransition(LiveGoodsDTO liveGoods);
    }
}
