package com.bbh.live.service.msg.notify.chatroom.impl;

import com.bbh.live.dao.dto.ChatroomStatusSyncDTO;
import com.bbh.live.service.msg.notify.chatroom.Status0SubProcessor;
import com.bbh.live.service.msg.notify.chatroom.Status0SubProcessorFactory;
import com.bbh.live.service.msg.notify.chatroom.StatusProcessor;
import com.bbh.live.service.msg.notify.chatroom.enums.ChatroomStatus;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 主动触发的动作
 * <AUTHOR>
 */
@Slf4j
@Service
@AllArgsConstructor
public class ActiveCallProcessor implements StatusProcessor {

    private final Status0SubProcessorFactory status0SubProcessorFactory;

    @Override
    public void process(ChatroomStatusSyncDTO dto) {
        var liveRoomId = dto.parseLiveRoomId();
        if (liveRoomId == null) {
            log.info("融云回调，未解析到直播间ID: {}", dto.getChatRoomId());
            return;
        }
        Status0SubProcessor processor = status0SubProcessorFactory.getProcessor(dto);
        if(processor != null){
            processor.process(dto, liveRoomId);
        }
    }

    @Override
    public boolean canHandle(ChatroomStatus status) {
        return status == ChatroomStatus.ACTIVE_CALL || status == ChatroomStatus.AUTO_KICKED_OUT;
    }

}
