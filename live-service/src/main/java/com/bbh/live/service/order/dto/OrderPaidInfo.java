package com.bbh.live.service.order.dto;

import cn.hutool.core.date.DateUtil;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR> dsy
 * @Date: 2024/9/3
 * @Description:
 */
@Setter
public class OrderPaidInfo {

    /**
     *  交易状态 S-成功，F-失败，U-处理中
     */
    @Getter
    private String status;

    /**
     *  交易金额
     */
    private String transAmt;

    /**
     * 支付时间字符串
     */
    private String txnTime;

    /**
     * 支付时间
     */
    private Date payAt;

    /**
     * 实际支付的支付表id，在收到支付回调后写入
     */
    @Getter
    @Setter
    private Long lastPaymentId;

    public boolean isSuccess() {
        return "S".equals(status);
    }

    public BigDecimal getTransAmt() {
        return new BigDecimal(transAmt);
    }

    public Date getPayAt() {
        if(payAt != null){
            return payAt;
        }
        try{
            payAt = DateUtil.parse(txnTime);
        }catch (Exception ignore){}

        if(payAt == null){
            try {
                payAt = new Date(txnTime);
            }catch (Exception ignore){}
        }

        if(payAt == null){
            payAt = new Date();
        }
        return payAt;
    }
}
