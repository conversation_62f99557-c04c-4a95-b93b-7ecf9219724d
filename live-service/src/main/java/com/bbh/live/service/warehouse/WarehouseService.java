package com.bbh.live.service.warehouse;

import com.bbh.base.ListBase;
import com.bbh.feign.dto.WarehouseGoodsQueryDTO;
import com.bbh.feign.vo.StorehouseVO;
import com.bbh.feign.vo.WarehouseGoodsVO;


/**
 * 仓库
 * <AUTHOR>
 */
public interface WarehouseService {

    /**
     * 查询仓库列表
     * @return
     */
    ListBase<StorehouseVO> getStorehouseList();

    /**
     * 查询仓库商品列表
     * @param goodsQueryDTO
     * @return
     */
    ListBase<WarehouseGoodsVO> getWarehouseGoodsList(WarehouseGoodsQueryDTO goodsQueryDTO);
}
