package com.bbh.live.service.msg.notify.chatroom.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 聊天室主动动作类型
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum ChatroomActionType {
    CREATE(0),
    JOIN(1),
    LEAVE(2),
    DESTROY(3);

    private final int value;

    public static ChatroomActionType fromValue(int value) {
        for (ChatroomActionType type : values()) {
            if (type.getValue() == value) {
                return type;
            }
        }
        throw new IllegalArgumentException("不支持的类型: " + value);
    }
}