package com.bbh.live.service.lottery.impl;

import com.bbh.enums.GlobalLotteryPrizePoolTypeEnum;
import com.bbh.live.dao.dto.LotteryPrizeItemDTO;
import com.bbh.live.service.lottery.strategy.PrizeProcessStrategy;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 中奖结果管理器
 * 负责管理和调用不同类型奖品的处理策略
 * <AUTHOR>
 */

@Service
@AllArgsConstructor
@Slf4j
public class PrizeResultManager {

    /** 所有可用的奖品处理策略列表 */
    private final List<PrizeProcessStrategy> prizeProcessStrategies;
    /** 已加载的奖品类型到处理策略的映射，使用懒加载方式 */
    private final Map<GlobalLotteryPrizePoolTypeEnum, PrizeProcessStrategy> strategyMap = new ConcurrentHashMap<>();

    /**
     * 获取指定奖品类型的处理策略
     * 如果策略尚未加载，则进行懒加载
     *
     * @param prizeType 奖品类型
     * @return 对应的奖品处理策略
     */
    private PrizeProcessStrategy getStrategy(GlobalLotteryPrizePoolTypeEnum prizeType) {
        return strategyMap.computeIfAbsent(prizeType, this::findStrategy);
    }

    /**
     * 查找指定奖品类型的处理策略
     *
     * @param prizeType 奖品类型
     * @return 匹配的奖品处理策略
     * @throws IllegalArgumentException 如果未找到匹配的策略
     */
    private PrizeProcessStrategy findStrategy(GlobalLotteryPrizePoolTypeEnum prizeType) {
        return prizeProcessStrategies.stream()
                .filter(strategy -> strategy.match(prizeType))
                .findFirst()
                .orElseThrow(() -> new IllegalArgumentException("未找到奖品类型的处理策略: " + prizeType));
    }

    /**
     * 处理奖品结果
     *
     * @param userId 用户ID
     * @param seatId 座位ID
     * @param prizeItem 奖品信息
     */
    public void processPrizeResult(Long userId, Long seatId, LotteryPrizeItemDTO prizeItem) {
        GlobalLotteryPrizePoolTypeEnum prizeType = prizeItem.getType();
        PrizeProcessStrategy strategy = getStrategy(prizeType);

        try {
            // 调用相应的策略处理奖品
            strategy.processPrize(userId, seatId, prizeItem);
            log.info("成功处理用户 {} 在席位 {} 的奖品: {}", userId, seatId, prizeItem);
        } catch (Exception e) {
            log.error("处理用户 {} 在席位 {} 的奖品 {} 时发生错误", userId, seatId, prizeItem, e);
        }
    }

    /**
     * 获取所有已加载的奖品类型
     * 主要用于调试和监控目的
     *
     * @return 已加载的奖品类型集合
     */
    public Set<GlobalLotteryPrizePoolTypeEnum> getLoadedStrategyTypes() {
        return new HashSet<>(strategyMap.keySet());
    }

}
