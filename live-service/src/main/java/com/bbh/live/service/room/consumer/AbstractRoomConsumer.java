package com.bbh.live.service.room.consumer;

import cn.hutool.core.util.CharsetUtil;
import cn.hutool.core.util.StrUtil;
import com.bbh.live.config.LiveServiceProperties;
import com.bbh.live.dao.mapper.LiveRoomMapper;
import com.bbh.model.LiveRoom;
import com.bbh.service.mq.service.CoreMqListener;
import com.bbh.util.LogExUtil;
import com.rabbitmq.client.Channel;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.retry.RetryException;

import java.io.IOException;

/**
 * 直播间消费者的抽象基类
 * <AUTHOR>
 */
@Slf4j
public abstract class AbstractRoomConsumer implements CoreMqListener, IRoomConsumer {

    @Resource
    protected LiveRoomMapper liveRoomMapper;
    @Resource
    protected LiveServiceProperties liveServiceProperties;

    @Override
    public void handle(Message message, Channel channel) {
        String body = StrUtil.str(message.getBody(), CharsetUtil.UTF_8);
        LogExUtil.infoLog("=============> " + getConsumerName() + ": " + body + " <=============");

        long deliveryTag = message.getMessageProperties().getDeliveryTag();

        try {
            long liveRoomId = analyzeLiveRoomId(body);
            LiveRoom liveRoom = liveRoomMapper.selectById(liveRoomId);

            if (liveRoom == null) {
                LogExUtil.warnLog(getConsumerName() + "出错，直播间不存在:" + body + " ");
                // 如果直播间不存在，我们仍然确认消息，因为重试也不会解决问题
                channel.basicAck(deliveryTag, false);
                return;
            }

            // 执行具体的执行逻辑
            processLiveRoom(liveRoom, body);

            // 如果处理成功，确认消息
            channel.basicAck(deliveryTag, false);
        } catch (RetryException e) {
            LogExUtil.errorLog("处理消息时发生错误", e);
            // 对于需要重试的异常，我们拒绝消息并要求重新入队
            try {
                channel.basicNack(deliveryTag, false, true);
            } catch (IOException ioException) {
                LogExUtil.errorLog("Failed to NACK message", ioException);
            }
        } catch (Exception e) {
            LogExUtil.warnLog(e.getMessage());
            // 对于其他错误，我们确认消息，因为重试也不会解决问题
            try {
                channel.basicAck(deliveryTag, false);
            } catch (IOException ioException) {
                LogExUtil.errorLog("Failed to ACK message", ioException);
            }
        }
    }

    /**
     * 获取消费者名称，用于日志记录
     * @return 消费者名称
     */
    protected abstract String getConsumerName();

    /**
     * 处理直播间
     * 此方法应由子类实现以定义特定行为
     * @param liveRoom 直播间对象
     * @param body 原始消息体
     */
    protected abstract void processLiveRoom(LiveRoom liveRoom, String body);
}