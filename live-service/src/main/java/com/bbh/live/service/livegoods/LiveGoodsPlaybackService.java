package com.bbh.live.service.livegoods;

import com.bbh.base.ListBase;
import com.bbh.live.controller.req.PlaybackGoodsQueryReq;
import com.bbh.live.dao.dto.vo.LiveRoomPlaybackGoodsVO;
import com.bbh.live.dao.dto.vo.LiveRoomToCeCheckVO;

import java.util.List;

/**
 * 商品回放清单
 * <AUTHOR>
 */
public interface LiveGoodsPlaybackService {

    /**
     * 移出云展的购物车
     * @param erpGoodsId ERP货品ID
     */
    void removeCeShoppingCart(Long erpGoodsId);
    /**
     * 回放商品列表
     * @param queryReq
     * @return
     */
    ListBase<LiveRoomPlaybackGoodsVO> getLiveGoodsList(PlaybackGoodsQueryReq queryReq);

    String getLiveVideoUrl(Long liveGoodsId);

    /**
     * 获取商品回放地址，如果当前商品没有存储回放地址，会自动进行更新存储
     * @param liveGoodsId 商品ID
     * @return 回放地址
     */
    String fetchPlaybackUrl(Long liveGoodsId);

    /**
     * 批量获取商品回放地址并更新数据库字段
     * @param liveGoodsIds 商品ID列表
     */
    void batchFetchGoodsPlaybackUrls(List<Long> liveGoodsIds);

    /**
     * 查12小时内结束的场次，如果存在没同步到云展的商品，如果有就把直播间信息返回
     * 直播间id、名称、开始时间、结束时间
     * @return 直播间信息
     */
    LiveRoomToCeCheckVO getLatestToCeRoom(Long liveRoomId);
}
