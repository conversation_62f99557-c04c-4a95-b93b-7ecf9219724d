package com.bbh.live.service.livegoods.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bbh.enums.LiveGoodsStatusEnum;
import com.bbh.live.config.LiveServiceProperties;
import com.bbh.live.dao.dto.QuerySimpleUserDTO;
import com.bbh.live.dao.dto.SimpleUserInfoDTO;
import com.bbh.live.dao.dto.livegoods.LiveGoodsDTO;
import com.bbh.live.dao.mapper.GlobalOrgSeatMapper;
import com.bbh.live.dao.mapper.LiveGoodsMapper;
import com.bbh.live.dao.service.ErpGoodsService;
import com.bbh.live.dao.service.LiveGoodsService;
import com.bbh.live.service.livegoods.cache.LiveGoodsCacheManager;
import com.bbh.live.service.livegoods.cache.info.LiveGoodsAuctionInfo;
import com.bbh.live.service.msg.MsgCacheBizService;
import com.bbh.live.service.msg.dto.GoodsTransferMsgDTO;
import com.bbh.live.service.room.LiveRoomCacheService;
import com.bbh.live.service.room.context.LiveRoomContextHolder;
import jakarta.annotation.Resource;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR> dsy
 * @Date: 2024/7/27
 * @Description:
 */
public abstract class AbstractLiveGoodsService {

    @Resource
    protected LiveGoodsService liveGoodsService;
    @Resource
    protected ErpGoodsService erpGoodsService;
    @Resource
    protected LiveGoodsCacheManager liveGoodsCacheManager;
    @Resource
    protected LiveRoomCacheService liveRoomCacheService;
    @Resource
    protected MsgCacheBizService msgCacheBizService;
    @Resource
    protected LiveGoodsMapper liveGoodsMapper;
    @Resource
    protected GlobalOrgSeatMapper globalOrgSeatMapper;
    @Resource
    protected LiveServiceProperties liveServiceProperties;

    protected void richLiveGoods(List<LiveGoodsDTO> liveGoodsDetailInfoList) {
        if(CollectionUtil.isEmpty(liveGoodsDetailInfoList)){
            return;
        }
        //当前无直播间信息或直播间不在直播中 则不处理
        if (LiveRoomContextHolder.getLiveRoomContext() == null) {
            return;
        }
        // 预查询传送消息列表
        List<GoodsTransferMsgDTO> goodsTransferMsgList = msgCacheBizService.getGoodsTransferMsgListForDirector(LiveRoomContextHolder.getLiveRoomContext().getRoomId());
        // 查询有成交人的席位信息
        List<SimpleUserInfoDTO> userInfoList = new ArrayList<>(0);
        List<Long> seatIdList = liveGoodsDetailInfoList.stream()
                .map(LiveGoodsDTO::getBelongSeatId)
                .filter(Objects::nonNull)
                .distinct().toList();
        if (CollUtil.isNotEmpty(seatIdList)) {
            IPage<SimpleUserInfoDTO> userInfoPage = globalOrgSeatMapper.selectPageWithVip(Page.of(1, -1), new QuerySimpleUserDTO().setSeatIdList(seatIdList));
            userInfoList = userInfoPage.getRecords();
        }

        var goodsIdMsgMap = goodsTransferMsgList.stream().collect(Collectors.toMap(msg -> msg.getGoods().getLiveGoodsId(), msg -> msg));
        //从redis缓存取竞拍中商品当前价格，结束时间. 是否传送等
        List<SimpleUserInfoDTO> finalUserInfoList = userInfoList;
        liveGoodsDetailInfoList.forEach(liveGoodsDetailInfo -> {
            if (liveGoodsDetailInfo.getLiveRoomId() == null || liveGoodsDetailInfo.getId() == null) {
                return;
            }

            // 如果是竞拍中
            if(LiveGoodsStatusEnum.AUCTION.equals(liveGoodsDetailInfo.getGoodsStatus())){
                // 从缓存获取当前价和结束时间
                LiveGoodsAuctionInfo auctionLiveGoods = liveGoodsCacheManager.getLiveGoodsAuctionCache().getAuctionLiveGoodsBeforeExpire(liveGoodsDetailInfo.getLiveRoomId(), liveGoodsDetailInfo.getId());
                if(auctionLiveGoods != null){
                    liveGoodsDetailInfo.setCurrentPrice(auctionLiveGoods.getCurrentPrice());
                    liveGoodsDetailInfo.setAuctionEndTime(auctionLiveGoods.getAuctionEndTime());
                }
            }
            // 还要拿成交人
            if (CollUtil.isNotEmpty(finalUserInfoList) && Objects.nonNull(liveGoodsDetailInfo.getBelongSeatId())) {
                // 匹配对应的席位信息
                finalUserInfoList.stream()
                        .filter(u -> Objects.equals(u.getSeatId(), liveGoodsDetailInfo.getBelongSeatId()))
                        .findFirst()
                        .ifPresent(userInfo -> {
                            // 优先取昵称，没有就拿席位名称
                            liveGoodsDetailInfo.setBelongSeatName(userInfo.getShowName());
                            // 拍号
                            liveGoodsDetailInfo.setBelongSeatAuctionCode(userInfo.getAuctionCode());
                        });
            }

            if(goodsIdMsgMap.containsKey(liveGoodsDetailInfo.getId())){
                GoodsTransferMsgDTO goodsTransferMsgDTO = goodsIdMsgMap.get(liveGoodsDetailInfo.getId());
                liveGoodsDetailInfo.setIsTransfer(true);
                liveGoodsDetailInfo.setTransferPrice(goodsTransferMsgDTO.getTransferPrice());
                liveGoodsDetailInfo.setTransferSeatId(goodsTransferMsgDTO.getUser().getSeatId());
            }
            // 是否传送中
            liveGoodsDetailInfo.setIsTransfer(goodsIdMsgMap.containsKey(liveGoodsDetailInfo.getId()));
        });

    }
}
