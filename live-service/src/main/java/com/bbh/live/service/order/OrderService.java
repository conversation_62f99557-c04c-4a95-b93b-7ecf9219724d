package com.bbh.live.service.order;

import com.bbh.live.dao.dto.CreateOrderDTO;
import com.bbh.live.dao.dto.vo.CreateOrderVO;

/**
 * 订单处理Service
 * <AUTHOR>
 * @deprecated use {@link OrderV2Service}
 * @see OrderV2Service
 */
@Deprecated
public interface OrderService {

    /**
     * 生成订单
     *
     * @param order 订单信息
     * @return 返回已创建的订单详情
     */
    CreateOrderVO createOrder(CreateOrderDTO order);

    /**
     * 准备订单信息
     *
     * @param order 里面是商品列表
     * @return 返回订单详情
     * @deprecated 已作废，使用 {@link OrderV2Service#prepareOrderInfoV2}
     */
    @Deprecated
    CreateOrderVO prepareOrderInfo(CreateOrderDTO order);

}
