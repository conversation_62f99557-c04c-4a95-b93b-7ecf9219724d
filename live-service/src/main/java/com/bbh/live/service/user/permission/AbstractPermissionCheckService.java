package com.bbh.live.service.user.permission;

import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.cloud.commons.lang.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.bbh.live.dao.mapper.GlobalOrgRoleMapper;
import com.bbh.live.dao.service.GlobalOrgSeatService;
import com.bbh.live.enums.PermissionCodeEnum;
import com.bbh.model.GlobalOrgRole;
import com.bbh.model.GlobalOrgSeat;
import com.bbh.secure.AuthUtil;
import com.bbh.util.LogExUtil;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2024/9/20 16:13
 * @description 权限校验父类
 */

public abstract class AbstractPermissionCheckService implements PermissionCheckService {

    public static GlobalOrgSeatService globalOrgSeatService;
    public static GlobalOrgRoleMapper globalOrgRoleMapper;

    @Override
    public boolean hasPermissionByPermissionCode(PermissionCodeEnum permissionCodeEnum) {
        String permissionCode = permissionCodeEnum.getPermissionCode();
        // 是否有全局权限
        if (AuthUtil.getUser().getIfMaster()) {
            return true;
        }

        if (StringUtils.isBlank(permissionCode)) {
            return false;
        }

        GlobalOrgSeat globalOrgSeat = getGlobalOrgSeatService().getById(AuthUtil.getSeatId());
        if (globalOrgSeat == null || CollectionUtils.isEmpty(globalOrgSeat.getRoleIdList())) {
            logWarn("未分配岗位", permissionCode, AuthUtil.getSeatId());
            return false;
        }

        List<GlobalOrgRole> globalOrgRoleList = getGlobalOrgRoleMapper().selectBatchIds(globalOrgSeat.getRoleIdList());
        if (globalOrgRoleList == null || CollectionUtils.isEmpty(globalOrgRoleList)) {
            logWarn("岗位不存在", permissionCode, AuthUtil.getSeatId());
            return false;
        }
        // 角色是否有全部权限
        int erpPermissionIfAllCount = globalOrgRoleList.stream().map(this::getPermissionIfAll).mapToInt(Integer::intValue).sum();
        if (erpPermissionIfAllCount > 0) {
            return true;
        }

        // 角色是否有 permissionCode 的权限
        Set<String> hasPermissionCodeList = globalOrgRoleList.stream().flatMap(o -> {
            List<String> permissionCodeList = getPermissionCodeList(o);
            return permissionCodeList == null ? Stream.of() : permissionCodeList.stream();
        }).collect(Collectors.toSet());
        if(!hasPermissionCodeList.contains(permissionCode)){
            logWarn("岗位未分配权限", permissionCode, AuthUtil.getSeatId());
            return false;
        }
        return true;
    }

    private synchronized GlobalOrgSeatService getGlobalOrgSeatService(){
        if(globalOrgSeatService == null) {
            globalOrgSeatService = SpringUtil.getBean(GlobalOrgSeatService.class);
        }
        return globalOrgSeatService;
    }

    private synchronized GlobalOrgRoleMapper getGlobalOrgRoleMapper(){
        if(globalOrgRoleMapper == null){
            globalOrgRoleMapper = SpringUtil.getBean(GlobalOrgRoleMapper.class);
        }
        return globalOrgRoleMapper;
    }

    private void logWarn(String reason, String permissionCode, Long seatId) {
        LogExUtil.warnLog("暂无权限：权限编码：" + permissionCode + ", 当前登陆人seatId：" + seatId + "，原因：" + reason);
    }

    /**
     * 是否拥有全部权限
     * @param globalOrgRole 角色
     * @return 0：否，1：是
     */
    protected abstract Integer getPermissionIfAll(GlobalOrgRole globalOrgRole);

    /**
     * 权限编码集合
     * @param globalOrgRole 角色
     * @return 权限编码集合
     */
    protected abstract List<String> getPermissionCodeList(GlobalOrgRole globalOrgRole);


    @Override
    public List<Long> getOrgSeatIdListByPermissionCodeAndOrgId(PermissionCodeEnum permissionCode, Long orgId) {
        List<GlobalOrgRole> globalOrgRoles = getGlobalOrgRoleMapper().selectList(Wrappers.lambdaQuery(GlobalOrgRole.class)
                .eq(GlobalOrgRole::getOrgId, orgId)
                .select(GlobalOrgRole::getId)
                .apply(" (" + permissionCode.getTypeEnum().getIfAllPermissionField() + "=1 or " +
                        " JSON_CONTAINS(" + permissionCode.getTypeEnum().getPermissionCodeListField() + ", "
                        + "'\"" + permissionCode.getPermissionCode() + "\"'))"));

        if (CollectionUtils.isEmpty(globalOrgRoles)) {
            return new ArrayList<>();
        }
        List<Long> roleIdList = globalOrgRoles.stream().map(GlobalOrgRole::getId).toList();
        return getGlobalOrgSeatService().getSeatIdListByOrgRoleList(roleIdList);
    }
}
