package com.bbh.live.service.order.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.bbh.enums.*;
import com.bbh.exception.ServiceException;
import com.bbh.live.controller.req.LiveGoodsQueryReq;
import com.bbh.live.dao.dto.AggregatedUserDTO;
import com.bbh.live.dao.dto.PaySuccessContext;
import com.bbh.live.dao.dto.livegoods.LiveGoodsDTO;
import com.bbh.live.dao.mapper.GlobalOrderItemMapper;
import com.bbh.live.dao.mapper.GlobalOrgSettlementBankMapper;
import com.bbh.live.dao.mapper.GlobalOrganizationMapper;
import com.bbh.live.dao.service.GlobalOrderPaymentService;
import com.bbh.live.dao.service.GlobalOrderService;
import com.bbh.live.dao.service.LiveGoodsService;
import com.bbh.live.service.order.dto.OrderBuilderContext;
import com.bbh.live.service.vipdeduction.GlobalVipDeductionService;
import com.bbh.model.*;
import com.bbh.secure.AuthUtil;
import com.bbh.util.AssertUtil;
import com.bbh.util.LogExUtil;
import com.bbh.vo.AuthUser;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 订单检查器
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OrderChecker {

    private final GlobalOrderItemMapper globalOrderItemMapper;
    private final LiveGoodsService liveGoodsService;
    private final GlobalOrgSettlementBankMapper globalOrgSettlementBankMapper;
    private final GlobalOrganizationMapper globalOrganizationMapper;
    private final GlobalOrderPaymentService globalOrderPaymentService;
    private final GlobalOrderService globalOrderService;
    private final FenbeiCalculator fenbeiCalculator;
    private final GlobalVipDeductionService globalVipDeductionService;
    private final OrderCalculator orderCalculator;

    @Value("${htb.annual_vip_deduction:6188}")
    private BigDecimal annualVipDeduction;

    /**
     * 检查所有商品的状态
     *
     * @param liveGoodsIdList 商品id列表
     */
    public void checkAllStatus(List<Long> liveGoodsIdList) {
        AssertUtil.assertTrue(CollUtil.isNotEmpty(liveGoodsIdList), "请选择结算商品");
        IPage<LiveGoodsDTO> page = liveGoodsService.getLiveGoodsList(new LiveGoodsQueryReq().setLiveGoodsIdList(liveGoodsIdList));
        List<LiveGoodsDTO> liveGoodsList = page.getRecords();
        checkOrderStatus(liveGoodsList);
        // 不再检查erp货品信息
        // checkGoodsListStatus(liveGoodsList);
        checkOrgStatusAndUpdateContext(liveGoodsList.stream().map(LiveGoodsDTO::getOrgId).distinct().toList(), null);
    }

    /**
     * 检查所有商品的状态
     *
     * @param liveGoodsIdList 商品id列表
     */
    public void checkAllStatusAndUpdateContext(List<Long> liveGoodsIdList, OrderBuilderContext context) {
        AssertUtil.assertTrue(CollUtil.isNotEmpty(liveGoodsIdList), "请选择结算商品");
        LiveGoodsQueryReq queryReq = new LiveGoodsQueryReq().setLiveGoodsIdList(liveGoodsIdList);
        queryReq.setPerPage(-1);
        IPage<LiveGoodsDTO> page = liveGoodsService.getLiveGoodsList(queryReq);
        List<LiveGoodsDTO> liveGoodsList = page.getRecords();

        if (context.getVipDeductionId() > 0 && context.getPayType().compareTo(GlobalPayTypeEnum.OFFLINE) != 0) {
            LiveGoodsDTO first = liveGoodsList.getFirst();
            // 检查通道费
            BigDecimal channelAmount = liveGoodsList.stream().map(item -> orderCalculator.computeChannelAmount(item.getSellPrice())).reduce(BigDecimal.ZERO, BigDecimal::add);
            GlobalVipDeduction vipDeduction = globalVipDeductionService.getOne(Wrappers.lambdaQuery(GlobalVipDeduction.class).eq(GlobalVipDeduction::getOrgId, first.getBelongOrgId()).ge(GlobalVipDeduction::getEndAt, DateUtil.now()).lt(GlobalVipDeduction::getUsedVipDeduction, NumberUtil.sub(annualVipDeduction, channelAmount)).orderByAsc(GlobalVipDeduction::getId).select(GlobalVipDeduction::getId, GlobalVipDeduction::getUsedVipDeduction, GlobalVipDeduction::getVipDeduction).last("limit 1"), false);
            if (ObjUtil.isNull(vipDeduction)) {
                throw new ServiceException("抵扣金余额不足，请重新支付");
            }
            context.setVipDeductionId(vipDeduction.getId());
        }


        context.setLiveGoodsList(liveGoodsList);
        // 订单状态
        checkOrderStatus(liveGoodsList);
        // 商品状态
        checkGoodsListStatus(liveGoodsList);
        // 商户状态
        checkOrgStatusAndUpdateContext(liveGoodsList.stream().map(LiveGoodsDTO::getOrgId).distinct().toList(), context);

        if (context.getVipDeductionId() == 0L) {
            // 检查分贝
            checkFenbei(liveGoodsList, context);
        }
    }


    /**
     * 检查分贝余额是否足够
     *
     * @param liveGoodsList 商品列表，提取出直播间数量，根据直播间数量计算需要扣除的分贝数
     */
    private void checkFenbei(List<LiveGoodsDTO> liveGoodsList, OrderBuilderContext context) {
        // 仅线下转账需要检查
        if (context.getPayType() != GlobalPayTypeEnum.OFFLINE) {
            return;
        }
        AggregatedUserDTO buyerUser = context.getBuyerUser();
        // 计算不同直播间的数量，每个直播间都需要扣除相应的分贝
        long count = liveGoodsList.stream().map(LiveGoodsDTO::getLiveRoomId).distinct().count();
        // 根据直播间数量计算需要扣除的分贝数
        FenbeiCalculator.OfflineResult offlineResult = fenbeiCalculator.calculateOfflineDeduction(count, buyerUser);
        long need = offlineResult.getTotalDeductFenbei();
        // 当前商户的分贝余额
        if (buyerUser.getOrganization() == null) {
            buyerUser.setOrganization(globalOrganizationMapper.selectById(buyerUser.getOrgId()));
        }
        Integer currentFenbei = buyerUser.getOrganization().getFenbei();
        // 检查分贝余额是否足够，不足则抛出异常
        if (currentFenbei < need) {
            throw new ServiceException("分贝余额不足，请充值或使用线上支付");
        }
    }

    /**
     * 检查商户状态
     *
     * @param orgIdList 商户id列表
     */
    public void checkOrgStatusAndUpdateContext(List<Long> orgIdList, OrderBuilderContext context) {
        // 检查商户是否有开户
        List<GlobalOrgSettlementBank> bankList = globalOrgSettlementBankMapper.selectList(Wrappers.lambdaQuery(GlobalOrgSettlementBank.class).in(GlobalOrgSettlementBank::getOrgId, orgIdList));
        AssertUtil.assertTrue(CollUtil.isNotEmpty(bankList), "该商家尚未完善收款账户，可联系商家填写后再进行支付");

        if (context != null) {
            // 写入上下文，避免后面再去查询
            context.setOrgSettlementBankList(bankList);
        }

        // 如果bankSuccessId等于null或0，代表没有结算账户，则无法成功结算
        // 判断平台子商户号如果不存在则无法支付
        List<Long> bankFailOrgIdList = bankList.stream().filter(bank -> StringUtils.isBlank(bank.getPlatMerchantNo())).map(GlobalOrgSettlementBank::getOrgId).toList();
        if (CollUtil.isEmpty(bankFailOrgIdList)) {
            return;
        }

        // 查询失败的商户名称，给予友好提示
        List<GlobalOrganization> organizationList = globalOrganizationMapper.selectList(Wrappers.lambdaQuery(GlobalOrganization.class).in(GlobalOrganization::getId, bankFailOrgIdList));
        throw new ServiceException("以下商户尚未完善收款账户，可联系商家填写后再进行支付：" + organizationList.stream().map(GlobalOrganization::getName).collect(Collectors.joining(",")));
    }

    /**
     * 检查订单状态，禁止重复下单
     *
     * @param liveGoodsList 下单的商品列表
     */
    public void checkOrderStatus(List<LiveGoodsDTO> liveGoodsList) {
        AssertUtil.assertNotNull(liveGoodsList, "请选择结算商品");
        List<Long> goodsIdList = liveGoodsList.stream().map(LiveGoodsDTO::getId).toList();
        AssertUtil.assertTrue(CollUtil.isNotEmpty(goodsIdList), "商品id列表不能为空");
        AuthUser user = AuthUtil.getUser();

        // 查订单item表是否已存在相同的数据
        List<GlobalOrderItem> globalOrderItems = globalOrderItemMapper.selectList(Wrappers.lambdaQuery(GlobalOrderItem.class).in(GlobalOrderItem::getBizGoodsId, goodsIdList).eq(GlobalOrderItem::getBuyerUserId, user.getUserId()).eq(GlobalOrderItem::getBuyerSeatId, user.getSeatId())
                // 除了已取消的订单，其他都不能重复
                .ne(GlobalOrderItem::getOrderStatus, GlobalOrderStatusEnum.CANCELED));

        if (CollUtil.isNotEmpty(globalOrderItems)) {
            // 提示哪些商品已经下过单
            List<String> goodsNameList = liveGoodsList.stream().filter(o -> globalOrderItems.stream().anyMatch(i -> i.getBizGoodsId().equals(o.getId()))).map(LiveGoodsDTO::getGlobalGoodsName).toList();
            throw new ServiceException("以下商品已下过单：" + String.join(",", goodsNameList));
        }
    }

    /**
     * 检查商品的状态，目前检查已开单、已售出、锁单、已成交
     *
     * @param goodsList 商品列表
     */
    public void checkGoodsListStatus(List<LiveGoodsDTO> goodsList) {
        if (goodsList.isEmpty()) {
            throw new ServiceException("未查询到有效的商品清单");
        }
        // 检查是否所有商品都有关联的erp_goods
        if (goodsList.stream().anyMatch(goods -> Objects.isNull(goods.getGlobalGoodsName()))) {
            throw new ServiceException("商品清单中有商品没有关联货品");
        }

        // 检查所有商品，把所有已开单的商品和已售出的id、name记录下来，检查结束后，如果存在已开单，就抛出异常，提示xx商品已开单，已售出同理
        // List<String> placedNameList = new ArrayList<>();
        // List<String> soldOutNameList = new ArrayList<>();
        List<String> statusErrorNameList = new ArrayList<>();
        // List<String> lockedNameList = new ArrayList<>();
        List<String> canceledNameList = new ArrayList<>();
        List<String> buyerCanceledNameList = new ArrayList<>();
        goodsList.forEach(goods -> {
            // ERP已开单
            // if (Boolean.TRUE.equals(goods.getErpPlaceOrderStatus())) {
            // placedNameList.add(goods.getGlobalGoodsName());
            // }
            // ERP已售出
            // if (Objects.equals(goods.getErpSaleStatus(), ErpGoodsSaleStatusEnum.SOLD_OUT.getType())) {
            // soldOutNameList.add(goods.getGlobalGoodsName());
            // }
            // 直播中未成交
            if (goods.getGoodsStatus() != LiveGoodsStatusEnum.TRADED) {
                statusErrorNameList.add(goods.getGlobalGoodsName());
            }
            // ERP已锁单
            // if (Boolean.TRUE.equals(goods.getErpIfLocked())) {
            // lockedNameList.add(goods.getGlobalGoodsName());
            // }
            // 售后已取消
            if (goods.getCancelStatus() == LiveGoodsCancelStatusEnum.APPROVAL) {
                canceledNameList.add(goods.getGlobalGoodsName());
            }
            // 自主取消已发起
            if (LiveGoodsBuyerCancelRecordStatusEnum.WAITING_SELLER.equals(goods.getBuyerCancelStatus()) || LiveGoodsBuyerCancelRecordStatusEnum.PLATFORM_REVIEWING.equals(goods.getBuyerCancelStatus())) {
                buyerCanceledNameList.add(goods.getGlobalGoodsName());
            }
        });
        if (!statusErrorNameList.isEmpty()) {
            throw new ServiceException("以下商品未在直播中成交，无法下单：" + String.join(",", statusErrorNameList));
        }
        // if (!lockedNameList.isEmpty()) {
        // throw new ServiceException("以下商品已锁定，无法下单：" + String.join(",", lockedNameList));
        // }
        // if (!placedNameList.isEmpty()) {
        // throw new ServiceException("以下商品已开单，无法下单：" + String.join(",", placedNameList));
        // }
        // if (!soldOutNameList.isEmpty()) {
        // throw new ServiceException("以下商品已售出，无法下单：" + String.join(",", soldOutNameList));
        // }
        if (!canceledNameList.isEmpty()) {
            throw new ServiceException("以下商品已被售后取消，无法下单：" + String.join(",", canceledNameList));
        }
        if (!buyerCanceledNameList.isEmpty()) {
            throw new ServiceException("以下商品已发起自主取消申请，无法下单：" + String.join(",", canceledNameList));
        }
    }

    public PaySuccessContext getPaySuccessContext(String outTradeNo) {
        // 校验支付流水是否存在
        var orderPayment = globalOrderPaymentService.lambdaQuery().eq(GlobalOrderPayment::getOutTradeNo, outTradeNo).one();
        AssertUtil.assertNotNull(orderPayment, "支付流水不存在");
        // 只检查payment是否存在，不检查是否关闭
        // AssertUtil.assertTrue(orderPayment.getPayStatus() == GlobalOrderPaymentStatusEnum.WAIT, "订单已关闭");
        // 校验订单是否存在或已支付
        var globalOrder = globalOrderService.getById(orderPayment.getGlobalOrderId());
        AssertUtil.assertNotNull(globalOrder, "订单不存在");

        // 检查是否已经存在支付完成的流水，代表当前这笔是重复支付，通过error及时通知报警，通过后台人工退款
        var existingPaidPayment = globalOrderPaymentService.lambdaQuery()
                .eq(GlobalOrderPayment::getGlobalOrderId, globalOrder.getId())
                .eq(GlobalOrderPayment::getPayStatus, GlobalOrderPaymentStatusEnum.PAID)
                // 排除当前流水号，只检查是否有其他已支付的流水
                .ne(GlobalOrderPayment::getOutTradeNo, outTradeNo)
                .one();
        if (existingPaidPayment != null) {
            String errorMessage = String.format("检测到重复支付回调, 终止处理, 订单号: %s, 当前流水号: %s, 已支付流水号: %s, 订单状态: %s, 请及时进行退款处理",
                    globalOrder.getOrderNo(), outTradeNo, existingPaidPayment.getOutTradeNo(), globalOrder.getOrderStatus());
            ServiceException serviceException = new ServiceException("重复支付，请及时联系工作人员进行退款");
            LogExUtil.errorLog(errorMessage, serviceException);
            throw serviceException;
        }

        AssertUtil.assertTrue(globalOrder.getOrderStatus() == GlobalOrderStatusEnum.TO_BE_PAID || globalOrder.getOrderStatus() == GlobalOrderStatusEnum.TRANSFER_AUDIT, "订单已关闭");

        return new PaySuccessContext(orderPayment, globalOrder);
    }
}
