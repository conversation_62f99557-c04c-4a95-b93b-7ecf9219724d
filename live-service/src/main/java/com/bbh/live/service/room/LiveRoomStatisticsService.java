package com.bbh.live.service.room;

import com.bbh.live.dao.dto.RoomIdDTO;
import com.bbh.live.dao.dto.vo.LiveRoomSaleInfo;
import com.bbh.live.dao.dto.vo.LiveRoomSaleStatisticsInfo;

/**
 * <AUTHOR> dsy
 * @Date: 2024/9/12
 * @Description: 直播间统计信息service
 */
public interface LiveRoomStatisticsService {

    /**
     *  直播间销售信息统计
     * @param roomId
     * @return
     */
    LiveRoomSaleInfo liveSaleInfo(RoomIdDTO roomId);

    /**
     * 直播间实时销售信息统计
     * @param roomId
     * @return
     */
    LiveRoomSaleStatisticsInfo realTimeSaleInfo(RoomIdDTO roomId);
}
