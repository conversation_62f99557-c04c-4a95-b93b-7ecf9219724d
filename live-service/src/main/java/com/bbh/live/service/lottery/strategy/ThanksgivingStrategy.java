package com.bbh.live.service.lottery.strategy;

import com.bbh.enums.GlobalLotteryPrizePoolTypeEnum;
import com.bbh.live.dao.dto.LotteryPrizeItemDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 谢谢惠顾
 * <AUTHOR>
 */
@Service
@Slf4j
public class ThanksgivingStrategy implements PrizeProcessStrategy {
    /**
     * 匹配
     *
     * @param type 匹配的类型
     * @return 是否匹配
     */
    @Override
    public boolean match(GlobalLotteryPrizePoolTypeEnum type) {
        return type == GlobalLotteryPrizePoolTypeEnum.THANKS;
    }

    /**
     * 处理奖品
     *
     * @param userId    用户ID
     * @param seatId    用户座位ID
     * @param prizeItem 奖品
     */
    @Override
    public void processPrize(Long userId, Long seatId, LotteryPrizeItemDTO prizeItem) {
        // do nothing
    }
}
