package com.bbh.live.service.msg;

import com.bbh.live.dao.dto.ChatroomStatusSyncDTO;

import java.util.List;

/**
 * 聊天室
 * <AUTHOR>
 */
public interface ChatroomService {

    /**
     * 构建直播间专用的聊天室ID
     * @param liveRoomId    直播间ID
     * @return 聊天室ID
     */
    String buildChatroomId(Long liveRoomId);

    /**
     * 创建直播间专用的聊天室
     * @param liveRoomId    直播间ID
     * @return 聊天室ID
     */
    String createChatroom(Long liveRoomId);

    /**
     * 创建聊天室
     * @param chatroomId 聊天室ID
     * @return 聊天室ID
     */
    String createChatroom(String chatroomId);

    /**
     * 聊天室状态同步
     */
    void chatroomStatusSync(List<ChatroomStatusSyncDTO> chatroomStatusSyncDTOList);

}
