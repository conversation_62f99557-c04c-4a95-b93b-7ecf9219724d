package com.bbh.live.service.virutalgoods.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.bbh.enums.*;
import com.bbh.exception.ServiceException;
import com.bbh.live.config.LiveServiceProperties;
import com.bbh.live.constant.ProjectConstant;
import com.bbh.live.dao.dto.PayDTO;
import com.bbh.live.dao.dto.vo.CreateOrderV2VO;
import com.bbh.live.dao.dto.vo.VirtualGoodsBuyVO;
import com.bbh.live.dao.mapper.GlobalVirtualOrderPaymentMapper;
import com.bbh.live.dao.service.GlobalOrderOtherGoodsDicService;
import com.bbh.live.dao.service.GlobalOrgSeatService;
import com.bbh.live.dao.service.GlobalVirtualGoodsOrderService;
import com.bbh.live.dao.service.IGlobalOrganizationService;
import com.bbh.live.handler.queue.DelayJob;
import com.bbh.live.handler.queue.DelayJobFactory;
import com.bbh.live.handler.queue.DelayQueueManager;
import com.bbh.live.service.buyer.vip.BuyerVipService;
import com.bbh.live.service.order.PayService;
import com.bbh.live.service.order.dto.OrderBuilderContext;
import com.bbh.live.service.virutalgoods.VirtualGoodsService;
import com.bbh.live.service.virutalgoods.processor.CompositeOrderProcessor;
import com.bbh.live.service.virutalgoods.processor.VirtualOrderProcessContext;
import com.bbh.model.*;
import com.bbh.service.RedisService;
import com.bbh.util.AssertUtil;
import com.bbh.util.LogExUtil;
import com.bbh.util.OrderNoGenerator;
import com.bbh.vo.Result;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR> dsy
 * @Date: 2024/9/12
 * @Description:
 */
@Service
@RequiredArgsConstructor
public class VirtualGoodsServiceImpl implements VirtualGoodsService {

    private final GlobalOrderOtherGoodsDicService globalOrderOtherGoodsDicService;
    private final GlobalOrgSeatService globalOrgSeatService;
    private final IGlobalOrganizationService globalOrganizationService;
    private final GlobalVirtualGoodsOrderService globalVirtualGoodsOrderService;
    private final DelayQueueManager delayQueueManager;
    private final CompositeOrderProcessor virtualOrderProcessor;
    private final BuyerVipService buyerVipService;
    private final GlobalVirtualOrderPaymentMapper globalOrderPaymentMapper;
    private final PayService payService;
    private final LiveServiceProperties liveServiceProperties;
    private final RedisService redisService;

    @Override
    public CreateOrderV2VO createOrder(VirtualGoodsBuyVO virtualGoodsBuyVO) {
        // 虚拟商品
        GlobalOrderOtherGoodsDic virtualGoods = globalOrderOtherGoodsDicService.getById(virtualGoodsBuyVO.getGoodsId());
        AssertUtil.assertNotNull(virtualGoods, "商品不存在");
        // 买手席位
        GlobalOrgSeat buyerSeat = globalOrgSeatService.lambdaQuery().eq(GlobalOrgSeat::getId, virtualGoodsBuyVO.getBuyerSeatId()).select(GlobalOrgSeat::getOrgId, GlobalOrgSeat::getId, GlobalOrgSeat::getUserId).one();
        AssertUtil.assertNotNull(buyerSeat, "用户不存在");

        // 执行前置处理
        boolean continueProcess = virtualOrderProcessor.preProcess(buyerSeat, virtualGoods);
        if (!continueProcess) {
            throw new ServiceException("订单创建校验未通过");
        }

        // 买手主体
        GlobalOrganization organization = globalOrganizationService.lambdaQuery().eq(GlobalOrganization::getId, buyerSeat.getOrgId()).select(GlobalOrganization::getId, GlobalOrganization::getName).one();
        Date now = new Date();
        // 订单超时结束时间
        Long orderOvertimeTime = ProjectConstant.VirtualGoodsOrder.ORDER_OVERTIME_TIME;
        Date orderExpireTime = DateUtil.offsetMillisecond(now, orderOvertimeTime.intValue());
        // 创建订单
        GlobalVirtualGoodsOrder globalVirtualGoodsOrder = new GlobalVirtualGoodsOrder();
        globalVirtualGoodsOrder.setGoodsId(virtualGoods.getId());
        globalVirtualGoodsOrder.setGoodsName(virtualGoods.getGoodsName());
        globalVirtualGoodsOrder.setGoodsType(virtualGoods.getGoodsSource().getValue());
        // 优先取前端传的，卖家保证金、卖家补偿金等场景下有值，其他场景为空，就用商品金额
        globalVirtualGoodsOrder.setGoodsPrice(Objects.requireNonNullElse(virtualGoodsBuyVO.getOrderPrice(), virtualGoods.getGoodsPrice()));
        globalVirtualGoodsOrder.setGoodsNumber(virtualGoods.getGoodsNumber());
        globalVirtualGoodsOrder.setGoodsCoverImg(virtualGoods.getGoodsImages());
        globalVirtualGoodsOrder.setOrgId(buyerSeat.getOrgId());
        globalVirtualGoodsOrder.setOrgName(organization.getName());
        // 优先取前端传的，卖家保证金、卖家补偿金等场景下有值，其他场景为空，就用商品金额
        globalVirtualGoodsOrder.setOrderPrice(Objects.requireNonNullElse(virtualGoodsBuyVO.getOrderPrice(), virtualGoods.getGoodsPrice()));
        globalVirtualGoodsOrder.setOrderState(GlobalVirtualGoodsOrderStateEnum.WAIT_PAY);
        globalVirtualGoodsOrder.setPayType(virtualGoodsBuyVO.getPayType());
        globalVirtualGoodsOrder.setBuyerSeatId(buyerSeat.getId());
        globalVirtualGoodsOrder.setBuyerUserId(buyerSeat.getUserId());
        globalVirtualGoodsOrder.setBuyerOrgId(buyerSeat.getOrgId());
        globalVirtualGoodsOrder.setOrderExpireTime(orderExpireTime);
        globalVirtualGoodsOrder.setExtraData(virtualGoodsBuyVO.getExtraData());
        globalVirtualGoodsOrder.setOrderNo(OrderNoGenerator.generateIdNumber(OrderNoGenerator.Service.GLOBAL, OrderNoGenerator.Function.VIRTUAL_GOODS, OrderNoGenerator.Business.ORDER, false));
        globalVirtualGoodsOrderService.save(globalVirtualGoodsOrder);

        // 订单加入延时队列，15分钟超时。支付成功回调后删除任务
        DelayJob<Long> delayJob = DelayJobFactory.createVirtualGoodsOrderDelayJob(globalVirtualGoodsOrder.getId(), ProjectConstant.VirtualGoodsOrder.ORDER_OVERTIME_TIME);
        delayQueueManager.addToQueue(delayJob);

        return new CreateOrderV2VO(globalVirtualGoodsOrder.getOrderNo(), globalVirtualGoodsOrder.getId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean orderPayCallback(String outTradeNo) {
        boolean lock = redisService.lock("Virtual_Osn_" + outTradeNo);
        try {
            if (!lock) {
                return false;
            }
            LogExUtil.infoLog(StrUtil.format("虚拟订单支付成功回调，订单号: {}", outTradeNo));
            // 支付流水信息
            GlobalVirtualOrderPayment globalOrderPayment = globalOrderPaymentMapper.selectOne(new LambdaQueryWrapper<>(GlobalVirtualOrderPayment.class).eq(GlobalVirtualOrderPayment::getOutTradeNo, outTradeNo));
            AssertUtil.assertNotNull(globalOrderPayment, "支付流水不存在");
            // 生产上回调比检查更快，因此如果已经支付，不再往下执行
            if (globalOrderPayment.getPayStatus() == GlobalOrderPaymentStatusEnum.PAID) {
                return true;
            }
            AssertUtil.assertTrue(globalOrderPayment.getPayStatus() == GlobalOrderPaymentStatusEnum.WAIT, "订单已关闭");

            GlobalVirtualGoodsOrder virtualGoodsOrder = globalVirtualGoodsOrderService.getOne(new LambdaUpdateWrapper<>(GlobalVirtualGoodsOrder.class).eq(GlobalVirtualGoodsOrder::getId, globalOrderPayment.getVirtualOrderId()));

            // 校验订单状态
            if (ObjUtil.isNull(virtualGoodsOrder) || !virtualGoodsOrder.getOrderState().equals(GlobalVirtualGoodsOrderStateEnum.WAIT_PAY)) {
                LogExUtil.infoLog(StrUtil.format("订单支付成功回调失败，订单状态异常，订单号: {}", outTradeNo));
                throw new ServiceException("订单异常");
            }

            // 回查支付中台支付状态 校验支付是否成功，只需要检查线上的，因为线下打款不走回调，并且及时查询也不会有结果
            if (!liveServiceProperties.getEnableMockPay()) {
                AssertUtil.assertTrue(payService.getOrderPaidInfoStatus(outTradeNo), "订单未支付");
            }

            try {
                // 虚拟商品类型
                GlobalOrderOtherGoodsDic goodsDic = globalOrderOtherGoodsDicService.getById(virtualGoodsOrder.getGoodsId());
                AssertUtil.assertNotNull(goodsDic, "虚拟商品不存在");

                // 买手信息
                var buyerVipInfo = buyerVipService.getUserBuyerVipInfoBySeatId(virtualGoodsOrder.getBuyerSeatId());

                // 处理订单
                Result result = virtualOrderProcessor.process(new VirtualOrderProcessContext(virtualGoodsOrder, goodsDic, buyerVipInfo));

                if (result.getCode() == ResponseCode.SUCCESS.getCode()) {
                    // 更新订单状态
                    virtualGoodsOrder.setOrderState(GlobalVirtualGoodsOrderStateEnum.DONE_DELIVER);
                    virtualGoodsOrder.setPayPrice(virtualGoodsOrder.getOrderPrice());
                    virtualGoodsOrder.setPayAt(new Date());
                    virtualGoodsOrder.setDeliverAt(new Date());
                } else {
                    // 记录错误信息
                    virtualGoodsOrder.setPayError(result.getMsg());
                }
                // 修改支付流水
                LambdaUpdateWrapper<GlobalVirtualOrderPayment> paymentUpdate = new LambdaUpdateWrapper<>();
                paymentUpdate.set(GlobalVirtualOrderPayment::getPayStatus, GlobalOrderPaymentStatusEnum.PAID);
                paymentUpdate.set(GlobalVirtualOrderPayment::getRealPayedAmount, virtualGoodsOrder.getOrderPrice());
                paymentUpdate.set(GlobalVirtualOrderPayment::getPayAt, new Date());
                paymentUpdate.eq(GlobalVirtualOrderPayment::getId, globalOrderPayment.getId());
                globalOrderPaymentMapper.update(paymentUpdate);
                // 更新订单状态
                globalVirtualGoodsOrderService.saveOrUpdate(virtualGoodsOrder);
            } finally {
                // 移除到期取消订单的延时队列
                DelayJob<Long> delayJob = DelayJobFactory.createVirtualGoodsOrderDelayJob(virtualGoodsOrder.getId(), ProjectConstant.VirtualGoodsOrder.ORDER_OVERTIME_TIME);
                delayQueueManager.remove(delayJob);
            }
        } finally {
            redisService.unLock("Virtual_Osn_" + outTradeNo);
        }
        return true;
    }

    /**
     * 检查支付是否成功
     *
     * @param orderId 订单号
     * @return 支付是否成功: true/false
     */
    @SuppressWarnings("all")
    @Override
    public boolean checkPaySuccess(Long orderId) {
        // order_no查到order_id，再查询最新的payment的out_trade_no
        GlobalVirtualGoodsOrder globalOrder = globalVirtualGoodsOrderService.getOne(Wrappers.lambdaQuery(GlobalVirtualGoodsOrder.class).eq(GlobalVirtualGoodsOrder::getId, orderId));
        AssertUtil.assertTrue(globalOrder != null, "订单不存在");

        // 查最新的订单流水号，因为每次支付后都会更新订单号
        String orderNo = globalOrder.getOrderNo();

        // 如果开启模拟支付，直接走回调成功
        if (liveServiceProperties.getEnableMockPay()) {
            mockPaySuccess(orderNo);
            return true;
        }

        // 正常流程要查一下支付流水
        GlobalVirtualOrderPayment orderPayment = globalOrderPaymentMapper.selectOne(Wrappers.lambdaQuery(GlobalVirtualOrderPayment.class).eq(GlobalVirtualOrderPayment::getVirtualOrderId, globalOrder.getId()).orderByDesc(GlobalVirtualOrderPayment::getCreatedAt).last("limit 1"));
        AssertUtil.assertTrue(orderPayment != null, "未找到支付流水");

        // 这里直接调支付成功的回调，在回调中会去检查中台的支付状态，如果未支付会抛ServiceException
        // 检查订单状态是否为已支付
        return orderPayCallback(orderPayment.getOutTradeNo());
    }

    @Transactional(rollbackFor = Exception.class)
    public void mockPaySuccess(String orderNo) {
        // 先创建支付流水
        PayDTO payDTO = new PayDTO();
        payDTO.setPayType(2);
        payDTO.setOrderNo(orderNo);
        payDTO.setCode("mock");
        var payResult = pay(payDTO);
        if (payResult != null) {
            var outTradeNo = ((Dict) payResult).getStr("tradeNo");
            // 再走回调
            orderPayCallback(outTradeNo);
        }
    }

    /**
     * 单独的调起支付接口
     *
     * @param payDTO 流水号
     * @return 调起支付参数
     */
    @SuppressWarnings("all")
    @Transactional
    @Override
    public Object pay(PayDTO payDTO) {
        AssertUtil.assertNotNull(payDTO.getOrderNo(), "订单流水号不能为空");
        AssertUtil.assertNotNull(payDTO.getPayType(), "支付方式不能为空");
        AssertUtil.assertNotNull(payDTO.getCode(), "code不能为空");

        String orderNo = payDTO.getOrderNo();
        GlobalPayTypeEnum payType = payDTO.getPayType() == 1 ? GlobalPayTypeEnum.WECHAT : GlobalPayTypeEnum.ALIPAY;

        // 根据流水号查询到订单
        GlobalVirtualGoodsOrder globalOrder = globalVirtualGoodsOrderService.getOne(Wrappers.lambdaQuery(GlobalVirtualGoodsOrder.class).eq(GlobalVirtualGoodsOrder::getOrderNo, orderNo));
        AssertUtil.assertFalse(globalOrder == null, "订单不存在");

        // 检查订单状态是否待支付
        assert globalOrder != null;
        AssertUtil.assertTrue(Objects.equals(globalOrder.getOrderState(), GlobalVirtualGoodsOrderStateEnum.WAIT_PAY), "订单不是待支付状态");
        Long orderId = globalOrder.getId();

        // 查payment表，看有没有未支付记录，如果有要调用中台的关闭
        List<GlobalVirtualOrderPayment> orderPaymentList = globalOrderPaymentMapper.selectList(Wrappers.lambdaQuery(GlobalVirtualOrderPayment.class).eq(GlobalVirtualOrderPayment::getVirtualOrderId, orderId).eq(GlobalVirtualOrderPayment::getPayStatus, GlobalOrderPaymentStatusEnum.WAIT));
        if (CollUtil.isNotEmpty(orderPaymentList)) {
            // 如果有要调用中台的关闭
            try {
                for (GlobalVirtualOrderPayment payment : orderPaymentList) {
                    // 要用payment的trade_no，因为这个是独立于order_no的
                    payService.orderClosePay(payment.getOutTradeNo());
                }
            } catch (Exception e) {
                // 捕获异常，确保中台不影响付款业务
                LogExUtil.errorLog(orderNo + " 关闭订单失败: " + e.getMessage(), e);
            }
        }

        // 关闭之后，更新这笔订单的payment的状态为自动取消
        globalOrderPaymentMapper.update(Wrappers.lambdaUpdate(GlobalVirtualOrderPayment.class).eq(GlobalVirtualOrderPayment::getVirtualOrderId, orderId).set(GlobalVirtualOrderPayment::getPayStatus, GlobalOrderPaymentStatusEnum.CANCEL_AUTO));

        // 生成新的流水号
        String newTradeNo = OrderNoGenerator.generateIdNumber(OrderNoGenerator.Service.GLOBAL, OrderNoGenerator.Function.VIRTUAL_GOODS, OrderNoGenerator.Business.ORDER, false);

        // 保存新的payment
        GlobalVirtualOrderPayment globalOrderPayment = new GlobalVirtualOrderPayment();
        globalOrderPayment.setVirtualOrderId(orderId);
        globalOrderPayment.setOutTradeNo(newTradeNo);
        globalOrderPayment.setPayStatus(GlobalOrderPaymentStatusEnum.WAIT);
        globalOrderPayment.setNeedPayAmount(globalOrder.getOrderPrice());
        globalOrderPayment.setRealPayedAmount(BigDecimal.ZERO);
        // 通道固定为江南银行
        globalOrderPayment.setPayChannel(GlobalOrderPaymentChannelEnum.JN);
        globalOrderPayment.setPayType(payType);
        globalOrderPaymentMapper.insert(globalOrderPayment);

        // 更新订单表，确保流水号一致，如果流水号不一致会导致财务对账对不上
        globalVirtualGoodsOrderService.lambdaUpdate().set(GlobalVirtualGoodsOrder::getOrderNo, newTradeNo).eq(GlobalVirtualGoodsOrder::getId, orderId).update();

        // 用新的流水号调用中台的支付接口
        OrderBuilderContext context = new OrderBuilderContext().setPaymentTradeNo(newTradeNo).setPayType(payType).setAliPayUserId(payDTO.getCode()).setWxOpenId(payDTO.getCode());
        // 直接返回支付参数
        return liveServiceProperties.getEnableMockPay() ?
                // 模拟支付，需要返回流水号
                new Dict().set("tradeNo", newTradeNo) :
                // 非模拟支付，返回真实的参数
                payService.virtualOrderOnlinePay(context, globalOrder);
    }
}
