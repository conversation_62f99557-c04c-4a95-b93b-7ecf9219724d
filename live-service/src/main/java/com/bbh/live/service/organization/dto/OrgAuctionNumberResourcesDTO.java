package com.bbh.live.service.organization.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2024/9/20 08:33
 * @description
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
public class OrgAuctionNumberResourcesDTO {

    /**
     * 拍号 id
     */
    private Long auctionNumberId;

    /**
     * 拍号
     */
    private Long auctionNumber;

    /**
     * 拍号所属店铺 id
     */
    private Long orgId;

    /**
     * 拍号所属席位
     */
    private Long seatId;

    /**
     * 拍号所属席位名称
     */
    private String seatName;

    /**
     * 拍号所属席位头像
     */
    private String avatar;
}
