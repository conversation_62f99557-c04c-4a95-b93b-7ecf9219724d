package com.bbh.live.service.livegoods;

import com.bbh.live.dao.dto.livegoods.LiveGoodsDTO;
import com.bbh.live.dao.service.LiveGoodsService;

/**
 * <AUTHOR> dsy
 * @Date: 2024/7/26
 * @Description:
 */
public interface LiveGoodsDetailService {

    /**
     * 根据id查询直播商品详情
     * @param liveGoodsId
     * @return
     */
    LiveGoodsDTO getLiveGoodsDetailInfo(Long liveGoodsId);


    /**
     * 根据id查询直播商品详情
     * @param liveGoodsId
     * @return
     */
    LiveGoodsDTO getLiveGoodsSimpleDetailInfo(Long liveGoodsId);

    /**
     * 根据id查询直播商品详情
     * @param liveGoodsId
     * @return
     */
    LiveGoodsDTO getLiveGoodsDetailInfo(Long liveGoodsId, Long globalGoodsId);

    /**
     * 已选商品列表
     * @param globalGoodsId
     * @return
     */
    LiveGoodsDTO getCheckedGoodsInfo(Long globalGoodsId);

    /**
     * 更新商品信息
     * @param liveGoodsDTO
     */
    void updateLiveGoods(LiveGoodsDTO liveGoodsDTO);


    LiveGoodsService getLiveGoodsService();

    /**
     * 下一件待上架商品
     * @param liveGoodsDTO
     * @return
     */
    LiveGoodsDTO getNextWaitPutAwayLiveGoods(LiveGoodsDTO liveGoodsDTO);

    /**
     * 检查商品是否可购买
     * @param liveGoodsDTO
     * @param ifLock
     */
    void checkLiveGoodsCanAddToCashierDesk(LiveGoodsDTO liveGoodsDTO, boolean ifLock);
}
