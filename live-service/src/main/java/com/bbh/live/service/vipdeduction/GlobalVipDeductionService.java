package com.bbh.live.service.vipdeduction;

import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bbh.live.dao.mapper.GlobalVipDeductionMapper;
import com.bbh.model.GlobalVipDeduction;

import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 * @date 2025/3/25 16:19
 */
@Service
@RequiredArgsConstructor
public class GlobalVipDeductionService extends ServiceImpl<GlobalVipDeductionMapper, GlobalVipDeduction> {}
