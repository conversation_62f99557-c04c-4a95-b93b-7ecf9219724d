package com.bbh.live.service.room;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.bbh.exception.ServiceException;
import com.bbh.live.constant.RedisKey;
import com.bbh.live.dao.mapper.LiveRoomMapper;
import com.bbh.live.dao.service.LiveRoomService;
import com.bbh.live.enums.LiveRoomCacheKeys;
import com.bbh.live.service.livegoods.cache.LiveGoodsCacheConstant;
import com.bbh.live.thread.ThreadPoolManager;
import com.bbh.model.LiveRoom;
import com.bbh.service.lock.HtbLockService;
import com.bbh.service.lock.bean.HtbLock;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanWrapper;
import org.springframework.beans.PropertyAccessorFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.stereotype.Service;

import java.beans.PropertyDescriptor;
import java.math.BigDecimal;
import java.time.Duration;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;

import static com.bbh.live.enums.LiveRoomCacheKeys.*;

@Service
@Slf4j
@AllArgsConstructor
public class LiveRoomCacheService implements RedisKey {

    private final StringRedisTemplate stringRedisTemplate;
    private final RedisTemplate redisTemplate;
    private final LiveRoomMapper liveRoomMapper;
    private final HtbLockService htbLockService;

    private final static Map<LiveRoomCacheKeys, Function<LiveRoom, ?>> PROPERTY_GETTERS = Map.of(
            SOLD_AMOUNT, LiveRoom::getSoldAmount,
            SOLD_COUNT, LiveRoom::getSoldCount,
            GOODS_COUNT, LiveRoom::getGoodsCount,
            SUBSCRIBE_COUNT, LiveRoom::getSubscribeCount,
            UNSOLD_COUNT, LiveRoom::getUnsoldCount,
            VIEW_COUNT, LiveRoom::getViewCount,
            REAL_COUNT, LiveRoom::getRealCount,
            PEAK_COUNT, LiveRoom::getRealMaxCount,
            REALTIME_COUNT, liveRoom -> 0
    );

    public final static Set<LiveRoomCacheKeys> IGNORE_TEST_BROADCAST_KEYS = Set.of(
            VIEW_COUNT,
            REAL_COUNT,
            PEAK_COUNT
    );

    /**
     * 递增直播间缓存中预约数量
     *
     * @param roomId 直播间ID
     */
    public void incrementSubscribeCount(Long roomId, long delta) {
        incrementCache(roomId, SUBSCRIBE_COUNT, delta);
    }

    /**
     * 增加商品数量的方法
     *
     * @param roomId 房间 ID
     * @param delta 增加的数量
     */
    public void incrementGoodsCount(Long roomId, long delta) {
        // 调用增加缓存的方法
        incrementCache(roomId, GOODS_COUNT, delta);
    }

    /**
     * 直播间观看次数
     */
    public void incrementViewCount(Long roomId, long delta) {
        // 调用增加缓存的方法
        incrementCache(roomId, VIEW_COUNT, delta);
    }

    /**
     * 实时人数，通过融云IM回调来实时进行增减
     */
    public void incrementRealtimeCount(Long roomId, long delta) {
        incrementCache(roomId, REALTIME_COUNT, delta);
        // 自动设置峰值人数
        autoUpdatePeakCount(roomId);
    }

    /**
     * 累计观看人数
     */
    public void incrementRealCount(Long roomId, long delta) {
        incrementCache(roomId, REAL_COUNT, delta);
    }

    /**
     * 自动设置峰值人数
     * @param roomId    直播间id
     */
    public void autoUpdatePeakCount(Long roomId) {
        // 获取实时人数
        int realtimeCount = getRealtimeCount(roomId);
        // 获取当前峰值
        int currentPeakCount = getValueOptional(roomId, PEAK_COUNT, Integer.class).orElse(0);

        // 如果实时人数大于峰值，则通过 incrementCache 更新峰值
        // 计算需要增加的差值
        if (realtimeCount > currentPeakCount) {
            long delta = realtimeCount - currentPeakCount;
            incrementCache(roomId, PEAK_COUNT, delta);
        }
    }

    /**
     * 获取实时在线人数
     * @param roomId    直播间id
     * @return         实时在线人数
     */
    public Integer getRealtimeCount(Long roomId) {
        return getValueOptional(roomId, REALTIME_COUNT, Integer.class).orElse(0);
    }

    /**
     * 直播商品数量
     * @param roomId
     * @return
     */
    public Integer getGoodsCount(Long roomId) {
        return getValueOptional(roomId, GOODS_COUNT, Integer.class).orElse(0);
    }

    /**
     * 增加直播间商品成交数量
     * @param roomId
     * @param delta
     */
    public void incrementGoodsTradeCount(Long roomId, long delta) {
        // 递增成交数量
        incrementCache(roomId, SOLD_COUNT, delta);
        // 更新直播间score
        updateRoomScore(roomId);
    }

    /**
     * 直播间商品成交数量
     * @param roomId
     * @return
     */
    public Long getGoodsTradeCount(Long roomId) {
        return getValueOptional(roomId, SOLD_COUNT, Long.class).orElse(0L);
    }

    /**
     * 增加直播间商品成交额
     * @param roomId
     * @param delta
     */
    public void incrLiveGoodsSoldAmount(Long roomId, long delta) {
        // 递增成交金额
        incrementCache(roomId, SOLD_AMOUNT, delta);
        // 更新直播间score
        updateRoomScore(roomId);
    }

    /**
     * 增加流拍商品数量
     */
    public void incrementUnsoldCount(Long roomId, long delta) {
        // 调用增加缓存的方法
        incrementCache(roomId, UNSOLD_COUNT, delta);
    }

    /**
     * 查询直播间商品成交额
     * @param roomId
     */
    public BigDecimal getGoodsSoldAmount(Long roomId) {
        // 调用增加缓存的方法
        return getAmountOptional(roomId, SOLD_AMOUNT).orElse(BigDecimal.ZERO);
    }

    /**
     * 异步更新直播间分数 <br>
     * 直播间分数 = 直播间成交额总和 * 0.00033 + 成交数量
     * @param roomId 直播间ID
     */
    public void updateRoomScore(Long roomId) {
        BigDecimal goodsSoldAmount = getGoodsSoldAmount(roomId);
        Long goodsTradeCount = getGoodsTradeCount(roomId);
        ThreadPoolManager.getGlobalBizExecutor().execute(() -> {
            // 成交额在直播中只会增不会减，因此来把小锁，先查当前分数，如果算出来的分数更高再去更新
            LiveRoomService liveRoomService = SpringUtil.getBean(LiveRoomService.class);
            RedissonClient redissonClient = SpringUtil.getBean(RedissonClient.class);
            RLock lock = redissonClient.getLock("updateRoomScore" + roomId);
            lock.lock();
            try {
                // 计算公式 = 直播间成交额总和 * 0.00033 + 成交数量
                BigDecimal score = goodsSoldAmount.multiply(BigDecimal.valueOf(0.00033)).add(BigDecimal.valueOf(goodsTradeCount));
                // 先查当前分数
                LiveRoom liveRoom = liveRoomService.getOne(Wrappers
                        .lambdaQuery(LiveRoom.class)
                        .select(LiveRoom::getScore)
                        .eq(LiveRoom::getId, roomId));
                // 如果算出来的分数更高再去更新
                if (score.compareTo(liveRoom.getScore()) > 0) {
                    liveRoomService.update(Wrappers.lambdaUpdate(LiveRoom.class).eq(LiveRoom::getId, roomId).set(LiveRoom::getScore, score));
                }
            } catch (Exception e) {
                log.error("更新直播间分数异常", e);
            } finally {
                lock.unlock();
            }
        });
    }

    /**
     * 递增数量
     *
     * @param roomId 房间 ID
     * @param incrementKey 要增加的键
     * @param delta 增加的数量
     */
    public void incrementCache(Long roomId, LiveRoomCacheKeys incrementKey, long delta) {
        String roomIdText = roomId.toString();
        String hashKey = buildKey(ROOM.getKey(), roomIdText);
        String fieldKey = incrementKey.getKey();

        LiveRoom liveRoom = null;
        if(!existsLiveRoom(roomId)){
            // 如果没有这个缓存，从数据库加载同步到缓存，关播后会清掉缓存，但是可以继续操作缓存
            liveRoom = loadFromDb(roomId);
        }
        // 试播不处理人数统计
        if(IGNORE_TEST_BROADCAST_KEYS.contains(incrementKey)){
            if(liveRoom == null){
                liveRoom = liveRoomMapper.selectOne(Wrappers.lambdaQuery(LiveRoom.class).eq(LiveRoom::getId, roomId).select(LiveRoom::getStartAt));
            }
            if(liveRoom == null || liveRoom.getStartAt().after(new Date())){
                return;
            }
        }

        try {
            // 如果有缓存的数量，通过lua做原子化的递增
            String script = """
                            local current = redis.call('HGET', KEYS[1], ARGV[1]);
                            current = current and tonumber(current) or 0;
                            local new = current + tonumber(ARGV[2])
                            if new < 0 then
                                new = 0
                            end;
                            redis.call('HSET', KEYS[1], ARGV[1], new);
                            return new;
                    """;
            stringRedisTemplate.execute(
                    new DefaultRedisScript<>(script, Long.class),
                    Collections.singletonList(hashKey),
                    fieldKey,
                    String.valueOf(delta)
            );
        } catch (Exception e) {
            log.error("更新直播间缓存异常", e);
        }
    }

    private LiveRoom loadFromDb(Long roomId) {
        HtbLock lock = htbLockService.getLock(buildKey(ROOM.getKey(), roomId.toString(), LOAD_CACHE_KEY.getKey()));
        lock.lock();
        LiveRoom liveRoom;
        try {
            liveRoom = liveRoomMapper.selectById(roomId);
            setLiveRoomAllCountValue(roomId, liveRoom);
        }finally {
            lock.unlock();
        }
        return liveRoom;
    }

    /**
     * 获取指定房间 ID 和键对应的数量的可选值方法
     *
     * @param roomId 房间 ID
     * @param key 缓存键
     * @return 数量的可选值
     */
    public Optional<Integer> getCountOptional(Long roomId, LiveRoomCacheKeys key) {
        // 调用获取值的可选值方法并指定类型为 Integer
        return getValueOptional(roomId, key, Integer.class);
    }

    /**
     * 获取指定房间 ID 和键对应的金额的可选值方法
     *
     * @param roomId 房间 ID
     * @param key 缓存键
     * @return 金额的可选值
     */
    public Optional<BigDecimal> getAmountOptional(Long roomId, LiveRoomCacheKeys key) {
        // 调用获取值的可选值方法并指定类型为 BigDecimal
        return getValueOptional(roomId, key, BigDecimal.class);
    }

    /**
     * 获取指定房间 ID、键和类型的值的可选值方法
     *
     * @param roomId 房间 ID
     * @param key 缓存键
     * @param clazz 类型
     * @return 值的可选值
     */
    public <T> Optional<T> getValueOptional(Long roomId, LiveRoomCacheKeys key, Class<T> clazz) {
        // 获取指定键的值
        Object value = stringRedisTemplate.opsForHash().get(buildKey(ROOM.getKey(), roomId.toString()), key.getKey());
        try {
            // 将值转换为指定类型并包装为可选值
            return Optional.ofNullable(Convert.convertQuietly(clazz, value, null));
        } catch (Exception e) {
            // 若转换异常则返回空可选值
            return Optional.empty();
        }
    }

    public void pushGoodsIds(Long roomId, Long...goodsId) {
        // 先取出原来的，转换成set，然后往里追加，再存回去
        Set<Long> goodsIds = getGoodsIds(roomId);
        goodsIds.addAll(Set.of(goodsId));
        stringRedisTemplate.opsForHash().put(buildKey(ROOM.getKey(), roomId.toString()), GOODS_IDS.getKey(), goodsIds.toString());
    }

    @SuppressWarnings("unchecked")
    public Set<Long> getGoodsIds(Long roomId) {
        // 先取出原来的，转换成set
        Object o = stringRedisTemplate.opsForHash().get(buildKey(ROOM.getKey(), roomId.toString()), GOODS_IDS.getKey());
        if (o == null) {
            return new HashSet<>();
        }
        return (Set<Long>) Convert.toCollection(Set.class, Long.class, o);
    }

    public void deleteGoodsId(Long roomId, Long...goodsId) {
        Set<Long> goodsIds = getGoodsIds(roomId);
        goodsIds.removeAll(Set.of(goodsId));
        stringRedisTemplate.opsForHash().put(buildKey(ROOM.getKey(), roomId.toString()), GOODS_IDS.getKey(), goodsIds.toString());
    }

    /**
     * 获取直播房间的方法
     *
     * @param roomId 房间 ID
     * @return 直播房间对象，如果不存在则返回 null
     */
    public LiveRoom getLiveRoom(Long roomId, String... ignoreProperty) {
        // 获取指定键的所有值
        Map<Object, Object> map = stringRedisTemplate.opsForHash().entries(buildKey(ROOM.getKey(), roomId.toString()));
        // 如果获取到的 map 为空
        if (MapUtil.isEmpty(map)) {
            return new LiveRoom();
        }
        // 将 map 转换为直播房间对象
        CopyOptions copyOptions = CopyOptions.create()
                .ignoreError()
                .ignoreNullValue()
                .setIgnoreProperties(ignoreProperty);
        LiveRoom room = BeanUtil.toBean(map, LiveRoom.class, copyOptions);
        room.setStreamStatus(null);
        room.setShowable(null);
        room.setFilterMode(null);
        room.setAnchorSeatId(null);
        room.setAnchorUserId(null);
        room.setIfTestBroadcast(null);
        // 成交金额和成交数量已修改为异步实时更新，为了兼容后台的取消成交
        room.setSoldCount(null);
        room.setSoldAmount(null);
        // 其他仍然是缓存
        room.setGoodsCount(room.getGoodsCount() == null ? 0 : room.getGoodsCount());
        room.setViewCount(room.getViewCount() == null ? 0 : room.getViewCount());
        room.setSubscribeCount(room.getSubscribeCount() == null ? 0 : room.getSubscribeCount());
        room.setUnsoldCount(room.getUnsoldCount() == null ? 0 : room.getUnsoldCount());
        room.setRealMaxCount(room.getRealMaxCount() == null ? 0 : room.getRealMaxCount());
        room.setRealCount(room.getRealCount() == null ? 0 : room.getRealCount());
        return room;
    }

    /**
     * 删除直播房间的方法
     *
     * @param roomId 房间 ID
     */
    public void deleteLiveRoom(Long roomId) {
        stringRedisTemplate.delete(buildKey(ROOM.getKey(), roomId.toString()));
    }

    /**
     * 更新直播间各个数量的缓存，会直接替换原有的缓存
     *
     * @param roomId    直播间ID
     * @param liveRoom  直播间
     */
    public void setLiveRoomAllCountValue(Long roomId, LiveRoom liveRoom) {
        if (roomId == null || liveRoom == null) {
            throw new ServiceException("房间ID和直播间不能同时为null");
        }

        // 检查缓存中是否已存在该直播间的信息
        // 注意：如果缓存中已存在直播间信息，这里不会更新
        if(existsLiveRoom(roomId)){
            log.info("直播间{}缓存存在，不进行更新", roomId);
            return;
        }

        Map<String, String> propertiesToCache = new LinkedHashMap<>();
        for (Map.Entry<LiveRoomCacheKeys, Function<LiveRoom, ?>> entry : PROPERTY_GETTERS.entrySet()) {
            Object value = entry.getValue().apply(liveRoom);
            if (value != null) {
                propertiesToCache.put(entry.getKey().getKey(), value.toString());
            }
        }

        // 加载缓存到直播间，过期时间为 直播结束时间 + 7天
        String redisKey = buildKey(ROOM.getKey(), roomId.toString());
        long expireSeconds = DateUtil.between(new Date(), liveRoom.getEndAt(), DateUnit.SECOND) + LiveGoodsCacheConstant.DEFAULT_EXPIRE_TIME;
        stringRedisTemplate.opsForHash().putAll(redisKey, propertiesToCache);
        stringRedisTemplate.expire(redisKey, Duration.ofSeconds(expireSeconds));
    }

    /**
     * 辅助方法：从getter函数获取属性名
     */
    private String getPropertyName(Function<LiveRoom, Object> getter, LiveRoom liveRoom) {
        if (getter == null || liveRoom == null) {
            return null;
        }

        BeanWrapper beanWrapper = PropertyAccessorFactory.forBeanPropertyAccess(liveRoom);
        PropertyDescriptor[] descriptors = beanWrapper.getPropertyDescriptors();
        for (PropertyDescriptor descriptor : descriptors) {
            log.info("descriptor: {}", descriptor.getName());
            if (descriptor.getReadMethod() != null) {
                String propertyName = descriptor.getName();
                log.info("propertyName: {}", propertyName);
                Object propertyValue = beanWrapper.getPropertyValue(propertyName);
                if (Objects.equals(propertyValue, getter.apply(liveRoom))) {
                    return propertyName;
                }
            }
        }

        return null;
    }

    // 检查直播间的key是否存在，如果不存在，要查询直播间数据，再更新到缓存，注意加个redis锁
    public boolean existsLiveRoom(Long liveRoomId) {
        String redisKey = buildKey(ROOM.getKey(), liveRoomId.toString());
        return Boolean.TRUE.equals(stringRedisTemplate.hasKey(redisKey));
    }

    /**
     * 记录进入直播间的人员
     *
     * @param roomId 房间 ID
     * @param seatId 用户seatId
     */
    @SuppressWarnings("all")
    public void addRoomUser(Long roomId, Long seatId) {
        String redisKey = buildKey(ROOM_USER.getKey(), roomId.toString());
        boolean create = !redisTemplate.hasKey(redisKey);

        redisTemplate.opsForList().rightPush(redisKey, seatId);
        if(create){
            redisTemplate.expire(redisKey, 7, TimeUnit.DAYS);
        }
    }

    /**
     * 移除进入直播间的人员
     * @param roomId
     * @param seatId
     */
    @SuppressWarnings("all")
    public void removeRoomUser(Long roomId, Long seatId) {
        String redisKey = buildKey(ROOM_USER.getKey(), roomId.toString());
        redisTemplate.opsForList().remove(redisKey, 0, seatId);
    }
}
