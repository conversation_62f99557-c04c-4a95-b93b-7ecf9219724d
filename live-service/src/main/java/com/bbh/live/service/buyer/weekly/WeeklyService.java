package com.bbh.live.service.buyer.weekly;

import com.bbh.live.dao.dto.QueryWeeklyDTO;
import com.bbh.live.dao.dto.QueryWeeksDTO;
import com.bbh.live.dao.dto.vo.WeeklySpuVO;
import com.bbh.live.dao.dto.vo.WeeklyVO;
import com.bbh.live.dao.dto.vo.WeeksVO;

import java.util.List;

/**
 * 行情周报 Service
 * <AUTHOR>
 */
public interface WeeklyService {

    /**
     * 查询行情周报
     *
     * @param queryWeeklyDTO 查询参数
     * @return 分页后的周报列表
     */
    WeeklyVO getWeekly(QueryWeeklyDTO queryWeeklyDTO);

    /**
     * 查询历史周报的筛选：年月周
     * @param queryWeeksDTO 查询参数
     * @return 年、月、周列表
     */
    List<WeeksVO> getWeeks(QueryWeeksDTO queryWeeksDTO);

    /**
     * 查询SPU的最近成交
     *
     * @param weeklySpuId spu的id
     * @return 最近成交记录
     */
    WeeklySpuVO getWeeklySpu(Long weeklySpuId);

    /**
     * 查询收到的行情周报数量
     * @param vipId 会员id
     * @return 收到的行情周报数量
     */
    Long getWeeklyCount(Long vipId);
}
