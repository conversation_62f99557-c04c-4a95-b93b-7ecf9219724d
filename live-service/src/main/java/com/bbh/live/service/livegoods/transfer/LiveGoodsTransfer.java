package com.bbh.live.service.livegoods.transfer;

import com.bbh.live.dao.dto.livegoods.LiveGoodsDTO;
import com.bbh.live.service.livegoods.context.LiveGoodsContext;

/**
 * <AUTHOR> dsy
 * @Date: 2024/7/29
 * @Description:
 */
public interface LiveGoodsTransfer<T extends LiveGoodsContext> {

    /**
     * 商品流转校验
     * @param liveGoodsDTO
     * @param context
     */
    void check(LiveGoodsDTO liveGoodsDTO, T context);

    /**
     * 商品状态转移需要执行的动作，比如入库，缓存
     * @param liveGoodsDTO
     * @param context
     */
    void action(LiveGoodsDTO liveGoodsDTO, T context);
}
