package com.bbh.live.service.livegoods.cache.redis;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import com.bbh.live.dao.service.LiveRoomService;
import com.bbh.live.service.livegoods.cache.LiveGoodsCacheConstant;
import com.bbh.live.service.redis.RedisHashService;
import com.bbh.model.LiveRoom;
import jakarta.annotation.Resource;
import org.jetbrains.annotations.NotNull;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;

import java.util.Date;

/**
 * <AUTHOR> dsy
 * @Date: 2024/7/27
 * @Description:
 */
public abstract class BaseRedisLiveGoodsCache implements ApplicationContextAware {


    @Resource
    protected RedisHashService redisHashService;
    @Resource
    protected RedissonClient redisson;
    @Resource
    protected LiveRoomService liveRoomService;

    protected ApplicationContext applicationContext;

    protected String builderKey(String category, String key) {
        return category + ":" + key;
    }


    protected Long getLiveRoomEndLeft(Long liveRoomId) {
        //直播结束时间不确定，这里过期时间默认给7天，直播结束后需要清理缓存
        LiveRoom liveRoom = liveRoomService.lambdaQuery().eq(LiveRoom::getId, liveRoomId)
                .select(LiveRoom::getEndAt)
                .one();
        if(liveRoom == null){
            return LiveGoodsCacheConstant.DEFAULT_EXPIRE_TIME;
        } else {
            return DateUtil.between(new Date(), liveRoom.getEndAt(), DateUnit.SECOND) + LiveGoodsCacheConstant.DEFAULT_EXPIRE_TIME;
        }
    }

    @Override
    public void setApplicationContext(@NotNull ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }
}
