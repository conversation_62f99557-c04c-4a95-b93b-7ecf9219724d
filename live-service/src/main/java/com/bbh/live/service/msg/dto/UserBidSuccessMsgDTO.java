package com.bbh.live.service.msg.dto;

import com.bbh.live.core.msg.MsgType;
import com.bbh.live.service.livegoods.cache.info.LiveGoodsAuctionBidInfo;
import com.bbh.live.service.msg.dto.base.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;

/**
 * 用户出价成功 的消息
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
public class UserBidSuccessMsgDTO extends BaseAuctionLimitMsg implements IMsg {

    /**
     * 商品信息
     */
    private BaseGoods goods;

    /**
     * 成交人信息
     */
    private BaseSeat user;

    /**
     * 出价金额
     */
    private BigDecimal bidAmount;

    /**
     * 竞拍剩余时间 单位：秒
     */
    private Long remainTime;

    /**
     * 竞拍剩余时间 单位：毫秒
     */
    private Long remainTimeMs;

    /**
     * 该商品出过价的人的席位ID集合
     */
    private List<LiveGoodsAuctionBidInfo> biddenList;

    /**
     * 试播客服id集合
     */
    private List<Long> testBroadcastSeatIdList;

    /**
     * 直播间是否正在试播
     */
    private Boolean ifTestBroadcast;

    private Integer tradeType;

    /**
     * 每个消息对应一个类型
     *
     * @return {@link MsgType}
     */
    @Override
    public String type() {
        return MsgType.BID_SUCCESS;
    }
}
