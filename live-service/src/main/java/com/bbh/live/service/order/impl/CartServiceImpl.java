package com.bbh.live.service.order.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bbh.base.ListBase;
import com.bbh.enums.GlobalBizTypeEnum;
import com.bbh.enums.LiveGoodsCancelStatusEnum;
import com.bbh.enums.LiveGoodsStatusEnum;
import com.bbh.live.constant.RedisKey;
import com.bbh.live.dao.dto.LiveShoppingCartDTO;
import com.bbh.live.dao.dto.LiveShoppingCartGroupVO;
import com.bbh.live.dao.dto.QueryShoppingCartDTO;
import com.bbh.live.dao.dto.SellerCashierDeskDTO;
import com.bbh.live.dao.dto.vo.CashierDeskVO;
import com.bbh.live.dao.dto.vo.SellerCashierDeskVO;
import com.bbh.live.dao.mapper.GlobalOrganizationMapper;
import com.bbh.live.dao.mapper.LiveGoodsMapper;
import com.bbh.live.enums.LiveOrderCacheKeys;
import com.bbh.live.enums.PermissionCheckTypeEnum;
import com.bbh.live.enums.PermissionCodeEnum;
import com.bbh.live.enums.RedPointTypeEnum;
import com.bbh.live.service.order.CartService;
import com.bbh.live.service.user.permission.PermissionCheckServiceFactory;
import com.bbh.model.GlobalOrganization;
import com.bbh.model.LiveGoods;
import com.bbh.secure.AuthUtil;
import com.bbh.util.AssertUtil;
import com.bbh.vo.AuthUser;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 购物车业务的Service
 * <AUTHOR>
 */
@Slf4j
@AllArgsConstructor
@Service
public class CartServiceImpl implements CartService, RedisKey {

    private final LiveGoodsMapper liveGoodsMapper;
    private final GlobalOrganizationMapper globalOrganizationMapper;
    private final StringRedisTemplate stringRedisTemplate;

    /**
     * 获取买手的购物车商品数量
     *
     * @return 购物车商品数量
     */
    @Override
    public Long getBuyerCartCount() {
        AuthUser user = AuthUtil.getUser();
        // 有全店权限，显示全店，没有权限，显示我的
        boolean hasPermission = PermissionCheckServiceFactory.getPermissionCheckService(PermissionCheckTypeEnum.PERMISSION_CHECK_TYPE_LIVE).hasPermissionByPermissionCode(PermissionCodeEnum.LIVE_CHECKOUT_CART);

        QueryShoppingCartDTO query = new QueryShoppingCartDTO();
        if (hasPermission) {
            // 全店的=购买人商户是我的商户
            query.setBuyerOrgId(user.getOrgId());
        } else {
            // 我的=购买人席位是我
            query.setBuyerSeatId(user.getSeatId());
        }
        List<LiveShoppingCartDTO> shoppingCartList = liveGoodsMapper.selectLiveShoppingCartList(query);
        return (long) shoppingCartList.size();
    }

    /**
     * 获取我的收银台，分为全店收银台和我的收银台 <br>
     * 按直播场次进行分组，分组的logo是商户图片 <br>
     * 全店收银台显示成交人，我的收银台不显示
     *
     * @return 收银台信息
     */
    @Override
    public CashierDeskVO getCashierDeskInfo(boolean ifOrg) {
        var user = AuthUtil.getUser();

        CashierDeskVO vo = new CashierDeskVO();

        // 查出全部商品
        QueryShoppingCartDTO query = new QueryShoppingCartDTO();
        query.setBuyerSeatId(ifOrg ? null : user.getSeatId());
        query.setBuyerOrgId(ifOrg ? user.getOrgId() : null);
        List<LiveShoppingCartDTO> shoppingCartList = liveGoodsMapper.selectLiveShoppingCartList(query);
        List<LiveShoppingCartGroupVO> normalList = groupingShoppingCart(shoppingCartList);
        vo.setListWaitPayInfo(normalList);

        // 创建时间超过24小时的要单独拎出来
        List<LiveShoppingCartDTO> timeoutList = shoppingCartList.stream().filter(item -> {
            var createdAt = item.getCreatedAt();
            return DateUtil.between(createdAt, new Date(), DateUnit.HOUR) >= 24;
        }).toList();
        List<LiveShoppingCartGroupVO> timeoutGroupList = groupingShoppingCart(timeoutList);
        vo.setListTimeOutWaitPayInfo(timeoutGroupList);
        vo.setTimeOutCount(timeoutList.size());

        // 更新未读缓存
        String redisBaseKey = ifOrg ? LiveOrderCacheKeys.SHOP_CENTER_RED_POINT.getKey() : LiveOrderCacheKeys.PERSONAL_CENTER_RED_POINT.getKey();
        String redisSuffixKey = ifOrg ? user.getOrgId().toString() : user.getSeatId().toString();
        String redisKey = buildKey(redisBaseKey, RedPointTypeEnum.purchaseCashierProductCount.toString(), redisSuffixKey);
        stringRedisTemplate.opsForValue().set(redisKey, DateUtil.now());

        return vo;
    }

    /**
     * 获取收银台角标数量，微商获取自己的商品数量，企业获取全店商品数量
     *
     * @return 商品数量
     */
    @Override
    public Long getCashierDeskCount() {
        var user = AuthUtil.getUser();
        var orgId = user.getOrgId();
        GlobalOrganization organization = globalOrganizationMapper.selectById(orgId);
        AssertUtil.assertNotNull(organization, "商家不存在");
        // 身份1 微商 2企业
        boolean ifOrg = Objects.equals(2, organization.getType());

        // 检查是否有查看全店收银台的权限
        boolean hasPermissionByPermissionCode = PermissionCheckServiceFactory.getPermissionCheckService(PermissionCodeEnum.LIVE_CHECKOUT_CART.getTypeEnum()).hasPermissionByPermissionCode(PermissionCodeEnum.LIVE_CHECKOUT_CART);

        // 组装查询条件
        QueryShoppingCartDTO query = new QueryShoppingCartDTO();
        query.setBizType(GlobalBizTypeEnum.LIVE.getCode());
        if (ifOrg && hasPermissionByPermissionCode) {
            query.setBuyerOrgId(orgId);
        } else {
            query.setBuyerSeatId(user.getSeatId());
        }
        List<LiveShoppingCartDTO> list = liveGoodsMapper.selectLiveShoppingCartList(query);
        return list == null ? 0L : list.size();
    }

    /**
     * 收银台商品按直播场次进行分组
     * @param shoppingCartList 购物车列表
     * @return  分组后的列表
     */
    @Override
    public List<LiveShoppingCartGroupVO> groupingShoppingCart(List<LiveShoppingCartDTO> shoppingCartList) {
        // 按直播场次进行分组
        return shoppingCartList.stream()
                .collect(Collectors.groupingBy(LiveShoppingCartDTO::getLiveRoomId))
                .entrySet().stream()
                .map(entry -> {
                    var liveRoomId = entry.getKey();
                    var goodsList = entry.getValue();
                    var firstItem = goodsList.getFirst();
                    return new LiveShoppingCartGroupVO()
                            .setLiveRoomId(liveRoomId)
                            .setOrgId(firstItem.getSellerOrgId())
                            .setOrgName(firstItem.getOrgName())
                            .setLogoUrl(firstItem.getLogoUrl())
                            .setRoomName(firstItem.getRoomName())
                            .setIfSpecial(firstItem.getIfSpecial())
                            .setStreamStatus(firstItem.getStreamStatus())
                            .setGoodsList(goodsList)
                            .setStartAt(firstItem.getRoomStartAt())
                            .setTotalGoodsCount(goodsList.size())
                            .setTotalGoodsPrice(goodsList.stream()
                                    .map(x -> Objects.requireNonNullElse(x.getSellPrice(), BigDecimal.ZERO))
                                    .reduce(BigDecimal.ZERO, BigDecimal::add)
                            );
                })
                // 按直播间开始时间倒序，测试时发现时间是null，因为直播间被删除了，这里做兼容
                .sorted(Comparator.comparing(
                        LiveShoppingCartGroupVO::getStartAt,
                        Comparator.nullsFirst(Comparator.naturalOrder())
                ).reversed())
                .toList();
    }

    @Override
    public SellerCashierDeskVO getSellerCashierDeskInfo(SellerCashierDeskDTO sellerCashierDeskDTO) {
        AuthUser user = AuthUtil.getUser();
        var orgId = user.getOrgId();

        // 更新未读缓存
        String redisKey = buildKey(LiveOrderCacheKeys.SHOP_CENTER_RED_POINT.getKey(), RedPointTypeEnum.saleCashierProductCount.toString(), orgId.toString());
        stringRedisTemplate.opsForValue().set(redisKey, DateUtil.now());

        // 先按成交商家分页
        Page<LiveGoods> orgPage = liveGoodsMapper.selectPage(
                Page.of(sellerCashierDeskDTO.getCurrentPage(), sellerCashierDeskDTO.getPerPage()),
                Wrappers.lambdaQuery(LiveGoods.class)
                        // 仅查询成交主体id
                        .select(LiveGoods::getBelongOrgId)
                        // 排除掉脏数据
                        .ne(LiveGoods::getBelongOrgId, 0)
                        .isNotNull(LiveGoods::getBelongOrgId)
                        // 只查售出商家id
                        .eq(true, LiveGoods::getOrgId, orgId)
                        // 关键字查询，关联ERP货品名称
                        .exists(StrUtil.isNotBlank(sellerCashierDeskDTO.getKeywords()),
                                """
                                SELECT 1 FROM erp_goods eg 
                                WHERE eg.id = live_goods.global_goods_id 
                                AND eg.deleted_at IS NULL 
                                AND eg.org_id = {1} 
                                AND eg.name LIKE CONCAT('%', {0}, '%') 
                                """, sellerCashierDeskDTO.getKeywords(), orgId)
                        // 成交时间开始
                        .ge(Objects.nonNull(sellerCashierDeskDTO.getOrderAtStart()), LiveGoods::getEndAt, sellerCashierDeskDTO.getOrderAtStart())
                        // 成交时间结束
                        .le(Objects.nonNull(sellerCashierDeskDTO.getOrderAtEnd()), LiveGoods::getEndAt, sellerCashierDeskDTO.getOrderAtEnd())
                        // 仅查询成交商品
                        .eq(LiveGoods::getGoodsStatus, LiveGoodsStatusEnum.TRADED)
                        // 排除掉已取消成交的
                        .ne(LiveGoods::getCancelStatus, LiveGoodsCancelStatusEnum.APPROVAL)
                        // 直播间id列表
                        .in(CollUtil.isNotEmpty(sellerCashierDeskDTO.getLiveRoomIdList()), LiveGoods::getLiveRoomId, sellerCashierDeskDTO.getLiveRoomIdList())
                        // 排除掉创建过订单的
                        .notExists("""
                            SELECT 1 FROM global_order_item goi 
                            WHERE goi.biz_goods_id = live_goods.id 
                            AND goi.deleted_at IS NULL 
                            AND goi.biz_type = 30 
                            AND goi.order_status != 60 
                        """)
                        // 按成交人主体分组
                        .groupBy(LiveGoods::getBelongOrgId)
        );

        if (CollUtil.isEmpty(orgPage.getRecords())) {
            return new SellerCashierDeskVO();
        }

        // 取出成交人ID列表
        List<Long> belongOrgIdList = orgPage.getRecords().stream().map(LiveGoods::getBelongOrgId).toList();

        // 查出全部商品
        QueryShoppingCartDTO query = new QueryShoppingCartDTO();
        query.setBizType(GlobalBizTypeEnum.LIVE.getCode());
        query.setSellerOrgId(orgId);
        query.setGlobalGoodsName(sellerCashierDeskDTO.getKeywords());
        query.setLiveSellTimeStart(sellerCashierDeskDTO.getOrderAtStart());
        query.setLiveSellTimeEnd(sellerCashierDeskDTO.getOrderAtEnd());
        query.setBuyerOrgIdList(belongOrgIdList);
        List<LiveShoppingCartDTO> shoppingCartList = liveGoodsMapper.selectLiveShoppingCartList(query);
        List<LiveShoppingCartGroupVO> groupingList = groupingShoppingCartByBuyer(shoppingCartList);
        SellerCashierDeskVO vo = new SellerCashierDeskVO();
        vo.setCurrentPage(orgPage.getCurrent()).setTotal(orgPage.getTotal()).setRecords(groupingList).setPerPage(orgPage.getSize());
        return vo;
    }

    private List<LiveShoppingCartGroupVO> groupingShoppingCartByBuyer(List<LiveShoppingCartDTO> shoppingCartList) {
        // 按直播场次进行分组
        return shoppingCartList.stream()
                .filter(item -> item.getBuyerOrgId() != null)
                .collect(Collectors.groupingBy(LiveShoppingCartDTO::getBuyerOrgId))
                .entrySet().stream()
                .map(entry -> {
                    var liveRoomId = entry.getKey();
                    var goodsList = entry.getValue();
                    var firstItem = goodsList.getFirst();
                    return new LiveShoppingCartGroupVO()
                            .setLiveRoomId(liveRoomId)
                            .setOrgId(firstItem.getBuyerOrgId())
                            .setOrgName(firstItem.getBuyerOrgName())
                            .setLogoUrl(firstItem.getBuyerOrgLogoUrl())
                            .setRoomName(firstItem.getRoomName())
                            .setIfSpecial(firstItem.getIfSpecial())
                            .setStreamStatus(firstItem.getStreamStatus())
                            .setGoodsList(goodsList)
                            .setStartAt(firstItem.getRoomStartAt())
                            .setTotalGoodsCount(goodsList.size())
                            .setTotalGoodsPrice(goodsList.stream()
                                    .map(x -> Objects.requireNonNullElse(x.getSellPrice(), BigDecimal.ZERO))
                                    .reduce(BigDecimal.ZERO, BigDecimal::add)
                            );
                })
                // 按直播间开始时间倒序，测试时发现时间是null，因为直播间被删除了，这里做兼容
                .sorted(Comparator.comparing(
                        LiveShoppingCartGroupVO::getStartAt,
                        Comparator.nullsFirst(Comparator.naturalOrder())
                ).reversed())
                .toList();
    }

    /**
     * 商家待结算，根据商品分组
     */
    @Override
    public ListBase<LiveShoppingCartDTO> getSellerCashierGoodsPageList(SellerCashierDeskDTO sellerCashierDeskDTO) {
        AuthUser user = AuthUtil.getUser();
        var orgId = user.getOrgId();

        // 更新未读缓存
        Integer orderStatus = sellerCashierDeskDTO.getOrderStatus();
        RedPointTypeEnum countType = RedPointTypeEnum.saleCashierProductCount;
        if (orderStatus != null) {
            switch (orderStatus) {
                case 10 ->countType= RedPointTypeEnum.salePendingPaymentCount;
                case 20 ->countType= RedPointTypeEnum.saleOfflineTransferPendingReviewCount;
            }

        }


        String redisKey = buildKey(LiveOrderCacheKeys.SHOP_CENTER_RED_POINT.getKey(), countType.toString(), orgId.toString());
        stringRedisTemplate.opsForValue().set(redisKey, DateUtil.now());

        // 查出全部商品
        QueryShoppingCartDTO query = new QueryShoppingCartDTO();
        query.setBizType(GlobalBizTypeEnum.LIVE.getCode());
        query.setSellerOrgId(orgId);
        query.setGlobalGoodsName(sellerCashierDeskDTO.getKeywords());
        query.setLiveSellTimeStart(sellerCashierDeskDTO.getOrderAtStart());
        query.setLiveSellTimeEnd(sellerCashierDeskDTO.getOrderAtEnd());
        query.setBizIdList(sellerCashierDeskDTO.getLiveRoomIdList());
        query.setOrderBy("lg.belong_org_id asc, lg.end_at desc");
        query.setOrderStatus(orderStatus);
//        Page<LiveShoppingCartDTO> pageList = liveGoodsMapper.selectLiveShoppingCartList(
//                Page.of(sellerCashierDeskDTO.getCurrentPage(), sellerCashierDeskDTO.getPerPage()),
//                query
//        );
        Page<LiveShoppingCartDTO> pageList = liveGoodsMapper.selectSellerLiveShoppingCartList(
                Page.of(sellerCashierDeskDTO.getCurrentPage(), sellerCashierDeskDTO.getPerPage()),
                query
        );

        return ListBase.pageConvertToListBase(pageList);
    }
}
