package com.bbh.live.service.buyerCancel.dto;

import com.bbh.model.LiveGoodsBuyerCancelRecord;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 买家取消记录详情扩展实体类
 *
 * <AUTHOR>
 * @since 2025-01-11
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class LiveGoodsBuyerCancelRecordDetailDTO extends LiveGoodsBuyerCancelRecord {

    /**
     * 销售价格
     */
    private BigDecimal sellPrice;

    /**
     * 销售时间
     */
    private Date sellAt;

    /**
     * 所属席位ID
     */
    private Long belongSeatId;

    /**
     * 所属用户ID
     */
    private Long belongUserId;

    /**
     * 所属组织ID
     */
    private Long belongOrgId;

    /**
     * 所属类型
     */
    private Integer belongType;

    /**
     * 交易类型
     */
    private Integer tradeType;

    /**
     * 直播商品编码
     */
    private Integer liveGoodsCode;

    /**
     * 直播视频URL
     */
    private String liveVideoUrl;

    private List<String> videoUrlList;

    /**
     * 商品名称
     */
    private String goodsName;

    /**
     * 商品图片列表
     */
    private String imgUrlList;

    /**
     * 全局创建时间
     */
    private Date globalCreatedAt;

    /**
     * 同行价格
     */
    private BigDecimal peerPrice;

    /**
     * 商品质量
     */
    private String quality;

    /**
     * 所属显示名称
     */
    private String belongShowName;

    /**
     * 所属名称
     */
    private String belongName;

    /**
     * 所属昵称
     */
    private String belongNickName;

    /**
     * 所属拍卖编码
     */
    private String belongAuctionCode;

    /**
     * 所属头像
     */
    private String belongAvatar;

    /**
     * 卖家组织名称
     */
    private String sellerOrgName;

    /**
     * 卖家组织Logo URL
     */
    private String sellerLogoUrl;

    /**
     * 买家组织名称
     */
    private String buyerOrgName;

    /**
     * 买家组织Logo URL
     */
    private String buyerLogoUrl;

    private String liveRoomName;

    private Date liveRoomStartAt;
}