package com.bbh.live.service.msg.support;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.bbh.enums.LiveOrgFilterModeEnum;
import com.bbh.enums.LiveRoomFilterModeEnum;
import com.bbh.live.dao.mapper.LiveOrgFilterMapper;
import com.bbh.live.dao.mapper.LiveRoomMapper;
import com.bbh.live.service.msg.dto.base.BaseAuctionLimitMsg;
import com.bbh.model.LiveOrgFilter;
import com.bbh.model.LiveRoom;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * 直播间限制处理器
 * <AUTHOR>
 */
@Service
@Slf4j
@AllArgsConstructor
public class LiveRoomLimitProcessor {

    private final LiveOrgFilterMapper liveOrgFilterMapper;
    private final LiveRoomMapper liveRoomMapper;

    public <T extends BaseAuctionLimitMsg> void fillLimitMsg(T t) {
        if (t == null || t.getLiveRoomId() == null) {
            return;
        }

        // 先查直播间的模式
        LiveRoom liveRoom = liveRoomMapper.selectById(t.getLiveRoomId());
        if (liveRoom == null) {
            return;
        }
        Long orgId = liveRoom.getOrgId();
        LiveRoomFilterModeEnum filterMode = liveRoom.getFilterMode();

        // 处理filterMode
        if (filterMode != null) {
            BaseAuctionLimitMsg.AuctionLimitUserMode limitMode = BaseAuctionLimitMsg.AuctionLimitUserMode.from(filterMode);
            if (limitMode != null) {
                t.setAuctionLimitUserMode(limitMode.getCode());
            }
        }

        // 再根据模式查白名单或者查黑名单
        List<LiveOrgFilter> filterList = liveOrgFilterMapper.selectList(Wrappers.lambdaQuery(LiveOrgFilter.class)
                .select(LiveOrgFilter::getUserId, LiveOrgFilter::getOrgId, LiveOrgFilter::getFilterMode)
                .eq(LiveOrgFilter::getOrgId, orgId)
        );

        if (filterList == null || filterList.isEmpty()) {
            // 设置空列表而不是null
            t.setBlacklist(List.of());
            t.setWhitelist(List.of());
            return;
        }

        // 白名单
        List<Long> whiteList = filterList.stream()
                .filter(x -> x != null && LiveOrgFilterModeEnum.WHITE_LIST.equals(x.getFilterMode()))
                .map(LiveOrgFilter::getUserId)
                .filter(Objects::nonNull)
                .toList();

        // 黑名单
        List<Long> blackList = filterList.stream()
                .filter(x -> x != null && LiveOrgFilterModeEnum.BLACKLIST.equals(x.getFilterMode()))
                .map(LiveOrgFilter::getUserId)
                .filter(Objects::nonNull)
                .toList();

        // 赋值
        t.setBlacklist(blackList);
        t.setWhitelist(whiteList);
    }

}
