package com.bbh.live.service.livegoods.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.bbh.base.ListBase;
import com.bbh.enums.GlobalBizTypeEnum;
import com.bbh.enums.LiveGoodsStatusEnum;
import com.bbh.enums.LiveRoomStreamStatusEnum;
import com.bbh.exception.ServiceException;
import com.bbh.feign.dto.LiveRecordDTO;
import com.bbh.feign.dto.RecordVideoDTO;
import com.bbh.live.config.LiveServiceProperties;
import com.bbh.live.controller.req.LiveGoodsQueryReq;
import com.bbh.live.controller.req.PlaybackGoodsQueryReq;
import com.bbh.live.dao.dto.livegoods.LiveGoodsDTO;
import com.bbh.live.dao.dto.vo.LiveRoomPlaybackGoodsVO;
import com.bbh.live.dao.dto.vo.LiveRoomToCeCheckVO;
import com.bbh.live.dao.mapper.CeShoppingCartMapper;
import com.bbh.live.dao.mapper.LiveGoodsMapper;
import com.bbh.live.dao.service.LiveGoodsService;
import com.bbh.live.dao.service.LiveRoomService;
import com.bbh.live.service.livegoods.LiveGoodsPlaybackService;
import com.bbh.live.service.room.LiveStreamService;
import com.bbh.live.thread.ThreadPoolManager;
import com.bbh.live.thread.VirtualRunner;
import com.bbh.model.CeShoppingCart;
import com.bbh.model.LiveGoods;
import com.bbh.model.LiveRoom;
import com.bbh.secure.AuthUtil;
import com.bbh.util.AssertUtil;
import com.bbh.util.EncryptUtil;
import com.bbh.util.LogExUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;

/**
 * 商品回放清单
 * <AUTHOR>
 */
@Service
@AllArgsConstructor
@Slf4j
public class LiveGoodsPlaybackServiceImpl implements LiveGoodsPlaybackService {

    private final LiveGoodsService liveGoodsService;
    private final LiveStreamService liveStreamService;
    private final CeShoppingCartMapper ceShoppingCartMapper;
    private final LiveGoodsMapper liveGoodsMapper;
    private final LiveServiceProperties liveServiceProperties;
    private final LiveRoomService liveRoomService;

    @Override
    public ListBase<LiveRoomPlaybackGoodsVO> getLiveGoodsList(PlaybackGoodsQueryReq queryReq) {
        queryReq.parseKeywords();
        var playbackLiveGoodsList = liveGoodsMapper.getPlaybackLiveGoodsList(queryReq, AuthUtil.getSeatId(), AuthUtil.getOrgId());
        playbackLiveGoodsList.forEach(goods -> {
            // ERP货品ID加密处理
            if (Objects.nonNull(goods.getGlobalGoodsId())) {
                goods.setGlobalEncryptGoodsId(EncryptUtil.encrypt(goods.getGlobalGoodsId().toString()));
            }
        });
        return new ListBase<>(playbackLiveGoodsList, (long) playbackLiveGoodsList.size(), null, null);
    }


    /**
     * 移出云展的购物车
     *
     * @param erpGoodsId ERP货品ID
     */
    @Override
    public void removeCeShoppingCart(Long erpGoodsId) {
        ceShoppingCartMapper.delete(Wrappers.lambdaQuery(CeShoppingCart.class)
                .eq(CeShoppingCart::getBizType, GlobalBizTypeEnum.CE)
                .eq(CeShoppingCart::getGoodsId, erpGoodsId)
        );
    }

    @Override
    public String getLiveVideoUrl(Long liveGoodsId) {
        AssertUtil.assertNotNull(liveGoodsId, "参数异常，请联系管理员");

        LiveGoods liveGoods = liveGoodsService.getById(liveGoodsId);
        AssertUtil.assertNotNull(liveGoods, "直播商品已失效，无法回放");
        AssertUtil.assertTrue(liveGoods.getPutawayAt() != null, "商品未在直播上架过，暂无回放视频");

        // 如果还没到上架时间+5分钟，就抛出业务异常提示
        LocalDateTime putawayTime = LocalDateTimeUtil.of(liveGoods.getPutawayAt());
        LocalDateTime fiveMinutesAfter = putawayTime.plusMinutes(liveServiceProperties.getGoodsVideoCatchTime());
        if (LocalDateTime.now().isBefore(fiveMinutesAfter)) {
            throw new ServiceException("回放正在生成中，请稍后查看");
        }

        // 如果过了时间，但是没有地址，就获取回放地址
        if (StrUtil.isBlank(liveGoods.getLiveVideoUrl())) {
            return fetchPlaybackUrl(liveGoodsId);
        }

        // 已经有地址，直接返回
        return liveGoods.getLiveVideoUrl();
    }

    /**
     * 获取商品回放地址
     *
     * @param liveGoodsId 商品ID
     * @return 回放地址
     */
    @Override
    public String fetchPlaybackUrl(Long liveGoodsId) {
        AssertUtil.assertNotNull(liveGoodsId, "参数异常，请联系管理员");

        LiveGoods liveGoods = liveGoodsService.getById(liveGoodsId);
        AssertUtil.assertNotNull(liveGoods, "直播商品已失效，无法回放");
        AssertUtil.assertTrue(liveGoods.getPutawayAt() != null, "商品未在直播上架过，暂无回放视频");

        // 已经有地址，直接返回
        if (StrUtil.isNotBlank(liveGoods.getLiveVideoUrl())) {
            return liveGoods.getLiveVideoUrl();
        }

        // 商品回放的开始时间，取上架时间前5分钟
        LocalDateTime putAwayAt = LocalDateTimeUtil.of(liveGoods.getPutawayAt());
        LocalDateTime putAwayAtBefore = putAwayAt.minusMinutes(liveServiceProperties.getGoodsVideoCatchTime());
        String putAwayAtDate = putAwayAtBefore.format(DateTimeFormatter.ofPattern(DatePattern.NORM_DATETIME_PATTERN));

        // 商品回放的结束时间，取上架时间的后5分钟，没错，需求是上架时间，不是结束时间
        LocalDateTime endAtAfter = putAwayAt.plusMinutes(liveServiceProperties.getGoodsVideoCatchTime());
        String endAtDate = endAtAfter.format(DateTimeFormatter.ofPattern(DatePattern.NORM_DATETIME_PATTERN));

        // 组装请求对象，向中台请求录像地址
        LiveRecordDTO liveRecordDTO = new LiveRecordDTO();
        liveRecordDTO.setLiveId(liveGoods.getLiveRoomId().toString());
        liveRecordDTO.setStartTime(putAwayAtDate);
        liveRecordDTO.setEndTime(endAtDate);
        liveRecordDTO.setRecordType(1);
        RecordVideoDTO indexFile = liveStreamService.getVideoRecord(liveRecordDTO);
        if (indexFile == null) {
            LogExUtil.errorLog("获取商品回放地址失败， liveRecordDTO = " + JSONUtil.toJsonStr(liveRecordDTO), null);
            // 没有地址不能报错，要给空字符串，客户端会做对应逻辑处理
            return "";
        }

        // 更新该商品的回放地址，仅更新单个字段
        LambdaUpdateWrapper<LiveGoods> lqw = new LambdaUpdateWrapper<>();
        lqw.set(LiveGoods::getLiveVideoUrl, indexFile.getVideoUrl())
                .eq(LiveGoods::getId, liveGoods.getId());
        liveGoodsService.update(lqw);

        return indexFile.getVideoUrl();
    }

    /**
     * 批量获取商品回放地址并更新数据库字段
     *
     * @param liveGoodsIds 商品ID列表
     */
    @Override
    public void batchFetchGoodsPlaybackUrls(List<Long> liveGoodsIds) {
        AssertUtil.assertFalse(CollectionUtil.isEmpty(liveGoodsIds), "参数异常， 没有liveGoodsIds");

        List<CompletableFuture<Void>> futures;
        try (var executor = ThreadPoolManager.virtualExecutors("goods-playback-fetch")) {
            futures = liveGoodsIds.stream()
                    .map(id -> CompletableFuture.runAsync(VirtualRunner.wrapper(() -> {
                        try {
                            fetchPlaybackUrl(id);
                        } catch (Exception e) {
                            LogExUtil.errorLog("获取商品回放地址失败， liveGoodsId = " + id, e);
                        }
                    }), executor))
                    .toList();

            // 等待所有任务完成，设置超时
            try {
                CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
                        .orTimeout(30, TimeUnit.SECONDS)
                        .join();
            } catch (CompletionException e) {
                if (e.getCause() instanceof TimeoutException) {
                    LogExUtil.errorLog("批量获取商品回放地址超时", e);
                } else {
                    LogExUtil.errorLog("批量获取商品回放地址时发生未知异常", e);
                }
            }
        }

        // 检查是否有任何任务失败，如果有，记录失败的ID
        List<Long> failedIds = new ArrayList<>();
        for (int i = 0; i < liveGoodsIds.size(); i++) {
            if (futures.get(i).isCompletedExceptionally()) {
                failedIds.add(liveGoodsIds.get(i));
            }
        }

        if (!failedIds.isEmpty()) {
            LogExUtil.warnLog("获取下列 id 的回放地址失败: " + failedIds);
        }
    }

    /**
     * 查12小时内结束的场次，如果存在没同步到云展的商品，如果有就把直播间信息返回
     * 直播间id、名称、开始时间、结束时间
     *
     * @return
     */
    @Override
    public LiveRoomToCeCheckVO getLatestToCeRoom(Long liveRoomId) {
        Date now = new Date();
        var orgId = AuthUtil.getOrgId();
        // 查还没有到清单展示结束时间的直播间
        LiveRoom liveRoom = liveRoomId == null ? liveRoomService.getOne(Wrappers.lambdaQuery(LiveRoom.class)
                // 只查自己商户的
                .eq(LiveRoom::getOrgId, orgId)
                // 还没到清单结束时间
                .gt(LiveRoom::getActualGoodsListEndAt, now)
                // 但是已经断流了
                .eq(LiveRoom::getStreamStatus, LiveRoomStreamStatusEnum.OFF)
                // 并且直播结束的
                .lt(LiveRoom::getEndAt, now)
                // 按开播时间倒序
                .orderByDesc(LiveRoom::getStartAt)
                // 只取一个
                .last("limit 1")
        ) : liveRoomService.getById(liveRoomId);
        // 没有就算了
        if (liveRoom == null) {
            return null;
        }
        // 有的话，还得再检查这个直播间符合同步条件的所有商品。
        // 注意查询时不过滤「是否同步到云展」，因为下面要同时计算已同步和未同步的数量
        LiveGoodsQueryReq queryReq = new LiveGoodsQueryReq();
        queryReq.setLiveRoomId(liveRoom.getId())
                .setLiveGoodsStatusList(List.of(LiveGoodsStatusEnum.WAIT_PUT_AWAY.getCode(), LiveGoodsStatusEnum.ABORTIVE_AUCTION.getCode()))
                .setFilterLockedOrSoldOutGoods(true)
                .setFilterOffhandGoods(false)
                .setPerPage(-1);
        IPage<LiveGoodsDTO> liveGoodsList = liveGoodsService.getLiveGoodsList(queryReq);
        // 如果商品都同步了，那也不管了
        if (CollectionUtil.isEmpty(liveGoodsList.getRecords())) {
            return null;
        }

        // 没同步的，返回标准的结构
        LiveRoomToCeCheckVO result = new LiveRoomToCeCheckVO();
        result.setRoomId(liveRoom.getId());
        result.setRoomName(liveRoom.getRoomName());
        result.setStartAt(liveRoom.getStartAt());
        result.setActualGoodsListEndAt(liveRoom.getActualGoodsListEndAt());
        result.setRemainingSeconds(DateUtil.between(liveRoom.getActualGoodsListEndAt(), now, DateUnit.SECOND));
        result.setUnSyncGoodsCount(liveGoodsList.getRecords().stream().filter(x -> Boolean.FALSE.equals(x.getIfSyncCe())).count());
        result.setHasSyncGoodsCount(liveGoodsList.getRecords().stream().filter(x -> Boolean.TRUE.equals(x.getIfSyncCe())).count());

        return result;
    }
}
