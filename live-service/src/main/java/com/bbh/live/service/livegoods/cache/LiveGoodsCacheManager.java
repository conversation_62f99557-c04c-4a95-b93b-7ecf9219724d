package com.bbh.live.service.livegoods.cache;

/**
 * <AUTHOR> dsy
 * @Date: 2024/7/26
 * @Description:
 */
public interface LiveGoodsCacheManager {

    /**
     * 竞拍商品缓存服务
     * @return
     */
    LiveGoodsAuctionCacheService getLiveGoodsAuctionCache();

    /**
     * 订阅商品缓存服务
     * @return
     */
    LiveGoodsSubscribeCacheService getLiveGoodsSubscribeCache();

    /**
     * 商品基本信息缓存
     * @return
     */
    LiveGoodsInfoCacheService getLiveGoodsInfoCache();

    /**
     * 清理所有缓存
     */
    void clearGoodsCache(Long liveRoomId);
}
