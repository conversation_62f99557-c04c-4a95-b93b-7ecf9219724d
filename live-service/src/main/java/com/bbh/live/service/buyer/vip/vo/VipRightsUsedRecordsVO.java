package com.bbh.live.service.buyer.vip.vo;

import com.bbh.base.ListBase;
import com.bbh.live.dao.dto.VipPeepLogDTO;
import com.bbh.model.VipBuyerChangeNicknameLog;
import com.bbh.model.VipBuyerLotteryLog;
import com.bbh.model.VipBuyerRefundFenbei;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2024/9/21
 * @description: 买手vip使用记录
 */
@Data
public class VipRightsUsedRecordsVO {

	/**
	 *  官方补偿记录
	 */
	private ListBase<OfficialCompensationRecord> officialCompensationRecords;

	/**
	 * 查看出价记录
	 */
	private ListBase<PeepRecord> peepRecords;

	/**
	 * VIP免费抽奖记录
	 */
	private ListBase<LotteryRecord> lotteryRecords;

	/**
	 * 修改昵称记录
	 */
	private ListBase<NicknameModifyRecord> nicknameModifyRecords;



	/**
	 * 官方补偿
	 */
	@Data
	@AllArgsConstructor
	@NoArgsConstructor
	public static class OfficialCompensationRecord {
		/**
		 * 创建时间
		 */
		private Date createdAt;
		/**
		 * 官方补偿分贝数量
		 */
		private Integer compensationFenbei;
		/**
		 * 商品名称
		 */
		private String goodsName;
		/**
		 * 活动名称
		 */
		private String activityName;
		/**
		 * 官方补偿类型 0-退款补偿，1-VIP权益未使用过期补偿
		 */
		private Integer compensationType;

		public static OfficialCompensationRecord builder(VipBuyerRefundFenbei item) {
			OfficialCompensationRecord record = new OfficialCompensationRecord();
			record.setCreatedAt(item.getCreatedAt());
			record.setCompensationFenbei(item.getRefundFenbei());
			record.setGoodsName(item.getAuctionNumber() + "-" + item.getGoodsName());
			record.setActivityName(item.getBizName());
			record.setCompensationType(item.getType().getCode());
			return record;
		}
	}

	@Data
	@AllArgsConstructor
	@NoArgsConstructor
	public static class PeepRecord {
		/**
		 * 创建时间
		 */
		private Date createdAt;
		/**
		 * 出价人
		 */
		private String seatName;

		/**
		 * 出价金额
		 */
		private BigDecimal bidAmount;

		/**
		 * 商品名称
		 */
		private String goodsName;

		/**
		 * 活动名称
		 */
		private String activityName;

		public static PeepRecord builder(VipPeepLogDTO vipPeepLogDTO) {
			PeepRecord peepRecord = new PeepRecord();
			peepRecord.setCreatedAt(vipPeepLogDTO.getCreatedAt());
			peepRecord.setSeatName(vipPeepLogDTO.getAuctionCode() + "-" + vipPeepLogDTO.getBidSeatName());
			peepRecord.setBidAmount(vipPeepLogDTO.getBidAmount());
			peepRecord.setGoodsName(vipPeepLogDTO.getGoodsName());
			peepRecord.setActivityName(vipPeepLogDTO.getBizName());
			return peepRecord;
		}
	}

	@Data
	@AllArgsConstructor
	@NoArgsConstructor
	public static class NicknameModifyRecord {
		/**
		 * 创建时间
		 */
		private Date createdAt;
		/**
		 * 修改后昵称
		 */
		private String afterNickname;
		/**
		 * 修改前昵称
		 */
		private String beforeNickname;

		public static NicknameModifyRecord builder(VipBuyerChangeNicknameLog vipBuyerChangeNicknameLog) {
			NicknameModifyRecord record = new NicknameModifyRecord();
			record.setCreatedAt(vipBuyerChangeNicknameLog.getCreatedAt());
			record.setBeforeNickname(vipBuyerChangeNicknameLog.getNameOld());
			record.setAfterNickname(vipBuyerChangeNicknameLog.getNameNew());
			return record;
		}
	}

	@Data
	@AllArgsConstructor
	@NoArgsConstructor
	public static class LotteryRecord {
		/**
		 * 创建时间
		 */
		private Date createdAt;
		/**
		 * 奖品名称
		 */
		private String prizeName;

		public static LotteryRecord builder(VipBuyerLotteryLog lotteryLog) {
			LotteryRecord record = new LotteryRecord();
			record.setCreatedAt(lotteryLog.getCreatedAt());
			record.setPrizeName(lotteryLog.getOwnName());
			return record;
		}
	}
}
