package com.bbh.live.service.buyer.sign;

import com.bbh.live.service.buyer.sign.vo.SignAwardFenBeiVO;
import com.bbh.live.service.buyer.sign.vo.SignInfoVo;

import java.util.Date;

/**
 * <AUTHOR> dsy
 * @Date: 2024/8/15
 * @Description:
 */
public interface BuyerSignService {

    /**
     * 用户签到
     * @param date 签到日期
     * @param buyerSeatId 买手席位id
     * @return 签到奖励分贝
     */
    SignAwardFenBeiVO sign(Date date, Long buyerSeatId);


    /**
     * 签到详情，本周签到天数，本月签到天数，本月获得的分贝奖励
     * @param date
     * @param buyerSeatId
     * @return
     */
    SignInfoVo signInfo(Date date, Long buyerSeatId);

    /**
     * 指定日期是否已签到
     * @param date
     * @param buyerSeatId
     * @return
     */
    boolean isSign(Date date, Long buyerSeatId);
}
