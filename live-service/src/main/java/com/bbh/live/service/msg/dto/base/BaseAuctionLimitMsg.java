package com.bbh.live.service.msg.dto.base;

import com.bbh.enums.LiveRoomFilterModeEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;

import java.util.List;

/**
 * 黑白名单相关的类
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class BaseAuctionLimitMsg extends BaseMsg {

    /** 直播间ID */
    private Long liveRoomId;
    /** 黑白名单模式 */
    private Integer auctionLimitUserMode;
    /** 黑名单ID列表 */
    private List<Long> blacklist;
    /** 白名单列表 */
    private List<Long> whitelist;

    @AllArgsConstructor
    @Getter
    public static enum AuctionLimitUserMode {

        /** 黑名单 */
        BLACKLIST(0),
        /** 白名单 */
        WHITELIST(1);

        private final Integer code;

        /**
         * 将LiveRoomFilterModeEnum转换为AuctionLimitUserMode
         *
         * @param filterMode LiveRoomFilterModeEnum枚举值
         * @return AuctionLimitUserMode枚举值，如果无法匹配则返回null
         */
        public static AuctionLimitUserMode from(LiveRoomFilterModeEnum filterMode) {
            if (filterMode == null) {
                return null;
            }
            return switch (filterMode) {
                case WHITE_LIST -> WHITELIST;
                case BLACKLIST -> BLACKLIST;
            };
        }
    }
}
