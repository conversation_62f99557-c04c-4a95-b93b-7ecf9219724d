package com.bbh.live.service.msg.dto;

import com.bbh.live.core.msg.MsgType;
import com.bbh.live.service.msg.dto.base.BaseSeat;
import com.bbh.live.service.msg.dto.base.IMsg;
import lombok.Data;

/**
 * 用户被解禁
 * <AUTHOR>
 */
@Data
public class UserChatUnbannedMsgDTO implements IMsg {

    /**
     * 被解禁的用户
     */
    private BaseSeat user;

    /**
     * 每个消息对应一个类型
     *
     * @return {@link MsgType}
     */
    @Override
    public String type() {
        return MsgType.USER_CHAT_UNBANNED;
    }
}
