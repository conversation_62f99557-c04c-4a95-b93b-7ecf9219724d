package com.bbh.live.service.sce;

import com.baomidou.mybatisplus.extension.service.IService;
import com.bbh.model.SceGoods;
import com.bbh.model.SceGoodsPriceChange;
import com.bbh.vo.AuthUser;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2025/5/8
 * @description: ${description}
 */
public interface SceGoodsPriceChangeService extends IService<SceGoodsPriceChange> {

    /**
     * 初始化商品价格记录
     * <p>
     * 该方法用于创建商品的初始价格记录，记录商品的初始售价。
     * 初始价格记录的旧价格和价格变动都设置为0，变更类型为初始化。
     *
     * @param authUser   当前操作用户信息
     * @param sceGoodsId 商品ID
     * @param sellPrice  初始售价
     * @return 创建的价格记录对象
     */

    List<SceGoodsPriceChange> init(AuthUser authUser, Map<Long, BigDecimal> sceGoodsIdSellPriceMap);

    /**
     * 初始化商品价格记录
     * <p>
     * 该方法用于创建商品的初始价格记录，记录商品的初始售价。
     * 初始价格记录的旧价格和价格变动都设置为0，变更类型为初始化。
     *
     * @param authUser   当前操作用户信息
     * @return 创建的价格记录对象
     */

    List<SceGoodsPriceChange> initBySceGoods(List<SceGoods> sceGoodsList);
}
