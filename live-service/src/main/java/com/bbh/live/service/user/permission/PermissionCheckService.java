package com.bbh.live.service.user.permission;

import com.bbh.live.enums.PermissionCodeEnum;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/9/20 16:08
 * @description
 */
public interface PermissionCheckService {

    /**
     * 根据权限码判断是否有权限
     * @param permissionCode 权限码
     * @return boolean
     */
    boolean hasPermissionByPermissionCode(PermissionCodeEnum permissionCode);

    /**
     * 获取店铺下拥有某权限的所有席位 id
     * @param permissionCode
     * @param orgId
     * @return
     */
    List<Long> getOrgSeatIdListByPermissionCodeAndOrgId(PermissionCodeEnum permissionCode, Long orgId);
}
