package com.bbh.live.service.msg.dto;

import com.bbh.live.core.msg.MsgType;
import com.bbh.live.service.msg.dto.base.BaseAuctionLimitMsg;
import com.bbh.live.service.msg.dto.base.BaseSeat;
import com.bbh.live.service.msg.dto.base.IMsg;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @Date 2024/7/29
 * @description: 踢出直播间消息体
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class UserOutNowMsgDTO extends BaseAuctionLimitMsg implements IMsg {

    /**
     * 直播间id
     */
    private Long liveRoomId;

    /**
     * 直播间限制模式
     */
    private Integer liveRoomFilterMode;

    /**
     * 来源: 10-用户拉黑商家, 20-商家拉黑用户'
     */
    private Integer sourceType;

    /**
     * 被拉黑的用户
     */
    private BaseSeat user;

    /**
     * 每个消息对应一个类型
     *
     * @return {@link MsgType}
     */
    @Override
    public String type() {
        return MsgType.USER_OUT_NOW;
    }
}
