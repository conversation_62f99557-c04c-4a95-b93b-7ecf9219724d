package com.bbh.live.service.virutalgoods.consumer;

import com.bbh.enums.GlobalVirtualGoodsOrderStateEnum;
import com.bbh.live.constant.DelayQueueTopics;
import com.bbh.live.dao.service.GlobalVirtualGoodsOrderService;
import com.bbh.live.enums.GlobalVirtualGoodsOrderCancelSource;
import com.bbh.live.handler.queue.DelayJob;
import com.bbh.live.handler.queue.DelayJobFactory;
import com.bbh.live.handler.queue.consumer.AbstractMessageConsumer;
import com.bbh.live.thread.ThreadPoolManager;
import com.bbh.model.GlobalVirtualGoodsOrder;
import com.bbh.service.mq.constants.MqConstant;
import com.bbh.service.mq.service.CoreMqListener;
import com.bbh.util.LogExUtil;
import com.bbh.util.ParamsUtil;
import com.rabbitmq.client.Channel;
import jakarta.annotation.Resource;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.nio.charset.Charset;
import java.util.Date;
import java.util.concurrent.Executor;

/**
 * <AUTHOR> dsy
 * @Date: 2024/8/20
 * @Description:
 */
@Component
public class VirtualGoodsOrderCancelConsumer extends AbstractMessageConsumer<Long> implements CoreMqListener {

    @Resource
    private GlobalVirtualGoodsOrderService globalVirtualGoodsOrderService;

    @Override
    public String topic() {
        return DelayQueueTopics.VIRTUAL_ORDER_TOPIC;
    }

    @Override
    public void consume(DelayJob<Long> delayJob) {
        //虚拟订单id
        Long orderId = Long.parseLong(delayJob.getJobId());
        //订单详细信息
        GlobalVirtualGoodsOrder goodsOrder = globalVirtualGoodsOrderService.lambdaQuery()
                .eq(GlobalVirtualGoodsOrder::getId, orderId)
                .eq(GlobalVirtualGoodsOrder::getOrderState, GlobalVirtualGoodsOrderStateEnum.WAIT_PAY)
                .select(GlobalVirtualGoodsOrder::getId, GlobalVirtualGoodsOrder::getOrderState, GlobalVirtualGoodsOrder::getOrderExpireTime)
                .one();
        if (goodsOrder == null) {
            return;
        }
        //判断是否已到达过期时间
        Date orderExpireTime = goodsOrder.getOrderExpireTime();
        if(!super.isTimeUp(orderExpireTime, () -> DelayJobFactory.createVirtualGoodsOrderDelayJob(orderId, null))){
            return;
        }
        //取消订单
        globalVirtualGoodsOrderService.cancelOrder(orderId, GlobalVirtualGoodsOrderCancelSource.SYSTEM);
    }

    @Override
    public Executor getExecutor() {
        return ThreadPoolManager.getGlobalBizExecutor();
    }

    @Override
    @RabbitListener(queuesToDeclare = @Queue(name = MqConstant.VIRTUAL_ORDER_PAY_CLOSE_DELAY, durable = "true"))
    public void handle(Message message, Channel channel) {

        String body = new String(message.getBody(), Charset.defaultCharset());
        Long orderId;
        try{
            orderId = ParamsUtil.getValueFromJsonStr(body, "virtualOrderId", Long.class);
            //订单详细信息
            GlobalVirtualGoodsOrder goodsOrder = globalVirtualGoodsOrderService.lambdaQuery()
                    .eq(GlobalVirtualGoodsOrder::getId, orderId)
                    .eq(GlobalVirtualGoodsOrder::getOrderState, GlobalVirtualGoodsOrderStateEnum.WAIT_PAY)
                    .select(GlobalVirtualGoodsOrder::getId, GlobalVirtualGoodsOrder::getOrderState, GlobalVirtualGoodsOrder::getOrderExpireTime)
                    .one();
            if (goodsOrder == null) {
                return;
            }
            //取消订单
            globalVirtualGoodsOrderService.cancelOrder(orderId, GlobalVirtualGoodsOrderCancelSource.SYSTEM);
        }catch (Exception e){
            LogExUtil.errorLog("虚拟订单超时关闭失败，消息内容：" + body, e);
        } finally {
            try {
                channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
            } catch (IOException ignore) {}
        }
    }
}
