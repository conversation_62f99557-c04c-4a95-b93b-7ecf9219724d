package com.bbh.live.service.buyerCancel.dto.response;

import com.bbh.enums.GlobalOrderTypeEnum;
import com.bbh.live.service.buyerCancel.dto.BuyerCancelBiz;
import com.bbh.live.service.buyerCancel.dto.LiveGoodsBuyerCancelRecordDetailDTO;
import lombok.Data;

/**
 * 买家取消成交记录列表项响应对象
 *
 * <AUTHOR>
 * @since 2025-01-10
 */
@Data
public class BuyerCancelRecordListVO implements BuyerCancelBiz {

    /**
     * 商品信息
     */
    private BuyerCancelRecordDetailVO.GoodsInfo goodsInfo;

    /**
     * 卖家组织信息
     */
    private BuyerCancelRecordDetailVO.OrgInfo sellerOrgInfo;

    /**
     * 买家组织信息
     */
    private BuyerCancelRecordDetailVO.OrgInfo buyerOrgInfo;

    /**
     * 成交人席位信息
     */
    private BuyerCancelRecordDetailVO.SeatInfo buyerSeatInfo;

    /**
     * 业务商品信息
     */
    private BuyerCancelRecordDetailVO.BizGoodsInfo bizGoods;

    /**
     * 直播间信息
     */
    private BuyerCancelRecordDetailVO.LiveRoomInfo liveRoomInfo;

    /**
     * 记录信息
     */
    private LiveGoodsBuyerCancelRecordDetailDTO recordInfo;

    /**
     * 已完成的描述信息
     */
    private String doneDesc;

    /**
     * 记录ID
     */
    private Long id;

    private GlobalOrderTypeEnum bizType;

    @Override
    public GlobalOrderTypeEnum getBizType() {
        return this.bizType;
    }

    @Override
    public void setBizType(GlobalOrderTypeEnum bizType) {
        this.bizType=bizType;
    }

}
