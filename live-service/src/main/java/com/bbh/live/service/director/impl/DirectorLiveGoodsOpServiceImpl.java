package com.bbh.live.service.director.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSON;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.bbh.base.Page;
import com.bbh.base.Sort;
import com.bbh.enums.*;
import com.bbh.exception.ServiceException;
import com.bbh.feign.ICeApiClient;
import com.bbh.feign.dto.LiveGoodsBatchToCeDTO;
import com.bbh.feign.dto.LiveGoodsSyncCeResult;
import com.bbh.live.config.LiveServiceProperties;
import com.bbh.live.constant.LiveGoodsConstant;
import com.bbh.live.constant.ProjectConstant;
import com.bbh.live.controller.req.LiveGoodsQueryReq;
import com.bbh.live.dao.dto.InteractiveMsgDTO;
import com.bbh.live.dao.dto.OffhandPutAwayDTO;
import com.bbh.live.dao.dto.VisibleDTO;
import com.bbh.live.dao.dto.livegoods.LiveGoodsDTO;
import com.bbh.live.dao.dto.livegoods.LiveGoodsPutAwayDTO;
import com.bbh.live.dao.dto.livegoods.LiveGoodsSortDTO;
import com.bbh.live.dao.dto.livegoods.LiveGoodsSyncCeDTO;
import com.bbh.live.dao.mapper.LiveGoodsMapper;
import com.bbh.live.dao.service.*;
import com.bbh.live.enums.LiveGoodsTradeCompletedTypeEnum;
import com.bbh.live.service.director.DirectorLiveGoodsOpService;
import com.bbh.live.service.livegoods.LiveGoodsDetailService;
import com.bbh.live.service.livegoods.LiveGoodsListService;
import com.bbh.live.service.livegoods.LiveGoodsPlaybackService;
import com.bbh.live.service.livegoods.cache.LiveGoodsAuctionCacheService;
import com.bbh.live.service.livegoods.cache.info.LiveGoodsAuctionBidInfo;
import com.bbh.live.service.livegoods.cache.info.LiveGoodsAuctionInfo;
import com.bbh.live.service.livegoods.context.AuctionFailLiveGoodsContext;
import com.bbh.live.service.livegoods.context.LiveGoodsContext;
import com.bbh.live.service.livegoods.context.PutAwayLiveGoodsContext;
import com.bbh.live.service.livegoods.context.TradeLiveGoodsContext;
import com.bbh.live.service.msg.MsgService;
import com.bbh.live.service.room.LiveRoomCacheService;
import com.bbh.live.service.room.context.LiveRoomContextHolder;
import com.bbh.live.service.sce.SceGoodsPriceChangeService;
import com.bbh.live.statemachine.goods.GoodsStateMachineManager;
import com.bbh.live.thread.ThreadPoolManager;
import com.bbh.live.util.LivePermissionChecker;
import com.bbh.live.util.PageUtils;
import com.bbh.model.*;
import com.bbh.secure.AuthUtil;
import com.bbh.service.RedisService;
import com.bbh.util.AssertUtil;
import com.bbh.util.LogExUtil;
import com.bbh.vo.AuthUser;
import com.google.common.collect.Lists;
import jakarta.annotation.Resource;
import jodd.util.StringUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * <AUTHOR> dsy
 * @Date: 2024/7/26
 * @Description:
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class DirectorLiveGoodsOpServiceImpl implements DirectorLiveGoodsOpService {

    private final LiveGoodsListService liveGoodsListService;
    private final LiveGoodsDetailService liveGoodsDetailService;
    private final GoodsStateMachineManager<LiveGoodsContext> goodsStateMachineManager;
    private final LiveRoomCacheService liveRoomCacheService;
    private final LiveRoomInteractiveMessageService liveRoomInteractiveMessageService;
    private final LiveRoomService liveRoomService;
    private final LiveGoodsPlaybackService liveGoodsPlaybackService;
    private final MsgService msgService;
    private final IErpStorehouseService erpStorehouseService;
    private final ErpGoodsService erpGoodsService;
    private final RedisService redisService;
    private final LiveGoodsMapper liveGoodsMapper;
    private final LiveGoodsAuctionCacheService liveGoodsAuctionCacheService;
    private final SceGoodsService sceGoodsService;
    private final SceGoodsPriceChangeService sceGoodsPriceChangeService;
    private final IGlobalOrganizationService globalOrganizationService;
    private final LiveServiceProperties liveServiceProperties;




    @Resource
    private ICeApiClient iCeApiClient;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String addLiveGoodsList(Long liveRoomId, List<Long> goodsIdList) {
        //导播权限校验
        LivePermissionChecker.assertDirector(LiveRoomContextHolder.getLiveRoomContext().getRoomId());

        String msg = "操作成功";
        if(CollectionUtil.isEmpty(goodsIdList)){
            return msg;
        }
        int addGoodsCount = liveGoodsListService.addLiveGoodsListFromErpStorehouse(liveRoomId, goodsIdList);
        if(addGoodsCount != goodsIdList.size()){
            msg = "您选择的部分商品失效，已为您自动移除";
        }
        if(addGoodsCount != 0){
            //增加直播商品数量
            liveRoomCacheService.incrementGoodsCount(liveRoomId, addGoodsCount);
        }
        return msg;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void removeLiveGoodsList(List<Long> liveGoodsIdList) {
        //导播权限校验
        LivePermissionChecker.assertDirector(LiveRoomContextHolder.getLiveRoomContext().getRoomId());

        if(CollectionUtil.isEmpty(liveGoodsIdList)){
            return;
        }
        int removeCount = liveGoodsListService.removeLiveGoodsList(liveGoodsIdList);
        //缓存减少直播商品数量
        liveRoomCacheService.incrementGoodsCount(LiveRoomContextHolder.getRoomId(), -removeCount);

        // 如果是开播前，要重新设置商品拍号
        if (LiveRoomContextHolder.liveRoomIsBeforeStart()) {
            resetAllGoodsCode(LiveRoomContextHolder.getLiveRoomContext().getRoomId());
        }

        // 发送商品删除消息
        msgService.goodsDelete(LiveRoomContextHolder.getLiveRoomContext().getRoomId(), liveGoodsIdList);
    }

    /**
     * 重置直播间的所有拍号
     * @param liveRoomId    直播间id
     */
    public void resetAllGoodsCode(Long liveRoomId) {
        // 按序号排序，查到直播间所有商品
        LiveGoodsQueryReq query = new LiveGoodsQueryReq();
        query.setPerPage(-1);
        query.setCurrentPage(1);
        // 前端按sort正序查，那这边要倒序查再去更新，这样code才对
        query.setSort(Sort.of(LiveGoodsConstant.DEFAULT_SORT_FIELD, Sort.Direction.ASC));
        query.setLiveRoomId(liveRoomId);
        IPage<LiveGoodsDTO> page = liveGoodsListService.getLiveGoodsList(query);
        if (page == null || CollUtil.isEmpty(page.getRecords())) {
            return;
        }
        List<LiveGoodsDTO> goodsList = page.getRecords();

        // 重置拍号，从1开始
        List<LiveGoods> updatedList = IntStream.range(0, goodsList.size())
                .mapToObj(i -> {
                    LiveGoodsDTO goods = goodsList.get(i);
                    LiveGoods liveGoods = new LiveGoods();
                    liveGoods.setLiveGoodsCode(i + 1L);
                    liveGoods.setId(goods.getId());
                    return liveGoods;
                })
                .collect(Collectors.toList());

        // 批量更新
        liveGoodsMapper.updateById(updatedList);
    }

    @Override
    public Boolean updateLiveGoodsVisible(VisibleDTO visibleDTO) {
        // 如果没传参数，就查询当前状态并取反
        boolean targetVisible;
        if (visibleDTO == null || visibleDTO.getVisible() == null) {
            boolean currentVisible = liveRoomService
                    .lambdaQuery()
                    .eq(LiveRoom::getId, LiveRoomContextHolder.getRoomId())
                    .select(LiveRoom::getIfGoodsListVisible)
                    .one()
                    .getIfGoodsListVisible();
            // 取反
            targetVisible = !currentVisible;
        } else {
            // 如果传了参数，直接使用要设置的目标状态
            targetVisible = visibleDTO.getVisible();
        }

        liveRoomService.lambdaUpdate()
                .eq(LiveRoom::getId, LiveRoomContextHolder.getRoomId())
                // 更新状态
                .set(LiveRoom::getIfGoodsListVisible, targetVisible)
                // 关闭时记录时间
                .set(!targetVisible, LiveRoom::getGoodsListLastCloseAt, new Date())
                .update();

        return targetVisible;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void completeLiveGoodsPutAwayInfo(LiveGoodsPutAwayDTO playAwayInfo) {
        //导播权限校验
        LivePermissionChecker.assertDirector(LiveRoomContextHolder.getLiveRoomContext().getRoomId());

        LiveGoodsDTO liveGoodsDetailInfo = liveGoodsDetailService.getLiveGoodsDetailInfo(playAwayInfo.getLiveGoodsId());
        AssertUtil.assertNotNull(liveGoodsDetailInfo, "商品不存在");

        //构建商品完善状态机执行上下文
        PutAwayLiveGoodsContext playAwayInfoContext = new PutAwayLiveGoodsContext();
        playAwayInfoContext.setIncreasePrice(playAwayInfo.getIncreasePrice())
                .setTradeType(playAwayInfo.getTradeType() == null ? liveGoodsDetailInfo.getTradeType() : playAwayInfo.getTradeType())
                .setStartPrice(playAwayInfo.getStartPrice())
                .setAuctionDuration(playAwayInfo.getAuctionDuration())
                .setDirectorRemark(playAwayInfo.getDirectorRemark())
                .setLiveGoodsId(playAwayInfo.getLiveGoodsId());
        // 完善商品信息
        goodsStateMachineManager.complete(liveGoodsDetailInfo, playAwayInfoContext);
    }

    @Override
    public void batchUpdateLiveGoodsSort(List<LiveGoodsSortDTO> liveGoodsSortList) {
        //导播权限校验
        LivePermissionChecker.assertDirector(LiveRoomContextHolder.getLiveRoomContext().getRoomId());

        if (CollectionUtil.isEmpty(liveGoodsSortList)) {
            return;
        }

        // 如果是开播前，还要更新拍号
        boolean isBeforeStart = LiveRoomContextHolder.getLiveRoomContext().getStartAt().after(new Date());
        if (isBeforeStart) {
            // sortList的顺序就是拍号正序
            AtomicLong counter = new AtomicLong(1);
            List<LiveGoods> collected = liveGoodsSortList
                    .stream()
                    .sorted(Comparator.comparing(LiveGoodsSortDTO::getSort))
                    .map(sort -> {
                        LiveGoods liveGoods = new LiveGoods();
                        liveGoods.setId(sort.getLiveGoodsId());
                        liveGoods.setLiveGoodsCode(counter.getAndIncrement());
                        return liveGoods;
                    })
                    .toList();
            liveGoodsMapper.updateById(collected);
        }

        // 该方法仅用于更新sort
        liveGoodsListService.batchUpdateLiveGoodsSortValue(liveGoodsSortList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void putAwayAndExplainLiveGoods(Long liveGoodsId) {
        //导播权限校验
        LivePermissionChecker.assertDirector(LiveRoomContextHolder.getLiveRoomContext().getRoomId());

        LiveGoodsDTO liveGoodsDetailInfo = liveGoodsDetailService.getLiveGoodsDetailInfo(liveGoodsId);
        AssertUtil.assertNotNull(liveGoodsDetailInfo, "商品不存在");
        //上架商品
        goodsStateMachineManager.putAway(liveGoodsDetailInfo);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void auctionLiveGoods(Long liveGoodsId) {
        //导播权限校验
        LivePermissionChecker.assertDirector(LiveRoomContextHolder.getLiveRoomContext().getRoomId());

        LiveGoodsDTO liveGoodsDetailInfo = liveGoodsDetailService.getLiveGoodsDetailInfo(liveGoodsId);
        AssertUtil.assertNotNull(liveGoodsDetailInfo, "商品不存在");
        //开始竞拍
        goodsStateMachineManager.auction(liveGoodsDetailInfo);
    }

    /**
     * 手动流拍，目前仅适用于一口价
     *
     * @param liveGoodsId 商品ID
     */
    @Override
    public void abortLiveGoods(Long liveGoodsId) {
        Long roomId = LiveRoomContextHolder.getLiveRoomContext().getRoomId();
        //导播权限校验
        LivePermissionChecker.assertDirector(roomId);
        // 拍卖商品无法流拍
        LiveGoodsDTO liveGoodsDetailInfo = liveGoodsDetailService.getLiveGoodsDetailInfo(liveGoodsId);
        if (liveGoodsDetailInfo.getTradeType() == LiveGoodsTradeTypeEnum.AUCTION) {
            throw new ServiceException("拍卖商品无法手动流拍");
        }

        // 检查是否有人出过价，禁止流拍
        List<LiveGoodsAuctionBidInfo> bidInfoList = liveGoodsAuctionCacheService.getAuctionGoodsBidInfo(roomId, liveGoodsId);
        if (CollUtil.isNotEmpty(bidInfoList)) {
            throw new ServiceException("有人出过价，禁止流拍");
        }

        // 检查是否结算中
        LiveGoodsAuctionInfo auctionLiveGoods = liveGoodsAuctionCacheService.getAuctionLiveGoods(roomId, liveGoodsId);
        if (auctionLiveGoods != null && auctionLiveGoods.getIfSettle()) {
            throw new ServiceException("商品结算中，无法流拍");
        }

        // 走通用的流拍流程
        LiveGoodsDTO goodsDetailInfo = liveGoodsDetailService.getLiveGoodsSimpleDetailInfo(liveGoodsId);
        goodsStateMachineManager.auctionFail(goodsDetailInfo, new AuctionFailLiveGoodsContext(goodsDetailInfo.getLiveGoodsId(), false));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void putAwayOffLiveGoods(Long liveGoodsId) {
        //导播权限校验
        LivePermissionChecker.assertDirector(LiveRoomContextHolder.getLiveRoomContext().getRoomId());

        LiveGoodsDTO liveGoodsDetailInfo = liveGoodsDetailService.getLiveGoodsDetailInfo(liveGoodsId);
        AssertUtil.assertNotNull(liveGoodsDetailInfo, "商品不存在");
        goodsStateMachineManager.putAwayOff(liveGoodsDetailInfo);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void interactiveMessageHandle(InteractiveMsgDTO interceptMsg) {
        //导播权限校验
        LivePermissionChecker.assertDirector(LiveRoomContextHolder.getLiveRoomContext().getRoomId());

        LiveRoomInteractiveMessage interactiveMessage = liveRoomInteractiveMessageService.lambdaQuery().eq(LiveRoomInteractiveMessage::getId, interceptMsg.getMsgId()).one();
        AssertUtil.assertNotNull(interactiveMessage, "消息不存在");
        AssertUtil.assertTrue(LiveRoomInteractiveMessageHandleStatusEnum.UNHANDLED.equals(interactiveMessage.getHandleStatus()), "消息已处理");

        if(Boolean.TRUE.equals(interceptMsg.getIfHandle())){
            switch (interceptMsg.getType()) {
                case BARGAIN:
                    LiveGoodsDTO liveGoodsDetailInfo = liveGoodsDetailService.getLiveGoodsDetailInfo(interceptMsg.getLiveGoodsId());
                    AssertUtil.assertNotNull(liveGoodsDetailInfo, "商品不存在");
                    //处理议价消息
                    TradeLiveGoodsContext context = new TradeLiveGoodsContext()
                            .setFinalPrice(interceptMsg.getBidPrice())
                            .setBuyerSeatId(interceptMsg.getBuyerSeatId())
                            .setTradeType(LiveGoodsTradeCompletedTypeEnum.BARGAIN);
                    context.setLiveGoodsId(interceptMsg.getLiveGoodsId());
                    goodsStateMachineManager.transferOrBidSuccess(liveGoodsDetailInfo, context);
                    interactiveMessage.setHandleStatus(LiveRoomInteractiveMessageHandleStatusEnum.AGREE);
                    break;
                    //处理讲解消息 将商品排到待上架的第一个
                case ASK_FOR_EXPLANATION:
                    liveGoodsListService.updateLiveGoodsSortAsWaitPutAwayFirst(interceptMsg.getLiveGoodsId());
                    break;
                default:
                    throw new ServiceException("不支持的消息类型");
            }
        } else {
            switch (interceptMsg.getType()) {
                case BARGAIN:
                    // 议价失败消息
                    interactiveMessage.setHandleStatus(LiveRoomInteractiveMessageHandleStatusEnum.REJECT);
                    // 直播间消息推送
                    msgService.userBargainReject(interceptMsg);
                    break;
                default:
                    throw new ServiceException("不支持的消息类型");
            }
        }
        // 更新消息处理状态
        liveRoomInteractiveMessageService.saveOrUpdate(interactiveMessage);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void abortiveAuctionGoodsReShelve(Long liveGoodsId) {
        //导播权限校验
        LivePermissionChecker.assertDirector(LiveRoomContextHolder.getLiveRoomContext().getRoomId());

        LiveGoodsDTO liveGoodsDetailInfo = liveGoodsDetailService.getLiveGoodsDetailInfo(liveGoodsId);
        AssertUtil.assertNotNull(liveGoodsDetailInfo, "商品不存在");
        //商品重新上架
        goodsStateMachineManager.abortiveAuctionGoodsReShelve(liveGoodsDetailInfo);
    }

    @Override
    @Transactional
    public void abortiveAuctionGoodsReShelveAndComplete(LiveGoodsPutAwayDTO playAwayInfo) {
        // 导播权限校验
        LivePermissionChecker.assertDirector(LiveRoomContextHolder.getLiveRoomContext().getRoomId());
        LiveGoodsDTO liveGoodsDetailInfo = liveGoodsDetailService.getLiveGoodsDetailInfo(playAwayInfo.getLiveGoodsId());
        AssertUtil.assertNotNull(liveGoodsDetailInfo, "商品不存在");
        AssertUtil.assertTrue(LiveGoodsStatusEnum.ABORTIVE_AUCTION.equals(liveGoodsDetailInfo.getGoodsStatus()), "流拍商品才可重新上架");
        // 流拍商品重新上架
        goodsStateMachineManager.abortiveAuctionGoodsReShelve(liveGoodsDetailInfo);
        // 完善商品信息
        PutAwayLiveGoodsContext playAwayInfoContext = new PutAwayLiveGoodsContext();
        playAwayInfoContext.setIncreasePrice(playAwayInfo.getIncreasePrice())
                .setTradeType(playAwayInfo.getTradeType() == null ? liveGoodsDetailInfo.getTradeType() : playAwayInfo.getTradeType())
                .setStartPrice(playAwayInfo.getStartPrice())
                .setAuctionDuration(playAwayInfo.getAuctionDuration())
                .setDirectorRemark(playAwayInfo.getDirectorRemark())
                .setLiveGoodsId(playAwayInfo.getLiveGoodsId());
        goodsStateMachineManager.complete(liveGoodsDetailInfo, playAwayInfoContext);
        // 上架
        goodsStateMachineManager.putAway(liveGoodsDetailInfo);
    }

    /**
     * 即拍即上功能
     * 导播每场直播都有固定的即拍即上次数，可以直接拍摄商品，加入自动生成的直播仓库中，并发到待上架讲解第一位
     * @param offhandPutAwayDTO
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void offhandPutAway(OffhandPutAwayDTO offhandPutAwayDTO) {
        //导播权限校验
        LivePermissionChecker.assertDirector(LiveRoomContextHolder.getLiveRoomContext().getRoomId());

        // 前置校验
        preCheck(offhandPutAwayDTO);

        String lockKey = String.format(LiveGoodsConstant.OFFHAND_PUT_AWAY_REDIS_KEY, offhandPutAwayDTO.getLiveRoomId());
        boolean locked = redisService.lock(lockKey, 5, TimeUnit.SECONDS);
        AssertUtil.assertTrue(locked, "操作频繁，请稍后再试");
        try{
            ErpGoods erpGoods = null;
            // 创建
            if(offhandPutAwayDTO.getGlobalGoodsId() == null){
                // 直播仓库
                var storehouseId = erpStorehouseService.getLiveOffhandStorehouse(offhandPutAwayDTO.getLiveRoomId());
                offhandPutAwayDTO.setStorehouseId(storehouseId);
                // 创建erp商品
                erpGoods = erpGoodsService.createErpGoodsByOffhand(offhandPutAwayDTO);
            } else {
                // 扫码添加
                erpGoods = erpGoodsService.getById(offhandPutAwayDTO.getGlobalGoodsId());
                AssertUtil.assertNotNull(erpGoods, "ERP商品不存在");
                AssertUtil.assertFalse(erpGoods.getSaleStatus() == 2 || erpGoods.getIfLocked() == 1 || erpGoods.getPlaceOrderStatus() == 1, "商品已售出，无法添加");
                LiveGoods one = liveGoodsDetailService.getLiveGoodsService().lambdaQuery().eq(LiveGoods::getGlobalGoodsId, erpGoods.getId())
                        .select(LiveGoods::getId, LiveGoods::getLiveGoodsCode).one();
                if(one != null){
                    throw new ServiceException("商品[" + one.getLiveGoodsCode() + "]已添加到本场直播,请不要重复添加");
                }
            }

            // 创建直播商品
            // 如果一口价且起拍价为0，则改为0.01
            if (offhandPutAwayDTO.getTradeType() == LiveGoodsTradeTypeEnum.SEC_KILL && offhandPutAwayDTO.getStartPrice() != null && offhandPutAwayDTO.getStartPrice().compareTo(BigDecimal.ZERO) <= 0) {
                offhandPutAwayDTO.setStartPrice(BigDecimal.valueOf(0.01));
            }
            LiveGoods liveGoods = liveGoodsDetailService.getLiveGoodsService().createOffhandLiveGoods(offhandPutAwayDTO, erpGoods.getId());
            // 直播商品数量+1
            liveRoomCacheService.incrementGoodsCount(offhandPutAwayDTO.getLiveRoomId(), 1);
            // 上架讲解
            goodsStateMachineManager.putAway(liveGoodsDetailService.getLiveGoodsDetailInfo(liveGoods.getId()));

            // 减少即拍即上次数
            if(!LiveRoomContextHolder.liveRoomIsTestBroadcast()){
                liveRoomService.lambdaUpdate().eq(LiveRoom::getId, offhandPutAwayDTO.getLiveRoomId()).setDecrBy(LiveRoom::getOffhandRemainTimes, 1).update();
            }
        }finally {
            redisService.unLock(lockKey);
        }
    }


    private void preCheck(OffhandPutAwayDTO offhandPutAwayDTO) {
        offhandPutAwayDTO.checkProperties();
        // 校验剩余次数
        AssertUtil.assertFalse(!LiveRoomContextHolder.liveRoomIsTestBroadcast()  && LiveRoomContextHolder.getLiveRoomContext().getOffhandRemainTimes() <= 0, "即拍即上次数已用尽");
        //检查是否有已上架和竞拍中的商品
        Wrapper<LiveGoods> query = Wrappers.lambdaQuery(LiveGoods.class).eq(LiveGoods::getLiveRoomId, offhandPutAwayDTO.getLiveRoomId()).in(LiveGoods::getGoodsStatus, LiveGoodsStatusEnum.PUT_AWAY, LiveGoodsStatusEnum.AUCTION);
        AssertUtil.assertFalse(liveGoodsDetailService.getLiveGoodsService().exists(query), "已有商品处于上架或竞拍中");
    }




    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchSyncToCe(List<LiveGoodsSyncCeDTO.LiveGoodsSyncCe> liveGoodsSyncList) {
        //商品id 同行价映射关系
        Map<Long, BigDecimal> goodsIdPeerPriceMap=new HashMap<>();
        Map<Long, LiveGoodsSyncCeDTO.LiveGoodsSyncCe> goodsIdPeerPriceMaps=new HashMap<>();
        liveGoodsSyncList.forEach(x->goodsIdPeerPriceMaps.put(x.getLiveGoodsId(), x));
        liveGoodsSyncList.forEach(x->goodsIdPeerPriceMap.put(x.getLiveGoodsId(), x.getPeerPrice()));
        //liveGoodsSyncList.forEach(x->goodsIdPeerPriceMap.put(x.getLiveGoodsId(), x.getPeerPrice()));
//        List<LiveGoods> liveGoodsList = liveGoodsDetailService.getLiveGoodsService().batchToCeLogicCheckAndReturnErpGoodsList(Lists.newArrayList(goodsIdPeerPriceMap.keySet()));
        //获取一次视频地址
//        List<Long> ids = liveGoodsList.stream()
//                .filter(x -> x.getPutawayAt() != null && StrUtil.isBlank(x.getLiveVideoUrl()))
//                .map(LiveGoods::getId).toList();
//        if(CollUtil.isNotEmpty(ids)){
//            liveGoodsPlaybackService.batchFetchGoodsPlaybackUrls(ids);
//        }
        //再次查询
        List<LiveGoods> liveGoodsList = liveGoodsDetailService.getLiveGoodsService().batchToCeLogicCheckAndReturnErpGoodsList(Lists.newArrayList(goodsIdPeerPriceMap.keySet()));
        //rpc同步云展
        LiveGoodsSyncCeResult result = this.syncGoodsToSce(liveGoodsList, goodsIdPeerPriceMaps);
        //同步失败的商品
        List<String> failUpGoodsIds = result.getFailUpgoodsIds();
        if(CollectionUtil.isNotEmpty(failUpGoodsIds)){
            //失败的商品id
            LogExUtil.errorLog("云展商品同步失败, 商品id:" + failUpGoodsIds, new ServiceException("云展商品同步失败"));
        }

        //成功同步的商品id
        List<Long> successErpGoodsIds = result.getSuccessUpgoodsIds().stream().map(Long::parseLong).toList();

        if(CollectionUtil.isNotEmpty(successErpGoodsIds)){
            // erp商品id 与 直播商品 映射关系
            Map<Long, LiveGoods> goodsIdErpGoodsIdMap = liveGoodsList.stream().collect(Collectors.toMap(LiveGoods::getGlobalGoodsId, Function.identity()));
            // 异步回写商品回放地址等信息
            List<LiveGoods> finalLiveGoodsList = liveGoodsList;
            ThreadPoolManager.getGlobalBizExecutor().execute(() -> writeBackSuccessGoods(goodsIdErpGoodsIdMap, successErpGoodsIds, List.of(finalLiveGoodsList.getFirst().getLiveRoomId()), result.getSuccessErpGoodsIdSceGoodsIdMap()));
        }
    }

    @Override
    public void batchSyncLiveVideo(List<LiveGoodsSyncCeDTO.LiveGoodsSyncCe> liveGoodsList) {
        if(CollUtil.isEmpty(liveGoodsList)){
            return;
        }
        //获取一次视频地址
        List<Long> ids = liveGoodsList.stream()
                .map(LiveGoodsSyncCeDTO.LiveGoodsSyncCe::getLiveGoodsId).toList();
        if(CollUtil.isNotEmpty(ids)){
            liveGoodsPlaybackService.batchFetchGoodsPlaybackUrls(ids);
        }
    }


    @Transactional(rollbackFor = Throwable.class)
    public void writeBackSuccessGoods(Map<Long, LiveGoods> goodsIdErpGoodsIdMap, List<Long> successErpGoodsIds, List<Long> finalRoomIds, Map<Long,Long> successErpGoodsIdSceGoodsIdMap) {
        try{
            //更新直播间同步云展状态
            liveRoomService.lambdaUpdate().in(LiveRoom::getId, finalRoomIds).set(LiveRoom::getIfSyncCe, true).update();
            Date now = new Date();
            //更新商品同步云展状态和时间
            List<LiveGoods> successSyncGoods = new ArrayList<>();
            successErpGoodsIds.forEach(erpGoodsId -> {
                LiveGoods liveGoods = goodsIdErpGoodsIdMap.get(erpGoodsId);
                if (liveGoods!=null){
                    liveGoods.setSceGoodsId(successErpGoodsIdSceGoodsIdMap.get(erpGoodsId));
                    liveGoods.setSyncCeAt(now);
                    successSyncGoods.add(liveGoods);
                }
            });
            liveGoodsDetailService.getLiveGoodsService().updateBatchById(successSyncGoods);
        }catch (Throwable ignore){
            log.error("更新直播间同步云展状态失败",ignore);
        }
    }

    private LiveGoodsSyncCeResult syncGoodsToCe(List<LiveGoods> liveGoodsList, Map<Long, BigDecimal> goodsIdPeerPriceMap){
        //同步到云展
        LiveGoodsBatchToCeDTO liveGoodsBatchToCeDTO = new LiveGoodsBatchToCeDTO();
        ArrayList<LiveGoodsBatchToCeDTO.GoodsInfosVO> infoList = new ArrayList<>();
        liveGoodsList.forEach(liveGoods -> {
            LiveGoodsBatchToCeDTO.GoodsInfosVO goodsInfosVO = new LiveGoodsBatchToCeDTO.GoodsInfosVO();
            // erp商品id
            goodsInfosVO.setGoodsId(liveGoods.getGlobalGoodsId());
            // 同行价
            goodsInfosVO.setPeerPrice(goodsIdPeerPriceMap.get(liveGoods.getId()));
            infoList.add(goodsInfosVO);
        });
        liveGoodsBatchToCeDTO.setGoodsList(infoList);
        LiveGoodsBatchToCeDTO.UserInfosVO userInfosVO = new LiveGoodsBatchToCeDTO.UserInfosVO();
        userInfosVO.setUpdateSeatId(AuthUtil.getSeatId());
        userInfosVO.setUpdateUserId(AuthUtil.getUserId());
        liveGoodsBatchToCeDTO.setUserInfos(userInfosVO);
        liveGoodsBatchToCeDTO.setOrgId(AuthUtil.getOrgId());
        liveGoodsBatchToCeDTO.setSeatId(AuthUtil.getSeatId());
        liveGoodsBatchToCeDTO.setUserId(AuthUtil.getUserId());
        Map<String, List> data = (Map<String, List>) iCeApiClient.liveGoodsBatchToCe(liveGoodsBatchToCeDTO).getData();
        LiveGoodsSyncCeResult result = new LiveGoodsSyncCeResult();
        result.setFailUpgoodsIds(data.get("fail_upgoods_ids"));
        result.setSuccessUpgoodsIds(data.get("success_upgoods_ids"));
        return result;
    }


    /**
     * 原先执行逻辑  现在如果已经在超级云展上架就不去更新信息
     * $ceGoodsModel = CeGoodsModel::query()->where(['global_goods_id' => $goodsId])->whereNull('deleted_at')->first();
     * if (!$ceGoodsModel) {
     *     $ceGoodsInfo = CeGoodsModel::create($goodsData);
     *
     *     $ceGoodsId                = $ceGoodsInfo->id;
     *     $goodsInfo                = GlobalGoodsModel::query()->find($goodsId);
     *     $goodsInfo->ce_goods_id   = $ceGoodsId;
     *     $goodsInfo->peer_price    = $upGoodsList[$goodsId];
     *     $goodsInfo->ce_putaway_at = Carbon::now();
     *     $goodsInfo->save();
     * } else {
     *     $goodsUpdateData['update_seat_id'] = $userUnfos['update_seat_id'];   // 创建席位id
     *     $goodsUpdateData['update_user_id'] = $userUnfos['update_user_id'];   // 创建人id
     *     $ceGoodsModel->update($goodsUpdateData);
     * }
     * @param liveGoodsList
     * @param goodsIdPeerPriceMap
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public LiveGoodsSyncCeResult syncGoodsToSce(List<LiveGoods> liveGoodsList, Map<Long, LiveGoodsSyncCeDTO.LiveGoodsSyncCe> goodsIdPeerPriceMap) {
        //同步到云展
        LiveGoodsSyncCeResult result = new LiveGoodsSyncCeResult();
        LocalDateTime now = LocalDateTime.now();
        if (CollectionUtil.isEmpty(liveGoodsList)) {
            return result;
        }

        List<Long> globalGoodsIdList = liveGoodsList.stream().map(LiveGoods::getGlobalGoodsId).toList();
        Map<Long, ErpGoods> erpGoodsMap = erpGoodsService.list(new LambdaQueryWrapper<ErpGoods>()
                .in(ErpGoods::getId, globalGoodsIdList))
                .stream().collect(Collectors.toMap(ErpGoods::getId, x -> x));

        Set<Long> sceGoodsMapByErpId = sceGoodsService.list(new LambdaQueryWrapper<SceGoods>()
                        .select(SceGoods::getGlobalGoodsId)
                        .in(SceGoods::getGlobalGoodsId, globalGoodsIdList))
                .stream().map(SceGoods::getGlobalGoodsId).collect(Collectors.toSet());

        //同步失败的erp商品id
        List<String> failUpgoodsIds = new ArrayList<>();
        List<String> successUpgoodsIds = new ArrayList<>();
        //同步成功的id
        ArrayList<SceGoods> sceGoodsList = new ArrayList<>();
        AuthUser user = null;
        try {
            user = AuthUtil.getUser();
        }catch (Exception e){
            log.info("自动同步云展商品 - {}", liveGoodsList.getFirst().getLiveRoomId());
        }

        int sceGoodsNum = 1;

        Long scePtAuctionId = sceGoodsService.queryScePtAuctionId();

        for (LiveGoods liveGoods : liveGoodsList) {
            Long globalGoodsId = liveGoods.getGlobalGoodsId();
            if (globalGoodsId == null) continue;
            ErpGoods erpGoods = erpGoodsMap.get(globalGoodsId);
            if (erpGoods == null) {
                failUpgoodsIds.add(globalGoodsId.toString());
                continue;
            }
            //已存在 跳过同步信息
            if (sceGoodsMapByErpId.contains(liveGoods.getGlobalGoodsId())) {
                successUpgoodsIds.add(globalGoodsId.toString());
                continue;
            }
            LiveGoodsSyncCeDTO.LiveGoodsSyncCe liveGoodsSyncCe = goodsIdPeerPriceMap.get(liveGoods.getId());
            SceGoods sceGoods = new SceGoods();
            sceGoods.setOrgId(liveGoods.getOrgId());
            sceGoods.setGlobalGoodsId(liveGoods.getGlobalGoodsId());
            sceGoods.setOriginalPrice(liveGoodsSyncCe.getPeerPrice());
            sceGoods.setCurrentPrice(liveGoodsSyncCe.getPeerPrice());
            sceGoods.setGoodsStatus(SceGoodsStatusEnum.PUTAWAY);
            sceGoods.setPutawayAt(LocalDateTime.ofInstant(liveGoods.getCreatedAt().toInstant(), ZoneId.systemDefault()));
            if(liveGoods.getPutawayAt() != null){
                sceGoods.setPutawayAt(LocalDateTime.ofInstant(liveGoods.getPutawayAt().toInstant(), ZoneId.systemDefault()));
            }
            sceGoods.setCreatedAt(now);
            sceGoods.setUpdatedAt(now);
            String goodsName = liveGoods.getGoodsName();
            if (StrUtil.isNotBlank(goodsName)) {
                sceGoods.setGoodsName(goodsName);
            } else {
                sceGoods.setGoodsName(erpGoods.getName());
            }
            List<String> imgList = JSONUtil.toList(StrUtil.toStringOrNull(liveGoods.getGoodsImgUrlList()), String.class);
            if (CollUtil.isNotEmpty(imgList)) {
                sceGoods.setImgUrlList(imgList);
            } else {
                sceGoods.setImgUrlList(erpGoods.getImgUrlList());
            }

            String goodsQuality = liveGoods.getGoodsQuality();
            if (StrUtil.isNotBlank(goodsQuality)) {
                sceGoods.setQuality(goodsQuality);
            } else {
                sceGoods.setQuality(erpGoods.getQuality());
            }
            String goodsDescription = liveGoods.getGoodsDescription();
            if (StrUtil.isNotBlank(goodsDescription)) {
                sceGoods.setDescription(goodsDescription);
            } else {
                sceGoods.setDescription(erpGoods.getDescription());
            }
            sceGoods.setOrgClassifyId(erpGoods.getOrgClassifyId());
            sceGoods.setBrandId(erpGoods.getBrandId());
            sceGoods.setOrgBrandId(erpGoods.getOrgBrandId());
            sceGoods.setOrgBrandName(erpGoods.getOrgBrandName());
            String liveVideoUrl = liveGoods.getLiveVideoUrl();
            if (StrUtil.isNotBlank(liveVideoUrl)) {
                sceGoods.setVideoUrlList(List.of(liveVideoUrl));
                sceGoods.setOriginalVideoUrlList(List.of(liveVideoUrl));
            }
            sceGoods.setIfStrict(0);
            sceGoods.setIfSupportOnlinePay(1);
            sceGoods.setIfSupportCall(0);
            sceGoods.setIfSupportTake(0);
            //05-07需求 有视频回放上架为严选
            BigDecimal currentPrice = sceGoods.getCurrentPrice();
            if (StrUtil.isNotBlank(liveVideoUrl) && currentPrice != null && currentPrice.compareTo(BigDecimal.ZERO) > 0) {
                sceGoods.setIfStrict(1);
                sceGoods.setIfSupportOnlinePay(1);
                sceGoods.setIfSupportCall(1);
                sceGoods.setIfSupportTake(1);
            } else if (currentPrice == null || currentPrice.compareTo(BigDecimal.ZERO) <= 0) {
                sceGoods.setCurrentPrice(null);
            }
            sceGoods.setPlatformClassifyId(liveGoods.getPlatformClassifyId());
            if(null != user){
                sceGoods.setCreateSeatId(user.getSeatId());
                sceGoods.setCreateUserId(user.getUserId());
                sceGoods.setUpdateSeatId(user.getSeatId());
                sceGoods.setUpdateUserId(user.getUserId());
            }

            sceGoods.setSource(SceGoodsSourceEnum.LIVE);
            if(StringUtil.isNotBlank(liveServiceProperties.getSceGoodsLabels())){
                sceGoods.setLabelIdList(Arrays.stream(liveServiceProperties.getSceGoodsLabels().split(","))
                        .map(String::trim)
                        .map(Long::parseLong)
                        .collect(Collectors.toList()));
            }

            //标记为微拍卖商品
            if(scePtAuctionId!=null){
                if (StrUtil.isNotBlank(liveVideoUrl) ) {
                    sceGoods.setAuctionId(scePtAuctionId);
                    sceGoods.setStartPrice(liveGoodsSyncCe.getStartPrice());
                    if(liveGoods.getIncreasePrice()!=null){
                        sceGoods.setIncreasePrice(liveGoods.getIncreasePrice());
                    }else{
                        sceGoods.setIncreasePrice(new BigDecimal(50));
                    }
                    sceGoods.setSceGoodsNum(sceGoodsNum);
                    sceGoodsNum++;
                }
                if(liveGoodsSyncCe.getType()!=null){
                    if (liveGoodsSyncCe.getType().equals(2)) {
                        sceGoods.setAuctionType(SceGoodsAuctionTypeEnum.PT);
                        sceGoods.setGoodsStatus(SceGoodsStatusEnum.WAIT_PUTAWAY);
                        sceGoodsList.add(sceGoods);
                    }
                }
            }
            if(liveGoodsSyncCe.getType()!=null){
                if(liveGoodsSyncCe.getType().equals(0)){
                    sceGoods.setAuctionType(null);
                } else if (liveGoodsSyncCe.getType().equals(1)) {
                    sceGoods.setAuctionType(SceGoodsAuctionTypeEnum.SCE);
                    sceGoodsList.add(sceGoods);
                }
            }else{
                sceGoods.setAuctionType(SceGoodsAuctionTypeEnum.SCE);
                sceGoodsList.add(sceGoods);
            }

        }
        sceGoodsService.saveBatch(sceGoodsList);
        Map<Long, BigDecimal> sceGoodsIdSellPriceMap = sceGoodsList.stream().filter(x -> Integer.valueOf(1).equals(x.getIfStrict())).collect(Collectors.toMap(SceGoods::getId, SceGoods::getCurrentPrice));
        List<SceGoods> sceGoodsPriceChangeUpdates = sceGoodsPriceChangeService.init(user, sceGoodsIdSellPriceMap).stream().map(x->{
            SceGoods up = new SceGoods();
            up.setId(x.getSceGoodsId());
            up.setLastPriceChangeId(x.getId());
            return up;
        }).collect(Collectors.toList());
        //绑定 改价记录id
        sceGoodsService.updateBatchById(sceGoodsPriceChangeUpdates);
        //更新erp商品信息
        List<ErpGoods> erpGoodsUpList = sceGoodsList.stream().map(x -> {
            ErpGoods erpGoods = new ErpGoods();
            erpGoods.setId(x.getGlobalGoodsId());
            erpGoods.setPeerPrice(x.getCurrentPrice());
            erpGoods.setCeGoodsId(x.getId());
            erpGoods.setCePutawayAt(DateUtil.parse(LocalDateTimeUtil.format(now, DatePattern.NORM_DATETIME_PATTERN), DatePattern.NORM_DATETIME_PATTERN));
            return erpGoods;
        }).toList();
        erpGoodsService.updateBatchById(erpGoodsUpList);

        //失败成功id
        result.setFailUpgoodsIds(failUpgoodsIds);
        successUpgoodsIds.addAll(sceGoodsList.stream().map(x -> x.getGlobalGoodsId().toString()).toList());
        result.setSuccessUpgoodsIds(successUpgoodsIds);

        //成功的商品id
        Map<Long,Long> successErpGoodsIdSceGoodsIdMap = new HashMap<>();
        sceGoodsList.forEach(x -> {
            successErpGoodsIdSceGoodsIdMap.put(x.getGlobalGoodsId(), x.getId());
        });
        result.setSuccessErpGoodsIdSceGoodsIdMap(successErpGoodsIdSceGoodsIdMap);
        return result;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchSyncToCeByLiveEnd(Long liveRoomId) {
        LiveRoom liveRoom = liveRoomService.getById(liveRoomId);
        if (liveRoom == null) return;
        GlobalOrganization org = globalOrganizationService.getById(liveRoom.getOrgId());

        //获取是否同步配置
        boolean ifNeedSyncCe =
                LiveSyncSceEnum.YES.equals(liveRoom.getIfNeedSyncCe())
                || LiveSyncSceEnum.YES.equals(org.getIfNeedLiveSyncSce());
        if(!ifNeedSyncCe){
            return;
        }
        //拼装同步参数
        LiveGoodsQueryReq liveGoodsQueryReq = new LiveGoodsQueryReq();
        liveGoodsQueryReq.setLiveRoomId(liveRoomId);
        liveGoodsQueryReq.setFilterOffhandGoods(false);
        liveGoodsQueryReq.setFilterLockedOrSoldOutGoods(true);
        liveGoodsQueryReq.setFilterIfSyncCe(false);
        //待上架和流拍
        liveGoodsQueryReq.setLiveGoodsStatusList(List.of(LiveGoodsStatusEnum.WAIT_PUT_AWAY.getCode(), LiveGoodsStatusEnum.ABORTIVE_AUCTION.getCode()));


        Page<LiveGoodsDTO> page = PageUtils.getPage(liveGoodsQueryReq, LiveGoodsDTO.class);
        page.setSearchCount(false);
        page.setCurrent(-1);
        page.setSize(-1);
        var pages =  liveGoodsMapper.getLiveGoodsList(page, liveGoodsQueryReq);
        if (CollUtil.isEmpty(pages.getRecords())) {
            return ;
        }

        List<LiveGoodsSyncCeDTO.LiveGoodsSyncCe> liveGoodsCe = new ArrayList<>();
                pages.getRecords().forEach(liveGoodsDTO -> {
            LiveGoodsSyncCeDTO.LiveGoodsSyncCe syncCe = new LiveGoodsSyncCeDTO.LiveGoodsSyncCe();
            syncCe.setLiveGoodsId(liveGoodsDTO.getId());
            syncCe.setPeerPrice(liveGoodsDTO.getPeerPrice());
            liveGoodsCe.add(syncCe);
        });
        LiveGoodsDTO liveGoods = pages.getRecords().getFirst();
        AuthUser user = new AuthUser();
        user.setUserId(liveGoods.getCreateUserId());
        user.setSeatId(liveGoods.getCreateSeatId());
        user.setOrgId(liveGoods.getOrgId());
        AuthUtil.setUser(user);

        this.batchSyncLiveVideo(liveGoodsCe);
        this.batchSyncToCe(liveGoodsCe);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void syncEndLiveRoomToSce() {

        //获取需要同步的直播间id 已结束未同步 开启同步的
        List<Long> liveRoomIds = liveGoodsMapper.getNeedSyncLiveRoomIdsToSce();
        if(CollectionUtil.isEmpty(liveRoomIds)){
            return;
        }

        //获取需要同步的live_goods goods_status  10 40
        List<LiveGoods> liveGoodsList = liveGoodsMapper.getLiveGoodsListForSyncEndLiveRoomToSce(liveRoomIds);
        if (CollectionUtil.isEmpty(liveGoodsList)){
            return;
        }
        //开始同步
        //暂存同行价
        Map<Long, BigDecimal> goodsIdPeerPriceMap=new HashMap<>();
        liveGoodsList.forEach(x-> goodsIdPeerPriceMap.put(x.getId(), x.getSellPrice()));

        //同步云展
        LiveGoodsSyncCeResult result = this.syncGoodsToSceAuto(liveGoodsList, goodsIdPeerPriceMap);

        //同步失败的商品
        List<String> failUpGoodsIds = result.getFailUpgoodsIds();
        if(CollectionUtil.isNotEmpty(failUpGoodsIds)){
            //失败的商品id
            LogExUtil.errorLog("云展商品同步失败, 商品id:" + failUpGoodsIds, new ServiceException("云展商品同步失败"));
        }

        //成功同步的商品id
        List<Long> successErpGoodsIds = result.getSuccessUpgoodsIds().stream().map(Long::parseLong).toList();

        if(CollectionUtil.isNotEmpty(successErpGoodsIds)){
            // erp商品id 与 直播商品 映射关系
            Map<Long, LiveGoods> goodsIdErpGoodsIdMap = liveGoodsList.stream().collect(Collectors.toMap(LiveGoods::getGlobalGoodsId, Function.identity()));
            // 异步回写商品回放地址等信息
            ThreadPoolManager.getGlobalBizExecutor().execute(() -> writeBackSuccessGoods(goodsIdErpGoodsIdMap, successErpGoodsIds, liveGoodsList.stream().map(LiveGoods::getLiveRoomId).toList(),result.getSuccessErpGoodsIdSceGoodsIdMap()));
        }


    }

    // 生成当前时间随机往前推 1~n 分钟的时间
    public static LocalDateTime getRandomTimeBeforeNow(int maxMinutes,LocalDateTime now) {
        Random random = new Random();
        int minutesToSubtract = random.nextInt(maxMinutes) + 1; // 1~maxMinutes 分钟
        return now.minusMinutes(minutesToSubtract);
    }

    /**
     * 自动同步云展
     * @param liveGoodsList 直播商品列表
     * @param goodsIdPeerPriceMap 同行价
     * @return 同步结果
     */
    private LiveGoodsSyncCeResult syncGoodsToSceAuto(List<LiveGoods> liveGoodsList, Map<Long, BigDecimal> goodsIdPeerPriceMap) {
        //同步到云展
        LiveGoodsSyncCeResult result = new LiveGoodsSyncCeResult();
        if (CollectionUtil.isEmpty(liveGoodsList)) {
            return result;
        }

        LocalDateTime now = LocalDateTime.now();
        List<Long> globalGoodsIdList = liveGoodsList.stream().map(LiveGoods::getGlobalGoodsId).toList();
        Map<Long, ErpGoods> erpGoodsMap = erpGoodsService.list(new LambdaQueryWrapper<ErpGoods>()
                        .in(ErpGoods::getId, globalGoodsIdList))
                .stream().collect(Collectors.toMap(ErpGoods::getId, x -> x));

        Set<Long> sceGoodsMapByErpId = sceGoodsService.list(new LambdaQueryWrapper<SceGoods>()
                        .select(SceGoods::getGlobalGoodsId)
                        .in(SceGoods::getGlobalGoodsId, globalGoodsIdList))
                .stream().map(SceGoods::getGlobalGoodsId).collect(Collectors.toSet());

        //同步失败的erp商品id
        List<String> failUpgoodsIds = new ArrayList<>();
        List<String> successUpgoodsIds = new ArrayList<>();
        //同步成功的id
        ArrayList<SceGoods> sceGoodsList = new ArrayList<>();
        Iterator<LiveGoods> iterator = liveGoodsList.iterator();
        int sceGoodsNum = 1;

        Long scePtAuctionId = sceGoodsService.queryScePtAuctionId();
        //微拍卖商家信息判断
        List<Long> orgIdList = liveGoodsList.stream().map(LiveGoods::getOrgId).distinct().toList();
        Map<Long, GlobalOrganization> globalOrganizationMap = globalOrganizationService.list(new LambdaQueryWrapper<GlobalOrganization>()
                        .in(GlobalOrganization::getId, orgIdList))
                .stream().collect(Collectors.toMap(GlobalOrganization::getId, x -> x));
        while (iterator.hasNext()) {
            LiveGoods liveGoods = iterator.next();

            Long globalGoodsId = liveGoods.getGlobalGoodsId();
            if (globalGoodsId == null) continue;
            ErpGoods erpGoods = erpGoodsMap.get(globalGoodsId);
            if (erpGoods == null) {
                failUpgoodsIds.add(globalGoodsId.toString());
                iterator.remove();
                continue;
            }
            //已存在 跳过同步信息
            if (sceGoodsMapByErpId.contains(liveGoods.getGlobalGoodsId())) {
                successUpgoodsIds.add(globalGoodsId.toString());
                iterator.remove();
                continue;
            }

            BigDecimal peerPrice = goodsIdPeerPriceMap.get(liveGoods.getId());

            BigDecimal currentPrice = goodsIdPeerPriceMap.get(liveGoods.getId());
            if(null == currentPrice || currentPrice.compareTo(BigDecimal.ZERO) <= 0){
                currentPrice = liveGoods.getStartPrice();
            }

            SceGoods sceGoods = new SceGoods();
            sceGoods.setOrgId(liveGoods.getOrgId());
            sceGoods.setGlobalGoodsId(liveGoods.getGlobalGoodsId());
            sceGoods.setOriginalPrice(currentPrice);
            sceGoods.setCurrentPrice(currentPrice);
            sceGoods.setGoodsStatus(SceGoodsStatusEnum.PUTAWAY);
            sceGoods.setPutawayAt(getRandomTimeBeforeNow(liveServiceProperties.getAutoSyncScePutawayTime(),now));
            sceGoods.setCreatedAt(now);
            sceGoods.setUpdatedAt(now);
            String goodsName = liveGoods.getGoodsName();
            if (StrUtil.isNotBlank(goodsName)) {
                sceGoods.setGoodsName(goodsName);
            } else {
                sceGoods.setGoodsName(erpGoods.getName());
            }
            List<String> imgList = JSONUtil.toList(StrUtil.toStringOrNull(liveGoods.getGoodsImgUrlList()), String.class);
            if (CollUtil.isNotEmpty(imgList)) {
                sceGoods.setImgUrlList(imgList);
            } else {
                sceGoods.setImgUrlList(erpGoods.getImgUrlList());
            }

            String goodsQuality = liveGoods.getGoodsQuality();
            if (StrUtil.isNotBlank(goodsQuality)) {
                sceGoods.setQuality(goodsQuality);
            } else {
                sceGoods.setQuality(erpGoods.getQuality());
            }
            String goodsDescription = liveGoods.getGoodsDescription();
            if (StrUtil.isNotBlank(goodsDescription)) {
                sceGoods.setDescription(goodsDescription);
            } else {
                sceGoods.setDescription(erpGoods.getDescription());
            }
            sceGoods.setOrgClassifyId(erpGoods.getOrgClassifyId());
            sceGoods.setBrandId(erpGoods.getBrandId());
            sceGoods.setOrgBrandId(erpGoods.getOrgBrandId());
            sceGoods.setOrgBrandName(erpGoods.getOrgBrandName());
            String liveVideoUrl = liveGoods.getLiveVideoUrl();

            if (StrUtil.isNotBlank(liveVideoUrl)) {
                sceGoods.setVideoUrlList(List.of(liveVideoUrl));
                sceGoods.setOriginalVideoUrlList(List.of(liveVideoUrl));
            }

            sceGoods.setIfStrict(0);
            sceGoods.setIfSupportOnlinePay(1);
            sceGoods.setIfSupportCall(0);
            sceGoods.setIfSupportTake(0);
            //05-07需求 有视频回放上架为严选
            if (StrUtil.isNotBlank(liveVideoUrl) && peerPrice != null && peerPrice.compareTo(BigDecimal.ZERO) > 0) {
                sceGoods.setIfStrict(1);
                sceGoods.setIfSupportOnlinePay(1);
                sceGoods.setIfSupportCall(1);
                sceGoods.setIfSupportTake(1);
            } else if (currentPrice == null || currentPrice.compareTo(BigDecimal.ZERO) <= 0) {
                sceGoods.setCurrentPrice(null);
            }
            sceGoods.setPlatformClassifyId(liveGoods.getPlatformClassifyId());
            sceGoods.setCreateSeatId(liveGoods.getCreateSeatId());
            sceGoods.setCreateUserId(liveGoods.getCreateUserId());
            sceGoods.setUpdateSeatId(liveGoods.getUpdateSeatId());
            sceGoods.setUpdateUserId(liveGoods.getUpdateUserId());

            sceGoods.setSource(SceGoodsSourceEnum.LIVE);
            if(StringUtil.isNotBlank(liveServiceProperties.getSceGoodsLabels())){
                sceGoods.setLabelIdList(Arrays.stream(liveServiceProperties.getSceGoodsLabels().split(","))
                        .map(String::trim)
                        .map(Long::parseLong)
                        .collect(Collectors.toList()));
            }
            //标记为微拍卖商品
            if(scePtAuctionId!=null) {
                GlobalOrganization globalOrganization = globalOrganizationMap.get(liveGoods.getOrgId());
                if (StrUtil.isNotBlank(liveVideoUrl) && globalOrganization.getIfNeedPtSyncSce().equals(LiveSyncSceEnum.YES)) {
                    sceGoods.setAuctionType(SceGoodsAuctionTypeEnum.PT);
                    sceGoods.setAuctionId(scePtAuctionId);
                    if(liveGoods.getSellPrice() == null){
                        sceGoods.setOriginalPrice(liveGoods.getStartPrice());
                        sceGoods.setCurrentPrice(liveGoods.getStartPrice());
                        sceGoods.setIfSupportOnlinePay(1);
                        sceGoods.setIfSupportTake(0);
                    }
                    sceGoods.setStartPrice(liveGoods.getStartPrice());
                    if(liveGoods.getIncreasePrice()!=null){
                        sceGoods.setIncreasePrice(liveGoods.getIncreasePrice());
                    }else{
                        sceGoods.setIncreasePrice(new BigDecimal(50));
                    }
                    sceGoods.setSceGoodsNum(sceGoodsNum);
                    sceGoods.setGoodsStatus(SceGoodsStatusEnum.WAIT_PUTAWAY);

                    sceGoodsNum++;
                }
            }
            sceGoodsList.add(sceGoods);
            sceGoodsMapByErpId.add(sceGoods.getGlobalGoodsId());
        }


        sceGoodsService.saveBatch(sceGoodsList);

        List<SceGoods> sceGoodsPriceChangeUpdates = sceGoodsPriceChangeService.initBySceGoods(sceGoodsList)
                .stream().map(x->{
            SceGoods up = new SceGoods();
            up.setId(x.getSceGoodsId());
            up.setLastPriceChangeId(x.getId());
            return up;
        }).collect(Collectors.toList());
        //绑定 改价记录id
        sceGoodsService.updateBatchById(sceGoodsPriceChangeUpdates);

        //更新erp商品信息
        List<ErpGoods> erpGoodsUpList = sceGoodsList.stream().map(x -> {
            ErpGoods erpGoods = new ErpGoods();
            erpGoods.setId(x.getGlobalGoodsId());
            erpGoods.setPeerPrice(x.getCurrentPrice());
            erpGoods.setCeGoodsId(x.getId());
            erpGoods.setCePutawayAt(DateUtil.parse(LocalDateTimeUtil.format(x.getPutawayAt(), DatePattern.NORM_DATETIME_PATTERN), DatePattern.NORM_DATETIME_PATTERN));
            return erpGoods;
        }).toList();
        erpGoodsService.updateBatchById(erpGoodsUpList);

        //失败成功id
        result.setFailUpgoodsIds(failUpgoodsIds);

        successUpgoodsIds.addAll(sceGoodsList.stream().map(x -> x.getGlobalGoodsId().toString()).toList());

        result.setSuccessUpgoodsIds(successUpgoodsIds);

        //成功的商品id
        Map<Long,Long> successErpGoodsIdSceGoodsIdMap = new HashMap<>();
        sceGoodsList.forEach(x -> {
            successErpGoodsIdSceGoodsIdMap.put(x.getGlobalGoodsId(), x.getId());
        });
        result.setSuccessErpGoodsIdSceGoodsIdMap(successErpGoodsIdSceGoodsIdMap);
        return result;
    }

}
