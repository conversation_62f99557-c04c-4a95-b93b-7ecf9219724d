package com.bbh.live.service.buyer.orgmap.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2024/10/9 09:04
 * @description
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
public class OrgAddressInfoVO {

    /**
     * 店铺id
     */
    private Long id;

    /**
     * 店铺id 加密
     */
    private String orgId;

    /**
     * 店铺id 未加密
     */
    private String originalOrgId;

    /**
     * 店铺名称
     */
    private String orgName;

    /**
     * 店长名称
     */
    private String bossName;

    /**
     * logo
     */
    private String logoUrl;

    /**
     * 距离
     */
    private Double distance;

    /**
     * 距离描述 1km  500m
     */
    private String distanceDesc;

    /**
     * 省
     */
    private String province;
    /**
     * 市
     */
    private String city;
    /**
     * 区
     */
    private String region;

    /**
     * 纬度
     */
    private Double lat;

    /**
     * 经度
     */
    private Double lng;

    /**
     * 省市区
     */
    private String addressDesc;

    /**
     * 详细地址
     */
    private String addressDetail;

    /**
     * 客服id
     */
    private String customerServiceId;

    /**
     * seatId
     */
    private String seatId;

}
