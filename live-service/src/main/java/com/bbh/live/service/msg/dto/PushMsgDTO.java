package com.bbh.live.service.msg.dto;

import com.bbh.live.service.msg.push.LivePushTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/10/23 14:38
 * @description
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
public class PushMsgDTO {

    private Long orgId;

    private List<Long> seatId;

    private Long userId;

    private Long orderId;

    private Long goodsId;

    private Long liveRoomId;

    private String goodsImage;

    private Object[] contentArgs;

    private LivePushTypeEnum pushType;

    private Map<String, Object> params;
}
