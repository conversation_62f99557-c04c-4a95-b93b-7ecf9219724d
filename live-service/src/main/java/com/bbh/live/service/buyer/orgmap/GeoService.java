package com.bbh.live.service.buyer.orgmap;

import com.bbh.live.service.buyer.orgmap.geo.GeoSearchParams;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/10/8 17:13
 * @description 经纬度计算接口
 */
public interface GeoService {

    /**
     * 计算两个坐标之间的距离 单位米
     * @param position1
     * @param position2
     * @return 距离
     */
    double getDistance(GeoPosition position1, GeoPosition position2);

    /**
     * 根据参数查询指定坐标与附近其他坐标的距离
     * @param args
     * @return 
     */
    Map<Long, Double> searchWithDistance(GeoSearchParams args);


    record GeoPosition(double longitude, double latitude){}
}
