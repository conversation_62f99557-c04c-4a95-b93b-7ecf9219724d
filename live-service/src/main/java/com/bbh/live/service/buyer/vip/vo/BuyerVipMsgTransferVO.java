package com.bbh.live.service.buyer.vip.vo;

import com.bbh.live.enums.BuyerVipTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <AUTHOR> dsy
 * @Date: 2024/9/13
 * @Description:
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
public class BuyerVipMsgTransferVO {

    private Long vipId;

    private Long buyerSeatId;

    /**
     * 0 不是
     * 1 是 没有过期
     * 2 是 但是过期了
     */
    private BuyerVipTypeEnum buyerVipType;

    /**
     * vip等级
     */
    private Integer vipLevel;

    /**
     *     是否年费会员
     */
    private Boolean isAnnualFeeVip = false;
}
