package com.bbh.live.service.redis.impl;

import cn.hutool.json.JSONUtil;
import com.bbh.live.service.redis.RedisHashService;
import com.bbh.util.ParamsUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
@AllArgsConstructor
public class RedisHashServiceImpl implements RedisHashService {

    private final StringRedisTemplate stringRedisTemplate;

    @Override
    public <T> void put(String key, String hashKey, T value) {
        String jsonStr = ParamsUtil.convertObjectToString(value);
        stringRedisTemplate.opsForHash().put(key, hashKey, jsonStr);
    }

    @Override
    public <T> void putWithExpire(String key, String hashKey, T value, long expire, TimeUnit timeUnit) {
        String jsonStr = JSONUtil.toJsonStr(value);
        stringRedisTemplate.opsForHash().put(key, hashKey, jsonStr);
        stringRedisTemplate.expire(key, expire, timeUnit);
    }

    @Override
    public <T> void putWithExpireAt(String key, String hashKey, T value, Date expireAt) {
        String jsonStr = JSONUtil.toJsonStr(value);
        stringRedisTemplate.opsForHash().put(key, hashKey, jsonStr);
        stringRedisTemplate.expireAt(key, expireAt);
    }

    @Override
    public <T> T get(String key, String hashKey, Class<T> clazz) {
        String objStr = getHashValue(key, hashKey);
        return JSONUtil.toBean(objStr, clazz);
    }

    @Override
    public <T> List<T> getList(String key, String hashKey, Class<T> clazz) {
        String objStr = getHashValue(key, hashKey);
        return JSONUtil.toList(objStr, clazz);
    }

    private String getHashValue(String key, String hashKey) {
        if (!stringRedisTemplate.opsForHash().hasKey(key, hashKey)) {
            return null;
        }
        return (String) stringRedisTemplate.opsForHash().get(key, hashKey);
    }

    @Override
    public <T> Map<String, T> entities(String key, Class<T> clazz) {
        if (Boolean.FALSE.equals(stringRedisTemplate.opsForValue().getOperations().hasKey(key))) {
            return Map.of();
        }
        Map<String, T> map = new HashMap<>(8);
        stringRedisTemplate.opsForHash().entries(key)
                .forEach((k, v) ->
                        map.put((String) k, JSONUtil.toBean((String) v, clazz)));
        return map;
    }

    @Override
    public void remove(String key, String hashKey) {
        stringRedisTemplate.opsForHash().delete(key, hashKey);
    }

    @Override
    public boolean remove(String key) {
        return Boolean.TRUE.equals(stringRedisTemplate.delete(key));
    }
}
