package com.bbh.live.service.user.permission;

import com.bbh.model.GlobalOrgRole;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/10/23 09:19
 * @description
 */
public class GlobalPermissionCheckServiceImpl extends AbstractPermissionCheckService {

    @Override
    protected Integer getPermissionIfAll(GlobalOrgRole globalOrgRole) {
        return globalOrgRole.getGlobalPermissionIfAll();
    }

    @Override
    protected List<String> getPermissionCodeList(GlobalOrgRole globalOrgRole) {
        return globalOrgRole.getGlobalPermissionCodeList();
    }
}
