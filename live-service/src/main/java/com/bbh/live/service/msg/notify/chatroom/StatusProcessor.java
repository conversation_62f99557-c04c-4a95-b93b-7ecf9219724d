package com.bbh.live.service.msg.notify.chatroom;

import com.bbh.live.dao.dto.ChatroomStatusSyncDTO;
import com.bbh.live.service.msg.notify.chatroom.enums.ChatroomStatus;

/**
 * 状态处理
 * <AUTHOR>
 */
public interface StatusProcessor {

    /**
     * 处理聊天室状态同步
     * @param dto 聊天室状态同步DTO
     */
    void process(ChatroomStatusSyncDTO dto);

    /**
     * 判断处理器是否可以处理该状态
     * @param status 聊天室状态
     * @return 如果可以处理返回true,否则返回false
     */
    boolean canHandle(ChatroomStatus status);

}
