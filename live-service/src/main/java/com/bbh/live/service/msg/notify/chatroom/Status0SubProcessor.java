package com.bbh.live.service.msg.notify.chatroom;

import com.bbh.live.dao.dto.ChatroomStatusSyncDTO;
import com.bbh.live.service.msg.notify.chatroom.enums.ChatroomActionType;

/**
 * 状态0的子处理
 * 聊天室事件类型取值如下：0 为创建聊天室、1 加入聊天室、2 退出聊天室、3 销毁聊天室。
 * <AUTHOR>
 */
public interface Status0SubProcessor {

    /**
     * 处理状态0的聊天室状态同步
     * @param dto 聊天室状态同步DTO
     * @param liveRoomId 直播间ID
     */
    void process(ChatroomStatusSyncDTO dto, Long liveRoomId);

    /**
     * 判断子处理器是否可以处理该事件类型
     * @param type 聊天室事件类型
     * @return 如果可以处理返回true,否则返回false
     */
    boolean canHandle(ChatroomActionType type);

}
