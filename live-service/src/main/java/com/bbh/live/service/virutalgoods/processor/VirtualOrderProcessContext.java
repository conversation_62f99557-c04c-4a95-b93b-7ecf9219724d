package com.bbh.live.service.virutalgoods.processor;

import com.bbh.live.service.buyer.vip.vo.UserBuyerVipInfoVO;
import com.bbh.model.GlobalOrderOtherGoodsDic;
import com.bbh.model.GlobalVirtualGoodsOrder;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> dsy
 * @Date: 2024/9/12
 * @Description:
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class VirtualOrderProcessContext {

    private GlobalVirtualGoodsOrder order;

    private GlobalOrderOtherGoodsDic goods;

    private UserBuyerVipInfoVO vipInfo;
}
