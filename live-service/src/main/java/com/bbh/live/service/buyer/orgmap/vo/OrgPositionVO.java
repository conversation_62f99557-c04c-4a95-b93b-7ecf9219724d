package com.bbh.live.service.buyer.orgmap.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024/10/9 15:37
 * @description
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
public class OrgPositionVO {

    /**
     * 店铺id
     */
    private Long id;

    /**
     * 店铺id
     */
    private String orgId;

    /**
     * 店铺名称
     */
    private String orgName;

    /**
     * 经度
     */
    private BigDecimal addrLongitude;
    /**
     * 纬度
     */
    private BigDecimal addrLatitude;

    /**
     * 距离
     */
    private String distance;

    /**
     * 是否可点击
     */
    private Boolean ifClick;
}
