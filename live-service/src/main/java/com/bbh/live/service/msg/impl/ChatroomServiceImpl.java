package com.bbh.live.service.msg.impl;

import com.bbh.live.core.msg.ImProperties;
import com.bbh.live.core.msg.ImService;
import com.bbh.live.dao.dto.ChatroomStatusSyncDTO;
import com.bbh.live.service.msg.ChatroomService;
import com.bbh.live.service.msg.support.ChatroomStatusSyncProcessor;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 聊天室
 * <AUTHOR>
 */
@Slf4j
@Service
@AllArgsConstructor
public class ChatroomServiceImpl implements ChatroomService {

    private final ImService imService;
    private final ChatroomStatusSyncProcessor chatroomStatusSyncProcessor;


    /**
     * 构建直播间专用的聊天室ID
     *
     * @param liveRoomId 直播间ID
     * @return 聊天室ID
     */
    @Override
    public String buildChatroomId(Long liveRoomId) {
        return ImProperties.LIVE_ROOM_CHAT_ROOM_ID_PREFIX + liveRoomId;
    }

    /**
     * 创建直播间专用的聊天室
     *
     * @param liveRoomId 直播间ID
     * @return 聊天室ID
     */
    @Override
    public String createChatroom(Long liveRoomId) {
        return createChatroom(buildChatroomId(liveRoomId));
    }

    @Override
    public String createChatroom(String chatroomId) {
        return imService.createChatRoom(chatroomId);
    }

    /**
     * 聊天室状态同步
     */
    @Override
    public void chatroomStatusSync(List<ChatroomStatusSyncDTO> chatroomStatusSyncDTOList) {
        chatroomStatusSyncProcessor.processStatusSync(chatroomStatusSyncDTOList);
    }


}
