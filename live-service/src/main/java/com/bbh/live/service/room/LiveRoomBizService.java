package com.bbh.live.service.room;

import com.bbh.base.ListBase;
import com.bbh.base.PageBase;
import com.bbh.live.core.msg.MsgDTO;
import com.bbh.live.dao.dto.*;
import com.bbh.live.dao.dto.vo.*;
import com.bbh.live.enums.LiveRoomEnhancedStatusEnum;
import com.bbh.live.enums.SeatRoleInRoomEnum;
import com.bbh.model.LiveRoom;
import com.bbh.vo.AuthUser;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 直播间业务
 * <AUTHOR>
 */
public interface LiveRoomBizService {

    /**
     * 创建直播间
     * @param createLiveRoomDTO 直播间基础信息
     */
    void createLiveRoom(CreateLiveRoomDTO createLiveRoomDTO);

    /**
     * 根据时间和流的状态，计算直播间真实状态
     * @param liveRoom  直播间
     * @return  直播间真实状态
     */
    LiveRoomEnhancedStatusEnum computedLiveRoomEnhancedStatus(LiveRoomVO liveRoom);

    /**
     * 根据时间和流的状态，计算直播间真实状态
     * @param liveRoomId    直播间id
     * @return  直播间真实状态
     */
    LiveRoomEnhancedStatusEnum computedLiveRoomEnhancedStatus(Long liveRoomId);

    /**
     * 分页查询直播间列表
     * @param queryLiveRoomDTO 查询参数
     * @return 分页结果
     */
    ListBase<LiveRoomVO> getLiveRoomList(QueryLiveRoomDTO queryLiveRoomDTO);

    /**
     * 获取直播间列表（用于上下滑动切换）
     *
     * @param queryDTO 查询参数（仅支持sort_room_id, perPage, currentPage）
     * @return 直播间列表
     */
    ListBase<LiveRoomVO> getLiveRoomListForSlide(QueryLiveRoomDTO queryDTO);

    /**
     * 查询导播的直播间详情
     * @param roomId 直播间id
     * @param isDirector 是否是导播调用
     * @return 直播间详情
     */
    DirectorRoomDetailVO getDirectorRoomDetail(Long roomId, Boolean isDirector);

    /**
     * 导播的直播间统计信息
     * @param roomId 直播间id
     * @return 直播间详情
     */
    DirectorRoomStatisticsVO getDirectorRoomStatistics(Long roomId);

    /**
     * 查询看播的直播间详情
     * @param roomId 直播间id
     * @return 直播间详情
     */
    WatcherRoomDetailVO getWatcherRoomDetail(Long roomId);

    /**
     * 查询导播的商品上架队列
     * @param roomId 直播间id
     * @return 上架队列
     */
    DirectorRoomGoodsQueueDTO getDirectorRoomGoodsQueue(Long roomId);

    /**
     * 更新直播公告
     * @param roomId 直播间ID
     * @param notice 公告
     */
    void updateNotice(Long roomId, String notice);

    /**
     * 获取看播的直播间卡片消息列表
     *
     * @param roomId 直播间ID
     * @return 需要显示的卡片消息列表
     */
    List<MsgDTO<?>> getBuyerRoomCardMessageList(Long roomId);

    /**
     * 获取导播的直播间内卡片消息列表
     * @param roomId 直播间ID
     * @return 需要显示的卡片消息
     */
    List<MsgDTO<?>> getDirectorRoomCardMessageList(Long roomId);

    /**
     * 预约直播间
     * @param roomId 直播间id
     */
    void subscribe(Long roomId);

    /**
     * 取消预约直播间
     * @param roomId 直播间id
     */
    void cancelSubscribe(Long roomId);

    /**
     * 获取推荐直播间列表
     * @return
     */
    List<LiveRoomRecommendVO> getRecommendRoomList();

    /**
     * 过滤黑白名单后 直播间是否可见
     * @param roomId
     * @param userId
     * @return
     */
    boolean liveRoomIsVisible(Long roomId, Long userId);

    /**
     * 获取直播间回放列表
     *      查询直播实际结束时间 12h（根据直播间的商品清单保持时长决定）以内，
     *      并且直播结束后把剩余商品同步到云展的直播场次
     *      同时过滤黑白名单
     *
     * @param page 分页信息
     * @return 回放列表
     */
    ListBase<PlayBackLiveRoomVO> getPlaybackLiveRoom(PageBase page);

    /**
     * 分享直播间
     * @param roomId
     */
    String shareLiveRoom(RoomIdDTO roomId);


    /**
     * 获取所有正在直播的直播间，过滤黑白名单
     * @param user
     * @return
     */
    List<LiveRoom> getInLiveRoomList(AuthUser user);

    /**
     * 计算直播间当前用户角色
     * @param liveRoomVO
     * @param directorMap
     * @param seatId
     * @return
     */
    SeatRoleInRoomEnum computedSeatRole(LiveRoomVO liveRoomVO, Map<Long, List<Long>> directorMap, Long seatId);

    /**
     * 计算是否直播间的员工
     * @param roomOrgSeatIdList 直播间商户的席位ID列表
     * @param seatId            当前人席位
     * @return                  是否直播间的员工
     */
    Boolean computedIfRelatedStaff(List<Long> roomOrgSeatIdList, Long seatId);

    /**
     * 直播间导播席位 id
     * @param liveRoomIdList 直播间id列表
     * @return
     */
    Map<Long, List<Long>> computedLiveRoomDirectorMap(List<Long> liveRoomIdList);

    /**
     * 获取直播间的剩余即拍即上次数
     */
    Integer getOffhandRemainTimes(Long roomId);

    /**
     * 当前用户是否有权限过滤黑名单
     * @param user
     * @return
     */
    Boolean ifFilterBlacklist(AuthUser user);

    /**
     * 检查看播的观看权限
     * @param roomId    直播间id
     * @return          是否有权限
     */
    Boolean checkWatcherPermission(Long roomId);

    /**
     * 分页查询小程序直播间列表
     * @param queryLiveRoomDTO 查询参数
     * @return 分页结果
     */
    ListBase<LiveRoomVO> getMiniLiveRoomList(QueryLiveRoomDTO queryLiveRoomDTO);

    ListBase<LiveRoomVO> getMiniLiveRoomListForSlide(QueryLiveRoomDTO queryDTO);

    /**
     * 商家是否存在违规记录
     *
     * @return true/false
     */
    Long countOrgIllegalRecord();

    /**
     * 增加成交金额
     * @param roomId        直播间id
     * @param tradeAmount   成交金额
     * @param goodsCount    成交商品数量
     */
    void incrLiveRoomTradeAmount(Long roomId, BigDecimal tradeAmount, Integer goodsCount);

    ListBase<LiveRoomVO> getFinishedTradeRoomList(QueryLiveRoomDTO queryLiveRoomDTO);

    /**
     * 用于导播的同步云展弹框提交
     * @param roomSyncSceDTO        参数
     */
    void submitSyncSce(RoomSyncSceDTO roomSyncSceDTO);
}
