package com.bbh.live.service.buyer;

import com.bbh.live.dao.dto.BargainGoodsDTO;
import com.bbh.live.dao.dto.livegoods.LiveGoodsSubscribeDTO;
import com.bbh.live.dao.dto.vo.AuctionBidVO;

/**
 * <AUTHOR> dsy
 * @Date: 2024/7/30
 * @Description: 买手端直播商品操作服务
 */
public interface BuyerLiveGoodsOpService {


    /**
     * 商品流拍后 议价
     *
     * @return
     */
    AuctionBidVO bargainLiveGoods(BargainGoodsDTO bargainGoods);

    /**
     * 商品添加到收银台
     * @param bargainGoods
     */
    void addGoodsToCashierDesk(BargainGoodsDTO bargainGoods);

    /**
     * 发送求讲解互动消息
     * @param bargainGoods
     */
    void askForExplanation(BargainGoodsDTO bargainGoods);

    /**
     * 竞拍出价
     *
     * @param bargainGoods
     */
    AuctionBidVO auctionBid(BargainGoodsDTO bargainGoods);

    /**
     * 商品预约
     * @param liveGoodsSubscribe
     */
    void subscribe(LiveGoodsSubscribeDTO liveGoodsSubscribe);

    /**
     * 取消预约
     * @param liveGoodsSubscribe
     */
    void cancelSubscribe(LiveGoodsSubscribeDTO liveGoodsSubscribe);

    /**
     * 关闭预约商品提醒卡片
     * @param liveGoodsSubscribe
     */
    void closeSubscribeCard(LiveGoodsSubscribeDTO liveGoodsSubscribe);

    /**
     * 传送商品 要了
     *
     * @param bargainGoods
     * @return
     */
    AuctionBidVO takeTheTransferGoods(BargainGoodsDTO bargainGoods);
}
