package com.bbh.live.service.admin;

import cn.hutool.extra.spring.SpringUtil;
import com.bbh.live.thread.ThreadPoolManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.rabbit.listener.MessageListenerContainer;
import org.springframework.amqp.rabbit.listener.RabbitListenerEndpointRegistry;
import org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * <AUTHOR>
 * @date 2024/12/5 16:41
 * @description
 */
@Service
public class AdminService {

    private static final Logger log = LoggerFactory.getLogger(AdminService.class);
    private final AtomicBoolean shutdown = new AtomicBoolean(false);


    public void shutdown() {
        if(shutdown.compareAndSet(false, true)){
            try{
                // 关闭竞拍结算消费者线程池
                ((ExecutorService)ThreadPoolManager.getAutoConsumeExecutor(-1)).shutdownNow();
                // 关闭mq消费者
                Collection<MessageListenerContainer> listenerContainers = SpringUtil.getBean(RabbitListenerEndpointRegistry.class).getListenerContainers();
                listenerContainers.forEach(container -> {
                    if(container instanceof SimpleMessageListenerContainer){
                        container.stop();
                    }
                });
            }catch (Throwable t){
                log.error("应用关闭异常: {}", t.getMessage(), t);
            }
        }
    }

    public boolean isShutdown() {
        return shutdown.get();
    }
}
