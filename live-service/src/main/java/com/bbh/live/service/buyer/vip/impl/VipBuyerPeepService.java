package com.bbh.live.service.buyer.vip.impl;

import com.bbh.enums.GlobalBizTypeEnum;
import com.bbh.live.dao.dto.VipPeepDTO;
import com.bbh.live.dao.dto.livegoods.LiveGoodsDTO;
import com.bbh.live.dao.mapper.GlobalOrgSeatMapper;
import com.bbh.live.dao.service.LiveRoomService;
import com.bbh.live.enums.BuyerVipTypeEnum;
import com.bbh.live.service.buyer.vip.BuyerVipService;
import com.bbh.live.service.buyer.vip.vo.UserBuyerVipInfoVO;
import com.bbh.live.service.livegoods.LiveGoodsDetailService;
import com.bbh.model.GlobalOrgSeat;
import com.bbh.model.LiveRoom;
import com.bbh.model.VipBuyerPeepLog;
import com.bbh.secure.AuthUtil;
import com.bbh.util.AssertUtil;
import com.bbh.vo.AuthUser;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 会员查看价格
 * <AUTHOR>
 */
@Service
@AllArgsConstructor
public class VipBuyerPeepService {

    private final BuyerVipService buyerVipService;
    private final LiveRoomService liveRoomService;
    private final GlobalOrgSeatMapper globalOrgSeatMapper;
    private final LiveGoodsDetailService liveGoodsDetailService;

    /**
     * 创建查看记录
     *
     * @param vipPeepDTO 查看参数
     */
    @Transactional(rollbackFor = Exception.class)
    public void createPoopLog(VipPeepDTO vipPeepDTO) {
        AssertUtil.assertNotNull(vipPeepDTO.getBidAmount(), "出价金额不能为空");
        AssertUtil.assertNotNull(vipPeepDTO.getBidSeatId(), "出价人不能为空");
        AssertUtil.assertNotNull(vipPeepDTO.getLiveRoomId(), "直播间ID不能为空");
        AssertUtil.assertNotNull(vipPeepDTO.getLiveGoodsId(), "商品ID不能为空");

        AuthUser user = AuthUtil.getUser();
        UserBuyerVipInfoVO vipInfo = buyerVipService.getUserBuyerVipInfoBySeatId(user.getSeatId());

        // 检查是否有权限
        AssertUtil.assertTrue(vipInfo.getPeepBuyerUnUsedTimes() != null && vipInfo.getBuyerVipType() == BuyerVipTypeEnum.VIP, "您不是会员，无法查看出价人");
        // 检查是否有使用次数
        AssertUtil.assertTrue(vipInfo.getPeepBuyerUnUsedTimes() > 0, "您的查看次数已用完");

        // 出价人席位信息
        GlobalOrgSeat bidUserSeat = globalOrgSeatMapper.selectById(vipPeepDTO.getBidSeatId());
        AssertUtil.assertNotNull(bidUserSeat, "出价人不存在");
        // 商品信息
        LiveGoodsDTO goodsDetailInfo = liveGoodsDetailService.getLiveGoodsDetailInfo(vipPeepDTO.getLiveGoodsId());
        AssertUtil.assertNotNull(goodsDetailInfo, "商品不存在");

        LiveRoom liveRoom = liveRoomService.lambdaQuery().eq(LiveRoom::getId, vipPeepDTO.getLiveRoomId()).select(LiveRoom::getId, LiveRoom::getRoomName).one();
        AssertUtil.assertNotNull(liveRoom, "直播间不存在");

        // 有权限就记录日志
        VipBuyerPeepLog peepLog = new VipBuyerPeepLog();
        peepLog.setVipBuyerCardId(vipInfo.getId());
        peepLog.setBidAmount(vipPeepDTO.getBidAmount());
        peepLog.setBidOrgId(bidUserSeat.getOrgId());
        peepLog.setBidSeatId(vipPeepDTO.getBidSeatId());
        peepLog.setBidUserId(bidUserSeat.getUserId());
        peepLog.setBidSeatName(bidUserSeat.getShowName());
        peepLog.setBizGoodsId(vipPeepDTO.getLiveGoodsId());
        peepLog.setBizId(vipPeepDTO.getLiveRoomId());
        peepLog.setBizType(GlobalBizTypeEnum.LIVE);
        peepLog.setBizName(liveRoom.getRoomName());
        peepLog.setGoodsName(goodsDetailInfo.getGlobalGoodsName());
        peepLog.setPeepSeatId(user.getSeatId());

        // 更新使用次数和日志
        buyerVipService.updateUseTimesBySeatId(user.getSeatId(), peepLog);
    }
}

