package com.bbh.live.service.buyer.impl;

import com.bbh.live.dao.dto.vo.AuctionBidVO;
import lombok.Getter;

import java.math.BigDecimal;

/**
 * 保证金校验专用的异常
 * <AUTHOR>
 */
@Getter
public class DepositCheckException extends RuntimeException {

    private final AuctionBidVO bidResult;

    public DepositCheckException(AuctionBidVO bidResult) {
        this.bidResult = bidResult;
    }

    public DepositCheckException(BigDecimal minBidDeposit, BigDecimal buyerDeposit) {
        this.bidResult = new AuctionBidVO().setIfNeedDepositCharge(true).setMinBidDeposit(minBidDeposit).setBuyerDeposit(buyerDeposit);
    }

}
