package com.bbh.live.service.buyerCancel.dto.param;

import com.bbh.enums.GlobalOrderTypeEnum;
import com.bbh.live.service.buyerCancel.dto.BuyerCancelBiz;
import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import java.io.Serializable;

/**
 * 买家取消记录查询参数
 * 专用于Mybatis查询参数
 *
 * <AUTHOR>
 * @since 2025-01-11
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LiveGoodsBuyerCancelRecordQueryParam  implements Serializable ,BuyerCancelBiz{

    private static final long serialVersionUID = 1L;

    /**
     * 取消状态
     */
    private Integer cancelStatus;

    /**
     * 买家组织ID
     */
    private Long buyerOrgId;

    /**
     * 卖家组织ID
     */
    private Long sellerOrgId;

    /**
     * 买家席位ID
     */
    private Long buyerSeatId;

    private Long recordId;

    private GlobalOrderTypeEnum bizType;

    @Override
    public GlobalOrderTypeEnum getBizType() {
        return this.bizType;
    }

    @Override
    public void setBizType(GlobalOrderTypeEnum bizType) {
         this.bizType=bizType;
    }

}