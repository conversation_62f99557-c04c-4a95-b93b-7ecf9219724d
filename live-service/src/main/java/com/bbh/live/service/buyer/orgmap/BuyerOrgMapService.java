package com.bbh.live.service.buyer.orgmap;

import com.bbh.live.controller.req.OrgMapSearchReq;
import com.bbh.live.service.buyer.orgmap.vo.OrgAddressInfoVO;
import com.bbh.live.service.buyer.orgmap.vo.OrgPositionVO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/10/9 12:03
 * @description
 */
public interface BuyerOrgMapService {


    /**
     * 获取当前用户附近的商家
     * @param searchReq
     * @return
     */
    List<OrgAddressInfoVO> getNearbyOrgSortedByDistanceAsc(OrgMapSearchReq searchReq);

    /**
     * 获取所有商家坐标信息
     * @param searchReq
     * @return
     */
    List<OrgPositionVO> getAllOrgWithDistance(OrgMapSearchReq searchReq);

    /**
     * 获取商家名片信息
     * @param searchReq
     * @return
     */
    OrgAddressInfoVO getOrgBusinessCard(OrgMapSearchReq searchReq);
}
