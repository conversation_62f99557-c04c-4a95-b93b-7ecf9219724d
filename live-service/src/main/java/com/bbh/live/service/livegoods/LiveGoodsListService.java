package com.bbh.live.service.livegoods;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.bbh.live.controller.req.LiveGoodsQueryReq;
import com.bbh.live.dao.dto.AbortiveAuctionGoodsDTO;
import com.bbh.live.dao.dto.livegoods.LiveGoodsDTO;
import com.bbh.live.dao.dto.livegoods.LiveGoodsSortDTO;
import com.bbh.live.dao.dto.livegoods.LiveGoodsStatisticsDTO;
import com.bbh.live.dao.dto.vo.AuctionLiveGoodsVO;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> dsy
 * @Date: 2024/7/26
 * @Description:
 */
public interface LiveGoodsListService {

    /**
     * 从erp仓库添加商品到直播清单
     * @param liveRoomId 直播间id
     * @param globalGoodsIdList erp商品id集合
     * @return 成功添加的商品数量
     */
    int addLiveGoodsListFromErpStorehouse(Long liveRoomId, List<Long> globalGoodsIdList);

    /**
     * 商品清单移除商品
     * @param liveGoodsIdList
     * @return 成功移除的商品数量
     */
    int removeLiveGoodsList(List<Long> liveGoodsIdList);

    /**
     *  直播商品清单 默认按照顺序号排序
     * @param queryReq
     * @return
     */
    IPage<LiveGoodsDTO> getLiveGoodsList(LiveGoodsQueryReq queryReq);

    /**
     * 批量更新直播商品排序
     * @param sortDTOList
     */
    void batchUpdateLiveGoodsSortValue(List<LiveGoodsSortDTO> sortDTOList);

    /**
     * 将当前商品放在待上架第一位
     * @param liveGoodsId
     */
    void updateLiveGoodsSortAsWaitPutAwayFirst(Long liveGoodsId);


    /**
     * 获取各个直播间的流拍商品列表商品
     * @param liveRoomIdList
     * @param fetchSize 每个直播间展示的数量 再见列表使用
     * @return
     */
    List<AbortiveAuctionGoodsDTO> getAuctionFailLiveGoodsListByLiveRoomId(Collection<Long> liveRoomIdList, Integer fetchSize);

    /**
     * 获取直播间所有有效的流拍商品数量
     * @param liveRoomIdList
     * @return
     */
    Map<Long, Long> getEffectiveAuctionFailLiveGoodsCount(Collection<Long> liveRoomIdList);

    /**
     * 导播直播间商品统计
     * @param queryReq
     * @return
     */
    LiveGoodsStatisticsDTO getDirectorLiveGoodsCount(LiveGoodsQueryReq queryReq);

    /**
     * 看播直播间商品统计
     *      过滤失效商品
     * @param queryReq
     * @return
     */
    LiveGoodsStatisticsDTO getBuyerLiveGoodsCount(LiveGoodsQueryReq queryReq);

    /**
     * 流派商品数量
     * @param liveRoomId
     * @return
     */
    Long getAuctionFailLiveGoodsCount(Long liveRoomId);

    /**
     * 获取直播间竞拍中的商品列表
     * @param liveRoomIds
     * @return
     */
    List<AuctionLiveGoodsVO> getAuctionLiveGoodsListByLiveRoom(List<Long> liveRoomIds);

    /**
     *  获取直播间上架讲解中的商品列表
     * @param liveRoomIds
     * @return
     */
    List<AuctionLiveGoodsVO> getPutAwayLiveGoodsListByLiveRoom(List<Long> liveRoomIds);
    /**
     * 同步至云展商品列表
     * @param liveGoodsQueryReq
     * @return
     */
    IPage<LiveGoodsDTO> getLiveGoodsToSceGoods(LiveGoodsQueryReq liveGoodsQueryReq);
}
