package com.bbh.live.service.director;

import com.bbh.base.ListBase;
import com.bbh.live.controller.req.LiveGoodsQueryReq;
import com.bbh.live.dao.dto.CheckedLiveGoodsDTO;
import com.bbh.live.dao.dto.livegoods.LiveGoodsCheckListDTO;
import com.bbh.live.dao.dto.livegoods.LiveGoodsDTO;

/**
 * <AUTHOR> dsy
 * @Date: 2024/7/26
 * @Description: 导播操作直播间商品service
 */
public interface DirectorLiveGoodsQueryService {
    /**
     * 查询商品清单里的商品
     * @param liveGoodsQueryReq
     * @return
     */
    ListBase<LiveGoodsDTO> getLiveGoodsList(LiveGoodsQueryReq liveGoodsQueryReq);

    ListBase<LiveGoodsDTO> getLiveGoodsSyncToSceSelectAll(LiveGoodsQueryReq liveGoodsQueryReq);

    /**
     * 已选择的直播商品
     * @return
     */
    ListBase<CheckedLiveGoodsDTO> getCheckedLiveGoodsList();

    /**
     * 查询待上架商品列表
     * @param liveRoomId
     * @return
     */
    ListBase<LiveGoodsDTO> getWaitPutAwayLiveGoodsList(Long liveRoomId);

    /**
     * 当前直播间正在上架中的商品
     * @param liveRoomId
     * @return
     */
    LiveGoodsDTO getPutAwayLiveGoodsDetail(Long liveRoomId);

    /**
     * 带统计信息的直播商品清单
     * @param queryReq
     * @return
     */
    LiveGoodsCheckListDTO getLiveGoodsCheckList(LiveGoodsQueryReq queryReq);

    /**
     * 直播商品统计
     * @param queryReq
     * @return
     */
    LiveGoodsCheckListDTO getGoodsCount(LiveGoodsQueryReq queryReq);

    /**
     * 同步至云展商品列表
     * @param liveGoodsQueryReq
     * @return
     */
    ListBase<LiveGoodsDTO> getLiveGoodsToSceGoods(LiveGoodsQueryReq liveGoodsQueryReq);
}
