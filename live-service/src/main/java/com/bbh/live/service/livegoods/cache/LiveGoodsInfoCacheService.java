package com.bbh.live.service.livegoods.cache;

import com.bbh.live.service.livegoods.cache.info.LiveGoodsInfoCacheInfo;

/**
 * <AUTHOR> dsy
 * @Date: 2024/7/27
 * @Description:
 */
public interface LiveGoodsInfoCacheService {

    /**
     * 添加商品基本信息
     * @param liveRoomId
     * @param liveGoodsId
     * @param context
     * @return
     */
    boolean insertLiveGoodsInfo(Long liveRoomId, Long liveGoodsId, LiveGoodsInfoCacheInfo context);

    /**
     * 删除商品信息缓存
     * @param liveRoomId
     * @param liveGoodsId
     * @return
     */
    boolean removeLiveGoodsInfo(Long liveRoomId, Long liveGoodsId);

    /**
     * 获取商品信息
     * @param liveRoomId
     * @param liveGoodsId
     * @return
     */
    LiveGoodsInfoCacheInfo getLiveGoodsInfo(Long liveRoomId, Long liveGoodsId);

    /**
     * 清楚所有缓存
     * @param liveRoomId
     */
    void clearAll(Long liveRoomId);
}
