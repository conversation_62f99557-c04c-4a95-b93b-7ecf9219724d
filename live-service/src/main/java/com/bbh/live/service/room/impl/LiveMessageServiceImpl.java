package com.bbh.live.service.room.impl;

import com.bbh.live.config.LiveServiceProperties;
import com.bbh.live.dao.dto.vo.AuctionBidVO;
import com.bbh.live.dao.service.GlobalOrgSettlementBankService;
import com.bbh.live.dao.service.IGlobalOrganizationService;
import com.bbh.live.service.msg.MsgService;
import com.bbh.live.service.msg.dto.CommonMemeMsgDTO;
import com.bbh.live.service.msg.dto.CommonTextMsgDTO;
import com.bbh.live.service.msg.dto.base.SourceType;
import com.bbh.live.service.permission.PermissionService;
import com.bbh.live.service.room.LiveMessageService;
import com.bbh.secure.AuthUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 直播间消息
 * <AUTHOR>
 */
@AllArgsConstructor
@Slf4j
@Service
public class LiveMessageServiceImpl implements LiveMessageService {

    private final MsgService msgService;
    private final IGlobalOrganizationService globalOrganizationService;
    private final GlobalOrgSettlementBankService globalOrgSettlementBankService;
    private final LiveServiceProperties liveServiceProperties;
    private final PermissionService permissionService;

    @Override
    public AuctionBidVO sendText(CommonTextMsgDTO commonTextMsgDTO) {
        var user = AuthUtil.getUser();

        AuctionBidVO result = validPermission(user.getOrgId(), user.getSeatId());
        if (result.getSuccess()) {
            msgService.text(
                    commonTextMsgDTO.getLiveRoomId(),
                    commonTextMsgDTO.getContent(),
                    AuthUtil.getSeatId(),
                    SourceType.getSourceType(commonTextMsgDTO.getSourceType())
            );
        }

        return result;
    }

    @Override
    public AuctionBidVO sendMeme(CommonMemeMsgDTO commonMemeMsgDTO) {
        AuctionBidVO result = validPermission(AuthUtil.getOrgId(), AuthUtil.getSeatId());
        if (result.getSuccess()) {
            msgService.meme(
                    commonMemeMsgDTO.getLiveRoomId(),
                    commonMemeMsgDTO.getMemeId(),
                    AuthUtil.getSeatId(),
                    SourceType.getSourceType(commonMemeMsgDTO.getSourceType())
            );
        }
        return result;
    }

    private AuctionBidVO validPermission(Long orgId, Long seatId) {
        return permissionService.checkBidPermission(orgId, seatId, "发言");
    }
}
