package com.bbh.live.service.livegoods.cache;

/**
 * <AUTHOR> dsy
 * @Date: 2024/7/27
 * @Description:
 */
public interface LiveGoodsCacheConstant {

    Long DEFAULT_EXPIRE_TIME = 60 * 60 * 24 * 7L;

    String LIVE_AUCTION_GOODS_KEY = "live:auction_goods:roomId";

    String LIVE_AUCTION_GOODS_HASH_KEY = "live_goods";

    String LIVE_AUCTION_GOODS_BID_SEAT_HASH_KEY = "bid_seats";

    String LIVE_GOODS_INFO_KEY = "live:goods_info:roomId";

    String LIVE_GOODS_SUBSCRIBE_KEY = "live:goods_subscribe:";
}
