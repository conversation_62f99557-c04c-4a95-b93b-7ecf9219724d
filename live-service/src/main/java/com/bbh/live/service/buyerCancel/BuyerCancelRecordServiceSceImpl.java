package com.bbh.live.service.buyerCancel;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bbh.base.ListBase;
import com.bbh.base.PageQuery;
import com.bbh.enums.*;
import com.bbh.feign.IDepositApiClient;
import com.bbh.live.config.CloudExhibitionProperties;
import com.bbh.live.dao.mapper.*;
import com.bbh.live.dao.service.GlobalOrgSeatService;
import com.bbh.live.dao.service.SceGoodsService;
import com.bbh.live.service.buyerCancel.converter.BuyerCancelRecordConverter;
import com.bbh.live.service.buyerCancel.dto.LiveGoodsBuyerCancelRecordDetailDTO;
import com.bbh.live.service.buyerCancel.dto.param.LiveGoodsBuyerCancelRecordQueryParam;
import com.bbh.live.service.buyerCancel.dto.request.BuyerCancelRecordQueryRequest;
import com.bbh.live.service.buyerCancel.dto.request.BuyerCancelRecordSubmitRequest;
import com.bbh.live.service.buyerCancel.dto.request.SellerCancelRecordApproveRequest;
import com.bbh.live.service.buyerCancel.dto.response.BuyerCancelRecordDetailVO;
import com.bbh.live.service.buyerCancel.dto.response.BuyerCancelRecordListVO;
import com.bbh.live.service.msg.MsgService;
import com.bbh.live.service.msg.push.LivePushTypeEnum;
import com.bbh.live.util.EnumConverterUtil;
import com.bbh.model.*;
import com.bbh.secure.AuthUtil;
import com.bbh.service.deposit.dto.GoodsInfoDTO;
import com.bbh.service.deposit.dto.UnFrozenDepositDTO;
import com.bbh.service.deposit.dto.UserInfoDTO;
import com.bbh.service.deposit.enums.CodeEnum;
import com.bbh.service.mq.enums.MqTopicEnum;
import com.bbh.service.mq.service.CoreMqService;
import com.bbh.util.AssertUtil;
import com.bbh.vo.AuthUser;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.Map;

/**
 * 超级云展自主取消 实现
 */
@AllArgsConstructor
@Slf4j
@Service("buyerCancelRecordServiceSceImpl")
@EnableConfigurationProperties(CloudExhibitionProperties.class)
public class BuyerCancelRecordServiceSceImpl extends ServiceImpl<LiveGoodsBuyerCancelRecordMapper, LiveGoodsBuyerCancelRecord> implements BuyerCancelRecordService {

    private final CloudExhibitionProperties cloudExhibitionProperties;
    private final SceGoodsService sceGoodsService;
    private final GlobalOrgSeatService globalOrgSeatService;
    private final CoreMqService coreMqService;
    private final GlobalOrderDutyConfigMapper globalOrderDutyConfigMapper;
    private final BuyerCancelRecordConverter buyerCancelRecordConverter;
    private final ErpGoodsMapper erpGoodsMapper;
    private final GlobalOrganizationMapper globalOrganizationMapper;
    private final LiveGoodsCancelApplyMapper liveGoodsCancelApplyMapper;
    private final MsgService msgService;
    private final IDepositApiClient depositApiClient;
    /**
     * 获取商品成交信息
     *
     * @param liveGoodsId
     */
    @Override
    public BuyerCancelRecordDetailVO getTransactionInfo(Long liveGoodsId) {
        AssertUtil.assertNotNull(liveGoodsId, "商品ID不能为空");

        SceGoods sceGoods = sceGoodsService.getById(liveGoodsId);
        AssertUtil.assertNotNull(sceGoods, "商品不存在");

        // erp
        ErpGoods erpGoods = erpGoodsMapper.selectById(sceGoods.getGlobalGoodsId());
        AssertUtil.assertNotNull(erpGoods, "ERP货品不存在");

        // 卖家商户信息
        GlobalOrganization sellerOrg = globalOrganizationMapper.selectById(sceGoods.getOrgId());
        AssertUtil.assertNotNull(sellerOrg, "卖家商户不存在");

        // 买家席位信息
        GlobalOrgSeat buyerSeat = globalOrgSeatService.getById(sceGoods.getBelongSeatId());
        AssertUtil.assertNotNull(buyerSeat, "买家席位不存在");

        // 转换成
        return buyerCancelRecordConverter.toTransactionInfo(sceGoods, erpGoods, sellerOrg, buyerSeat);
    }

    /**
     * 买家提交审核
     *
     * @param request
     * @return
     */
    @Transactional
    @Override
    public LiveGoodsBuyerCancelRecord submit(BuyerCancelRecordSubmitRequest request) {
        AuthUser user = AuthUtil.getUser();
        Long liveGoodsId = request.getLiveGoodsId();
        GlobalOrderTypeEnum bizType = request.getBizType();

        // 参数检查
        AssertUtil.assertNotNull(liveGoodsId, "商品ID不能为空");
        AssertUtil.assertNotNull(request.getApplyReasonId(), "取消原因不能为空");
        AssertUtil.assertTrue(StrUtil.isNotBlank(request.getApplyDescription()) || CollUtil.isNotEmpty(request.getApplyImgList()), "请填写补充说明或上传凭证");

        // 检查是否有正在处理的记录
        // 撤销=逻辑删除，所以直接查当前用户及该商品的记录即可
        boolean exists = this.lambdaQuery()
                .eq(LiveGoodsBuyerCancelRecord::getLiveGoodsId, liveGoodsId)
                .eq(LiveGoodsBuyerCancelRecord::getBizType, bizType)
                .exists();
        AssertUtil.assertFalse(exists, "该商品平台已发起取消审核，无需重复申请");

        // 检查商品信息
        SceGoods sceGoods = sceGoodsService.getById(liveGoodsId);
        Long sceGoodsId = sceGoods.getId();
        AssertUtil.assertNotNull(sceGoods, "商品不存在");
        AssertUtil.assertTrue(sceGoods.getGoodsStatus() == SceGoodsStatusEnum.TRADED, "商品未成交，无法申请");
            // 检查是否有后台发起的取消审核
        AssertUtil.assertTrue(sceGoods.getCancelStatus() != LiveGoodsCancelStatusEnum.WAIT_VERIFY, "商品正在后台取消成交审核中，无法重复申请");
        AssertUtil.assertTrue(sceGoods.getCancelStatus() != LiveGoodsCancelStatusEnum.APPROVAL, "商品已被后台取消成交，无法重复申请");

        boolean existsCancelApply = liveGoodsCancelApplyMapper.exists(new LambdaQueryWrapper<LiveGoodsCancelApply>()
                .eq(LiveGoodsCancelApply::getBizGoodsId, sceGoodsId)
                .eq(LiveGoodsCancelApply::getBizId, bizType.getCode()).isNull(LiveGoodsCancelApply::getDeletedAt));
        AssertUtil.assertFalse(existsCancelApply, "商品正在后台取消成交审核中，无法重复申请");

        // 解析取消原因的文本
        GlobalOrderDutyConfig orderDutyConfig = globalOrderDutyConfigMapper.selectById(request.getApplyReasonId());
        AssertUtil.assertNotNull(orderDutyConfig, "取消原因不存在");
        String cancelReason = orderDutyConfig.getName();
        // 当前时间
        Date now = new Date();
        // 自动同意的时间
        Date applyTimeoutAt = DateUtil.offsetSecond(now, cloudExhibitionProperties.getGoodsBuyerCancelApplyAutoTimeoutDuration());
        // 系统推送的时间，applyTimeoutAt前一小时
        Date systemPushAt = DateUtil.offsetHour(applyTimeoutAt, -1);

        // 插入记录
        LiveGoodsBuyerCancelRecord record = new LiveGoodsBuyerCancelRecord();
        record.setOsn(IdUtil.fastSimpleUUID())
                .setLiveGoodsId(liveGoodsId)
                .setLiveRoomId(0L)
                .setGlobalGoodsId(sceGoods.getGlobalGoodsId())
                .setSellerOrgId(sceGoods.getOrgId())
                .setCancelStatus(LiveGoodsBuyerCancelRecordStatusEnum.WAITING_SELLER)
                .setTimeoutAt(applyTimeoutAt)
                .setBuyerOrgId(user.getOrgId())
                .setBuyerSeatId(user.getSeatId())
                .setBuyerUserId(user.getUserId())
                .setBuyerSeatName(globalOrgSeatService.getSeatName(user.getSeatId()))
                .setBuyerApplyAt(now)
                .setCancelReasonType(request.getApplyReasonId())
                .setCancelReason(cancelReason)
                .setCancelDescription(request.getApplyDescription())
                .setEvidenceImageUrlList(request.getApplyImgList())
                .setIfNotified(false)
                .setNotifyAt(systemPushAt);
        this.save(record);


        sceGoodsService.update(Wrappers.lambdaUpdate(SceGoods.class)
                .set(SceGoods::getCancelStatus, LiveGoodsCancelStatusEnum.WAIT_VERIFY.getCode())
                .set(SceGoods::getCancelInitiateAt,now)
                .eq(SceGoods::getId, sceGoodsId));

        // 发送 买家发起取消申请-> 卖家 推送消息
        msgService.notifySellerBuyerCancelApproval(record.getId(), record.getSellerOrgId(), LivePushTypeEnum.NOTIFY_SELLER_SUBMIT_CANCEL_APPROVAL, Map.of("id", record.getId()));
        // 发送 买家发起取消申请-> 买家 推送消息
        msgService.notifyBuyerCancelApproval(record.getId(), record.getBuyerOrgId(), record.getBuyerSeatId(), LivePushTypeEnum.NOTIFY_BUYER_SUBMIT_CANCEL_APPROVAL, Map.of("id", record.getId()));

        // 延迟: 自动同意applyTimeoutAt
        coreMqService.send(MqTopicEnum.LIVE_BUYER_CANCEL_REVIEW_TIMEOUT, JSONUtil.toJsonStr(record), DateUtil.between(now, applyTimeoutAt, DateUnit.MS));
        // 延迟：自动推送
        coreMqService.send(MqTopicEnum.LIVE_BUYER_CANCEL_REVIEW_TIMEOUT_PUSH, JSONUtil.toJsonStr(record), DateUtil.between(now, systemPushAt, DateUnit.MS));

        return record;
    }



    /**
     * 买家撤销审核
     *
     * @param recordId
     */
    @Transactional
    @Override
    public void revoke(Long recordId) {
        // 参数检查
        AssertUtil.assertNotNull(recordId, "记录ID不能为空");

        // 查记录
        LiveGoodsBuyerCancelRecord record = this.getById(recordId);
        Long liveGoodsId = record.getLiveGoodsId();
        AssertUtil.assertNotNull(record, "记录不存在");

        // 如果已经被商家处理过或是被平台处理过(大于0)，则禁止撤销
        AssertUtil.assertFalse(record.getCancelStatus().getCode() > 0, "已被处理，无法撤销");

        // 逻辑删除
        this.removeById(recordId);
        //删除审核单以后 退回商品取消状态
        sceGoodsService.update(Wrappers.lambdaUpdate(SceGoods.class)
                .set(SceGoods::getCancelStatus, LiveGoodsCancelStatusEnum.NO_CANCEL.getCode())
                .set(SceGoods::getCancelInitiateAt,null)
                .eq(SceGoods::getId, liveGoodsId));
    }

    /**
     * 买家查看审核详情
     *
     * @param recordId
     * @param ifBuyer
     */
    @Override
    public BuyerCancelRecordDetailVO getRecordDetail(Long recordId, boolean ifBuyer) {
        AuthUser user = AuthUtil.getUser();
        BuyerCancelRecordQueryRequest request = new BuyerCancelRecordQueryRequest();
        request.setIfBuyer(ifBuyer);
        request.setRecordId(recordId);
        request.setIfMy(false);
        request.setBizType(GlobalOrderTypeEnum.SCE);

        // 查询详细信息
        ListBase<BuyerCancelRecordListVO> recordList = getRecordList(request,new PageQuery());
        AssertUtil.assertTrue(CollUtil.isNotEmpty(recordList.getRecords()), "记录不存在");

        // 获取第一个
        BuyerCancelRecordListVO record = recordList.getRecords().getFirst();
        BuyerCancelRecordDetailVO detail = BeanUtil.copyProperties(record, BuyerCancelRecordDetailVO.class);
        // 申请人：拍号-昵称
        GlobalOrgSeat applySeat = globalOrgSeatService.getById(record.getRecordInfo().getBuyerSeatId());
        detail.setApplyShowName(applySeat.getAuctionCode() + "-" + applySeat.getShowName());
        // 其他申请信息
        detail.setApplyImgList(record.getRecordInfo().getEvidenceImageUrlList());
        detail.setApplyDescription(record.getRecordInfo().getCancelDescription());
        detail.setApplyReason(record.getRecordInfo().getCancelReason());
        // token信息
        detail.setLiveRyBuyerToken(StrUtil.format("bbh_{}_ry_{}", user.getOrgId(), user.getSeatId()));
        // 联系商家，对方的org_id
        detail.setLiveRyServiceToken(StrUtil.format("live_{}_kefu_01", record.getRecordInfo().getSellerOrgId()));
        // 商家处理结果
        String sellerHandleResultPattern = record.getRecordInfo().getIfDeduct() ? "同意并要求{}" : "同意，无需{}";
        String sellerHandleResultDesc = StrUtil.format(sellerHandleResultPattern, ifBuyer ? "扣保证金" : "补偿");
        detail.setSellerHandleResultDesc(sellerHandleResultDesc);
        // 平台处理结果: 补偿22元/扣22元保证金
        if (record.getRecordInfo().getCancelStatus() == LiveGoodsBuyerCancelRecordStatusEnum.COMPLETED
                && record.getRecordInfo().getPlatformProcessAt() != null) {
            String pattern = ifBuyer ? "扣{}元保证金" : "补偿{}元";
            BigDecimal amount = ifBuyer ? record.getRecordInfo().getDeductDepositAmount() : record.getRecordInfo().getCompensateSellerAmount();
            detail.setPlatformHandleResultDesc(StrUtil.format(pattern, amount.setScale(0, RoundingMode.DOWN)));
        }

        // 记录信息
        BuyerCancelRecordDetailVO.StatusInfo statusInfo = ifBuyer ?
                buyerCancelRecordConverter.toBuyerStatus(record.getRecordInfo())
                : buyerCancelRecordConverter.toSellerStatus(record.getRecordInfo());
        detail.setStatusInfo(statusInfo);

        return detail;
    }

    /**
     * 买家审核列表，要区分个人的和店铺的
     *
     * @param request
     */
    @Override
    public ListBase<BuyerCancelRecordListVO> getRecordList(BuyerCancelRecordQueryRequest request, PageQuery pageQuery) {
        AuthUser user = AuthUtil.getUser();

        // 转换成查询参数
        LiveGoodsBuyerCancelRecordQueryParam query = new LiveGoodsBuyerCancelRecordQueryParam();
        // 买家或卖家
        if (request.getIfBuyer()) {
            query.setBuyerOrgId(user.getOrgId());

            // 个人或店铺
            if (request.getIfMy()) {
                query.setBuyerSeatId(user.getSeatId());
            }
        } else {
            query.setSellerOrgId(user.getOrgId());
        }
        // 状态
        if (request.getStatus() != null) {
            query.setCancelStatus(request.getStatus().getCode());
        }
        if (request.getRecordId() != null) {
            query.setRecordId(request.getRecordId());
        }
        query.setBizType(request.getBizType());
        // 查询列表
        Page<LiveGoodsBuyerCancelRecordDetailDTO> pageResult = baseMapper.sceRecordDetailPage(Page.of(pageQuery.getCurrentPage(), pageQuery.getPerPage()), query);
        // 转换成VO
        return buyerCancelRecordConverter.toRecordList(pageResult, !request.getIfBuyer());
    }

    /**
     * 卖家审核通过
     *
     * @param request
     */
    @Transactional
    @Override
    public void sellerApprove(SellerCancelRecordApproveRequest request) {
        AuthUser user = getSellerApproveAuthUser();

        // 参数检查
        AssertUtil.assertNotNull(request.getOrderId(), "记录ID不能为空");
        Boolean ifDeduct = request.getIfDeduct();
        AssertUtil.assertNotNull(ifDeduct, "是否扣款不能为空");

        // 查记录
        LiveGoodsBuyerCancelRecord record = this.getById(request.getOrderId());
        AssertUtil.assertNotNull(record, "记录不存在");
        AssertUtil.assertTrue(record.getCancelStatus().getCode() == 0, "记录已被处理");

        // 更新记录
        LiveGoodsBuyerCancelRecordStatusEnum cancelRecordStatusEnum = ifDeduct ? LiveGoodsBuyerCancelRecordStatusEnum.PLATFORM_REVIEWING :
                LiveGoodsBuyerCancelRecordStatusEnum.COMPLETED;

        // 更新记录
        LambdaUpdateWrapper<LiveGoodsBuyerCancelRecord> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(LiveGoodsBuyerCancelRecord::getId, request.getOrderId())
                .set(LiveGoodsBuyerCancelRecord::getIfDeduct, ifDeduct)
                .set(LiveGoodsBuyerCancelRecord::getEndAt, new Date())
                // 扣除就进平台审核，不扣除就直接完成
                .set(LiveGoodsBuyerCancelRecord::getCancelStatus, cancelRecordStatusEnum)
                .set(LiveGoodsBuyerCancelRecord::getSellerSeatId, user.getSeatId())
                .set(LiveGoodsBuyerCancelRecord::getSellerUserId, user.getUserId())
                .set(LiveGoodsBuyerCancelRecord::getSellerSeatName, globalOrgSeatService.getSeatName(user.getSeatId()))
                // 是否自动处理，超时的就是自动处理
                .set(LiveGoodsBuyerCancelRecord::getIfAutoProcess, request.getIfTimeout())
                .set(Boolean.TRUE.equals(ifDeduct),LiveGoodsBuyerCancelRecord::getEndAt,new Date())
                .set(LiveGoodsBuyerCancelRecord::getSellerProcessAt, new Date());
        this.update(updateWrapper);

        if (LiveGoodsBuyerCancelRecordStatusEnum.COMPLETED.equals(cancelRecordStatusEnum)){
            //更新商品状态为已取消
            updateSceGoods(sceGoodsService.getById(record.getLiveGoodsId()),record);
            //解冻
            unFrozenDeposit(record,globalOrgSeatService.getById(user.getSeatId()));
        }

        if(request.getIfTimeout()){
            // 自动审批处理 推送 卖家 消息
            msgService.notifySellerBuyerCancelApproval(record.getId(), record.getSellerOrgId(), LivePushTypeEnum.NOTIFY_SELLER_AFTER_AUTO_CANCEL_APPROVAL, Map.of("id", record.getId()));
        }
        if(request.getIfTimeout() || ifDeduct){
            // 自动审批处理/卖家审批扣钱 推送 买家 消息
            msgService.notifyBuyerCancelApproval(record.getId(), record.getBuyerOrgId(), record.getBuyerSeatId(), LivePushTypeEnum.NOTIFY_BUYER_CANCEL_AUTO_OR_DEDUCT, Map.of("id", record.getId()));
        }
        if(!ifDeduct){
            msgService.notifySellerBuyerCancelApproval(record.getId(), record.getSellerOrgId(), LivePushTypeEnum.NOTIFY_SELLER_CANCEL_APPROVAL_NO_DEDUCT, Map.of("id", record.getId()));
            msgService.notifyBuyerCancelApproval(record.getId(), record.getBuyerOrgId(), record.getBuyerSeatId(), LivePushTypeEnum.NOTIFY_BUYER_CANCEL_APPROVAL_NO_DEDUCT, Map.of("id", record.getId()));
        }
    }

    /**
     * 解冻
     */
    public void unFrozenDeposit(LiveGoodsBuyerCancelRecord cancelRecord,GlobalOrgSeat globalOrgSeat) {
        UnFrozenDepositDTO unFrozenDepositDTO = new UnFrozenDepositDTO();
        if (globalOrgSeat != null) {
            unFrozenDepositDTO.setCreateId(globalOrgSeat.getId());
            unFrozenDepositDTO.setCreateName(globalOrgSeat.getShowName());
        }
        GoodsInfoDTO goodsInfoDTO = new GoodsInfoDTO();
        goodsInfoDTO.setCode(EnumConverterUtil.globalOrderTypeEnum2CodeEnum(cancelRecord.getBizType()));
        goodsInfoDTO.setGoodsId(cancelRecord.getLiveGoodsId());
        UserInfoDTO userInfoDTO = new UserInfoDTO();
        userInfoDTO.setId(cancelRecord.getBuyerOrgId());
        unFrozenDepositDTO.setGoodsInfo(goodsInfoDTO);
        unFrozenDepositDTO.setSuccessInfo(userInfoDTO);
        log.warn("卖家审核通过freezeService.unFrozenDeposit:{}", unFrozenDepositDTO);
        depositApiClient.unFrozenDeposit(unFrozenDepositDTO);
        log.info("解冻保证金完成");
    }


    @Transactional(rollbackFor = Exception.class)
    public void updateSceGoods(SceGoods sceGoods, LiveGoodsBuyerCancelRecord cancelRecord) {
        SceGoods ups = new SceGoods();
        ups.setId(cancelRecord.getLiveGoodsId());
        ups.setCancelStatus(LiveGoodsCancelStatusEnum.APPROVAL);
        ups.setCancelAt(LocalDateTime.now());
        ups.setCancelFrom(CancelFromEnum.USER);
        ups.setCancelDutyType(dutyFromConvert(cancelRecord.getDeductDutyFrom()));
        sceGoodsService.updateById(ups);

        //erp_goods
        Long globalGoodsId = sceGoods.getGlobalGoodsId();
        if (globalGoodsId !=null) {
            LambdaUpdateWrapper<ErpGoods> erpGoodsUpdater = new LambdaUpdateWrapper<>(ErpGoods.class)
                    .eq(ErpGoods::getId, globalGoodsId)
                    .eq(ErpGoods::getSaleStatus, ErpGoodsSaleStatusEnum.SOLD_OUT.getType())
                    .eq(ErpGoods::getPlaceOrderStatus, 0)
                    .eq(ErpGoods::getSaleType, ErpGoodsSaleTypeEnum.EXHIBITION.getType())
                    // 商品状态=售出
                    .set(ErpGoods::getSaleStatus, ErpGoodsSaleStatusEnum.NORMAL.getType())
                    // 售出时间
                    .set(ErpGoods::getSaleAt,null)
                    // 售出渠道=SCE
                    .set(ErpGoods::getSaleType, 0);
            erpGoodsMapper.update(erpGoodsUpdater);
        }
    }

    public CancelDutyTypeEnum dutyFromConvert(LiveGoodsBuyerCancelRecordDeductDutyFromEnum deductDutyFromEnum) {
        CancelDutyTypeEnum typeEnum = null;
        if (deductDutyFromEnum != null) {
            switch (deductDutyFromEnum) {
                case LiveGoodsBuyerCancelRecordDeductDutyFromEnum.BUYER -> typeEnum = CancelDutyTypeEnum.BUYER;
                case LiveGoodsBuyerCancelRecordDeductDutyFromEnum.SELLER -> typeEnum = CancelDutyTypeEnum.SELLER;
                case LiveGoodsBuyerCancelRecordDeductDutyFromEnum.PLATFORM -> typeEnum = CancelDutyTypeEnum.PLATFORM;
            }
        }
        return typeEnum;
    }


    private AuthUser getSellerApproveAuthUser() {
        AuthUser user = new AuthUser();
        user.setSeatId(0L);
        user.setUserId(0L);
        user.setOrgId(0L);
        user.setIfMaster(false);
        return AuthUtil.getUser(user);
    }

}
