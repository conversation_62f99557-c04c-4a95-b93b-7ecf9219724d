package com.bbh.live.service.organization.dto;

import com.bbh.enums.CreateFromEnum;
import com.bbh.live.enums.BuyerVipTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/9/20 08:41
 * @description
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
public class OrgVipResourcesDTO {

    /**
     * vip id
     */
    private Long vipId;

    /**
     * vip 等级
     */
    private Integer vipLevel;

    /**
     * 是否年费vip
     */
    private Boolean ifAnnualFeeVip;

    /**
     * vip 状态 1:正常 2:过期
     */
    private BuyerVipTypeEnum vipStatus;

    /**
     * vip 过期时间
     */
    private Date vipEndTime;

    /**
     * 拍号所属席位
     */
    private Long seatId;

    /**
     * 拍号所属席位名称
     */
    private String seatName;

    /**
     * 拍号所属席位头像
     */
    private String avatar;

    /**
     * 创建来源
     */
    private CreateFromEnum createdFrom;
}
