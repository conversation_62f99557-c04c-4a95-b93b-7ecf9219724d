package com.bbh.live.service.user.permission;

import com.bbh.live.enums.PermissionCheckTypeEnum;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/9/21 09:36
 * @description
 */
public class PermissionCheckServiceFactory {

    private static final Map<PermissionCheckTypeEnum, PermissionCheckService> PERMISSION_CHECK_SERVICE_MAP = new HashMap<>(16);

    static {
        PERMISSION_CHECK_SERVICE_MAP.put(PermissionCheckTypeEnum.PERMISSION_CHECK_TYPE_ERP, new ErpPermissionCheckServiceImpl());
        PERMISSION_CHECK_SERVICE_MAP.put(PermissionCheckTypeEnum.PERMISSION_CHECK_TYPE_LIVE, new LivePermissionCheckServiceImpl());
        PERMISSION_CHECK_SERVICE_MAP.put(PermissionCheckTypeEnum.PERMISSION_CHECK_TYPE_GLOBAL, new GlobalPermissionCheckServiceImpl());
    }

    public static PermissionCheckService getPermissionCheckService(PermissionCheckTypeEnum permissionCheckTypeEnum) {
        return PERMISSION_CHECK_SERVICE_MAP.get(permissionCheckTypeEnum);
    }

    public static PermissionCheckService getLivePermissionCheck() {
        return PERMISSION_CHECK_SERVICE_MAP.get(PermissionCheckTypeEnum.PERMISSION_CHECK_TYPE_LIVE);
    }
}
