package com.bbh.live.service.livegoods.cache.info;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR> dsy
 * @Date: 2024/8/8
 * @Description:
 */
@Getter
@AllArgsConstructor
public enum AuctionResult {

    /**
     * 出价失败
     */
    AUCTION_CACHE_DELETED(-1),

    /**
     * 出价失败
     */
    PRICE_UPDATE_FAIL(0),
    /**
     * 出价成功
     */
    PRICE_UPDATE_SUCCESS(1),
    /**
     * 出价和更新时间同时成功
     */
    PRICE_AND_TIME_UPDATE_SUCCESS(2),
    /**
     * 正在结算
     */
    SETTLING(3),
    /**
     * 更新第一个出价人成功
     */
    FIRST_BIDDER_UPDATE_SUCCESS(4),

    /**
     * 正在撤销
     */
    LIVE_GOODS_REVOKING(5),
    ;


    private final Integer code;

    public static AuctionResult of(Integer code) {
        for (AuctionResult auctionResult : values()) {
            if (auctionResult.code.equals(code)) {
                return auctionResult;
            }
        }
        return null;
    }

    public boolean isSuccess() {
        return this != AuctionResult.PRICE_UPDATE_FAIL &&
                this != AuctionResult.SETTLING &&
                this != AuctionResult.AUCTION_CACHE_DELETED &&
                this != AuctionResult.LIVE_GOODS_REVOKING;
    }
}
