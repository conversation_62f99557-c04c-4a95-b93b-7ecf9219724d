package com.bbh.live.service.director;

import com.bbh.live.dao.dto.*;
import com.bbh.live.dao.dto.livegoods.LiveGoodsPutAwayDTO;
import com.bbh.live.dao.dto.livegoods.LiveGoodsSortDTO;
import com.bbh.live.dao.dto.livegoods.LiveGoodsSyncCeDTO;

import java.util.List;

/**
 * <AUTHOR> dsy
 * @Date: 2024/7/26
 * @Description: 导播操作直播间商品service
 */
public interface DirectorLiveGoodsOpService {

    /**
     * 添加直播商品清单
     * @param liveRoomId 直播间
     * @param goodsIdList 所有erp商品id集合，包括原来已添加的和本次新添加的
     */
    String addLiveGoodsList(Long liveRoomId, List<Long> goodsIdList);

    /**
     * 移除商品清单
     * @param goodsIdList 直播间商品id集合
     */
    void removeLiveGoodsList(List<Long> goodsIdList);

    /**
     * 设置直播商品对看播是否可见
     */
    Boolean updateLiveGoodsVisible(VisibleDTO visibleDTO);

    /**
     * 修改|完善 上架信息
     * @param playAwayInfo
     * @return
     */
    void completeLiveGoodsPutAwayInfo(LiveGoodsPutAwayDTO playAwayInfo);

    /**
     * 批量修改商品排序
     * @param liveGoodsSortList
     */
    void batchUpdateLiveGoodsSort(List<LiveGoodsSortDTO> liveGoodsSortList);


    /**
     * 上架讲解商品
     * @param liveGoodsId
     */
    void putAwayAndExplainLiveGoods(Long liveGoodsId);


    /**
     * 开始竞拍
     * @param liveGoodsId
     */
    void auctionLiveGoods(Long liveGoodsId);

    /**
     * 手动流拍，目前仅适用于一口价
     * @param liveGoodsId   商品ID
     */
    void abortLiveGoods(Long liveGoodsId);

    /**
     * 主动下架，撤回
     * @param liveGoodsId
     */
    void putAwayOffLiveGoods(Long liveGoodsId);

    /**
     *  处理互动消息
     * @param interceptMsg
     */
    void interactiveMessageHandle(InteractiveMsgDTO interceptMsg);


    /**
     * 流拍商品重新上架，回到待上架列表第一个位置
     * @param liveGoodsId
     */
    void abortiveAuctionGoodsReShelve(Long liveGoodsId);

    /**
     * 流拍商品重新上架（完善上架信息，并到上架中队列）
     * @param playAwayInfo
     */
    void abortiveAuctionGoodsReShelveAndComplete(LiveGoodsPutAwayDTO playAwayInfo);

    /**
     * 即拍即上
     */
    void offhandPutAway(OffhandPutAwayDTO offhandPutAwayDTO);

    /**
     * 直播结束，剩余商品批量同步到云展
     * @param liveGoodsList
     */
    void batchSyncToCe(List<LiveGoodsSyncCeDTO.LiveGoodsSyncCe> liveGoodsList);
    /**
     * 直播同步视频
     * @param liveGoodsList
     */
    void batchSyncLiveVideo(List<LiveGoodsSyncCeDTO.LiveGoodsSyncCe> liveGoodsList);

    /**
     * 直播结束同步视频
     * @param liveRoomId 直播间id
     */
    void batchSyncToCeByLiveEnd(Long liveRoomId);

    /**
     * 直播结束后商品同步新云展 上架时间往前十分钟随机
     */
    void syncEndLiveRoomToSce();
}
