package com.bbh.live.service.buyerCancel.dto.response;

import com.bbh.enums.GlobalOrderTypeEnum;
import com.bbh.live.service.buyerCancel.dto.BuyerCancelBiz;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 买家取消成交记录详情响应对象
 *
 * <AUTHOR>
 * @since 2025-01-10
 */
@Data
public class BuyerCancelRecordDetailVO  {

    /**
     * 商品信息
     */
    private GoodsInfo goodsInfo;

    /**
     * 状态信息
     */
    private StatusInfo statusInfo;

    /**
     * 卖家组织信息
     */
    private OrgInfo sellerOrgInfo;

    /**
     * 买家组织信息
     */
    private OrgInfo buyerOrgInfo;

    /**
     * 买家Token: live_2357_buyer_3920390
     */
    private String liveRyBuyerToken;

    /**
     * 服务Token: live_334_kefu_01
     */
    private String liveRyServiceToken;

     /*
        "apply_show_name":"2223-XXX",
        "apply_reason":"不想要了",
        "apply_description":"成色与描述不符",
        "apply_img_list":['xxx'],
        "ive_ry_buyer_token": "live_2357_buyer_3920390",
        "live_ry_service_token": "live_334_kefu_01",
         */

    private String applyShowName;
    private String applyReason;
    private String applyDescription;
    private List<String> applyImgList;

    // 商家处理结果
    private String sellerHandleResultDesc;

    // 平台处理结果
    private String platformHandleResultDesc;

    /**
     * 记录ID
     */
    private Long id;

    @Data
    public static class LiveRoomInfo {
        private Long id;
        private String roomName;
        private Date start;
    }

    @Data
    public static class BizGoodsInfo {
        private Long bizGoodsId;
        private Long bizId;
        private String liveGoodsCode;
    }

    /**
     * 商品信息内部类
     */
    @Data
    public static class GoodsInfo {
        /**
         * 商品ID
         */
        private Long id;

        /**
         * 商品名称
         */
        private String name;

        /**
         * 商品图片列表
         */
        private String imgUrlList;

        /**
         * 商品质量
         */
        private String quality;

        /**
         * 同行价格
         */
        private BigDecimal peerPrice;

        /**
         * 创建时间
         */
        private Date createdAt;

        /**
         * 图片详情列表
         */
        private List<String> imgDetailUrlList;

        /**
         * 商品价格
         */
        private BigDecimal goodsPrice;

        /**
         * 业务商品ID
         */
        private GlobalOrderTypeEnum bizType;

        /**
         * 业务商品ID
         */
        private Long bizGoodsId;

        /**
         * 业务ID
         */
        private Long bizId;

        /**
         * 直播商品编码
         */
        private String liveGoodsCode;

        /**
         * 所属名称
         */
        private String belongName;

        /**
         * 所属昵称
         */
        private String belongNickName;

        /**
         * 所属显示名称
         */
        private String belongShowName;

        /**
         * 所属拍卖编码
         */
        private String belongAuctionCode;

        /**
         * 直播间ID
         */
        private Long liveRoomId;

        /**
         * 直播商品ID
         */
        private Long liveGoodsId;

        private Integer tradeType;

        private Integer belongType;

        private String liveVideoUrl;

        private List<String> videoUrlList;
    }

    /**
     * 状态信息内部类
     * "status_info":{
     *   "status": 65,
     *   "calc_title": "平台审核退款未通过",
     *   "calc_desc": "2024-12-25 18:48:38 发起退款",
     *   "cancel_title": "平台审核中",
     *   "cancel_desc": "商家已同意，并要求扣除保证金",
     * },
     */
    @Data
    public static class StatusInfo {
        /**
         * 状态码
         */
        private Integer status;

        /**
         * 标题: 待平台处理
         */
        private String calcTitle;

        /**
         * 描述：2024-12-25 18:48:38 发起申请
         */
        private String calcDesc;

        /**
         * 标题: 平台审核中
         */
        private String cancelTitle;

        /**
         * 描述：商家已同意……
         */
        private String cancelDesc;
    }

    /**
     * 卖家组织信息内部类
     */
    @Data
    public static class OrgInfo {
        /**
         * 组织ID
         */
        private Long id;

        /**
         * 组织名称
         */
        private String name;

        /**
         * 组织Logo URL
         */
        private String logoUrl;
    }

    @Data
    public static class SeatInfo {
        /**
         * ID
         */
        private Long id;

        /**
         * 名称
         */
        private String name;

        /**
         * 头像
         */
        private String avatar;
    }


}
