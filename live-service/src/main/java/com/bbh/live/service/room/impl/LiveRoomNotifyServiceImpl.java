package com.bbh.live.service.room.impl;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.bbh.enums.LiveRoomStreamStatusEnum;
import com.bbh.live.dao.dto.LiveStreamNotifyDTO;
import com.bbh.live.dao.service.LiveRoomService;
import com.bbh.live.enums.LiveNotifyTypeEnum;
import com.bbh.live.service.room.LiveRoomNotifyService;
import com.bbh.live.service.room.LiveStreamService;
import com.bbh.model.LiveRoom;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 直播间通知服务
 * <AUTHOR>
 */
@Service
@AllArgsConstructor
@Slf4j
public class LiveRoomNotifyServiceImpl implements LiveRoomNotifyService {

    private final LiveRoomService liveRoomService;
    private final LiveStreamService liveStreamService;

    /**
     * 更新直播视频URL
     *
     * @param dto 直播流通知数据传输对象
     */
    @Override
    public void updateLiveVideoUrl(LiveStreamNotifyDTO dto) {
        LambdaUpdateWrapper<LiveRoom> updateWrapper = new LambdaUpdateWrapper<LiveRoom>()
                .eq(LiveRoom::getId, dto.getLiveId())
                .set(LiveRoom::getLiveVideoUrl, dto.getVideoUrl());
        liveRoomService.update(updateWrapper);
    }

    /**
     * 处理直播通知
     *
     * @param dto 直播流通知数据传输对象
     */
    @Override
    public void handleLiveNotify(LiveStreamNotifyDTO dto) {
        // 参数校验
        if (dto == null || dto.getLiveId() == null) {
            log.error("直播回调 参数异常:{}", dto);
            return;
        }

        // 获取直播间信息
        LiveRoom liveRoom = liveRoomService.getById(dto.getLiveId());
        if (liveRoom == null) {
            log.error("直播回调 直播间不存在:{}", dto);
            return;
        }

        // 处理回调通知
        handleLiveNotification(dto, liveRoom);
    }

    /**
     * 处理直播通知
     * @param dto 通知数据传输对象
     * @param liveRoom 直播间对象
     */
    private void handleLiveNotification(LiveStreamNotifyDTO dto, LiveRoom liveRoom) {
        switch (LiveNotifyTypeEnum.fromType(dto.getNotifyType())) {
            case LIVE_NOTIFY -> handleLiveStreamNotification(dto, liveRoom);
            case LIVE_VIDEO_RECORD_NOTIFY -> updateLiveVideoUrl(dto);
            case null, default -> log.error("未知的通知类型: {}", dto.getNotifyType());
        }
    }

    /**
     * 处理直播流通知
     * @param dto 通知数据传输对象
     * @param liveRoom 直播间对象
     */
    private void handleLiveStreamNotification(LiveStreamNotifyDTO dto, LiveRoom liveRoom) {
        switch (dto.getStreamStatus()) {
            case LiveRoomStreamStatusEnum.OFF -> liveStreamService.stopLiveByStreamCallback(liveRoom.getId());
            case LiveRoomStreamStatusEnum.ON -> liveStreamService.startLiveByStreamCallback(liveRoom.getId());
            case null, default -> log.warn("未知的直播流状态: {}", dto.getStreamStatus());
        }
    }
}
