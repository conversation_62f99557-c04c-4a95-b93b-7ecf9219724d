package com.bbh.live.service.msg.dto;

import com.bbh.live.core.msg.MsgType;
import com.bbh.live.service.msg.dto.base.BaseAuctionLimitMsg;
import com.bbh.live.service.msg.dto.base.BaseGoods;
import com.bbh.live.service.msg.dto.base.IGodViewMsg;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 商品上架讲解 的卡片弹窗消息
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
public class GoodsPutawayMsgDTO extends BaseAuctionLimitMsg implements IGodViewMsg {

    private BaseGoods goods;

    /**
     * 试播客服id集合
     */
    private List<Long> testBroadcastSeatIdList;

    /**
     * 直播间是否正在试播
     */
    private Boolean ifTestBroadcast;

    private List<?> godViewList;

    /** 交易类型：竞拍、一口价 */
    private Integer tradeType;

    /**
     * 每个消息对应一个类型
     *
     * @return {@link MsgType}
     */
    @Override
    public String type() {
        return MsgType.PUTAWAY;
    }

    @Override
    public void setGodViewList(List<?> godViewList) {
        this.godViewList = godViewList;
    }

}
