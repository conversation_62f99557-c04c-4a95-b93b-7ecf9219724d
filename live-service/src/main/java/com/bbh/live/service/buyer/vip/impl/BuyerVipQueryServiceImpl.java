package com.bbh.live.service.buyer.vip.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.bbh.base.ListBase;
import com.bbh.base.Page;
import com.bbh.live.controller.req.VipRightsUseRecordsReq;
import com.bbh.live.dao.dto.VipPeepLogDTO;
import com.bbh.live.dao.service.*;
import com.bbh.live.enums.VipRightsTypeEnum;
import com.bbh.live.service.buyer.vip.BuyerVipQueryService;
import com.bbh.live.service.buyer.vip.BuyerVipService;
import com.bbh.live.service.buyer.vip.vo.BuyerVipMsgTransferVO;
import com.bbh.live.service.buyer.vip.vo.UserBuyerVipInfoVO;
import com.bbh.live.service.buyer.vip.vo.VipBuyerWholeTimeUsedVO;
import com.bbh.live.service.buyer.vip.vo.VipRightsUsedRecordsVO;
import com.bbh.live.service.buyer.weekly.WeeklyService;
import com.bbh.live.util.PageUtils;
import com.bbh.model.*;
import com.bbh.secure.AuthUtil;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/9/21 16:14
 * @description
 */
@Service
@AllArgsConstructor
public class BuyerVipQueryServiceImpl implements BuyerVipQueryService {

    private final BuyerVipService buyerVipService;
    private final VipBuyerPayRecordService iVipBuyerPayRecordService;
    private final WeeklyService weeklyService;
    private final VipBuyerPeepLogService vipBuyerPeepLogService;
    private final VipBuyerRefundFenbeiService vipBuyerRefundFenbeiService;
    private final VipBuyerChangeNicknameLogService vipBuyerChangeNicknameLogService;
    private final VipBuyerPutAwayViewLogService vipBuyerPutAwayViewLogService;
    private final VipBuyerLotteryLogService vipBuyerLotteryLogService;

    @Override
    public VipBuyerWholeTimeUsedVO getWholeTimeUsedInfo(Long vipId) {
        VipBuyerWholeTimeUsedVO vipBuyerWholeTimeUsedInfo = new VipBuyerWholeTimeUsedVO();
        UserBuyerVipInfoVO vipInfo;
        if(vipId == null){
            vipInfo = buyerVipService.getUserBuyerVipInfoBySeatId(AuthUtil.getSeatId());
        } else {
            vipInfo = buyerVipService.getUserBuyerVipInfoByVipId(vipId);
        }
        vipId = vipInfo.getId();
        // 支付节省
        vipBuyerWholeTimeUsedInfo.setSaveMoney(vipInfo.getSaveMoney());
        // 分贝节省
        vipBuyerWholeTimeUsedInfo.setSaveFenbei(vipInfo.getSaveFenbei());
        // 获得的额外分贝
        vipBuyerWholeTimeUsedInfo.setTotalGetFenbei(vipInfo.getTotalGetFenbei());
        // 收到的行情周报
        long weeklyCount = weeklyService.getWeeklyCount(vipId);
        vipBuyerWholeTimeUsedInfo.setTotalWeekly(weeklyCount);
        // 会员使用天数
        Long allDaysUntilNow = iVipBuyerPayRecordService.getVipUsedDays(vipId);
        vipBuyerWholeTimeUsedInfo.setAllUsedDays(allDaysUntilNow);
        // 查看出价人次数
        long peepCount = vipBuyerPeepLogService.lambdaQuery().eq(VipBuyerPeepLog::getVipBuyerCardId, vipInfo.getId()).count();
        vipBuyerWholeTimeUsedInfo.setTotalPeep(peepCount);
        // 上帝视角使用次数
        long godViewCount = vipBuyerPutAwayViewLogService.lambdaQuery().eq(VipBuyerPutAwayViewLog::getVipBuyerCardId, vipInfo.getId()).count();
        vipBuyerWholeTimeUsedInfo.setTotalGodView(godViewCount);
        return vipBuyerWholeTimeUsedInfo;
    }

    @Override
    public ListBase<?> getVipRightsUsedRecords(VipRightsUseRecordsReq recordsReq) {
        Long vipId = recordsReq.getVipId();
        if(vipId == null) {
            BuyerVipMsgTransferVO vipInfo = buyerVipService.getBuyerVipMsgTransferVO(AuthUtil.getSeatId());
            if(vipInfo == null){ return new ListBase<>();}
            vipId = vipInfo.getVipId();
        }
        // 类型
        VipRightsTypeEnum rightsType = recordsReq.getRightsType();
        return switch (rightsType){
            case OFFICIAL_COMPENSATION:
                // 官方补偿记录
                LambdaQueryWrapper<VipBuyerRefundFenbei> queryWrapper = Wrappers.lambdaQuery(VipBuyerRefundFenbei.class)
                        .eq(VipBuyerRefundFenbei::getVipBuyerCardId, vipId)
                        .ge(VipBuyerRefundFenbei::getCreatedAt, recordsReq.getStartAt())
                        .le(VipBuyerRefundFenbei::getCreatedAt, recordsReq.getEndAt())
                        .orderByDesc(VipBuyerRefundFenbei::getCreatedAt);
                Page<VipBuyerRefundFenbei> page = PageUtils.getPage(recordsReq, VipBuyerRefundFenbei.class);
                com.baomidou.mybatisplus.extension.plugins.pagination.Page<VipBuyerRefundFenbei> refundPage = vipBuyerRefundFenbeiService.page(page, queryWrapper);
                List<VipRightsUsedRecordsVO.OfficialCompensationRecord> recordList = refundPage.getRecords().stream().map(VipRightsUsedRecordsVO.OfficialCompensationRecord::builder).toList();
                yield new ListBase<>(recordList, refundPage.getTotal(), refundPage.getCurrent(), refundPage.getSize());
            case PEEP:
                // 出价记录
                com.baomidou.mybatisplus.extension.plugins.pagination.Page<VipPeepLogDTO> peepPage = vipBuyerPeepLogService.getPeepLogsByVipId(vipId, recordsReq.getStartAt(), recordsReq.getEndAt(), PageUtils.getPage(recordsReq, VipBuyerPeepLog.class));
                List<VipRightsUsedRecordsVO.PeepRecord> peepRecordList = peepPage.getRecords().stream().map(VipRightsUsedRecordsVO.PeepRecord::builder).toList();
                yield new ListBase<>(peepRecordList, peepPage.getTotal(), peepPage.getCurrent(), peepPage.getSize());
            case NICKNAME_MODIFY:
                // 昵称修改记录
                Page<VipBuyerChangeNicknameLog> nicknameLogPage = vipBuyerChangeNicknameLogService.lambdaQuery()
                        .eq(VipBuyerChangeNicknameLog::getVipBuyerCardId, vipId)
                        .ge(VipBuyerChangeNicknameLog::getCreatedAt, recordsReq.getStartAt())
                        .le(VipBuyerChangeNicknameLog::getCreatedAt, recordsReq.getEndAt())
                        .orderByDesc(VipBuyerChangeNicknameLog::getCreatedAt)
                        .page(PageUtils.getPage(recordsReq, VipBuyerChangeNicknameLog.class));
                List<VipRightsUsedRecordsVO.NicknameModifyRecord> nicknameModifyRecords = nicknameLogPage.getRecords().stream().map(VipRightsUsedRecordsVO.NicknameModifyRecord::builder).toList();
                yield new ListBase<>(nicknameModifyRecords, nicknameLogPage.getTotal(), nicknameLogPage.getCurrent(), nicknameLogPage.getSize());
            case LOTTERY:
                // 免费抽奖记录
                LambdaQueryWrapper<VipBuyerLotteryLog> lotteryQueryWrapper = Wrappers.lambdaQuery(VipBuyerLotteryLog.class)
                        .eq(VipBuyerLotteryLog::getVipBuyerCardId, vipId)
                        .ge(VipBuyerLotteryLog::getCreatedAt, recordsReq.getStartAt())
                        .le(VipBuyerLotteryLog::getCreatedAt, recordsReq.getEndAt())
                        .orderByDesc(VipBuyerLotteryLog::getCreatedAt);
                Page<VipBuyerLotteryLog> lotteryPage = PageUtils.getPage(recordsReq, VipBuyerLotteryLog.class);
                vipBuyerLotteryLogService.page(lotteryPage, lotteryQueryWrapper);
                List<VipRightsUsedRecordsVO.LotteryRecord> lotteryRecords = lotteryPage.getRecords().stream().map(VipRightsUsedRecordsVO.LotteryRecord::builder).toList();
                yield new ListBase<>(lotteryRecords, lotteryPage.getTotal(), lotteryPage.getCurrent(), lotteryPage.getSize());
        };
    }
}
