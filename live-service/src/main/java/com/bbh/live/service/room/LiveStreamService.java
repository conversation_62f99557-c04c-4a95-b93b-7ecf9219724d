package com.bbh.live.service.room;


import com.bbh.feign.dto.LiveConfigDTO;
import com.bbh.feign.dto.LiveDTO;
import com.bbh.feign.dto.LiveRecordDTO;
import com.bbh.feign.dto.RecordVideoDTO;
import com.bbh.model.LiveRoom;

/**
 * 直播流相关 sdk
 *
 * <AUTHOR>
 */
public interface LiveStreamService {

    /**
     * 创建推流配置
     */
    LiveConfigDTO createLiveConfig(LiveDTO dto);

    /**
     * 获取回放录像
     */
    RecordVideoDTO getVideoRecord(LiveRecordDTO liveRecordDTO);

    /**
     * 后台控制 禁止推流
     */
    void forbidLiveStream(LiveDTO dto);

    /**
     * 后台控制 恢复推流
     */
    void resumeLiveStream(LiveDTO dto);

    /**
     * 开始直播
     *
     * @param roomId 直播间ID
     */
    void startLive(Long roomId);

    /**
     * 通过流回调开始直播
     *
     * @param roomId 直播间ID
     */
    void startLiveByStreamCallback(Long roomId);

    /**
     * 停止直播
     *
     * @param roomId 直播间ID
     */
    void stopLive(Long roomId);

    /**
     * 通过流回调停止直播
     *
     * @param roomId 直播间ID
     */
    void stopLiveByStreamCallback(Long roomId);

    /**
     * 默认不检查是否首次开播
     * 首次开播时，生产两条延迟队列： <br>
     * 1. 结束前3分钟的开始倒计时 <br>
     * 2. 到时间后的强制关播 <br>
     *
     * @param liveRoom 直播间
     */
    void produceLiveDelayQueue(LiveRoom liveRoom);
}
