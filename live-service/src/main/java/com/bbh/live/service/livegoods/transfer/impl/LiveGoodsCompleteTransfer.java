package com.bbh.live.service.livegoods.transfer.impl;

import com.bbh.enums.LiveGoodsTradeTypeEnum;
import com.bbh.live.dao.dto.livegoods.LiveGoodsDTO;
import com.bbh.live.service.livegoods.context.PutAwayLiveGoodsContext;
import com.bbh.live.service.msg.MsgService;
import com.bbh.live.util.NumberUtils;
import com.bbh.model.LiveGoods;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

/**
 * <AUTHOR> dsy
 * @Date: 2024/7/29
 * @Description:
 */
@AllArgsConstructor
@Service("liveGoodsCompleteTransfer")
public class LiveGoodsCompleteTransfer extends AbstractLiveGoodsTransfer<PutAwayLiveGoodsContext> {

    private final MsgService msgService;

    @Override
    protected void doCheck(LiveGoodsDTO liveGoodsDTO, PutAwayLiveGoodsContext context) {
        //完善商品信息不校验必填
        NumberUtils.checkGoodsPrice(liveGoodsDTO.getTradeType(), liveGoodsDTO.getStartPrice());
    }

    @Override
    public void doAction(LiveGoodsDTO liveGoods, PutAwayLiveGoodsContext context) {
        // 完善信息 商品状态不变
        liveGoods.setAuctionDuration(context.getAuctionDuration());
        liveGoods.setStartPrice(context.getStartPrice());
        liveGoods.setIncreasePrice(context.getIncreasePrice());
        liveGoods.setTradeType(context.getTradeType());
        liveGoods.setDirectorRemark(context.getDirectorRemark());

        // 一口价，如果是零元，要强制改为0.01
        BigDecimal startPrice = context.getStartPrice() == null ? null : context.getStartPrice();
        if (context.getTradeType() == LiveGoodsTradeTypeEnum.SEC_KILL && startPrice != null && startPrice.compareTo(BigDecimal.ZERO) <= 0) {
            startPrice = BigDecimal.valueOf(0.01);
        }

        var updateChainWrapper = liveGoodsDetailService.getLiveGoodsService().lambdaUpdate().eq(LiveGoods::getId, liveGoods.getId())
                .set(LiveGoods::getTradeType, context.getTradeType())
                .set(LiveGoods::getAuctionDuration, context.getAuctionDuration())
                .set(LiveGoods::getIncreasePrice, context.getIncreasePrice())
                .set(LiveGoods::getStartPrice, startPrice)
                .set(LiveGoods::getDirectorRemark, context.getDirectorRemark());
        updateChainWrapper.update();

        // 发送弹幕消息
        msgService.goodsUpdate(liveGoods.getLiveRoomId(), liveGoods.getId());
    }
}
