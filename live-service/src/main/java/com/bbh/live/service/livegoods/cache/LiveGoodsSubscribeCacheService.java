package com.bbh.live.service.livegoods.cache;

/**
 * <AUTHOR> dsy
 * @Date: 2024/9/10
 * @Description:
 */
public interface LiveGoodsSubscribeCacheService {

    /**
     * 订阅商品
     * @param liveRoomId
     * @param liveGoodsId
     * @param buyerSeatId
     */
    void subscribeLiveGoods(Long liveRoomId, Long liveGoodsId, Long buyerSeatId);

    /**
     * 取消订阅
     * @param liveRoomId
     * @param liveGoodsId
     * @param buyerSeatId
     */
    void cancelSubscribeLiveGoods(Long liveRoomId, Long liveGoodsId, Long buyerSeatId);

    /**
     * 是否已订阅
     * @param liveRoomId
     * @param liveGoodsId
     * @param buyerSeatId
     * @return
     */
    boolean isSubscribe(Long liveRoomId, Long liveGoodsId, Long buyerSeatId);


    /**
     *  直播结束调用，清理商品相关的预约缓存
     * @param liveRoomId
     */
    void clearAll(Long liveRoomId);


}
