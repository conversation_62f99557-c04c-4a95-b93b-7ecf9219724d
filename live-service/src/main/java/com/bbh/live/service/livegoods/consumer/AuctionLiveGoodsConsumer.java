package com.bbh.live.service.livegoods.consumer;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.lang.WeightRandom;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.RandomUtil;
import com.bbh.live.constant.DelayQueueTopics;
import com.bbh.live.constant.LiveGoodsConstant;
import com.bbh.live.dao.dto.livegoods.LiveGoodsDTO;
import com.bbh.live.dao.service.GlobalOrgSeatService;
import com.bbh.live.enums.BuyerVipTypeEnum;
import com.bbh.live.enums.LiveGoodsTradeCompletedTypeEnum;
import com.bbh.live.handler.queue.DelayJob;
import com.bbh.live.handler.queue.DelayJobFactory;
import com.bbh.live.handler.queue.consumer.AbstractMessageConsumer;
import com.bbh.live.service.buyer.vip.BuyerVipService;
import com.bbh.live.service.buyer.vip.vo.UserBuyerVipInfoVO;
import com.bbh.live.service.livegoods.LiveGoodsDetailService;
import com.bbh.live.service.livegoods.cache.LiveGoodsCacheManager;
import com.bbh.live.service.livegoods.cache.info.LiveGoodsAuctionBidInfo;
import com.bbh.live.service.livegoods.cache.info.LiveGoodsAuctionInfo;
import com.bbh.live.service.livegoods.context.AuctionFailLiveGoodsContext;
import com.bbh.live.service.livegoods.context.LiveGoodsContext;
import com.bbh.live.service.livegoods.context.TradeLiveGoodsContext;
import com.bbh.live.service.room.context.LiveRoomContextHolder;
import com.bbh.live.statemachine.goods.GoodsStateMachineManager;
import com.bbh.live.thread.ThreadPoolManager;
import com.bbh.model.LiveGoods;
import com.bbh.service.lock.HtbLockService;
import com.bbh.service.lock.bean.HtbLock;
import com.bbh.service.mq.constants.MqConstant;
import com.bbh.service.mq.service.CoreMqListener;
import com.bbh.util.AssertUtil;
import com.bbh.util.LogExUtil;
import com.bbh.util.ParamsUtil;
import com.rabbitmq.client.Channel;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.aop.framework.AopContext;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.nio.charset.Charset;
import java.util.*;
import java.util.concurrent.Executor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR> dsy
 * @Date: 2024/7/29
 * @Description:
 */
@Service
public class AuctionLiveGoodsConsumer extends AbstractMessageConsumer<Long> implements ApplicationListener<AuctionEndEvent>, CoreMqListener {

    private static final Logger log = LoggerFactory.getLogger(AuctionLiveGoodsConsumer.class);
    @Resource
    private LiveGoodsCacheManager liveGoodsCacheManager;
    @Resource
    private GoodsStateMachineManager<LiveGoodsContext> goodsStateMachineManager;
    @Resource
    private LiveGoodsDetailService liveGoodsDetailService;
    @Resource
    private HtbLockService htbLockService;
    @Resource
    private BuyerVipService buyerVipService;
    @Resource
    private GlobalOrgSeatService globalOrgSeatService;

    @Override
    public String topic() {
        return DelayQueueTopics.AUCTION_TOPIC;
    }

    @Override
    public void consume(DelayJob<Long> delayJob) {
        log.info("开始消费竞拍结束消息: 直播间id={}: 商品id={}", delayJob.getJobId(), delayJob.getData());
        String liveRoomIdStr = delayJob.getJobId();
        AssertUtil.assertTrue(StringUtils.isNotBlank(liveRoomIdStr), "竞拍结束消费失败，直播间id为空");
        long liveRoomId = Long.parseLong(liveRoomIdStr);

        //查询竞拍中缓存
        LiveGoodsAuctionInfo auctionCacheInfo = liveGoodsCacheManager.getLiveGoodsAuctionCache().getAuctionLiveGoods(liveRoomId, delayJob.getData());
        AssertUtil.assertNotNull(auctionCacheInfo, "竞拍商品不存在");

        //兜底逻辑处理，防止队列延迟时间与实际结束时间不一致。时间未到不执行
        if (!isTimeUp(liveRoomId, auctionCacheInfo)) {
            return;
        }

        // 结算加锁
        HtbLock htbLock = htbLockService.getLock(String.format(LiveGoodsConstant.SETTLE_LOCK_KEY, liveRoomId, delayJob.getData()));
        boolean locked = htbLock.tryLock(5, TimeUnit.SECONDS);
        int retryCount = 0;
        while(!locked){
            locked = htbLock.tryLock(5, TimeUnit.SECONDS);
            log.info("直播竞拍结算重试次数:{}, 直播间id:{}, 商品id:{}", ++retryCount, liveRoomId, delayJob.getData());
            if(!locked){
                ThreadUtil.sleep(50);
            }
        }
        try {
            //最新的竞拍信息
            auctionCacheInfo = liveGoodsCacheManager.getLiveGoodsAuctionCache().getAuctionLiveGoods(liveRoomId, auctionCacheInfo.getLiveGoodsId());
            //时间燃尽，处理商品流拍或成交
            ((AuctionLiveGoodsConsumer)AopContext.currentProxy()).handleAuctionEnd(auctionCacheInfo);
        } finally {
            htbLock.unlock();
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void handleAuctionEnd(LiveGoodsAuctionInfo auctionCacheInfo) {
        Long buyerSeatId = auctionCacheInfo.getBuyerSeatId();
        Long liveGoodsId = auctionCacheInfo.getLiveGoodsId();
        LiveGoodsDTO goodsDetailInfo = liveGoodsDetailService.getLiveGoodsSimpleDetailInfo(liveGoodsId);
        AssertUtil.assertNotNull(goodsDetailInfo, "商品不存在，id:" + liveGoodsId);
        LiveRoomContextHolder.initRoomContext(goodsDetailInfo.getLiveRoomId());
        try {
            if (buyerSeatId == null) {
                //商品流拍
                goodsStateMachineManager.auctionFail(goodsDetailInfo, new AuctionFailLiveGoodsContext(goodsDetailInfo.getLiveGoodsId(), false));
            } else {
                //竞拍成功
                try {
                    TradeLiveGoodsContext context = new TradeLiveGoodsContext()
                            .setFinalPrice(auctionCacheInfo.getCurrentPrice())
                            .setBuyerSeatId(buyerSeatId)
                            .setTradeType(LiveGoodsTradeCompletedTypeEnum.AUCTION);
                    context.setLiveGoodsId(liveGoodsId);
                    goodsStateMachineManager.trade(goodsDetailInfo, context);
                }catch (Throwable t){
                    //成交失败按照流拍处理
                    goodsStateMachineManager.auctionFail(goodsDetailInfo, new AuctionFailLiveGoodsContext(goodsDetailInfo.getLiveGoodsId(), true));
                    LogExUtil.errorLog("直播间[" + goodsDetailInfo.getLiveRoomId() + "]商品[" + goodsDetailInfo.getId() + "]成交处理异常，转为流拍处理. 异常：" + ExceptionUtil.getRootCauseMessage(t), t);
                }
            }
        } catch (Throwable t) {
            log.error("直播间[{}]商品[{}]竞拍结算异常。异常：{}", goodsDetailInfo.getLiveRoomId(), goodsDetailInfo.getId(), ExceptionUtil.getRootCauseMessage(t), t);
        } finally {
            LiveRoomContextHolder.clearRoom();
        }
    }


    private boolean isTimeUp(Long liveRoomId, LiveGoodsAuctionInfo auctionCacheInfo) {
        Date now = DateUtil.date();
        //实际结束时间
        Date actualAuctionEndTime = auctionCacheInfo.getAuctionEndTime();
        if (log.isDebugEnabled()) {
            log.debug("直播间商品竞拍结束消费处理，商品id:{}, 当前时间：{}, 竞拍结束时间：{}", auctionCacheInfo.getLiveGoodsId(), DateUtil.format(now, DatePattern.NORM_DATETIME_PATTERN), DateUtil.format(actualAuctionEndTime, DatePattern.NORM_DATETIME_PATTERN));
        }
        //判断时间是否燃尽
        boolean timeUp = super.isTimeUp(actualAuctionEndTime, () -> DelayJobFactory.createAuctionDelayJob(liveRoomId, auctionCacheInfo.getLiveGoodsId(), 0L));
        if (timeUp) {
            // 开始结算，记录标记位
            Boolean markSuccess = liveGoodsCacheManager.getLiveGoodsAuctionCache().setAuctionGoodsBeginSettle(liveRoomId, auctionCacheInfo.getLiveGoodsId());
            // 修改失败，说明已在处理
            if(!markSuccess){
                log.warn("商品[{}]竞拍结算已开始，本次结算不执行", auctionCacheInfo.getLiveGoodsId());
                return false;
            }
        }
        return timeUp;
    }


    @Override
    public Executor getExecutor() {
        return ThreadPoolManager.getAuctionMessageConsumerExecutor();
    }



    @Override
    public void onApplicationEvent(@NotNull AuctionEndEvent event) {
        // 竞拍商品已结束，但是未结算 (可能是应用 crash，断电极端情况)。兜底处理
        LiveGoodsAuctionInfo goodsAuctionInfo = event.getSource();
        LiveGoods liveGoods = liveGoodsDetailService.getLiveGoodsService().lambdaQuery()
                .eq(LiveGoods::getId, goodsAuctionInfo.getLiveGoodsId())
                .select(LiveGoods::getLiveRoomId)
                .one();
        if(liveGoods == null){
            return;
        }
        // 重新消费
        Long liveRoomId = liveGoods.getLiveRoomId();
        DelayJob<Long> delayJob = DelayJobFactory.createAuctionDelayJob(liveRoomId, goodsAuctionInfo.getLiveGoodsId(), 100L);
        delayQueueManager.addToQueue(delayJob);
    }

    /**
     * 一口价结算消费
     * @param message
     * @param channel
     */
    @Override
    @RabbitListener(queuesToDeclare = @Queue(name = MqConstant.SEC_KILL, durable = "true"))
    public void handle(Message message, Channel channel) {
        String body = new String(message.getBody(), Charset.defaultCharset());
        Long liveRoomId = ParamsUtil.getValueFromJsonStr(body, "liveRoomId", Long.class);
        Long liveGoodsId = ParamsUtil.getValueFromJsonStr(body, "liveGoodsId", Long.class);
        log.info("一口价开始结算，直播间：【{}】,商品 id：【{}】, 时间：{}", liveRoomId, liveGoodsId, DateUtil.format(new Date(), DatePattern.NORM_DATETIME_PATTERN));

        try{
            if(liveGoodsId == null || liveRoomId == null){
                log.error("一口价秒杀结算失败，直播间或商品不存在，roomId:{}, goodsId{}", liveRoomId, liveGoodsId);
                return;
            }

            //查询竞拍中缓存
            LiveGoodsAuctionInfo auctionCacheInfo = liveGoodsCacheManager.getLiveGoodsAuctionCache().getAuctionLiveGoods(liveRoomId, liveGoodsId);
            if(auctionCacheInfo == null){
                log.error("一口价秒杀结算失败，商品不存在，roomId:{}, goodsId{}", liveRoomId, liveGoodsId);
                return;
            }
            // 开始结算，记录标记位
            Boolean markSuccess = liveGoodsCacheManager.getLiveGoodsAuctionCache().setAuctionGoodsBeginSettle(liveRoomId, auctionCacheInfo.getLiveGoodsId());
            // 修改失败，说明已在处理
            if(!markSuccess){
                log.warn("商品[{}]结算已开始，本次结算不执行", auctionCacheInfo.getLiveGoodsId());
                return;
            }
            // 所有出价人
            List<LiveGoodsAuctionBidInfo> auctionGoodsBidInfoList = liveGoodsCacheManager.getLiveGoodsAuctionCache().getAuctionGoodsBidInfo(liveRoomId, liveGoodsId);
            // 根据权重选出其中一位出价人
            Long luckBuyer = pickLuckyBuyer(auctionGoodsBidInfoList);
            // 设置为成交人
            auctionCacheInfo.setBuyerSeatId(luckBuyer);
            // 开始结算
            ((AuctionLiveGoodsConsumer)AopContext.currentProxy()).handleAuctionEnd(auctionCacheInfo);
        }catch (Exception e){
            log.error("一口价结算异常，直播间：【{}】,商品 id：【{}】, 原因：{}", liveRoomId, liveGoodsId, e.getMessage(), e);
            liveGoodsCacheManager.getLiveGoodsAuctionCache().resetAuctionGoodsSettle(liveRoomId, liveGoodsId);
        }finally {
            try {
                channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
            } catch (IOException ex) {
                LogExUtil.errorLog("Failed to ACK message", ex);
            }
        }
    }


    private Long pickLuckyBuyer(List<LiveGoodsAuctionBidInfo> auctionGoodsBidInfoList){
        if(auctionGoodsBidInfoList.isEmpty()){
            return null;
        }
        if(auctionGoodsBidInfoList.size() == 1){
            return auctionGoodsBidInfoList.getFirst().getSeatId();
        }
        Set<Long> bidSeatSet = auctionGoodsBidInfoList.stream().map(LiveGoodsAuctionBidInfo::getSeatId).collect(Collectors.toSet());
        // 会员权重
        Map<Long, UserBuyerVipInfoVO> userBuyerVipInfoByUidList = buyerVipService.getUserBuyerVipInfoByUidList(new ArrayList<>(bidSeatSet));
        // 席位权重
        Map<Long, Integer> seatWightMap = globalOrgSeatService.getSeatWightMap(bidSeatSet);
        if(seatWightMap.isEmpty()){
            return null;
        }

        List<WeightRandom.WeightObj<Long>> weightObjs = new ArrayList<>();
        // 合并权重
        seatWightMap.forEach((seatId, wight) -> {
            UserBuyerVipInfoVO userBuyerVipInfoVO = userBuyerVipInfoByUidList.get(seatId);
            if(userBuyerVipInfoVO != null && userBuyerVipInfoVO.getBuyerVipType() == BuyerVipTypeEnum.VIP){
                wight += userBuyerVipInfoVO.getVipConfig().getSecKillRandomWeight();
            }
            weightObjs.add(new WeightRandom.WeightObj<>(seatId, wight));
        });

        // 根据权重随机抽取一个买家
        WeightRandom<Long> weightRandom = RandomUtil.weightRandom(weightObjs);
        return weightRandom.next();
    }
}
