package com.bbh.live.service.msg.push;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.text.MessageFormat;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/10/23 13:32
 * @description
 */
@Getter
@AllArgsConstructor
public enum LivePushTypeEnum {

    /**
     * 议价失败
     */
    BARGAIN_FAIL(101, "议价失败", "【{0}】议价¥{1,number,#,###}被卖家拒绝", "go_app",  null, null),

    /**
     * 议价成功
     */
    BARGAIN_SUCCESS(102, "议价成功", "【{0}】议价¥{1,number,#,###}，卖家同意，商品已加入收银台", null, "/pages/home/<USER>", new HashMap<>(){{
        put("type", 2);
        put("hideTabBar", 1);
        put("cashierBackNarBar", 1);
    }}),

    /**
     * 订单需要发货
     */
    DELIVER_ORDER_GOODS(103, "发货提醒", "您有新订单需要发货，请尽快处理以确保买家及时收到商品", null, "/pages/onlineExhibition/order/sale/detail", new HashMap<>(){{
        put("id", null);
    }}),

    OFFLINE_TRANSFER_FAILED(31, "线下转账审核失败", "线下转账审核失败，请尽快处理", null, "/pages/onlineExhibition/order/sale/detail", new HashMap<>(){{
        put("id", null);
    }}),

    /**
     * 买家提交取消成交申请 - 通知卖家
     */
    NOTIFY_SELLER_SUBMIT_CANCEL_APPROVAL(401, "商品取消成交申请待审核", "您有新的商品取消成交申请待审核", null, "/pages/goodsCancelTransaction/seller/detail", new HashMap<>(){{
        put("id", null);
    }}),

    /**
     * 买家提交取消成交申请 - 通知买家
     */
    NOTIFY_BUYER_SUBMIT_CANCEL_APPROVAL(402, "申请成功待卖家审核", "您的取消成交申请已申请，待卖家审核", null, "/pages/goodsCancelTransaction/buyer/detail", new HashMap<>(){{
        put("id", null);
    }}),

    /**
     * 卖家未处理 即将处理前一个小时
     */
    NOTIFY_SELLER_BEFORE_AUTO_CANCEL_APPROVAL(403, "取消成交即将自动审核", "您有取消成交申请待处理，如不及时审核，系统将在规定时间内自动审批通过。", null, "/pages/goodsCancelTransaction/seller/detail", new HashMap<>(){{
        put("id", null);
    }}),

    /**
     * 卖家未处理，系统自动处理后
     */
    NOTIFY_SELLER_AFTER_AUTO_CANCEL_APPROVAL(404, "取消成交自动审核", "买家取消成交申请已自动通过。", null, "/pages/goodsCancelTransaction/seller/detail", new HashMap<>(){{
        put("id", null);
    }}),

    /**
     * 系统自动审批后/商家审批扣钱后
     */
    NOTIFY_BUYER_CANCEL_AUTO_OR_DEDUCT(405, "取消成交申请待平台处理", "您的商品取消成交申请商家已自动审核，待平台处理", null, "/pages/goodsCancelTransaction/buyer/detail", new HashMap<>(){{
        put("id", null);
    }}),

    /**
     * 平台处理后 - 买家
     */
    NOTIFY_BUYER_CANCEL_AFTER_PLATFORM_HANDLE(406, "取消成交申请已通过", "您的取消成交申请已通过", null, "/pages/goodsCancelTransaction/buyer/detail", new HashMap<>(){{
        put("id", null);
    }}),

    /**
     * 平台处理后 - 卖家
     */
    NOTIFY_SELLER_CANCEL_AUTO_OR_DEDUCT(407, "平台已同意取消", "平台已同意买家商品取消成交申请", null, "/pages/goodsCancelTransaction/seller/detail", new HashMap<>(){{
        put("id", null);
    }}),


    /**
     * 商家审批不扣钱后 - 买家
     */
    NOTIFY_BUYER_CANCEL_APPROVAL_NO_DEDUCT(408, "取消成交申请已通过", "您的取消成交申请已通过", null, "/pages/goodsCancelTransaction/buyer/detail", new HashMap<>(){{
        put("id", null);
    }}),

    /**
     * 商家审批不扣钱后 - 卖家
     */
    NOTIFY_SELLER_CANCEL_APPROVAL_NO_DEDUCT(409, "平台已同意取消", "平台已同意买家商品取消成交申请", null, "/pages/goodsCancelTransaction/seller/detail", new HashMap<>(){{
        put("id", null);
    }}),
    ;


    @EnumValue
    @JsonValue
    private final Integer type;

    /**
     * 推送标题
     */
    private final String title;

    /**
     * 推送内容，需要格式化
     */
    private final String content;

    private final String afterOpen;

    /**
     * 点击查看对应的页面
     */
    private final String path;

    /**
     * 点击查看所需要的参数
     */
    private final Map<String, Object> params;

    public String getContent(Object ... arguments) {
        return MessageFormat.format(content, arguments);
    }

}
