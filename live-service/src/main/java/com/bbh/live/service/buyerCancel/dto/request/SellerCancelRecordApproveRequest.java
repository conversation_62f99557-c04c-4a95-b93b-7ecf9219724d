package com.bbh.live.service.buyerCancel.dto.request;

import com.bbh.enums.GlobalOrderTypeEnum;
import com.bbh.live.service.buyerCancel.dto.BuyerCancelBiz;
import lombok.Data;

/**
 * 卖家审核通过请求对象
 *
 * <AUTHOR>
 * @since 2025-01-10
 */
@Data
public class SellerCancelRecordApproveRequest implements BuyerCancelBiz {

    /**
     * 记录ID
     */
    private Long orderId;

    /**
     * 是否扣除
     */
    private Boolean ifDeduct;

    /**
     * 是否超时后自动取消
     */
    private Boolean ifTimeout = false;


    private GlobalOrderTypeEnum bizType;

    @Override
    public GlobalOrderTypeEnum getBizType() {
        return this.bizType;
    }

    @Override
    public void setBizType(GlobalOrderTypeEnum bizType) {
        this.bizType=bizType;
    }
}