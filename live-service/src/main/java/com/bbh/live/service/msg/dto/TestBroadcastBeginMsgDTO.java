package com.bbh.live.service.msg.dto;

import com.bbh.live.core.msg.MsgType;
import com.bbh.live.service.msg.dto.base.BaseMsg;
import com.bbh.live.service.msg.dto.base.IMsg;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> dsy
 * @Date: 2024/9/4
 * @Description:
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class TestBroadcastBeginMsgDTO extends BaseMsg implements IMsg {

    private String liveRoomName;

    private Long liveRoomId;

    @Override
    public String type() {
        return MsgType.TEST_BROADCAST_BEGIN;
    }
}
