package com.bbh.live.service.sce;

import com.bbh.enums.ChangeTypeEnum;
import com.bbh.live.dao.mapper.SceGoodsPriceChangeMapper;
import com.bbh.model.SceGoods;
import com.bbh.model.SceGoodsPriceChange;
import com.bbh.vo.AuthUser;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2025/5/8
 * @description: ${description}
 */
@Service
public class SceGoodsPriceChangeServiceImpl extends ServiceImpl<SceGoodsPriceChangeMapper, SceGoodsPriceChange> implements SceGoodsPriceChangeService {
    /**
     * 初始化商品价格记录
     * <p>
     * 该方法用于创建商品的初始价格记录，记录商品的初始售价。
     * 初始价格记录的旧价格和价格变动都设置为0，变更类型为初始化。
     *
     * @param authUser   当前操作用户信息
     * @param sceGoodsId 商品ID
     * @param sellPrice  初始售价
     * @return 创建的价格记录对象
     */
    @Transactional(rollbackFor = Exception.class)
    public List<SceGoodsPriceChange> init(AuthUser authUser, Map<Long, BigDecimal> sceGoodsIdSellPriceMap) {
        List<SceGoodsPriceChange> result = new ArrayList<>();
        for (Map.Entry<Long, BigDecimal> entry : sceGoodsIdSellPriceMap.entrySet()) {
            Long sceGoodsId = entry.getKey();
            BigDecimal sellPrice = entry.getValue();
            // 参数校验
            if (authUser == null || sceGoodsId == null || sellPrice == null) {
                continue;
            }
            // 创建初始价格记录
            SceGoodsPriceChange priceChange = new SceGoodsPriceChange();
            // 设置基本信息
            priceChange.setSceGoodsId(sceGoodsId);
            priceChange.setCreateOrgId(authUser.getOrgId());
            priceChange.setCreateSeatId(authUser.getSeatId());
            priceChange.setCreateUserId(authUser.getUserId());
            priceChange.setCreatedAt(new Date());
            // 设置价格信息
            priceChange.setSellPriceOld(BigDecimal.ZERO);
            priceChange.setChangeSellPrice(BigDecimal.ZERO);
            priceChange.setChangeType(ChangeTypeEnum.INIT);
            priceChange.setSellPriceNew(sellPrice);
            result.add(priceChange);
        }
        this.saveBatch(result);

        return result;
    }

    @Override
    public List<SceGoodsPriceChange> initBySceGoods(List<SceGoods> sceGoodsList) {
        List<SceGoodsPriceChange> result = new ArrayList<>();
        for (SceGoods sceGoods : sceGoodsList) {
            // 创建初始价格记录
            SceGoodsPriceChange priceChange = new SceGoodsPriceChange();
            // 设置基本信息
            priceChange.setSceGoodsId(sceGoods.getId());
            priceChange.setCreateOrgId(sceGoods.getOrgId());
            priceChange.setCreateSeatId(sceGoods.getCreateSeatId());
            priceChange.setCreateUserId(sceGoods.getCreateUserId());
            priceChange.setCreatedAt(new Date());
            // 设置价格信息
            priceChange.setSellPriceOld(BigDecimal.ZERO);
            priceChange.setChangeSellPrice(BigDecimal.ZERO);
            priceChange.setChangeType(ChangeTypeEnum.INIT);
            priceChange.setSellPriceNew(sceGoods.getCurrentPrice());
            result.add(priceChange);
        }
        this.saveBatch(result);
        return result;
    }
}
