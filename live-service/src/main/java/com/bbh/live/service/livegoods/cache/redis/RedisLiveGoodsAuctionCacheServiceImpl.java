package com.bbh.live.service.livegoods.cache.redis;

import com.bbh.exception.ServiceException;
import com.bbh.live.service.livegoods.cache.LiveGoodsAuctionCacheService;
import com.bbh.live.service.livegoods.cache.LiveGoodsCacheConstant;
import com.bbh.live.service.livegoods.cache.info.AuctionResult;
import com.bbh.live.service.livegoods.cache.info.LiveGoodsAuctionBidInfo;
import com.bbh.live.service.livegoods.cache.info.LiveGoodsAuctionCacheObj;
import com.bbh.live.service.livegoods.cache.info.LiveGoodsAuctionInfo;
import com.bbh.live.service.livegoods.consumer.AuctionEndEvent;
import com.bbh.live.service.redis.LuaScript;
import com.bbh.live.service.redis.LuaScriptExecuteEngine;
import com.bbh.live.service.room.context.LiveRoomContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR> dsy
 * @Date: 2024/7/27
 * @Description: 处理竞拍中商品的缓存服务 hash存储
 * 结构
 *      key：     live_auction_goods:{liveRoomId}
 *      --- 商品信息
 *      hashKey :         live_goods:{goodsId}
 *      value  :          {liveGoodsId, currentPrice, increasePrice, auctionEndTime, buyerSeatId, ifSettle}
 *      --- 出价人
 *      hashKey :         bid_seats:{goodsId}
 *      value  :          [{seat_id:Int,bid_price:double}]
 * 。每个直播间只有一个正在竞拍中的商品
 */
@Slf4j
@Component
public class RedisLiveGoodsAuctionCacheServiceImpl extends BaseRedisLiveGoodsCache implements LiveGoodsAuctionCacheService {

    @Override
    public boolean insertAuctionLiveGoods(Long liveRoomId, LiveGoodsAuctionInfo context) {
        Long expireTime = getLiveRoomEndLeft(liveRoomId);
        //转缓存对象
        LiveGoodsAuctionCacheObj cacheObj = LiveGoodsAuctionCacheObj.transfer2AuctionCacheObj(context);
        redisHashService.putWithExpire(buildKey(liveRoomId), buildHashKey(context.getLiveGoodsId()), cacheObj, expireTime, TimeUnit.SECONDS);
        return true;
    }

    @Override
    public LiveGoodsAuctionInfo getAuctionLiveGoods(Long liveRoomId, Long liveGoodsId) {
        LiveGoodsAuctionCacheObj cacheObj = redisHashService.get(buildKey(liveRoomId), buildHashKey(liveGoodsId), LiveGoodsAuctionCacheObj.class);
        if (cacheObj == null || cacheObj.getCurrentPrice() == null || cacheObj.getAuctionEndTime() == null) {
            return null;
        }

        return LiveGoodsAuctionCacheObj.transfer2AuctionInfo(cacheObj).setLiveGoodsId(liveGoodsId);
    }

    @Override
    public LiveGoodsAuctionInfo getAuctionLiveGoodsBeforeExpire(Long liveRoomId, Long liveGoodsId) {
        LiveGoodsAuctionInfo liveGoodsAuctionInfo = getAuctionLiveGoods(liveRoomId, liveGoodsId);
        if(liveGoodsAuctionInfo == null){
            return null;
        }
        if(!liveGoodsAuctionInfo.getIfSettle() && liveGoodsAuctionInfo.getAuctionEndTime().before(new Date())){
            applicationContext.publishEvent(new AuctionEndEvent(liveGoodsAuctionInfo));
            return null;
        }
        return liveGoodsAuctionInfo;
    }

    @Override
    public void removeAuctionLiveGoods(Long liveRoomId, Long liveGoodsId) {
        redisHashService.remove(buildKey(liveRoomId), buildHashKey(liveGoodsId));
    }

    @Override
    public void clearAll(Long liveRoomId) {
        redisHashService.remove(buildKey(liveRoomId));
    }

    @Override
    public Date getAuctionGoodsEndTime(Long liveRoomId, Long liveGoodsId) {
        Long auctionEndTime = getAuctionGoodsFieldValue(liveRoomId, liveGoodsId, "auctionEndTime", Long.class);
        return auctionEndTime == null ? null : new Date(auctionEndTime);
    }

    @Override
    public BigDecimal getAuctionGoodsCurrentPrice(Long liveRoomId, Long liveGoodsId) {
        return getAuctionGoodsFieldValue(liveRoomId, liveGoodsId, "currentPrice", BigDecimal.class);
    }

    private <T> T getAuctionGoodsFieldValue(Long liveRoomId, Long liveGoodsId, String field, Class<T> clazz) {
        return LuaScriptExecuteEngine.execScipt(LuaScript.AUCTION_LIVE_GOODS_FIELD_VALUE, clazz,
                Arrays.asList(buildKey(liveRoomId), buildHashKey(liveGoodsId)), field);
    }

    @Override
    public Boolean setAuctionGoodsBeginSettle(Long liveRoomId, Long liveGoodsId) {
        return setAuctionGoodsFieldValueCAS(liveRoomId, liveGoodsId, "ifSettle", "1", "0");
    }

    @Override
    public Boolean resetAuctionGoodsSettle(Long liveRoomId, Long liveGoodsId) {
        return setAuctionGoodsFieldValue(liveRoomId, liveGoodsId, "ifSettle", "0");
    }

    @Override
    public Boolean setAuctionGoodsBeginRevoke(Long liveRoomId, Long liveGoodsId) {
        return LuaScriptExecuteEngine.execScipt(LuaScript.AUCTION_LIVE_GOODS_SET_REVOKE, Boolean.class,
                Arrays.asList(buildKey(liveRoomId), buildHashKey(liveGoodsId)));
    }

    private Boolean setAuctionGoodsFieldValue(Long liveRoomId, Long liveGoodsId, String field, Object value) {
        return LuaScriptExecuteEngine.execScipt(LuaScript.AUCTION_LIVE_GOODS_SET_FIELD_VALUE, Boolean.class,
                Arrays.asList(buildKey(liveRoomId), buildHashKey(liveGoodsId)), field, value);
    }

    private Boolean setAuctionGoodsFieldValueCAS(Long liveRoomId, Long liveGoodsId, String field, Object newValue, Object expectedValue) {
        return LuaScriptExecuteEngine.execScipt(LuaScript.AUCTION_LIVE_GOODS_SET_FIELD_VALUE, Boolean.class,
                Arrays.asList(buildKey(liveRoomId), buildHashKey(liveGoodsId)), field, newValue, expectedValue);
    }

    @Override
    public AuctionResult auctionBid(Long liveRoomId, LiveGoodsAuctionInfo pre, LiveGoodsAuctionInfo cur) {
        Long res = 1L;
        switch (pre.getTradeType()){
            case AUCTION:
                //竞拍剩余时间低于当前时间时，加时间，单位要求毫秒
                long expireTimeThreshold = LiveRoomContextHolder.getLiveRoomContext().getIncreaseSurplusTime() * 1000;
                //所加时间，单位要求毫秒
                long increaseTime = LiveRoomContextHolder.getLiveRoomContext().getIncreaseTime() * 1000;
                res = LuaScriptExecuteEngine.execScipt(LuaScript.AUCTION_LIVE_GOODS_UPDATE_PRICE, Long.class,
                        Arrays.asList(buildKey(liveRoomId), buildHashKey(cur.getLiveGoodsId())),
                        cur.getCurrentPrice().toString(), cur.getBuyerSeatId().toString(),
                        String.valueOf(expireTimeThreshold),
                        String.valueOf(increaseTime),
                        String.valueOf(System.currentTimeMillis()));
                break;
            case SEC_KILL:
                if (pre.getBuyerSeatId() != null){
                    break;
                }
                // 尝试修改为第一个出价人
                res = LuaScriptExecuteEngine.execScipt(LuaScript.AUCTION_LIVE_GOODS_UPDATE_FIRST_BIDDER, Long.class,
                        Arrays.asList(buildKey(liveRoomId), buildHashKey(cur.getLiveGoodsId())),
                        cur.getBuyerSeatId().toString());
        }

        AuctionResult result = AuctionResult.of(res.intValue());
        if(result.isSuccess()){
            // 插入出价记录
            this.insertLiveGoodsBidInfo(liveRoomId, cur.getLiveGoodsId(), cur.getBuyerSeatId(), cur.getCurrentPrice());
        }
        return result;
    }

    /**
     * 竞拍商品出过价的席位ID列表
     *
     * @param liveRoomId  直播间ID
     * @param liveGoodsId 商品ID
     * @return 席位ID列表
     */
    @Override
    public List<LiveGoodsAuctionBidInfo> getAuctionGoodsBidInfo(Long liveRoomId, Long liveGoodsId) {
        List<LiveGoodsAuctionBidInfo> bidInfoList = redisHashService.getList(buildKey(liveRoomId), buildBidInfoHashKey(liveGoodsId), LiveGoodsAuctionBidInfo.class);
        return Objects.requireNonNullElse(bidInfoList, new ArrayList<>());
    }

    /**
     * 插入竞拍商品中出价人的席位
     *
     * @param liveRoomId  直播间ID
     * @param liveGoodsId 商品ID
     * @param seatId      席位ID
     * @param bidAmount   出价金额
     */
    @Override
    public boolean insertLiveGoodsBidInfo(Long liveRoomId, Long liveGoodsId, Long seatId, BigDecimal bidAmount) {
        try {
            List<LiveGoodsAuctionBidInfo> bidInfoList = getAuctionGoodsBidInfo(liveRoomId, liveGoodsId);

            // 查找是否已存在该席位的出价信息
            Optional<LiveGoodsAuctionBidInfo> bidInfoOptional = bidInfoList.stream().filter(bidInfo -> bidInfo.getSeatId().equals(seatId)).findFirst();
            if (bidInfoOptional.isPresent()) {
                LiveGoodsAuctionBidInfo liveGoodsAuctionBidInfo = bidInfoOptional.get();
                if(liveGoodsAuctionBidInfo.getBidAmount().compareTo(bidAmount) == 0){
                    return true;
                } else {
                    liveGoodsAuctionBidInfo.setBidAmount(bidAmount);
                }
            } else {
                bidInfoList.add(new LiveGoodsAuctionBidInfo(seatId, bidAmount));
            }

            redisHashService.put(buildKey(liveRoomId), buildBidInfoHashKey(liveGoodsId), bidInfoList);
            return true;
        }catch (Throwable t){
            log.error("插入redis出价记录失败，原因：{}", t.getMessage(), t);
            throw new ServiceException(t.getMessage());
        }
    }

    private String buildKey(Long liveRoomId){
        return builderKey(LiveGoodsCacheConstant.LIVE_AUCTION_GOODS_KEY, liveRoomId.toString());
    }

    private String buildHashKey(Long liveGoodsId){
        return builderKey(LiveGoodsCacheConstant.LIVE_AUCTION_GOODS_HASH_KEY, liveGoodsId.toString());
    }

    private String buildBidInfoHashKey(Long liveGoodsId){
        return builderKey(LiveGoodsCacheConstant.LIVE_AUCTION_GOODS_BID_SEAT_HASH_KEY, liveGoodsId.toString());
    }
}
