package com.bbh.live.service.msg.dto;

import com.bbh.live.core.msg.MsgType;
import com.bbh.live.service.msg.dto.base.BaseMsg;
import com.bbh.live.service.msg.dto.base.BaseSeat;
import com.bbh.live.service.msg.dto.base.IMsg;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 关注了商家的消息
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class OrgAttentionMsgDTO extends BaseMsg implements IMsg {

    /**
     * 被关注的商家名称
     */
    private String orgName;

    /**
     * 被关注的商家ID
     */
    private Long orgId;

    /**
     * 谁关注的
     */
    private BaseSeat user;

    /**
     * 在哪个直播间
     */
    private Long liveRoomId;

    /**
     * 每个消息对应一个类型
     *
     * @return {@link MsgType}
     */
    @Override
    public String type() {
        return MsgType.ORG_ATTENTION;
    }
}
