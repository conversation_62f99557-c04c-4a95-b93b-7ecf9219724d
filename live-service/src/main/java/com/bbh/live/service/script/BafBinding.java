package com.bbh.live.service.script;

import groovy.lang.Binding;
import org.springframework.context.ApplicationContext;

/**
 * <AUTHOR>
 * @date 2024/12/3 14:32
 * @description
 */
public class BafBinding extends Binding {
    private ApplicationContext applicationContext;

    public BafBinding(){}

    public BafBinding(ApplicationContext appContext) {
        this.applicationContext = appContext;
    }

    public Object getVariable(String name) {
        if (super.hasVariable(name))
            return super.getVariable(name);

        if (applicationContext.containsBean(name)) {
            return applicationContext.getBean(name);
        } else
            return null;
    }

    public void setVariable(String name, Object value) {
        super.setVariable(name, value);
    }
}
