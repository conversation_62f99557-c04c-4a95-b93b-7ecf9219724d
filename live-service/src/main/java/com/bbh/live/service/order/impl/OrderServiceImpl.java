package com.bbh.live.service.order.impl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bbh.enums.LiveRoomStreamStatusEnum;
import com.bbh.live.controller.req.LiveGoodsQueryReq;
import com.bbh.live.dao.dto.CreateOrderDTO;
import com.bbh.live.dao.dto.livegoods.LiveGoodsDTO;
import com.bbh.live.dao.dto.vo.CreateOrderVO;
import com.bbh.live.dao.mapper.GlobalOrganizationMapper;
import com.bbh.live.dao.mapper.LiveGoodsMapper;
import com.bbh.live.dao.mapper.LiveRoomMapper;
import com.bbh.live.dao.service.GlobalOrderItemService;
import com.bbh.live.dao.service.GlobalOrderService;
import com.bbh.live.dao.service.GlobalOrderSubService;
import com.bbh.live.service.order.OrderService;
import com.bbh.live.service.order.OrderV2Service;
import com.bbh.live.service.order.dto.OrderBuilderContext;
import com.bbh.live.service.order.dto.PreparedOrderDTO;
import com.bbh.live.service.user.UserInfoService;
import com.bbh.model.*;
import com.bbh.util.AssertUtil;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 订单业务的Service
 *
 * <AUTHOR>
 * @deprecated 已作废，使用 {@link OrderV2Service}
 * @see OrderV2Service
 */
@Service
@AllArgsConstructor
@Slf4j
@Deprecated
public class OrderServiceImpl implements OrderService {

    private final GlobalOrderService globalOrderService;
    private final LiveGoodsMapper liveGoodsMapper;
    private final GlobalOrganizationMapper globalOrganizationMapper;
    private final LiveRoomMapper liveRoomMapper;
    private final GlobalOrderItemService globalOrderItemService;
    private final GlobalOrderSubService globalOrderSubService;
    private final OrderChecker orderChecker;
    private final UserInfoService userInfoService;
    private final OrderEntityBuilder orderEntityBuilder;

    /**
     * 生成订单
     *
     * @param createOrderDTO 订单信息
     * @return 返回已创建的订单详情
     */
    @Transactional
    @Override
    @Deprecated
    @SuppressWarnings("all")
    public CreateOrderVO createOrder(CreateOrderDTO createOrderDTO) {
        PreparedOrderDTO preparedOrder = buildPreparedOrder(createOrderDTO);
        // 保存订单
        globalOrderService.save(preparedOrder.getGlobalOrder());
        Long orderId = preparedOrder.getGlobalOrder().getId();

        // 批量录入order_sub，录入之后再依次更新order_item列表中满足条件的item的sub_id
        for (GlobalOrderSub orderSub : preparedOrder.getOrderSubList()) {
            orderSub.setOrderNo(preparedOrder.getGlobalOrder().getOrderNo());
            orderSub.setGlobalOrderId(orderId);
        }
        globalOrderSubService.saveBatch(preparedOrder.getOrderSubList());

        // 批量录入order_item，需要填充sub订单的id
        for (GlobalOrderItem orderItem : preparedOrder.getOrderItemList()) {
            preparedOrder.getOrderSubList().stream().filter(x -> x.getSellerOrgId().equals(orderItem.getSellerOrgId()))
                .findFirst().ifPresent(x -> orderItem.setGlobalOrderSubId(x.getId()));
            // 按商户取出所有的商品项，一个商户对应一个子订单
            orderItem.setGlobalOrderId(orderId);
        }
        globalOrderItemService.saveBatch(preparedOrder.getOrderItemList());

        return buildCreateOrderVO(preparedOrder);
    }

    public PreparedOrderDTO buildPreparedOrder(CreateOrderDTO createOrderDTO) {
        AssertUtil.assertNotNull(createOrderDTO.getOrgList(), "商户列表不能为空");
        AssertUtil.assertNotNull(createOrderDTO.getPayType(), "支付方式不能为空");
        AssertUtil.assertNotNull(createOrderDTO.getDeliveryType(), "发货方式不能为空");
        AssertUtil.assertTrue(StrUtil.isNotBlank(createOrderDTO.getReceiveAddressInfo())
            && JSONUtil.isTypeJSON(createOrderDTO.getReceiveAddressInfo()), "收货地址格式非法");

        // 入参已经根据商户分组，但还是要提取出所有的商品ID，先统一进行状态检查
        List<CreateOrderDTO.Goods> goodsList =
            createOrderDTO.getOrgList().stream().flatMap(org -> org.getGoodsList().stream()).toList();
        List<Long> goodsIdList = goodsList.stream().map(CreateOrderDTO.Goods::getLiveGoodsId).distinct().toList();
        AssertUtil.assertFalse(CollUtil.isEmpty(goodsIdList), "商品列表不能为空");

        // 查询出商品列表，便于进行各状态的统一检查
        LiveGoodsQueryReq liveGoodsQueryReq = new LiveGoodsQueryReq();
        liveGoodsQueryReq.setLiveGoodsIdList(goodsIdList);
        List<LiveGoodsDTO> liveGoodsList =
            liveGoodsMapper.getLiveGoodsList(Page.of(1, -1), liveGoodsQueryReq).getRecords();
        AssertUtil.assertFalse(CollUtil.isEmpty(liveGoodsList), "未查询到有效的商品列表");

        // 检查订单
        orderChecker.checkOrderStatus(liveGoodsList);

        // 检查商品状态
        orderChecker.checkGoodsListStatus(liveGoodsList);

        // 调用通用方法，传入createOrderDTO
        OrderBuilderContext context = new OrderBuilderContext();
        context.setIfUseFenbei(createOrderDTO.getIfUseFenbei());
        context.setBuyerUser(userInfoService.current());
        context.setReceiveAddressInfo(createOrderDTO.getReceiveAddressInfo());
        context.setReceiveAddressId(createOrderDTO.getReceiveAddressId());
        context.setPayType(createOrderDTO.getPayType());
        return orderEntityBuilder.buildOrderResult(context);
    }

    private CreateOrderVO buildCreateOrderVO(PreparedOrderDTO prepareOrderDTO) {
        return wrapPreparedOrderInfo(prepareOrderDTO);
    }

    /**
     * 准备下单信息，前端需要传按商户分组后的商品列表
     *
     * @param createOrderDTO 订单
     * @return 返回订单详情
     */
    @Override
    public CreateOrderVO prepareOrderInfo(CreateOrderDTO createOrderDTO) {
        PreparedOrderDTO preparedOrder = buildPreparedOrder(createOrderDTO);
        return wrapPreparedOrderInfo(preparedOrder);
    }

    /**
     * 对准备好的订单信息进行再包装，根据商户进行分组
     *
     * @param preparedOrder 订单准备信息
     * @return 根据商户分组后的订单商品列表
     */
    @Deprecated
    private CreateOrderVO wrapPreparedOrderInfo(PreparedOrderDTO preparedOrder) {
        CreateOrderVO vo = new CreateOrderVO();
        // 提取出商户ID列表
        Set<Long> orgIds =
            preparedOrder.getOrderSubList().stream().map(GlobalOrderSub::getSellerOrgId).collect(Collectors.toSet());
        // 先查询出商户信息列表，放到内存里方便后面处理
        List<GlobalOrganization> organizationList =
            globalOrganizationMapper.selectList(Wrappers.lambdaQuery(GlobalOrganization.class)
                .select(GlobalOrganization::getId, GlobalOrganization::getName, GlobalOrganization::getLogoUrl)
                .in(GlobalOrganization::getId, orgIds));
        // 同样查询出商户直播中的信息
        List<LiveRoom> liveRoomList = liveRoomMapper.selectList(Wrappers.lambdaQuery(LiveRoom.class)
            .select(LiveRoom::getOrgId, LiveRoom::getId, LiveRoom::getStreamStatus).in(LiveRoom::getOrgId, orgIds)
            // 只要直播中
            .eq(LiveRoom::getStreamStatus, LiveRoomStreamStatusEnum.ON));

        // 根据商户对订单商品进行分组
        List<CreateOrderVO.Org> orderOrgList = organizationList.stream().map(x -> {
            CreateOrderVO.Org org = new CreateOrderVO.Org();
            org.setOrgId(x.getId());
            org.setLogoUrl(x.getLogoUrl());
            org.setName(x.getName());

            // 直播信息，一个商户只可能同时有一场直播
            liveRoomList.stream().filter(liveRoom -> liveRoom.getOrgId().equals(x.getId())).findFirst()
                .ifPresent(liveRoom -> {
                    org.setLiveRoomId(liveRoom.getId());
                    org.setIfLived(true);
                });

            // 商户订单合并的金额
            preparedOrder.getOrderSubList().stream().filter(orderSub -> orderSub.getSellerOrgId().equals(x.getId()))
                .findFirst().ifPresent(orderSub -> {
                    org.setTotalGoodsPrice(orderSub.getTotalGoodsPrice());
                    org.setTotalNeedPayAmount(orderSub.getNeedPayAmount());
                    org.setTotalServiceAmount(orderSub.getServiceAmount());
                    org.setTotalBuyerServiceAmount(orderSub.getBuyerServiceAmount());
                    org.setTotalChannelAmount(orderSub.getChannelAmount());
                    org.setRemark(orderSub.getOrderRemark());
                });

            // 商品明细
            org.setGoodsList(new ArrayList<>());
            preparedOrder.getOrderItemList().stream().filter(orderItem -> orderItem.getSellerOrgId().equals(x.getId()))
                .forEach(orderItem -> {
                    // 商品详情
                    preparedOrder.getLiveGoodsList().stream()
                        .filter(liveGoods -> liveGoods.getId().equals(orderItem.getBizGoodsId())).findFirst()
                        .ifPresent(liveGoods -> {
                            CreateOrderVO.Goods item = new CreateOrderVO.Goods();
                            // 商品基础信息
                            BeanUtil.copyProperties(liveGoods, item);
                            // 补全金额等相关数据
                            item.setId(orderItem.getBizGoodsId());
                            item.setLiveGoodsId(orderItem.getBizGoodsId());
                            item.setGoodsName(liveGoods.getGlobalGoodsName());
                            item.setNeedPayAmount(orderItem.getNeedPayAmount());
                            item.setServiceAmount(orderItem.getServiceAmount());
                            item.setBuyerServiceAmount(orderItem.getBuyerServiceAmount());
                            item.setBuyerServiceRate(orderItem.getBuyerServiceRate());
                            item.setChannelAmount(orderItem.getChannelAmount());
                            org.getGoodsList().add(item);
                        });
                });

            return org;
        }).toList();
        vo.setOrgList(orderOrgList);

        // 总订单信息，金额等
        GlobalOrder globalOrder = preparedOrder.getGlobalOrder();
        vo.setTotalGoodsNum(globalOrder.getGoodsNum());
        vo.setTotalGoodsPrice(globalOrder.getTotalGoodsPrice());
        vo.setTotalNeedPayAmount(globalOrder.getNeedPayAmount());
        vo.setTotalServiceAmount(
            orderOrgList.stream().reduce(BigDecimal.ZERO, (a, b) -> a.add(b.getTotalServiceAmount()), BigDecimal::add));
        vo.setTotalBuyerServiceAmount(orderOrgList.stream().reduce(BigDecimal.ZERO,
            (a, b) -> a.add(b.getTotalBuyerServiceAmount()), BigDecimal::add));
        vo.setTotalChannelAmount(
            orderOrgList.stream().reduce(BigDecimal.ZERO, (a, b) -> a.add(b.getTotalChannelAmount()), BigDecimal::add));
        // 备注等其他信息
        vo.setRemark(globalOrder.getOrderRemark());
        vo.setLastPayAt(globalOrder.getLastPayAt());
        vo.setGlobalOrderId(globalOrder.getId());
        vo.setGlobalOrderNo(globalOrder.getOrderNo());
        // 分贝抵扣信息
        vo.setCanDeductionFenbei(preparedOrder.getOnlineResult().getCanDeductionFenbei());
        vo.setDeductionFenbeiCount(preparedOrder.getOnlineResult().getTotalFenbeiDeductionCount());
        vo.setDeductionFenbeiAmount(preparedOrder.getOnlineResult().getTotalFenbeiDeductionAmount());
        vo.setOverDeductionFenbeiAmount(preparedOrder.getOnlineResult().getOverDeductionFenbeiAmount());

        if (preparedOrder.getCreateOrderParam() != null) {
            vo.setDeliveryType(preparedOrder.getCreateOrderParam().getDeliveryType());
            vo.setPayType(preparedOrder.getCreateOrderParam().getPayType());
            vo.setReceiveAddressId(preparedOrder.getCreateOrderParam().getReceiveAddressId());
            vo.setReceiveAddressInfo(preparedOrder.getCreateOrderParam().getReceiveAddressInfo());
        }

        return vo;
    }
}
