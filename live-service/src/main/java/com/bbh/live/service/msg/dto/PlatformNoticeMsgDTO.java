package com.bbh.live.service.msg.dto;

import com.bbh.live.core.msg.MsgType;
import com.bbh.live.service.msg.dto.base.BaseMsg;
import com.bbh.live.service.msg.dto.base.IMsg;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 平台公告
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PlatformNoticeMsgDTO extends BaseMsg implements IMsg {

    private Long liveRoomId;

    private String content;

    /**
     * 每个消息对应一个类型
     *
     * @return {@link MsgType}
     */
    @Override
    public String type() {
        return MsgType.PLATFORM_NOTICE;
    }
}
