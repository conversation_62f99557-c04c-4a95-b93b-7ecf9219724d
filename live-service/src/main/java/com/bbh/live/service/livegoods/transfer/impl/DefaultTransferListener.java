package com.bbh.live.service.livegoods.transfer.impl;

import com.bbh.live.dao.dto.livegoods.LiveGoodsDTO;
import com.bbh.live.dao.service.LiveGoodsSubscribeService;
import com.bbh.live.service.livegoods.cache.LiveGoodsCacheManager;
import com.bbh.live.service.livegoods.transfer.LiveGoodsTransferManager;
import com.bbh.live.service.msg.MsgService;
import com.bbh.model.LiveGoodsSubscribe;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> dsy
 * @Date: 2024/9/10
 * @Description:
 */
@Component
@AllArgsConstructor
public class DefaultTransferListener implements LiveGoodsTransferManager.Listener {

    private final MsgService msgService;
    private final LiveGoodsCacheManager liveGoodsCacheManager;
    private final LiveGoodsSubscribeService liveGoodsSubscribeService;

    @Override
    public void waitPutAwayTransition(LiveGoodsDTO goods) {
        // 检查商品有无订阅，有订阅再去发
        if (!liveGoodsSubscribeService.hasGoodsSubscribe(goods.getLiveGoodsId())) {
            // 没有商品订阅数据，直接返回
            return;
        }

        // 通知商品的订阅者
        msgService.goodsSubscribe(goods.getLiveRoomId(), goods.getId());
    }

    /**
     *  商品上架状态结束
     * @param goods
     */
    @Override
    public void putAwayTransitionEnd(LiveGoodsDTO goods) {
        // 检查商品有无订阅，有订阅再去发
        if (!liveGoodsSubscribeService.hasGoodsSubscribe(goods.getLiveGoodsId())) {
            // 没有商品订阅数据，直接返回
            return;
        }
        // 商品传送成交/议价成交/撤回 关闭预约卡片消息
        msgService.goodsSubscribeClose(goods.getLiveRoomId(), goods.getId());
    }

    @Override
    public void auctionTransitionEnd(LiveGoodsDTO goods) {
        // 清空竞拍商品缓存
        liveGoodsCacheManager.getLiveGoodsAuctionCache().clearAll(goods.getLiveRoomId());
        // 检查商品有无订阅，有订阅再去发
        if (!liveGoodsSubscribeService.hasGoodsSubscribe(goods.getLiveGoodsId())) {
            // 没有商品订阅数据，直接返回
            return;
        }
        // 商品成交/流拍/撤回 关闭预约卡片消息
        msgService.goodsSubscribeClose(goods.getLiveRoomId(), goods.getId());
    }

    @Override
    public void reShelveTransition(LiveGoodsDTO liveGoods) {
        // 重置商品预约记录处理状态为未处理，下次上架时继续提醒
        liveGoodsSubscribeService.lambdaUpdate()
                .eq(LiveGoodsSubscribe::getLiveGoodsId, liveGoods.getId())
                .eq(LiveGoodsSubscribe::getLiveRoomId, liveGoods.getLiveRoomId())
                .set(LiveGoodsSubscribe::getIfHandled, 0)
                .update();
    }
}
