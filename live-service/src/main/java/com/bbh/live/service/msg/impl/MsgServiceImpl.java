package com.bbh.live.service.msg.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.bbh.enums.LiveOrgFilterModeEnum;
import com.bbh.live.config.LiveServiceProperties;
import com.bbh.live.constant.RedisKey;
import com.bbh.live.controller.req.LiveGoodsQueryReq;
import com.bbh.live.core.msg.ImProperties;
import com.bbh.live.core.msg.ImService;
import com.bbh.live.core.msg.MsgDTO;
import com.bbh.live.dao.dto.BargainGoodsDTO;
import com.bbh.live.dao.dto.livegoods.LiveGoodsDTO;
import com.bbh.live.dao.mapper.*;
import com.bbh.live.dao.service.*;
import com.bbh.live.enums.*;
import com.bbh.live.service.buyer.vip.BuyerVipService;
import com.bbh.live.service.buyer.vip.vo.BuyerVipMsgTransferVO;
import com.bbh.live.service.livegoods.cache.info.LiveGoodsAuctionBidInfo;
import com.bbh.live.service.msg.*;
import com.bbh.live.service.msg.dto.*;
import com.bbh.live.service.msg.dto.base.*;
import com.bbh.live.service.msg.push.AppMessagePushService;
import com.bbh.live.service.msg.push.LivePushTypeEnum;
import com.bbh.live.service.msg.support.LiveRoomLimitProcessor;
import com.bbh.live.service.room.LiveRoomBizService;
import com.bbh.live.service.user.permission.PermissionCheckServiceFactory;
import com.bbh.live.thread.ThreadPoolManager;
import com.bbh.model.*;
import com.bbh.util.AssertUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 统一处理消息发送
 * <AUTHOR>
 */
@AllArgsConstructor
@Service
@Slf4j
public class MsgServiceImpl implements MsgService, RedisKey {

    private final LiveRoomMapper liveRoomMapper;
    private final LiveRoomService liveRoomService;
    private final ImService imService;
    private final MsgCacheService msgCacheService;
    private final LiveGoodsService liveGoodsService;
    private final GlobalOrgSeatService globalOrgSeatService;
    private final LiveGoodsTransferMapper liveGoodsTransferMapper;
    private final VipMemeClassInfoMapper vipMemeClassInfoMapper;
    private final LiveServiceProperties liveServiceProperties;
    private final MsgCacheBizService msgCacheBizService;
    private final StringRedisTemplate stringRedisTemplate;
    private final BuyerVipService buyerVipService;
    private final LiveRoomDirectorService liveRoomDirectorService;
    private final MsgLogService msgLogService;
    private final AppMessagePushService appMessagePushService;
    private final IGlobalOrganizationService globalOrganizationService;
    private final GlobalOrderItemService globalOrderItemService;
    private final LiveRoomLimitProcessor liveRoomLimitProcessor;
    private final GlobalBuyerRightMapper globalBuyerRightMapper;
    private final GlobalBuyerRightRecordSeatBindMapper globalBuyerRightRecordSeatBindMapper;

    /**
     * 商品预约消息，等价于商品上架消息 <br>
     * 给订阅商品的用户发消息 <br>
     * 商品上架时进行发送 <br>
     * 只需要发一条消息，内容是商品信息<br>
     * 因为直播间详情中返回了该直播间内该用户订阅的所有商品 {@link LiveRoomBizService#getWatcherRoomDetail} <br>
     * 前端会判断这条消息里的商品id是否在订阅商品的缓存中 <br>
     */
    @Override
    public void goodsSubscribe(Long liveRoomId, @NotNull Long liveGoodsId) {
        asyncExec(() -> {
            // 组装消息，一个商品只要发一次
            GoodsSubscribeMsgDTO dto = new GoodsSubscribeMsgDTO()
                    // 点击卡片，通过直播间id跳转对应直播间
                    .setLiveRoomId(liveRoomId)
                    .setGoods(retrieveGoodsInfo(liveGoodsId));
            dto.setSourceType(SourceType.NOTICE.getCode());
            // 发送消息
            sendMsg(dto, new MsgContext());
        });
    }

    @Override
    public void goodsSubscribeClose(Long liveRoomId, Long liveGoodsId) {
        asyncExec(() -> {
            GoodsSubscribeCloseMsgDTO dto = new GoodsSubscribeCloseMsgDTO();
            dto.setLiveRoomId(liveRoomId);
            dto.setGoods(retrieveGoodsInfo(liveGoodsId));
            dto.setSourceType(SourceType.NOTICE.getCode());
            sendMsg(dto, new MsgContext().setLiveRoomId(liveRoomId));
        });
    }

    @Override
    public void goodsAuctionSuccess(@NotNull Long liveRoomId, Long liveGoodsId, Long buyerSeatId, BigDecimal belongPrice) {
        syncExec(() -> {
            BaseGoods goods = retrieveGoodsInfo(liveGoodsId);
            GoodsAuctionSuccessDTO dto = new GoodsAuctionSuccessDTO();
            dto.setLiveRoomId(liveRoomId);
            dto.setUser(retrieveSeatInfo(buyerSeatId));
            dto.setGoods(goods);
            dto.setBelongPrice(belongPrice);
            dto.setSourceType(SourceType.NOTICE.getCode());
            dto.setTradeType(goods.getTradeType());
            if(ifLiveRoomTestBroadcast(liveRoomId)){
                dto.setIfTestBroadcast(true);
                dto.setTestBroadcastSeatIdList(liveServiceProperties.getLiveRoomTestSeatIdList());
            } else {
                dto.setIfTestBroadcast(false);
            }
            sendMsg(dto, new MsgContext().setLiveRoomId(liveRoomId));
        });
    }

    /**
     * 传送消息
     *
     * @param liveRoomId 直播间id
     * @param buyerSeatId 买手席位
     * @param liveGoodsTransferId 传送内容ID
     */
    @Override
    public void goodsTransfer(@NotNull Long liveRoomId, Long buyerSeatId, Long liveGoodsTransferId) {
        asyncExec(() -> {
            LiveGoodsTransfer liveGoodsTransfer = liveGoodsTransferMapper.selectById(liveGoodsTransferId);
            if (liveGoodsTransfer == null) {
                log.info("[商品传送消息通知]没有找到[transfer_id={}]的传送内容", liveGoodsTransferId);
                return;
            }

            BaseGoods goods = retrieveGoodsInfo(liveGoodsTransfer.getLiveGoodsId());

            GoodsTransferMsgDTO dto = new GoodsTransferMsgDTO()
                    .setLiveRoomId(liveRoomId)
                    .setSellerOrgId(liveGoodsTransfer.getOrgId())
                    .setGoods(goods)
                    .setUser(retrieveSeatInfo(buyerSeatId))
                    .setTransferPrice(liveGoodsTransfer.getTransferPrice())
                    .setRemainTime(liveServiceProperties.getMessageCountdownTime())
                    .setExpiredAt(DateUtil.offset(DateUtil.date(), DateField.SECOND, liveServiceProperties.getMessageCountdownTime()));
            dto.setSourceType(SourceType.NOTICE.getCode());
            dto.setTradeType(goods.getTradeType());

            // 发消息
            sendMsg(dto, new MsgContext().setLiveRoomId(liveRoomId).setSenderSeatId(liveGoodsTransfer.getCreateSeatId()));
        });
    }

    /**
     * 取消传送/传送关闭
     */
    @Override
    public void goodsTransferClose(@NotNull Long liveRoomId, Long buyerSeatId, Long liveGoodsId) {
        asyncExec(() -> {
            BaseGoods goods = retrieveGoodsInfo(liveGoodsId);
            GoodsTransferCloseMsgDTO dto = new GoodsTransferCloseMsgDTO()
                    .setLiveRoomId(liveRoomId)
                    .setGoods(goods)
                    .setUser(retrieveSeatInfo(buyerSeatId));
            dto.setTradeType(goods.getTradeType());
            dto.setSourceType(SourceType.NOTICE.getCode());

            // 发消息
            sendMsg(dto, new MsgContext().setLiveRoomId(liveRoomId).setSenderSeatId(buyerSeatId));
            //清理缓存
            msgCacheBizService.removeGoodsTransferMsg(liveRoomId, buyerSeatId, liveGoodsId);
        });
    }

    /**
     * 传送消息-已同意/已成交
     *
     * @param liveRoomId    直播间ID
     * @param liveGoodsId   商品ID
     * @param tradeSeatId   成交人席位ID
     */
    @Override
    public void goodsTransferAgreed(@NotNull Long liveRoomId, Long liveGoodsId, Long tradeSeatId) {
        asyncExec(() -> {
            BaseGoods goods = retrieveGoodsInfo(liveGoodsId);
            // 商品传送成交消息
            GoodsTransferAgreedMsgDTO dto = new GoodsTransferAgreedMsgDTO();
            dto.setLiveRoomId(liveRoomId);
            dto.setGoods(goods)
                    .setUser(retrieveSeatInfo(tradeSeatId))
                    .setSourceType(SourceType.BUYER.getCode());
            dto.setTradeType(goods.getTradeType());
            sendMsg(dto, new MsgContext().setLiveRoomId(liveRoomId));
        });
    }

    /**
     * 商品开启竞拍
     *
     * @param liveRoomId    直播间ID
     * @param liveGoodsId   商品ID
     */
    @Override
    public void goodsAuctionStart(@NotNull Long liveRoomId, @NotNull Long liveGoodsId, Date auctionEndAt) {
        asyncExec(() -> {
            BaseGoods goods = retrieveGoodsInfo(liveGoodsId);

            GoodsAuctionStartMsgDTO dto = new GoodsAuctionStartMsgDTO();
            dto.setLiveRoomId(liveRoomId);
            dto.setGoods(goods);
            // 来源
            long betweenMs = DateUtil.between(DateUtil.date(), auctionEndAt, DateUnit.MS);
            // 剩余时间s
            dto.setRemainTime(NumberUtil.round((double) betweenMs / 1000, 0, RoundingMode.UP).longValue());
            dto.setRemainTimeMs(betweenMs);
            dto.setSourceType(SourceType.NOTICE.getCode());
            dto.setTradeType(goods.getTradeType());

            sendMsg(dto, new MsgContext().setLiveRoomId(liveRoomId));
        });
    }

    /**
     * 商品流拍
     *
     * @param liveRoomId    直播间ID
     * @param liveGoodsId   商品ID
     */
    @Override
    public void goodsAbortedAuction(@NotNull Long liveRoomId, Long liveGoodsId) {
        asyncExec(() -> {
            BaseGoods goods = retrieveGoodsInfo(liveGoodsId);

            // 商品流拍消息
            GoodsAuctionAbortedMsgDTO dto = new GoodsAuctionAbortedMsgDTO();
            dto.setLiveRoomId(liveRoomId);
            dto.setGoods(goods);
            dto.setSourceType(SourceType.NOTICE.getCode());
            dto.setTradeType(goods.getTradeType());

            sendMsg(dto, new MsgContext().setLiveRoomId(liveRoomId));
        });
    }

    /**
     * 商品上架讲解
     *
     * @param liveRoomId    直播间ID
     * @param liveGoodsId   商品ID
     */
    @Override
    @SuppressWarnings("all")
    public void goodsPutAway(@NotNull Long liveRoomId, Long liveGoodsId) {
        asyncExec(() -> {
            // 商品信息
            BaseGoods goods = retrieveGoodsInfo(liveGoodsId);

            // 组装消息
            GoodsPutawayMsgDTO dto = new GoodsPutawayMsgDTO();
            dto.setLiveRoomId(liveRoomId);
            dto.setGoods(goods);
            dto.setSourceType(SourceType.NOTICE.getCode());

            // 处理试播逻辑
            if (ifLiveRoomTestBroadcast(liveRoomId)) {
                // 查询出直播间的主播
                LiveRoom liveRoom = liveRoomMapper.selectOne(Wrappers.lambdaQuery(LiveRoom.class)
                        .select(LiveRoom::getAnchorSeatId, LiveRoom::getAnchorUserId, LiveRoom::getId)
                        .eq(LiveRoom::getId, liveRoomId)
                );
                Long anchorSeatId = liveRoom.getAnchorSeatId();

                // 还有导播
                List<LiveRoomDirector> roomDirectorList = liveRoomDirectorService.listByRoomIds(List.of(liveRoomId));
                List<Long> directorSeatIdList = roomDirectorList.stream().map(LiveRoomDirector::getDirectorSeatId).collect(Collectors.toList());

                // 本来配置的客服id
                List<Long> liveRoomTestSeatIdList = Optional.ofNullable(liveServiceProperties.getLiveRoomTestSeatIdList())
                        .orElse(Collections.emptyList());

                // 合到一块
                List<Long> seatIdList = Stream.concat(directorSeatIdList.stream(), liveRoomTestSeatIdList.stream()).collect(Collectors.toList());

                dto.setIfTestBroadcast(true);
                dto.setTestBroadcastSeatIdList(seatIdList);
            } else {
                dto.setIfTestBroadcast(false);
            }

            // 交易类型
            dto.setTradeType(goods.getTradeType());

            // 先发一条单个聊天室的
            sendMsg(dto, new MsgContext().setLiveRoomId(liveRoomId).setTargetType(MsgContext.TargetType.SINGLE));

        });
    }

    /**
     * 商品撤回
     *
     * @param liveRoomId  直播间ID
     * @param liveGoodsId 商品ID
     */
    @Override
    public void goodsRevoke(@NotNull Long liveRoomId, Long liveGoodsId) {
        asyncExec(() -> {
            BaseGoods goods = retrieveGoodsInfo(liveGoodsId);

            // 商品撤回消息
            GoodsRevokeMsgDTO dto = new GoodsRevokeMsgDTO();
            dto.setLiveRoomId(liveRoomId);
            dto.setGoods(goods);
            dto.setSourceType(SourceType.NOTICE.getCode());
            dto.setTradeType(goods.getTradeType());

            sendMsg(dto, new MsgContext().setLiveRoomId(liveRoomId));
        });
    }

    /**
     * 用户发起议价
     *'
     * @param liveRoomId
     * @param liveGoodsId
     * @param buyerSeatId
     */
    @Override
    public void userBargain(@NotNull Long liveRoomId, Long liveGoodsId, Long buyerSeatId, BigDecimal bargainPrice) {
        asyncExec(() -> {
            BaseGoods goods = retrieveGoodsInfo(liveGoodsId);
            //用户发起议价消息
            UserBargainMsgDTO dto = new UserBargainMsgDTO();
            dto.setLiveRoomId(liveRoomId)
                    .setGoods(goods)
                    .setUser(retrieveSeatInfo(buyerSeatId))
                    .setBargainPrice(bargainPrice)
                    .setSourceType(SourceType.BUYER.getCode());
            dto.setTradeType(goods.getTradeType());

            sendMsg(dto, new MsgContext().setLiveRoomId(liveRoomId).setSenderSeatId(buyerSeatId));
        });
    }

    /**
     * 用户求讲解消息
     * @param liveRoomId
     * @param liveGoodsId
     * @param buyerSeatId
     */
    @Override
    public void userAskForExplanation(@NotNull Long liveRoomId, Long liveGoodsId, Long buyerSeatId) {
        asyncExec(() -> {
            // 求讲解功能已移除，不再进行实现
        });
    }

    /**
     * 用户议价成功，导播通知用户
     *
     * @param bargainGoodsDTO
     */
    @Override
    public void userBargainAgreed(BargainGoodsDTO bargainGoodsDTO) {
        asyncExec(() -> {
            // 用户议价成功，导播通知用户
            UserBargainAgreeMsgDTO dto = new UserBargainAgreeMsgDTO();
            BaseSeat baseSeat = retrieveSeatInfo(bargainGoodsDTO.getBuyerSeatId());
            BaseGoods baseGoods = retrieveGoodsInfo(bargainGoodsDTO.getLiveGoodsId());
            dto.setLiveRoomId(bargainGoodsDTO.getLiveRoomId());
            dto.setGoods(baseGoods)
                    .setUser(baseSeat)
                    .setSourceType(SourceType.LIVE_DIRECTOR.getCode());
            dto.setTradeType(baseGoods.getTradeType());
            sendMsg(dto, new MsgContext().setLiveRoomId(bargainGoodsDTO.getLiveRoomId()));
            // APP 消息
            PushMsgDTO pushMsg = new PushMsgDTO()
                    .setOrgId(baseSeat.getOrgId()).setSeatId(List.of(baseSeat.getSeatId()))
                    .setUserId(baseSeat.getUserId()).setPushType(LivePushTypeEnum.BARGAIN_SUCCESS)
                    .setGoodsId(baseGoods.getGlobalGoodsId()).setContentArgs(new Object[]{baseGoods.getGlobalGoodsName(), bargainGoodsDTO.getBidPrice()})
                    .setLiveRoomId(bargainGoodsDTO.getLiveRoomId())
                    .setParams(LivePushTypeEnum.BARGAIN_SUCCESS.getParams())
                    .setGoodsImage(baseGoods.getImgUrlList().getFirst());
            appMessagePushService.pushMessage(pushMsg);
        });
    }

    @Override
    public void userBargainReject(BargainGoodsDTO bargainGoodsDTO) {
        asyncExec(() -> {
            // 导播拒绝用户议价 通知用户
            UserBargainRejectMsgDTO dto = new UserBargainRejectMsgDTO();
            BaseSeat baseSeat = retrieveSeatInfo(bargainGoodsDTO.getBuyerSeatId());
            BaseGoods baseGoods = retrieveGoodsInfo(bargainGoodsDTO.getLiveGoodsId());
            dto.setLiveRoomId(bargainGoodsDTO.getLiveRoomId())
                    .setGoods(baseGoods)
                    .setUser(baseSeat)
                    .setSourceType(SourceType.LIVE_DIRECTOR.getCode());
            dto.setTradeType(baseGoods.getTradeType());
            // 直播间消息
            sendMsg(dto, new MsgContext().setLiveRoomId(bargainGoodsDTO.getLiveRoomId()));
            // APP 消息
            PushMsgDTO pushMsg = new PushMsgDTO()
                    .setOrgId(baseSeat.getOrgId()).setSeatId(List.of(baseSeat.getSeatId()))
                    .setUserId(baseSeat.getUserId()).setPushType(LivePushTypeEnum.BARGAIN_FAIL)
                    .setGoodsId(baseGoods.getGlobalGoodsId())
                    .setLiveRoomId(bargainGoodsDTO.getLiveRoomId())
                    .setContentArgs(new Object[]{baseGoods.getGlobalGoodsName(), bargainGoodsDTO.getBidPrice()})
                    .setGoodsImage(baseGoods.getImgUrlList().getFirst());
            appMessagePushService.pushMessage(pushMsg);
        });
    }

    @Override
    public void userBidSuccess(@NotNull BargainGoodsDTO bargainGoodsDTO, Date auctionEndAt, List<LiveGoodsAuctionBidInfo> biddenList) {
        BaseGoods goods = retrieveGoodsInfo(bargainGoodsDTO.getLiveGoodsId());

        UserBidSuccessMsgDTO msg = new UserBidSuccessMsgDTO();
        msg.setBidAmount(bargainGoodsDTO.getBidPrice());
        msg.setLiveRoomId(bargainGoodsDTO.getLiveRoomId());
        msg.setUser(retrieveSeatInfo(bargainGoodsDTO.getBuyerSeatId()));
        msg.setGoods(goods);
        long betweenMs = DateUtil.between(DateUtil.date(), auctionEndAt, DateUnit.MS);
        msg.setRemainTime(NumberUtil.round((double) betweenMs / 1000, 0, RoundingMode.UP).longValue());
        msg.setRemainTimeMs(betweenMs);
        msg.setBiddenList(biddenList);
        msg.setSourceType(SourceType.BUYER.getCode());
        msg.setTradeType(goods.getTradeType());
        if(ifLiveRoomTestBroadcast(bargainGoodsDTO.getLiveRoomId())){
            msg.setIfTestBroadcast(true);
            msg.setTestBroadcastSeatIdList(liveServiceProperties.getLiveRoomTestSeatIdList());
        } else {
            msg.setIfTestBroadcast(false);
        }
        sendMsg(msg, new MsgContext().setLiveRoomId(bargainGoodsDTO.getLiveRoomId()).setSenderSeatId(bargainGoodsDTO.getBuyerSeatId()));
    }

    /**
     * 直播结束
     *
     * @param liveRoom 直播间id
     */
    @Override
    public void liveEnd(@NotNull LiveRoom liveRoom, LiveRoomEnhancedStatusEnum status) {
        asyncExec(() -> {
            LiveEndMsgDTO dto = new LiveEndMsgDTO()
                    .setLiveRoomId(liveRoom.getId())
                    .setLiveRoomName(liveRoom.getRoomName())
                    .setRoomStatus(status.getCode())
                    .setStartAt(liveRoom.getStartAt())
                    .setEndAt(liveRoom.getEndAt())
                    .setActualStartAt(liveRoom.getActualStartAt())
                    .setActualEndAt(Objects.requireNonNullElse(liveRoom.getActualEndAt(), new Date()))
                    .setGoodsListDuration(liveRoom.getGoodsListDuration())
                    .setActualGoodsListEndAt(DateUtil.offsetHour(Objects.requireNonNullElse(liveRoom.getActualEndAt(), new Date()), liveRoom.getGoodsListDuration()))
                    .setGoodsListExpiredAt(DateUtil.offsetHour(liveRoom.getEndAt(), liveRoom.getGoodsListDuration()));
            dto.setSourceType(SourceType.NOTICE.getCode());
            sendMsg(dto, new MsgContext().setLiveRoomId(liveRoom.getId()).setSenderUserId(0L));
        });
    }

    /**
     * 开始直播
     *
     * @param liveRoomId 直播间id
     */
    @Override
    public void liveStart(@NotNull Long liveRoomId) {
        asyncExec(() -> {
            LiveRoom liveRoom = liveRoomMapper.selectById(liveRoomId);
            if (liveRoom == null) {
                log.info("[开始直播消息]没有找到[liveRoomId={}]的直播间", liveRoomId);
                return;
            }

            LiveStartMsgDTO dto = new LiveStartMsgDTO()
                    .setLiveRoomId(liveRoomId)
                    .setLiveRoomName(liveRoom.getRoomName());
            dto.setSourceType(SourceType.NOTICE.getCode());
            sendMsg(dto, new MsgContext().setLiveRoomId(liveRoomId).setSenderUserId(0L));
        });
    }

    /**
     * 直播间公告变更通知
     *
     * @param liveRoomId 直播间ID
     * @param content     公告内容
     */
    @Override
    public void roomNoticeChanged(@NotNull Long liveRoomId, @NotNull String content) {
        asyncExec(() -> {
            RoomNoticeChangedMsgDTO dto = new RoomNoticeChangedMsgDTO()
                    .setLiveRoomId(liveRoomId)
                    .setContent(content);
            dto.setSourceType(SourceType.SYSTEM_NOTICE.getCode());
            sendMsg(dto, new MsgContext().setLiveRoomId(liveRoomId).setSenderUserId(0L));
        });
    }

    /**
     * 用户进入直播间
     *
     * @param liveRoomId 直播间ID
     * @param userId     用户ID
     * @param seatId     席位ID
     */
    @Override
    public void userEnterRoom(@NotNull Long liveRoomId, Long userId, @NotNull Long seatId) {
        // 允许配置是否发送userEnter消息
        if (Boolean.FALSE.equals(liveServiceProperties.getIfSendUserEnterMsg())) {
            return;
        }
        asyncExec(() -> {
            // 判断是否导播，导播不发
            boolean isDirector = liveRoomDirectorService.isLiveRoomDirector(liveRoomId, seatId);
            if (isDirector) {
                log.info("[用户进入直播间]导播不发[liveRoomId={}]的直播间", liveRoomId);
                return;
            }
            // 判断是否为主播，主播也不发
            LiveRoom liveRoom = liveRoomMapper.selectById(liveRoomId);
            if (liveRoom == null) {
                log.info("[用户进入直播间]没有找到[liveRoomId={}]的直播间", liveRoomId);
                return;
            }
            if (Objects.equals(liveRoom.getAnchorSeatId(), seatId)) {
                log.info("[用户进入直播间]主播不发[liveRoomId={}]的直播间", liveRoomId);
                return;
            }
            // 试播中不发
            if (liveRoomService.ifLiveRoomTestBroadcast(liveRoom)) {
                log.info("[用户进入直播间]试播中不发[liveRoomId={}]的直播间", liveRoomId);
                return;
            }

            // 这里要做缓存，单个用户在同一个直播间，N秒内仅发一次
            String intervalKey = buildKey(LiveRoomCacheKeys.USER_ENTER_MSG_EXPIRED.getKey(), liveRoomId.toString(), seatId.toString());
            if (Boolean.TRUE.equals(stringRedisTemplate.hasKey(intervalKey))) {
                log.info("[用户进入直播间]用户在同一个直播间，N秒内仅发一次[liveRoomId={}]的直播间", liveRoomId);
                return;
            }
            UserEnterRoomDTO dto = new UserEnterRoomDTO();
            dto.setLiveRoomId(liveRoomId);
            dto.setUser(retrieveSeatInfo(seatId));
            dto.setSourceType(SourceType.NOTICE.getCode());
            sendMsg(dto, new MsgContext().setLiveRoomId(liveRoomId).setSenderUserId(0L).setTargetType(MsgContext.TargetType.SINGLE));
            stringRedisTemplate.opsForValue().set(intervalKey, "1", liveServiceProperties.getUserEnterLiveRoomMsgInterval(), TimeUnit.SECONDS);
        });
    }

    /**
     * 普通文本消息
     *
     * @param liveRoomId   直播间ID
     * @param content      文本内容
     * @param senderSeatId 发送人席位
     * @param sourceType   消息来源
     */
    @Override
    public void text(@NotNull Long liveRoomId, @NotNull String content, @NotNull Long senderSeatId, SourceType sourceType) {
        asyncExec(() -> {
            CommonTextMsgDTO msg = new CommonTextMsgDTO();
            msg.setLiveRoomId(liveRoomId);
            msg.setContent(content);
            msg.setUser(retrieveSeatInfo(senderSeatId));
            msg.setSourceType(sourceType.getCode());
            sendMsg(msg, new MsgContext().setLiveRoomId(liveRoomId).setSenderUserId(msg.getUser().getSeatId()));
        });
    }

    /**
     * 表情包
     *
     * @param liveRoomId   直播间ID
     * @param memeId       表情ID
     * @param senderSeatId 发送人
     * @param sourceType   消息来源
     */
    @Override
    public void meme(@NotNull Long liveRoomId, @NotNull Long memeId, @NotNull Long senderSeatId, SourceType sourceType) {
        asyncExec(() -> {
            VipMemeClassInfo memeClassInfo = vipMemeClassInfoMapper.selectById(memeId);
            if (memeClassInfo == null) {
                log.info("[表情包消息]没有找到[memeId={}]的表情包", memeId);
                return;
            }

            CommonMemeMsgDTO msg = new CommonMemeMsgDTO();
            msg.setLiveRoomId(liveRoomId);
            msg.setMemeId(memeId);
            msg.setMemeName(memeClassInfo.getName());
            msg.setEmojiUrl(memeClassInfo.getEmojiUrl());
            msg.setUser(retrieveSeatInfo(senderSeatId));
            msg.setSourceType(sourceType.getCode());
            sendMsg(msg, new MsgContext().setLiveRoomId(liveRoomId).setSenderSeatId(msg.getUser().getSeatId()));
        });
    }

    /**
     * 发送试播开始消息
     * @param room    直播间id
     */
    @Override
    public void sendTestBroadcastBeginMsg(LiveRoom room) {
        asyncExec(() -> {
            TestBroadcastBeginMsgDTO msg = new TestBroadcastBeginMsgDTO();
            msg.setLiveRoomId(room.getId());
            msg.setLiveRoomName(room.getRoomName());
            msg.setSourceType(SourceType.NOTICE.getCode());
            sendMsg(msg, new MsgContext().setLiveRoomId(room.getId()));
        });
    }

    /**
     *  发送试播结束消息
     * @param room    直播间id
     */
    @Override
    public void sendTestBroadcastEndMsg(LiveRoom room) {
        asyncExec(() -> {
            TestBroadcastEndMsgDTO msg = new TestBroadcastEndMsgDTO();
            msg.setLiveRoomId(room.getId());
            msg.setLiveRoomName(room.getRoomName());
            msg.setSourceType(SourceType.NOTICE.getCode());
            sendMsg(msg, new MsgContext().setLiveRoomId(room.getId()));
        });
    }

    /**
     * 发送平台公告，这个消息实际由后台发送
     *
     * @param liveRoomId 直播间id
     * @param content    内容
     */
    @Override
    public void sendPlatformNotice(Long liveRoomId, String content) {
        asyncExec(() -> {
            PlatformNoticeMsgDTO msg = new PlatformNoticeMsgDTO();
            msg.setLiveRoomId(liveRoomId);
            msg.setContent(content);
            msg.setSourceType(SourceType.SYSTEM_NOTICE.getCode());
            sendMsg(msg, new MsgContext().setLiveRoomId(liveRoomId));
        });
    }

    /**
     * 发送系统公告
     *
     * @param liveRoomId 直播间id
     * @param content    内容
     */
    @Override
    public void sendSystemNotice(Long liveRoomId, String content) {
        asyncExec(() -> {
            SystemNoticeMsgDTO msg = new SystemNoticeMsgDTO();
            msg.setLiveRoomId(liveRoomId);
            msg.setContent(content);
            msg.setSourceType(SourceType.SYSTEM_NOTICE.getCode());
            sendMsg(msg, new MsgContext().setLiveRoomId(liveRoomId));
        });
    }

    /**
     * 更新商品信息
     *
     * @param liveRoomId  直播间id
     * @param liveGoodsId 商品id
     */
    @Override
    public void goodsUpdate(Long liveRoomId, Long liveGoodsId) {
        asyncExec(() -> {
            BaseGoods goods = retrieveGoodsInfo(liveGoodsId);

            GoodsUpdateMsgDTO msg = new GoodsUpdateMsgDTO();
            msg.setLiveRoomId(liveRoomId);
            msg.setGoods(goods);
            msg.setSourceType(SourceType.SYSTEM_NOTICE.getCode());
            msg.setTradeType(goods.getTradeType());

            sendMsg(msg, new MsgContext().setLiveRoomId(liveRoomId));
        });
    }

    /**
     * 新增商品信息
     *
     * @param liveRoomId  直播间id
     * @param liveGoodsList 商品id
     */
    @Override
    public void goodsInsert(Long liveRoomId, List<Long> liveGoodsList) {
        asyncExec(() -> {
            LiveGoodsService liveGoodsService = SpringUtil.getBean(LiveGoodsService.class);
            LiveGoodsQueryReq query = new LiveGoodsQueryReq();
            query.setLiveGoodsIdList(liveGoodsList);
            IPage<LiveGoodsDTO> fullGoodsListPage = liveGoodsService.getLiveGoodsList(query);
            List<LiveGoodsDTO> fullGoodsList = fullGoodsListPage.getRecords();
            if (CollUtil.isEmpty(fullGoodsList)) {
                return;
            }

            GoodsInsertMsgDTO msg = new GoodsInsertMsgDTO();
            msg.setLiveRoomId(liveRoomId);
            msg.setGoodsList(fullGoodsList.stream().map(x -> {
                BaseGoods goods = new BaseGoods();
                goods.setQuality(x.getQuality());
                goods.setLiveGoodsId(x.getLiveGoodsId());
                goods.setGlobalGoodsId(x.getGlobalGoodsId());
                goods.setLiveGoodsCode(x.getLiveGoodsCode());
                goods.setTradeType(x.getTradeType().getCode());
                goods.setIncreasePrice(x.getIncreasePrice());
                goods.setStartPrice(x.getStartPrice());
                goods.setGlobalGoodsName(x.getGlobalGoodsName());
                goods.setDirectorRemark(x.getDirectorRemark());
                goods.setImgUrlList(x.getImgUrlList());
                goods.setGoodsStatus(x.getGoodsStatus().getCode());
                return goods;
            }).toList());
            msg.setSourceType(SourceType.SYSTEM_NOTICE.getCode());
            sendMsg(msg, new MsgContext().setLiveRoomId(liveRoomId));
        });
    }

    /**
     * 删除商品
     *
     * @param liveRoomId  直播间id
     * @param liveGoodsId 商品id
     */
    @Override
    public void goodsDelete(Long liveRoomId, List<Long> liveGoodsId) {
        asyncExec(() -> {
            GoodsDeleteMsgDTO msg = new GoodsDeleteMsgDTO();
            msg.setLiveRoomId(liveRoomId);
            msg.setLiveGoodsIdList(liveGoodsId);
            msg.setSourceType(SourceType.SYSTEM_NOTICE.getCode());
            sendMsg(msg, new MsgContext().setLiveRoomId(liveRoomId));
        });
    }

    /**
     * 商家拉黑用户，让用户直接离开直播间
     *
     * @param liveRoomId 直播间ID
     * @param userId     用户ID
     * @param seatId     席位ID
     */
    @Override
    public void letUserOutNow(Long liveRoomId, Long userId, Long seatId) {
        asyncExec(() -> {
            UserOutNowMsgDTO userOutNowMsgDTO = new UserOutNowMsgDTO();
            userOutNowMsgDTO.setLiveRoomId(liveRoomId);
            userOutNowMsgDTO.setLiveRoomFilterMode(LiveOrgFilterModeEnum.BLACKLIST.getCode());
            userOutNowMsgDTO.setUser(retrieveSeatInfo(seatId));
            userOutNowMsgDTO.setSourceType(SourceType.NOTICE.getCode());
            sendMsg(userOutNowMsgDTO, new MsgContext().setLiveRoomId(liveRoomId));
        });
    }

    /**
     * 开始直播倒计时
     *
     * <AUTHOR>
     */
    @Override
    public void startLiveCountdown(Long liveRoomId, Long remainingTime) {
        asyncExec(() -> {
            LiveCountdownStartMsgDTO msg = new LiveCountdownStartMsgDTO();
            msg.setLiveRoomId(liveRoomId);
            msg.setRemainTime(remainingTime);
            msg.setSourceType(SourceType.NOTICE.getCode());
            sendMsg(msg, new MsgContext().setLiveRoomId(liveRoomId));
        });
    }

    /**
     * 关闭直播倒计时
     *
     * <AUTHOR>
     */
    @Override
    public void closeLiveCountdown(Long liveRoomId) {
        asyncExec(() -> {
            LiveCountdownClosedMsgDTO msg = new LiveCountdownClosedMsgDTO();
            msg.setLiveRoomId(liveRoomId);
            msg.setSourceType(SourceType.NOTICE.getCode());
            sendMsg(msg, new MsgContext().setLiveRoomId(liveRoomId));
        });
    }

    @Override
    public void deliveryOrderGoods(List<GlobalOrderSub> orderSubList) {
        asyncExec(() -> {
            // 商家订单
            for (GlobalOrderSub orderSub : orderSubList){
                // 需要发送的 用户 店铺老板，有发货权限的员工
                List<Long> recevierSeatIdList = PermissionCheckServiceFactory.getPermissionCheckService(PermissionCheckTypeEnum.PERMISSION_CHECK_TYPE_LIVE)
                        .getOrgSeatIdListByPermissionCodeAndOrgId(PermissionCodeEnum.LIVE_SALES_ORDER_DELIVERY, orderSub.getSellerOrgId());
                // 老板的席位 id
                Long masterSeatId = globalOrganizationService.getMasterSeatIdByOrgId(orderSub.getSellerOrgId());
                if(!recevierSeatIdList.contains(masterSeatId)){
                    recevierSeatIdList.addFirst(masterSeatId);
                }
                // 查询订单任意一个商品的图片作为消息封面
                String imgUrl = globalOrderItemService.getGoodsImageByGlobalOrderSubId(orderSub.getId());
                PushMsgDTO pushMsg = new PushMsgDTO()
                        .setPushType(LivePushTypeEnum.DELIVER_ORDER_GOODS)
                        .setOrderId(orderSub.getId())
                        .setOrgId(orderSub.getSellerOrgId())
                        .setSeatId(recevierSeatIdList)
                        .setGoodsImage(imgUrl)
                        .setParams(new HashMap<>(2){{
                            put("id", orderSub.getId());
                        }});
                appMessagePushService.pushMessage(pushMsg);
            }
        });
    }

    /**
     * 线下转账审核失败
     */
    @Override
    public void offlineTransferFailed(Long orderId, Long buyerOrgId, Long buyerSeatId) {
        asyncExec(() -> {
            PushMsgDTO pushMsg = new PushMsgDTO()
                    .setPushType(LivePushTypeEnum.DELIVER_ORDER_GOODS)
                    .setOrderId(orderId)
                    .setOrgId(buyerOrgId)
                    .setSeatId(List.of(buyerSeatId))
                    .setGoodsImage("")
                    .setPushType(LivePushTypeEnum.OFFLINE_TRANSFER_FAILED)
                    .setParams(new HashMap<>(2){{
                        put("global_order_id", orderId);
                        put("buyer_org_id", buyerOrgId);
                        put("buyer_seat_id", buyerSeatId);
                    }});
            appMessagePushService.pushMessage(pushMsg);
        });
    }

    /**
     * 关注了商家
     *
     * @param seatId          谁关注
     * @param beAttendedOrgId 被关注的商家
     */
    @Override
    public void orgAttention(Long seatId, Long liveRoomId, Long beAttendedOrgId) {
        GlobalOrganization organization = globalOrganizationService.getById(beAttendedOrgId);
        AssertUtil.assertNotNull(organization, "被关注的商户不存在");

        OrgAttentionMsgDTO msg = new OrgAttentionMsgDTO();
        msg.setLiveRoomId(liveRoomId);

        msg.setOrgId(organization.getId());
        msg.setOrgName(organization.getName());
        msg.setUser(retrieveSeatInfo(seatId));
        msg.setSourceType(SourceType.NOTICE.getCode());
        sendMsg(msg, new MsgContext().setLiveRoomId(liveRoomId));
    }


    @Override
    public void notifySellerBuyerCancelApproval(Long applyOrderId, Long sellerOrgId, LivePushTypeEnum pushType, Map<String, Object> params) {
        asyncExec(() -> {
            // 需要发送的 用户 店铺老板，有查看销售订单权限的员工
            List<Long> recevierSeatIdList = PermissionCheckServiceFactory.getLivePermissionCheck()
                    .getOrgSeatIdListByPermissionCodeAndOrgId(PermissionCodeEnum.LIVE_SALES_ORDER, sellerOrgId);

            // 老板的席位 id
            Long masterSeatId = globalOrganizationService.getMasterSeatIdByOrgId(sellerOrgId);
            if(!recevierSeatIdList.contains(masterSeatId)){
                recevierSeatIdList.addFirst(masterSeatId);
            }

            if (CollUtil.isEmpty(recevierSeatIdList)) {
                return;
            }

            PushMsgDTO pushMsg = new PushMsgDTO()
                    .setPushType(pushType)
                    .setOrderId(applyOrderId)
                    .setOrgId(sellerOrgId)
                    .setSeatId(recevierSeatIdList)
                    .setGoodsImage("")
                    .setParams(params);
            appMessagePushService.pushMessage(pushMsg);
        });
    }

    /**
     * 取消成交，发送给买家
     * @param applyOrderId
     * @param buyerSeatId
     * @param pushType
     * @param params
     */
    @Override
    public void notifyBuyerCancelApproval(Long applyOrderId, Long buyerOrgId, Long buyerSeatId, LivePushTypeEnum pushType, Map<String, Object> params) {
        asyncExec(() -> {
            PushMsgDTO pushMsg = new PushMsgDTO()
                    .setPushType(pushType)
                    .setOrderId(applyOrderId)
                    .setOrgId(buyerOrgId)
                    .setSeatId(List.of(buyerSeatId))
                    .setGoodsImage("")
                    .setParams(params);
            appMessagePushService.pushMessage(pushMsg);
        });
    }

    /**
     * 检索席位的完整信息，包含昵称、拍号、会员信息等
     * @param seatId    席位ID
     * @return          完整的席位信息
     */
    @Override
    public BaseSeat retrieveSeatInfo(@NotNull Long seatId) {
        BaseSeat seat = new BaseSeat();
        seat.setSeatId(seatId);

        // 获取昵称
        GlobalOrgSeat globalOrgSeat = globalOrgSeatService.lambdaQuery().eq(GlobalOrgSeat::getId, seatId).one();
        if (globalOrgSeat != null) {
            seat.setOrgId(globalOrgSeat.getOrgId());
            seat.setUserId(globalOrgSeat.getUserId());
            seat.setNickName(globalOrgSeat.getShowName());
            seat.setAvatar(globalOrgSeat.getAvatar());
            seat.setAuctionCode(globalOrgSeat.getAuctionCode());
        }

        // 会员
        BuyerVipMsgTransferVO vipInfo = buyerVipService.getBuyerVipMsgTransferVO(seatId);
        if (vipInfo != null) {
            seat.setIsVip(vipInfo.getBuyerVipType() == BuyerVipTypeEnum.VIP);
            seat.setVipLevel(vipInfo.getVipLevel() == null ? -1 : vipInfo.getVipLevel());
            seat.setIsAnnualFeeVip(vipInfo.getIsAnnualFeeVip());
        }

        // 商户信息
        GlobalOrganization organization = globalOrganizationService.getById(seat.getOrgId());
        if (organization != null) {
            seat.setOrgType(organization.getType());
        }
        return seat;
    }

    /**
     * 获取完整的商品信息，包含商品名称、品级、拍号、起拍价等
     * @param liveGoodsId    商品ID
     * @return               完整的商品信息
     */
    @Override
    public BaseGoods retrieveGoodsInfo(@NotNull Long liveGoodsId) {
        // 查询商品信息
        return liveGoodsService.getBaseGoodsInfo(liveGoodsId);
    }

    private BaseGoods retrieveBidSuccessGoodsInfo(@NotNull Long liveGoodsId) {
        var liveGoods = liveGoodsService.lambdaQuery().eq(LiveGoods::getId, liveGoodsId)
                .select(LiveGoods::getId, LiveGoods::getLiveGoodsCode, LiveGoods::getStartPrice, LiveGoods::getIncreasePrice, LiveGoods::getAuctionDuration)
                .one();

        BaseGoods baseGoods = new BaseGoods();
        baseGoods.setLiveGoodsId(liveGoodsId);
        baseGoods.setLiveGoodsCode(liveGoods.getLiveGoodsCode().toString());
        baseGoods.setStartPrice(liveGoods.getStartPrice());
        baseGoods.setIncreasePrice(liveGoods.getIncreasePrice());
        baseGoods.setAuctionDuration(liveGoods.getAuctionDuration());
        return baseGoods;
    }

    /**
     * 异步发送消息，如果当前事务未提交，则等提交之后再发送消息
     * @param runnable    发送消息的任务
     */
    private void asyncExec(Runnable runnable) {
        if(TransactionSynchronizationManager.isSynchronizationActive()){
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
                @Override
                public void afterCommit() {
                    ThreadPoolManager.getRongYunMsgSendExecutor().execute(runnable);
                }
            });
        } else {
            ThreadPoolManager.getRongYunMsgSendExecutor().execute(runnable);
        }
    }

    private void syncExec(Runnable runnable) {
        if(TransactionSynchronizationManager.isSynchronizationActive()){
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
                @Override
                public void afterCommit() {
                    runnable.run();
                }
            });
        } else {
            runnable.run();
        }
    }

    /**
     * 检查并初始化上下文
     * @param ctx 上下文
     */
    private void checkContext(MsgContext ctx) {
        // 补充席位ID
        if (ctx.getSenderSeatId() == null && ctx.getSenderUserId() == 0) {
            ctx.setSenderSeatId(0L);
        }
        if (ctx.getSenderUserId() == null) {
            ctx.setSenderUserId(0L);
        }
        if (ctx.getSenderSeatId() == null) {
            ctx.setSenderSeatId(0L);
        }
    }

    private <T extends IExpiringMsg> void saveExpiring(T t) {
        msgCacheService.saveExpiringMessage(t.expiredAt(), t);
    }

    private <T extends IMultiExpiringMsg> void saveExpiring(T t) {
        msgCacheService.saveExpiringMessage(t.expiredAt(), t);
    }

    private <T extends BaseAuctionLimitMsg> void setAuctionLimit(T t) {
        liveRoomLimitProcessor.fillLimitMsg(t);
    }

    private <T extends IGodViewMsg> void sendGodViewMsg(T t) {
        List<MsgDTO<?>> godsPerspectiveGoodsList = SpringUtil.getBean(LiveRoomMsgService.class).getGodsPerspectiveGoodsList();
        t.setGodViewList(godsPerspectiveGoodsList);

        // 创建新的上下文，并设置相关标记
        MsgContext newContext = new MsgContext()
                .setTargetType(MsgContext.TargetType.GLOBAL)
                .setGodViewProcessed(true);
        MsgDTO<T> msgDTO = buildMsgDTO(t, newContext);
        msgLogService.log(msgDTO);
        imService.sendChatroomTxtMessage(msgDTO);
    }

    /**
     * 发送IM消息
     * @param t 消息拓展内容
     * @param context 上下文
     * @param <T> 拓展内容，要求实现 {@link IMsg}
     */
    public <T extends IMsg> void sendMsg(T t,  MsgContext context) {
        // 检查并初始化上下文
        checkContext(context);

        // 需要记录过期时间的消息
        if (t instanceof IExpiringMsg) {
            saveExpiring((IExpiringMsg) t);
        }
        if (t instanceof IMultiExpiringMsg) {
            saveExpiring((IMultiExpiringMsg) t);
        }
        // 记录时间戳
        if (t instanceof BaseMsg) {
            // 13位时间戳
            ((BaseMsg) t).setTimestamp(System.currentTimeMillis());
        }
        // 黑白名单
        if (t instanceof BaseAuctionLimitMsg) {
            setAuctionLimit((BaseAuctionLimitMsg) t);
        }
        // 上帝视角，默认是只发单个聊天室，不发全局聊天室
        if (t instanceof IGodViewMsg) {
            context.setTargetType(MsgContext.TargetType.SINGLE);
        }

        MsgDTO<T> msgDTO = buildMsgDTO(t, context);
        msgLogService.log(msgDTO);
        imService.sendChatroomTxtMessage(msgDTO);

        // 上帝视角 - 只有未处理过的才需要处理
        if (t instanceof IGodViewMsg) {
            //if(AuthUtil.getUser(null) != null){
                sendGodViewMsg((IGodViewMsg) t);
            //}
        }
    }

    /**
     * 组装融云消息
     * @param t                 消息体
     * @param context           上下文，包含发送人uid、发送人席位id、房间id
     * @return                  融云消息体
     * @param <T>               实现{@link IMsg}的类
     */
    @Override
    public <T extends IMsg>MsgDTO<T> buildMsgDTO(T t, MsgContext context) {
        // 组装消息
        MsgDTO<T> msgDTO = new MsgDTO<>();
        msgDTO.setSenderUserId(context.getSenderUserId());
        msgDTO.setSenderSeatId(context.getSenderSeatId());
        msgDTO.setMsgChannel(ImProperties.LIVE_ROOM_CHAT_ROOM_ID_PREFIX);
        msgDTO.setMsgType(t.type());
        msgDTO.setData(t);

        // 聊天室ID设置
        setChatRoomIds(msgDTO, context);

        // 直播间ID设置
        Optional.ofNullable(context.getLiveRoomId())
                .ifPresent(liveRoomId -> msgDTO.setLiveRoomIds(CollectionUtil.newHashSet(liveRoomId)));

        return msgDTO;
    }

    private void setChatRoomIds(MsgDTO<?> msgDTO, MsgContext context) {
        Set<String> chatRoomIds = new HashSet<>();

        // 全局聊天室ID总是需要添加的情况
        if (context.getLiveRoomId() == null ||
                context.getTargetType() == MsgContext.TargetType.GLOBAL ||
                context.getTargetType() == MsgContext.TargetType.BOTH) {
            chatRoomIds.add(ImProperties.LIVE_ROOM_CHAT_ROOM_FULL_INFO);
        }

        // 添加特定直播间的聊天室ID
        if (context.getLiveRoomId() != null &&
                (context.getTargetType() == MsgContext.TargetType.SINGLE ||
                        context.getTargetType() == MsgContext.TargetType.BOTH)) {
            chatRoomIds.add(ImProperties.LIVE_ROOM_CHAT_ROOM_ID_PREFIX + context.getLiveRoomId());
        }

        msgDTO.setChatRoomIds(chatRoomIds);
    }

    public Boolean ifLiveRoomTestBroadcast(Long liveRoomId) {
        return liveRoomService.ifLiveRoomTestBroadcast(liveRoomId);
    }
}
