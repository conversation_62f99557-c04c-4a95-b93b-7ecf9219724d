package com.bbh.live.service.msg.dto;

import com.bbh.live.core.msg.MsgType;
import com.bbh.live.service.msg.dto.base.BaseSeat;
import com.bbh.live.service.msg.dto.base.IMsg;
import lombok.Data;

/**
 * 用户被禁言
 * <AUTHOR>
 */
@Data
public class UserChatBannedMsgDTO implements IMsg {

    /**
     * 被禁言的用户
     */
    private BaseSeat user;

    /**
     * 每个消息对应一个类型
     *
     * @return {@link MsgType}
     */
    @Override
    public String type() {
        return MsgType.USER_CHAT_BANNED;
    }
}
