package com.bbh.live.service.lottery.strategy;


import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.bbh.enums.FenbeiDetailEventEnum;
import com.bbh.enums.GlobalFenbeiDetailOperateSourceEnum;
import com.bbh.enums.GlobalFenbeiDetailTypeEnum;
import com.bbh.enums.GlobalLotteryPrizePoolTypeEnum;
import com.bbh.live.dao.dto.LotteryPrizeItemDTO;
import com.bbh.live.dao.mapper.GlobalOrgSeatMapper;
import com.bbh.live.dao.mapper.GlobalOrganizationMapper;
import com.bbh.live.dao.service.GlobalFenbeiDetailService;
import com.bbh.model.GlobalFenbeiDetail;
import com.bbh.model.GlobalOrganization;
import com.bbh.util.AssertUtil;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * 分贝 处理策略
 * <AUTHOR>
 */
@AllArgsConstructor
@Service
public class FenbeiStrategy implements PrizeProcessStrategy {

    private final GlobalOrganizationMapper globalOrganizationMapper;
    private final GlobalOrgSeatMapper globalOrgSeatMapper;
    private final GlobalFenbeiDetailService globalFenbeiDetailService;

    /**
     * 匹配
     *
     * @param type 匹配的类型
     * @return 是否匹配
     */
    @Override
    public boolean match(GlobalLotteryPrizePoolTypeEnum type) {
        return type == GlobalLotteryPrizePoolTypeEnum.FENBEI;
    }

    /**
     * 处理奖品
     *
     * @param userId    用户ID
     * @param seatId    用户座位ID
     * @param prizeItem 奖品
     */
    @Override
    public void processPrize(Long userId, Long seatId, LotteryPrizeItemDTO prizeItem) {
        var seat = globalOrgSeatMapper.selectById(seatId);
        Long orgId = seat.getOrgId();
        var org = globalOrganizationMapper.selectById(orgId);

        // 奖品对应的分贝数量
        String extraData = prizeItem.getExtraData();
        AssertUtil.assertNotNull(extraData, "分贝奖品配置有误");
        int changedFenbeiNum = Integer.parseInt(extraData);
        AssertUtil.assertTrue(changedFenbeiNum > 0, "分贝奖品配置有误");

        // 增加用户的分贝余额，保存分贝记录
        globalOrganizationMapper.update(Wrappers.lambdaUpdate(GlobalOrganization.class)
                .eq(GlobalOrganization::getId, seat.getOrgId())
                .setIncrBy(GlobalOrganization::getFenbei, changedFenbeiNum)
        );

        // 分贝明细记录
        GlobalFenbeiDetail globalFenbeiDetail = new GlobalFenbeiDetail();
        globalFenbeiDetail.setAccountBalance(org.getFenbei() + changedFenbeiNum);
        globalFenbeiDetail.setChangeNumber(changedFenbeiNum);
        globalFenbeiDetail.setType(GlobalFenbeiDetailTypeEnum.IN);
        globalFenbeiDetail.setEvent(FenbeiDetailEventEnum.LOTTERY_REWARD.getEvent());
        globalFenbeiDetail.setEventDescribe(FenbeiDetailEventEnum.LOTTERY_REWARD.getEventDescribe());
        globalFenbeiDetail.setOperateSource(GlobalFenbeiDetailOperateSourceEnum.APP_BBH);
        globalFenbeiDetailService.save(globalFenbeiDetail);
    }

}
