package com.bbh.live.service.buyer.impl;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.extra.servlet.JakartaServletUtil;
import cn.hutool.http.useragent.UserAgent;
import cn.hutool.http.useragent.UserAgentUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.bbh.enums.*;
import com.bbh.exception.ServiceException;
import com.bbh.live.config.LiveBizProperties;
import com.bbh.live.config.LiveServiceProperties;
import com.bbh.live.constant.LiveGoodsConstant;
import com.bbh.live.dao.dto.BargainGoodsDTO;
import com.bbh.live.dao.dto.RequestInfoDTO;
import com.bbh.live.dao.dto.livegoods.LiveGoodsDTO;
import com.bbh.live.dao.dto.livegoods.LiveGoodsSubscribeDTO;
import com.bbh.live.dao.dto.vo.AuctionBidVO;
import com.bbh.live.dao.service.*;
import com.bbh.live.enums.LiveGoodsTradeCompletedTypeEnum;
import com.bbh.live.handler.queue.DelayJob;
import com.bbh.live.handler.queue.DelayJobFactory;
import com.bbh.live.handler.queue.DelayQueueManager;
import com.bbh.live.service.buyer.BuyerLiveGoodsOpService;
import com.bbh.live.service.livegoods.LiveGoodsDetailService;
import com.bbh.live.service.livegoods.cache.LiveGoodsCacheManager;
import com.bbh.live.service.livegoods.cache.info.AuctionResult;
import com.bbh.live.service.livegoods.cache.info.LiveGoodsAuctionBidInfo;
import com.bbh.live.service.livegoods.cache.info.LiveGoodsAuctionInfo;
import com.bbh.live.service.livegoods.context.LiveGoodsContext;
import com.bbh.live.service.livegoods.context.TradeLiveGoodsContext;
import com.bbh.live.service.msg.MsgService;
import com.bbh.live.service.permission.PermissionService;
import com.bbh.live.service.room.context.LiveRoomContextHolder;
import com.bbh.live.statemachine.goods.GoodsStateMachineManager;
import com.bbh.live.thread.ThreadPoolManager;
import com.bbh.live.util.DepositUtils;
import com.bbh.model.*;
import com.bbh.secure.AuthUtil;
import com.bbh.service.deposit.dto.BidToFrozenDTO;
import com.bbh.service.lock.HtbLockService;
import com.bbh.service.lock.bean.HtbLock;
import com.bbh.service.mq.enums.MqTopicEnum;
import com.bbh.service.mq.service.CoreMqService;
import com.bbh.util.AssertUtil;
import com.bbh.util.EnvironmentUtil;
import com.bbh.util.LogExUtil;
import jakarta.servlet.http.HttpServletRequest;
import lombok.AllArgsConstructor;
import org.apache.groovy.util.Maps;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR> dsy
 * @Date: 2024/7/30
 * @Description:
 */
@Service
@AllArgsConstructor
public class BuyerLiveGoodsOpServiceImpl implements BuyerLiveGoodsOpService {

    private static final Logger log = LoggerFactory.getLogger(BuyerLiveGoodsOpServiceImpl.class);
    private static final String GOODS_SUBSCRIBE_LOCK_KEY = "goods_subscribe_lock:%s : %s";

    private final LiveGoodsCacheManager liveGoodsCacheManager;
    private final LiveGoodsDetailService liveGoodsDetailService;
    private final MsgService msgService;
    private final GlobalOrgSeatService globalOrgSeatService;
    private final LiveRoomInteractiveMessageService liveRoomInteractiveMessageService;
    private final LiveGoodsBidService liveGoodsBidService;
    private final DelayQueueManager delayQueueManager;
    private final LiveGoodsSubscribeService liveGoodsSubscribeService;
    private final LiveGoodsTransferService liveGoodsTransferService;
    private final GoodsStateMachineManager<LiveGoodsContext> goodsStateMachineManager;
    private final HtbLockService htbLockService;
    private final PermissionService permissionService;
    private final LiveServiceProperties liveServiceProperties;
    private final CoreMqService coreMqService;
    private final HttpServletRequest httpServletRequest;
    private final LiveBizProperties live;
    private final GlobalUserBidForbidService globalUserBidForbidService;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public AuctionBidVO bargainLiveGoods(BargainGoodsDTO bargainGoods) {
        bargainGoods.checkProperties();
        LiveGoodsDTO detailInfo = liveGoodsDetailService.getLiveGoodsDetailInfo(bargainGoods.getLiveGoodsId());
        if (detailInfo.getTradeType() == LiveGoodsTradeTypeEnum.AUCTION) {
            AssertUtil.assertTrue(bargainGoods.getBidPrice().compareTo(BigDecimal.ZERO) > 0, "价格必须大于0");
            AssertUtil.assertTrue(bargainGoods.getBidPrice().compareTo(new BigDecimal("99999999")) <= 0, "金额过大，不能超过99999999元");
        }
        // 校验商品状态
        liveGoodsDetailService.checkLiveGoodsCanAddToCashierDesk(detailInfo, false);
        AssertUtil.assertTrue(detailInfo.getGoodsStatus() == LiveGoodsStatusEnum.ABORTIVE_AUCTION, "只有流拍商品才能议价");

        AuctionBidVO auctionBidVO = new AuctionBidVO();
        if (!EnvironmentUtil.isProfile("local")) {
            // 检查保证金相关的权限
            auctionBidVO = permissionService.checkBidPermission(
                    bargainGoods.getBuyerOrgId(),
                    bargainGoods.getBidPrice(),
                    bargainGoods.getBuyerSeatId(),
                    bargainGoods.getLiveGoodsId(),
                    "采购"
            );
            if (auctionBidVO != null && !auctionBidVO.getSuccess()) {
                return auctionBidVO;
            }
        }

        //弹幕内容
        String content = String.format(LiveGoodsConstant.BARGAIN_MESSAGE_CONTENT, detailInfo.getLiveGoodsCode(), bargainGoods.getBidPrice().toString());
        bargainGoods.setBuyerSeatId(AuthUtil.getSeatId());

        // 议价金额
        BigDecimal bidPrice = bargainGoods.getBidPrice();
        if (detailInfo.getTradeType() == LiveGoodsTradeTypeEnum.SEC_KILL && bidPrice != null && bidPrice.compareTo(BigDecimal.ZERO) <= 0) {
            bidPrice = BigDecimal.valueOf(0.01);
            bargainGoods.setBidPrice(bidPrice);
        }

        LiveRoomInteractiveMessage liveRoomInteractiveMessageInfo = liveRoomInteractiveMessageService.getOne(new LambdaQueryWrapper<LiveRoomInteractiveMessage>()
                .eq(LiveRoomInteractiveMessage::getLiveRoomId, bargainGoods.getLiveRoomId())
                .eq(LiveRoomInteractiveMessage::getLiveGoodsId, detailInfo.getLiveGoodsId())
                .eq(LiveRoomInteractiveMessage::getCreateSeatId, AuthUtil.getSeatId())
                .eq(LiveRoomInteractiveMessage::getHandleStatus, LiveRoomInteractiveMessageHandleStatusEnum.UNHANDLED.getCode())
        );
        if(liveRoomInteractiveMessageInfo != null){
            //更新互动消息
            LiveRoomInteractiveMessage liveRoomInteractiveMessageUpdate = new LiveRoomInteractiveMessage();
            liveRoomInteractiveMessageUpdate.setId(liveRoomInteractiveMessageInfo.getId());
            liveRoomInteractiveMessageUpdate.setContent(content);
            liveRoomInteractiveMessageUpdate.setType(LiveRoomInteractiveMessageTypeEnum.BARGAIN);
            liveRoomInteractiveMessageUpdate.setExtraData(JSONUtil.parseObj(bargainGoods));
            liveRoomInteractiveMessageUpdate.setBargainPrice(bidPrice);
            liveRoomInteractiveMessageService.updateById(liveRoomInteractiveMessageUpdate);
        }else{
            //保存互动消息
            LiveRoomInteractiveMessage liveRoomInteractiveMessage = new LiveRoomInteractiveMessage();
            liveRoomInteractiveMessage.setLiveRoomId(bargainGoods.getLiveRoomId());
            liveRoomInteractiveMessage.setLiveGoodsId(bargainGoods.getLiveGoodsId());
            liveRoomInteractiveMessage.setContent(content);
            liveRoomInteractiveMessage.setType(LiveRoomInteractiveMessageTypeEnum.BARGAIN);
            liveRoomInteractiveMessage.setExtraData(JSONUtil.parseObj(bargainGoods));
            liveRoomInteractiveMessage.setBargainPrice(bidPrice);
            liveRoomInteractiveMessageService.save(liveRoomInteractiveMessage);
        }
        // 发送互动消息
        msgService.userBargain(bargainGoods.getLiveRoomId(), bargainGoods.getLiveGoodsId(), bargainGoods.getBuyerSeatId(), bargainGoods.getBidPrice());

        return auctionBidVO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addGoodsToCashierDesk(BargainGoodsDTO bargainGoods) {

        //成交人
        Long buyerSeatId = bargainGoods.getBuyerSeatId();
        GlobalOrgSeat globalOrgSeat = globalOrgSeatService.lambdaQuery()
                .eq(GlobalOrgSeat::getId, buyerSeatId)
                .select(GlobalOrgSeat::getOrgId, GlobalOrgSeat::getUserId).
                one();
        AssertUtil.assertNotNull(globalOrgSeat, "成交人信息不存在");

        // 如果是零元成交，要强制把金额改成0.01，避免后续金额流程出问题
        BigDecimal bidPrice = bargainGoods.getBidPrice();
        if (bidPrice.compareTo(BigDecimal.ZERO) == 0) {
            bidPrice = BigDecimal.valueOf(0.01);
        }

        // 更新商品成交信息
        liveGoodsDetailService.getLiveGoodsService().lambdaUpdate()
                .eq(LiveGoods::getId, bargainGoods.getLiveGoodsId())
                .set(LiveGoods::getGoodsStatus, LiveGoodsStatusEnum.TRADED)
                .set(LiveGoods::getBelongOrgId, globalOrgSeat.getOrgId())
                .set(LiveGoods::getBelongSeatId, buyerSeatId)
                .set(LiveGoods::getBelongUserId, globalOrgSeat.getUserId())
                .set(LiveGoods::getSellPrice, bidPrice)
                .update();
    }

    @Override
    public void askForExplanation(BargainGoodsDTO bargainGoods) {
        LiveGoodsDTO detailInfo = liveGoodsDetailService.getLiveGoodsDetailInfo(bargainGoods.getLiveGoodsId());
        AssertUtil.assertNotNull(detailInfo, "商品不存在");
        //弹幕内容
        String content = String.format(LiveGoodsConstant.ASK_FOR_EXPLANATION_MESSAGE_CONTENT, detailInfo.getLiveGoodsCode());

        //保存互动消息
        LiveRoomInteractiveMessage liveRoomInteractiveMessage = new LiveRoomInteractiveMessage();
        liveRoomInteractiveMessage.setLiveRoomId(bargainGoods.getLiveRoomId());
        liveRoomInteractiveMessage.setContent(content);
        liveRoomInteractiveMessage.setType(LiveRoomInteractiveMessageTypeEnum.ASK_FOR_EXPLANATION);
        liveRoomInteractiveMessage.setExtraData(JSONUtil.parseObj(bargainGoods));
        liveRoomInteractiveMessageService.save(liveRoomInteractiveMessage);
        //发送互动消息
        msgService.userAskForExplanation(bargainGoods.getLiveRoomId(), bargainGoods.getLiveGoodsId(), bargainGoods.getBuyerSeatId());
    }

    //region 竞拍出价
    @Override
    public AuctionBidVO auctionBid(BargainGoodsDTO bargainGoods) {
        bargainGoods.checkProperties();
        //检查是否出价被禁止
        globalUserBidForbidService.checkBidForbid(AuthUtil.getUserId(), GlobalOrderTypeEnum.LIVE);

        Date now = new Date();
        // 商品竞拍缓存
        LiveGoodsAuctionInfo auctionLiveGoods = liveGoodsCacheManager.getLiveGoodsAuctionCache().getAuctionLiveGoods(bargainGoods.getLiveRoomId(), bargainGoods.getLiveGoodsId());
        //商品状态，加价幅度校验
        auctionBidCheck(bargainGoods, auctionLiveGoods, now);
        //当前买手信息
        GlobalOrgSeat orgSeat = globalOrgSeatService.lambdaQuery().eq(GlobalOrgSeat::getId,bargainGoods.getBuyerSeatId())
                .select(GlobalOrgSeat::getId, GlobalOrgSeat::getOrgId, GlobalOrgSeat::getUserId).one();

        AuctionBidVO auctionBidVO = new AuctionBidVO();
        if (!EnvironmentUtil.isProfile("local")) {
            // 出价权限和余额检查
            auctionBidVO = permissionService.checkBidPermission(orgSeat.getOrgId(), bargainGoods.getBidPrice(), bargainGoods.getBuyerSeatId(), bargainGoods.getLiveGoodsId(), "采购");
            if (auctionBidVO != null && !auctionBidVO.getSuccess()) {
                return auctionBidVO;
            }
        }

        if (log.isDebugEnabled()) {
            log.debug("出价前：------- 商品id:{}, 出价:{}, 买手席位id:{}, 到期时间：{},剩余时间：{}ms", bargainGoods.getLiveGoodsId(), bargainGoods.getBidPrice(), bargainGoods.getBuyerSeatId(), DateUtil.format(auctionLiveGoods.getAuctionEndTime(), DatePattern.NORM_DATETIME_PATTERN), DateUtil.between(auctionLiveGoods.getAuctionEndTime(), now, DateUnit.MS));
        }

        HtbLock settleLock = htbLockService.getLock(String.format(LiveGoodsConstant.SETTLE_LOCK_KEY, bargainGoods.getLiveRoomId(), bargainGoods.getLiveGoodsId()));
        boolean lock = settleLock.tryLock(1, TimeUnit.SECONDS);
        AssertUtil.assertTrue(lock, "商品结算中，无法出价");
        try {
            // 最新的商品信息
            auctionLiveGoods = liveGoodsCacheManager.getLiveGoodsAuctionCache().getAuctionLiveGoods(bargainGoods.getLiveRoomId(), bargainGoods.getLiveGoodsId());
            // 再次校验商品状态
            auctionBidCheck(bargainGoods, auctionLiveGoods, now);

            //开始抢购，尝试修改商品价格，只有高于当前价格才能修改
            LiveGoodsAuctionInfo liveGoodsAuctionInfo = new LiveGoodsAuctionInfo().setLiveGoodsId(bargainGoods.getLiveGoodsId()).setCurrentPrice(bargainGoods.getBidPrice()).setBuyerSeatId(bargainGoods.getBuyerSeatId());
            AuctionResult auctionResult = liveGoodsCacheManager.getLiveGoodsAuctionCache().auctionBid(bargainGoods.getLiveRoomId(), auctionLiveGoods, liveGoodsAuctionInfo);

            LiveGoodsAuctionInfo afterAuctionLiveGoods = liveGoodsCacheManager.getLiveGoodsAuctionCache().getAuctionLiveGoods(bargainGoods.getLiveRoomId(), bargainGoods.getLiveGoodsId());
            //处理出价结果
            processAuctionResult(auctionResult, bargainGoods, afterAuctionLiveGoods, now);

            if (log.isDebugEnabled()) {
                log.debug("出价后：------- 商品id:{}, 出价:{}, 买手席位id:{}, 到期时间：{}。剩余时间：{}ms", bargainGoods.getLiveGoodsId(), bargainGoods.getBidPrice(), bargainGoods.getBuyerSeatId(), DateUtil.format(afterAuctionLiveGoods.getAuctionEndTime(), DatePattern.NORM_DATETIME_PATTERN), DateUtil.between(afterAuctionLiveGoods.getAuctionEndTime(), now, DateUnit.MS));
            }

            if(afterAuctionLiveGoods.getTradeType() == LiveGoodsTradeTypeEnum.AUCTION){
                List<LiveGoodsAuctionBidInfo> biddenList = liveGoodsCacheManager.getLiveGoodsAuctionCache().getAuctionGoodsBidInfo(bargainGoods.getLiveRoomId(), bargainGoods.getLiveGoodsId());
                //发送消息更新直播间竞拍商品价格和过期时间
                msgService.userBidSuccess(bargainGoods, afterAuctionLiveGoods.getAuctionEndTime(), biddenList);
            }

            //出价成功处理
            afterBidSuccess(bargainGoods, orgSeat, auctionBidVO.getBidToFrozen());

            return new AuctionBidVO().setSuccess(true);
        }finally {
            settleLock.unlock();
        }
    }

    private void auctionBidCheck(BargainGoodsDTO bargainGoods, LiveGoodsAuctionInfo auctionLiveGoods, Date now) {
        //当前竞拍中的商品信息
        if (auctionLiveGoods == null) {
            LiveGoods one = liveGoodsDetailService.getLiveGoodsService().lambdaQuery().eq(LiveGoods::getId, bargainGoods.getLiveGoodsId()).select(LiveGoods::getGoodsStatus).one();
            AssertUtil.assertNotNull(one, "出价商品不存在");
            if (one.getGoodsStatus().equals(LiveGoodsStatusEnum.TRADED)) {
                throw new ServiceException("商品已被抢走");
            } else if (one.getGoodsStatus().equals(LiveGoodsStatusEnum.ABORTIVE_AUCTION)) {
                throw new ServiceException("商品已流拍");
            } else {
                throw new ServiceException("竞拍未开始");
            }
        }
        AssertUtil.assertFalse(auctionLiveGoods.getIfSettle(), "商品结算中，无法出价");

        if(auctionLiveGoods.getTradeType() == LiveGoodsTradeTypeEnum.SEC_KILL){
            return;
        }
        AssertUtil.assertFalse(auctionLiveGoods.getAuctionEndTime().before(now), "商品竞拍已结束");

        BigDecimal currentPrice = auctionLiveGoods.getCurrentPrice();
        if(auctionLiveGoods.getBuyerSeatId() == null){
            // 当前无人出价。
            if(currentPrice.compareTo(BigDecimal.ZERO) == 0){
                // 起拍价是0时，出价必须大于等于加价幅度。
                AssertUtil.assertTrue(bargainGoods.getBidPrice().compareTo(auctionLiveGoods.getIncreasePrice()) >= 0, "出价不得低于加价幅度");
            } else {
                //不为0时，可以是起拍价，或者大于起拍价加加价幅度
                if(bargainGoods.getBidPrice().compareTo(currentPrice) != 0){
                    AssertUtil.assertTrue(bargainGoods.getBidPrice().compareTo(currentPrice.add(auctionLiveGoods.getIncreasePrice())) >= 0, "出价不得低于当前价格加加价幅度");
                }
            }
        } else {

            // 出价金额校验，必须大于商品加价幅度
            if (bargainGoods.getBidPrice().compareTo(currentPrice.add(auctionLiveGoods.getIncreasePrice())) < 0) {
                throw new ServiceException("出价不得低于当前价加上加价幅度");
            }
        }
    }

    private void processAuctionResult(AuctionResult auctionResult, BargainGoodsDTO bargainGoods, LiveGoodsAuctionInfo auctionLiveGoods, Date now){
        switch (auctionResult){
            case AUCTION_CACHE_DELETED,
                 LIVE_GOODS_REVOKING:
                throw new ServiceException("商品竞拍已结束");
            case PRICE_UPDATE_FAIL:
                throw new ServiceException("出价低于当前最高价，得再大方些哦~");
            case SETTLING:
                throw new ServiceException("商品结算中，无法出价");
            case PRICE_AND_TIME_UPDATE_SUCCESS:
                //延长延时队列结束时间,只有剩余时间低于指定阈值的才处理
                overTimeDelayJob(bargainGoods, auctionLiveGoods, now);
                break;
            case FIRST_BIDDER_UPDATE_SUCCESS:
                //一口价 第一个人出价成功 发送延时消息，1s 后结算
                coreMqService.send(MqTopicEnum.SEC_KILL, JSONUtil.toJsonStr(
                        Maps.of("liveRoomId", bargainGoods.getLiveRoomId(), "liveGoodsId", bargainGoods.getLiveGoodsId())), liveServiceProperties.getSecKillDuration());
            default:
                // do nothing
        }
    }
    private void afterBidSuccess(BargainGoodsDTO bargainGoods, GlobalOrgSeat orgSeat, BidToFrozenDTO bidToFrozenDTO) {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        // 提前获取请求信息
        RequestInfoDTO requestInfo = null;
        if (attributes != null) {
            HttpServletRequest request = attributes.getRequest();
            requestInfo = new RequestInfoDTO();
            requestInfo.setClientIP(JakartaServletUtil.getClientIP(request));
            requestInfo.setUserAgent(request.getHeader("User-Agent"));
            requestInfo.setAppVersion(request.getHeader("app_version"));
            requestInfo.setAppInfo(request.getHeader("app_info"));
        }

        final RequestInfoDTO finalRequestInfo = requestInfo;
        ThreadPoolManager.getGlobalBizExecutor().execute(() -> {
            try {
                // 在新线程中设置请求上下文
                RequestContextHolder.setRequestAttributes(attributes);
                //插入出价记录
                insertBidRecord(bargainGoods, orgSeat, finalRequestInfo);
                //冻结保证金
                try {
                    DepositUtils.bidToFrozen(bidToFrozenDTO);
                } catch (Throwable t) {
                    LogExUtil.errorLog("商品竞拍扣除保证金失败, 商品id={}, 出价人席位id={}, 出价人商户id={}, 出价={}", t,
                            bargainGoods.getLiveGoodsId(),
                            bargainGoods.getBuyerSeatId(),
                            orgSeat.getOrgId(),
                            bargainGoods.getBidPrice()
                    );
                }
            } catch (Exception e) {
                log.error("after bid success error ", e);
            } finally {
                // 清理请求上下文
                RequestContextHolder.resetRequestAttributes();
            }
        });
    }

    private void overTimeDelayJob(BargainGoodsDTO bargainGoods, LiveGoodsAuctionInfo preAuctionLiveGoods, Date now) {
        Integer auctionTimeAppendThreshold = LiveRoomContextHolder.getLiveRoomContext().getIncreaseSurplusTime();
        Integer auctionTimeAppendSeconds = LiveRoomContextHolder.getLiveRoomContext().getIncreaseTime();

        //之前的结束时间
        Date preAuctionEndTime = preAuctionLiveGoods.getAuctionEndTime();

        //距离结束剩余时间ms
        long diffMillis = DateUtil.between(now, preAuctionEndTime, DateUnit.MS);

        //剩余时间小于阈值，则延长竞拍时间
        if (diffMillis > 0 && diffMillis < auctionTimeAppendThreshold * 1000) {
            //过期时间 = 剩余结束时间 + 延长时间
            long delay = diffMillis + (auctionTimeAppendSeconds * 1000);
            DelayJob<Long> auctionDelayJob = DelayJobFactory.createAuctionDelayJob(bargainGoods.getLiveRoomId(), bargainGoods.getLiveGoodsId(), delay);
            //重新入队
            delayQueueManager.addToQueue(auctionDelayJob);
        }
    }

    /**
     * 保存出价记录，成功后返回记录ID
     * @param bargainGoods 出价商品
     * @return 记录ID
     */
    @SuppressWarnings("all")
    private Long insertBidRecord(BargainGoodsDTO bargainGoods, GlobalOrgSeat orgSeat, RequestInfoDTO requestInfo) {
        try {
            AssertUtil.assertNotNull(orgSeat, "用户不存在");
            LiveGoodsBid liveGoodsBid = new LiveGoodsBid();
            liveGoodsBid.setBidAmount(bargainGoods.getBidPrice());
            liveGoodsBid.setBidSeatId(bargainGoods.getBuyerSeatId());
            liveGoodsBid.setBidUserId(orgSeat.getUserId());
            liveGoodsBid.setLiveGoodsId(bargainGoods.getLiveGoodsId());
            liveGoodsBid.setLiveRoomId(bargainGoods.getLiveRoomId());
            liveGoodsBid.setBidOrgId(orgSeat.getOrgId());

            // 使用提前获取的请求信息
            if (requestInfo != null) {
                liveGoodsBid.setIpAddress(requestInfo.getClientIP());
                UserAgent userAgent = UserAgentUtil.parse(requestInfo.getUserAgent());
                liveGoodsBid.setDeviceType(userAgent.getPlatform().getName());
                liveGoodsBid.setDeviceModel(requestInfo.getAppInfo());
                liveGoodsBid.setOsVersion(userAgent.getOs().getName() + " " + userAgent.getOsVersion());
                liveGoodsBid.setAppVersion(requestInfo.getAppVersion());
            }

            // save
            liveGoodsBidService.save(liveGoodsBid);
            return liveGoodsBid.getId();
        } catch (Throwable t) {
            LogExUtil.errorLog(String.format("商品竞拍出价记录保存失败: 商品id=%s，出价人席位id=%s, 出价=%s", bargainGoods.getLiveGoodsId(), bargainGoods.getBuyerSeatId(), bargainGoods.getBidPrice()), t);
        }
        return null;
    }
    // endregion


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void subscribe(LiveGoodsSubscribeDTO liveGoodsSubscribe) {
        LiveGoodsDTO goodsDetailInfo = liveGoodsDetailService.getLiveGoodsDetailInfo(liveGoodsSubscribe.getLiveGoodsId());
        AssertUtil.assertTrue(goodsDetailInfo != null, "商品不存在");

        String lockKey = String.format(GOODS_SUBSCRIBE_LOCK_KEY, liveGoodsSubscribe.getLiveGoodsId(), AuthUtil.getSeatId());
        HtbLock lock = htbLockService.getLock(lockKey);
        if(lock.isLocked() || !lock.lock()){
            return;
        }
        try{
            LiveGoodsSubscribe liveGoodsSubscribePo;
            liveGoodsSubscribePo = liveGoodsSubscribeService.lambdaQuery()
                    .eq(LiveGoodsSubscribe::getLiveGoodsId, liveGoodsSubscribe.getLiveGoodsId())
                    .eq(LiveGoodsSubscribe::getLiveRoomId, liveGoodsSubscribe.getLiveRoomId())
                    .eq(LiveGoodsSubscribe::getCreateSeatId, AuthUtil.getSeatId())
                    .one();

            //更新
            if(liveGoodsSubscribePo != null){
                liveGoodsSubscribePo.setRemark(liveGoodsSubscribe.getRemark());
                liveGoodsSubscribeService.updateById(liveGoodsSubscribePo);
                return;
            }else {
                liveGoodsSubscribePo = new LiveGoodsSubscribe();
            }
            //新增商品预约记录
            liveGoodsSubscribePo.setLiveGoodsId(liveGoodsSubscribe.getLiveGoodsId());
            liveGoodsSubscribePo.setLiveRoomId(liveGoodsSubscribe.getLiveRoomId());
            liveGoodsSubscribePo.setRemark(liveGoodsSubscribe.getRemark());
            liveGoodsSubscribeService.save(liveGoodsSubscribePo);
            //商品预约数量+1
            liveGoodsDetailService.getLiveGoodsService().incrGoodsSubscribeCount(liveGoodsSubscribe.getLiveGoodsId(), 1);
            //更新订阅记录缓存
            liveGoodsCacheManager.getLiveGoodsSubscribeCache().subscribeLiveGoods(liveGoodsSubscribe.getLiveRoomId(), liveGoodsSubscribe.getLiveGoodsId(), AuthUtil.getSeatId());
        }finally {
            lock.unlock();
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cancelSubscribe(LiveGoodsSubscribeDTO liveGoodsSubscribe) {
        LiveGoodsDTO goodsDetailInfo = liveGoodsDetailService.getLiveGoodsDetailInfo(liveGoodsSubscribe.getLiveGoodsId());
        AssertUtil.assertTrue(goodsDetailInfo != null, "商品不存在");

        //商品预约记录

        liveGoodsSubscribeService.remove(Wrappers.lambdaQuery(LiveGoodsSubscribe.class)
                .eq(LiveGoodsSubscribe::getLiveGoodsId, liveGoodsSubscribe.getLiveGoodsId())
                .eq(LiveGoodsSubscribe::getLiveRoomId, liveGoodsSubscribe.getLiveRoomId()));
        //商品预约数量-1
        liveGoodsDetailService.getLiveGoodsService().incrGoodsSubscribeCount(liveGoodsSubscribe.getLiveGoodsId(), -1);
        //更新订阅记录缓存
        liveGoodsCacheManager.getLiveGoodsSubscribeCache().cancelSubscribeLiveGoods(liveGoodsSubscribe.getLiveRoomId(), liveGoodsSubscribe.getLiveGoodsId(), AuthUtil.getSeatId());
    }

    @Override
    public void closeSubscribeCard(LiveGoodsSubscribeDTO liveGoodsSubscribe) {
        //预约商品标记已处理，再次进入直播间不展示
        liveGoodsSubscribeService.lambdaUpdate()
                .eq(LiveGoodsSubscribe::getLiveGoodsId, liveGoodsSubscribe.getLiveGoodsId())
                .eq(LiveGoodsSubscribe::getLiveRoomId, liveGoodsSubscribe.getLiveRoomId())
                .set(LiveGoodsSubscribe::getIfHandled, true)
                .update();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AuctionBidVO takeTheTransferGoods(BargainGoodsDTO bargainGoods) {
        bargainGoods.checkProperties();
        LiveGoodsDTO goodsDetailInfo = liveGoodsDetailService.getLiveGoodsDetailInfo(bargainGoods.getLiveGoodsId());
        AssertUtil.assertTrue(goodsDetailInfo != null, "商品不存在");

        // 检查保证金相关的权限
        AuctionBidVO auctionBidVO = permissionService.checkBidPermission(bargainGoods.getBuyerOrgId(), bargainGoods.getBuyerSeatId(), "采购");
        if (auctionBidVO != null && !auctionBidVO.getSuccess()) {
            return auctionBidVO;
        }

        //修改传送记录
        boolean updated = liveGoodsTransferService.lambdaUpdate()
                .eq(LiveGoodsTransfer::getLiveGoodsId, bargainGoods.getLiveGoodsId())
                .eq(LiveGoodsTransfer::getLiveRoomId, bargainGoods.getLiveRoomId())
                .eq(LiveGoodsTransfer::getTargetSeatId, AuthUtil.getSeatId())
                .gt(LiveGoodsTransfer::getExpireAt, new Date())
                .eq(LiveGoodsTransfer::getIfHandled, false)
                .set(LiveGoodsTransfer::getIfHandled, true)
                .update();
        //幂等处理
        if(!updated){
            return auctionBidVO;
        }
        //商品成交
        TradeLiveGoodsContext context = new TradeLiveGoodsContext()
                .setBuyerSeatId(AuthUtil.getSeatId())
                .setFinalPrice(bargainGoods.getBidPrice())
                .setTradeType(LiveGoodsTradeCompletedTypeEnum.TRANSFER);
        context.setLiveGoodsId(bargainGoods.getLiveGoodsId());
        goodsStateMachineManager.transferOrBidSuccess(goodsDetailInfo, context);

        return auctionBidVO;
    }
}
