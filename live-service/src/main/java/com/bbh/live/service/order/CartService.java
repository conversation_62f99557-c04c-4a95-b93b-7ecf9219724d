package com.bbh.live.service.order;

import com.bbh.base.ListBase;
import com.bbh.live.dao.dto.LiveShoppingCartDTO;
import com.bbh.live.dao.dto.LiveShoppingCartGroupVO;
import com.bbh.live.dao.dto.SellerCashierDeskDTO;
import com.bbh.live.dao.dto.vo.CashierDeskVO;
import com.bbh.live.dao.dto.vo.SellerCashierDeskVO;

import java.util.List;

/**
 * 购物车Service
 * <AUTHOR>
 */
public interface CartService {

    /**
     * 获取买手的购物车商品数量
     *
     * @return 购物车商品数量
     */
    Long getBuyerCartCount();

    /**
     * 获取我的收银台，分为全店收银台和我的收银台 <br>
     * 按直播场次进行分组，分组的logo是商户图片 <br>
     * 全店收银台显示成交人，我的收银台不显示
     *
     * @param ifOrg 是否是商户全店收银台
     * @return 收银台信息
     */
    CashierDeskVO getCashierDeskInfo(boolean ifOrg);

    /**
     * 获取收银台角标数量，微商获取自己的商品数量，企业获取全店商品数量
     * @return 商品数量
     */
    Long getCashierDeskCount();

    /**
     * 收银台商品按直播场次进行分组
     * @param shoppingCartList 购物车列表
     * @return  分组后的列表
     */
    List<LiveShoppingCartGroupVO> groupingShoppingCart(List<LiveShoppingCartDTO> shoppingCartList);

    /**
     * 卖家收银台，要根据直播间分组，同时支持商品模糊查询、支持成交时间查询
     * @param sellerCashierDeskDTO  查询条件
     * @return  分组后的商品清单
     */
    SellerCashierDeskVO getSellerCashierDeskInfo(SellerCashierDeskDTO sellerCashierDeskDTO);

    /**
     * 商家待结算，根据商品分组
     */
    ListBase<LiveShoppingCartDTO> getSellerCashierGoodsPageList(SellerCashierDeskDTO sellerCashierDeskDTO);
}
