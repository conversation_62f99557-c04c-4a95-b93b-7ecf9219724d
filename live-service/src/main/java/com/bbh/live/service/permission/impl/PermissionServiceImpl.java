package com.bbh.live.service.permission.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.bbh.enums.LiveRoomStreamStatusEnum;
import com.bbh.exception.AuthException;
import com.bbh.exception.ServiceException;
import com.bbh.feign.IGlobalApiClient;
import com.bbh.live.config.LiveServiceProperties;
import com.bbh.live.dao.dto.BargainGoodsDTO;
import com.bbh.live.dao.dto.vo.AuctionBidVO;
import com.bbh.live.dao.mapper.*;
import com.bbh.live.dao.service.GlobalOrgSeatService;
import com.bbh.live.dao.service.GlobalOrgSettlementBankService;
import com.bbh.live.dao.service.IGlobalOrganizationService;
import com.bbh.live.service.permission.PermissionService;
import com.bbh.live.util.DepositUtils;
import com.bbh.model.*;
import com.bbh.secure.AuthUtil;
import com.bbh.service.deposit.DepositService;
import com.bbh.service.deposit.dto.BidToFrozenDTO;
import com.bbh.service.deposit.dto.GetFrozenDepositDTO;
import com.bbh.service.deposit.dto.UserInfoDTO;
import com.bbh.vo.AuthUser;
import feign.FeignException;
import jakarta.annotation.Resource;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 权限
 * <AUTHOR>
 */
@Slf4j
@AllArgsConstructor
@Service
public class PermissionServiceImpl implements PermissionService {

    @Resource
    private IGlobalApiClient globalApiClient;

    private final IGlobalOrganizationService globalOrganizationService;
    private final GlobalOrgSettlementBankService globalOrgSettlementBankService;
    private final LiveServiceProperties liveServiceProperties;
    private final GlobalOrgSeatService globalOrgSeatService;
    private final LiveRoomDirectorMapper liveRoomDirectorMapper;
    private final LiveRoomMapper liveRoomMapper;
    private final DepositService depositService;
    private final GlobalMerchantAuthRecordMapper globalMerchantAuthRecordMapper;
    private final GlobalBuyerRightMapper globalBuyerRightMapper;
    private final GlobalSellerRightMapper globalSellerRightMapper;
    private final UserChatBanRecordMapper userChatBanRecordMapper;

    /**
     * 获取当前用户的权限code列表，Map的key是不同模块，Map的value是code集合
     */
    @Override
    public Object getOwnList() {
        try {
            return globalApiClient.getUserDeptPermissionOwnCodeList();
        } catch (FeignException.Unauthorized e) {
            throw new AuthException();
        }
    }

    /**
     * 当前用户是否导播或主播
     */
    @Override
    public boolean isDirectorOrAnchor() {
        AuthUser user = AuthUtil.getUser();
        var seatId = user.getSeatId();
        var orgId = user.getOrgId();

        List<LiveRoom> liveRoomList = new ArrayList<>();

        // 查询当前用户是否在导播名单中
        List<LiveRoomDirector> directorList = liveRoomDirectorMapper.selectList(Wrappers.lambdaQuery(LiveRoomDirector.class)
                .select(LiveRoomDirector::getOrgId, LiveRoomDirector::getLiveRoomId)
                .eq(LiveRoomDirector::getDirectorSeatId, seatId)
        );
        if (CollUtil.isNotEmpty(directorList)) {
            List<Long> liveRoomIdList = directorList.stream().map(LiveRoomDirector::getLiveRoomId).distinct().collect(Collectors.toList());
            if (CollUtil.isNotEmpty(liveRoomIdList)) {
                // 查询绑定的这些直播间是否正在开播或者准备开播（没到结束时间 或者 到了结束时间但没断流）
                liveRoomList = liveRoomMapper.selectList(Wrappers.lambdaQuery(LiveRoom.class)
                        .in(LiveRoom::getId, liveRoomIdList)
                        .and(w -> {
                            // 没到结束时间
                            w.ge(LiveRoom::getEndAt, new Date())
                                    // 或者到了结束时间但流没有中断
                                    .or(w1 ->
                                            w1.lt(LiveRoom::getEndAt, new Date())
                                                    .eq(LiveRoom::getStreamStatus, LiveRoomStreamStatusEnum.ON)
                                    );
                        })
                );
            }
        }

        // 查询自己是主播，没到结束时间、或者到了结束时间但流没有中断的直播间
        List<LiveRoom> anchorLiveRoomList = liveRoomMapper.selectList(Wrappers.lambdaQuery(LiveRoom.class)
                .eq(LiveRoom::getAnchorSeatId, seatId)
                .and(w -> {
                    // 没到结束时间
                    w.ge(LiveRoom::getEndAt, new Date())
                            // 或者到了结束时间但流没有中断
                            .or(w1 ->
                                    w1.lt(LiveRoom::getEndAt, new Date())
                                            .eq(LiveRoom::getStreamStatus, LiveRoomStreamStatusEnum.ON)
                            );
                })
        );
        if (CollUtil.isNotEmpty(anchorLiveRoomList)) {
            liveRoomList.addAll(anchorLiveRoomList);
        }

        // 如果能查到上述条件的直播间，代表是导播
        return !liveRoomList.isEmpty();
    }

    /**
     * 是否认证
     *
     * @param orgId 商户ID
     * @return 是否认证
     */
    @Override
    public boolean isAuth(Long orgId) {
        GlobalOrganization organization = globalOrganizationService.getById(orgId);
        return organization != null && organization.getAuthSuccessId() > 0;
    }

    /**
     * 检查保证金的权限
     *
     * @param orgId
     * @param actionName
     */
    @Override
    public AuctionBidVO checkBidPermission(Long orgId, String actionName) {
        return checkBidPermission(orgId, null, null, null, actionName);
    }

    @Override
    public AuctionBidVO checkBidPermission(Long orgId, Long seatId, String actionName) {
        return checkBidPermission(orgId, null, seatId, null, actionName);
    }

    @Override
    public AuctionBidVO checkBidPermission(Long orgId) {
        return checkBidPermission(orgId, "采购");
    }

    @Override
    public AuctionBidVO checkBidPermission() {
        return checkBidPermission(AuthUtil.getOrgId(), null);
    }

    /**
     * 检查权限
     * @param orgId             出价人的商户ID
     * @param bidPrice          出价金额，允许为null
     * @param seatId            出价人的席位ID，当出价金额为null时，允许为null
     * @param liveGoodsId       商品ID，当出价金额为null时，允许为null
     * @param actionName        操作名称
     * @return                  出价结果
     */
    @Override
    public AuctionBidVO checkBidPermission(Long orgId, BigDecimal bidPrice, Long seatId, Long liveGoodsId, String actionName) {
        // 创建返回对象，用于记录各种状态
        AuctionBidVO result = new AuctionBidVO();

        // 先获取当前的主体信息
        GlobalOrganization organization = globalOrganizationService.lambdaQuery()
                .eq(GlobalOrganization::getId, orgId)
                .select(GlobalOrganization::getAuthSuccessId,
                        GlobalOrganization::getBuyerManualTotalDeposit,
                        GlobalOrganization::getBuyerVisualTotalDeposit,
                        GlobalOrganization::getBuyerFreezeDeposit
                )
                .one();
        // 如果主体不存在，直接抛出异常
        if (organization == null) {
            throw new ServiceException("商户不存在");
        }

        // 当前席位信息
        GlobalOrgSeat orgSeat = globalOrgSeatService.lambdaQuery().eq(GlobalOrgSeat::getId, seatId).one();
        if (orgSeat == null) {
            throw new ServiceException("席位不存在");
        }

        // 检查是否被禁言，但不直接返回，只设置状态
        String userPhone = orgSeat.getUserPhone();
        UserChatBanRecord userChatBanRecord = userChatBanRecordMapper.selectOne(Wrappers
                .lambdaQuery(UserChatBanRecord.class)
                .eq(UserChatBanRecord::getUserPhone, userPhone)
                .orderByDesc(UserChatBanRecord::getId)
                .last("limit 1")
        );
        Optional.ofNullable(userChatBanRecord)
                .ifPresent(x -> result.setChatBanned(x.getRemark()));

        // 检查采购权限
        if (Boolean.TRUE.equals(liveServiceProperties.getCheckBuyerRightsPermission()) && !hasBuyerRights(seatId, orgId)) {
            // 没权限，要查当前主体未过期的席位数量
            return result.setNeedRights(getBuyerRightsSeatCount(orgId));
        }

        // 最小额度
        BigDecimal minBidDeposit = liveServiceProperties.getMinBidDeposit();
        BigDecimal buyerTotalDeposit = organization.getBuyerManualTotalDeposit().add(organization.getBuyerVisualTotalDeposit());
        actionName = actionName == null ? "出价" : actionName;

        // 特权保证金不足100，再需要认证，也就是特权账户不检查认证状态
        if (organization.getBuyerVisualTotalDeposit().compareTo(minBidDeposit) <= 0 && organization.getAuthSuccessId() <= 0) {
            // 检查实名认证的状态
            GlobalMerchantAuthRecord merchantAuthRecord = globalMerchantAuthRecordMapper.selectOne(Wrappers.lambdaQuery(GlobalMerchantAuthRecord.class)
                    .eq(GlobalMerchantAuthRecord::getOrgId, orgId)
                    .orderByDesc(GlobalMerchantAuthRecord::getId)
                    .last("limit 1")
            );
            // 未认证
            if (merchantAuthRecord == null) {
                return result.setNeedAuth(AuctionBidVO.AuthStatus.NO_AUTH, actionName);
            }
            // 0,5,10 = 认证中
            if (merchantAuthRecord.getAuthStatus() <= 10) {
                return result.setNeedAuth(AuctionBidVO.AuthStatus.AUTHING, actionName);
            }
            // 30 = 认证失败, 20 = 认证成功
            if (merchantAuthRecord.getAuthStatus() == 30) {
                return result.setNeedAuth(AuctionBidVO.AuthStatus.AUTH_FAILED, actionName);
            }
        }

        // 保证金总额检查
        if (buyerTotalDeposit.compareTo(minBidDeposit) < 0) {
            return result
                    .setRechargePopup("账户余额", buyerTotalDeposit, StrUtil.format("额度不足{}元，可能影响后续商品{}", minBidDeposit.toPlainString(), actionName));
        }

        if (seatId == null || bidPrice == null || liveGoodsId == null) {
            return result.setSuccess(true);
        }

        // 计算出要冻结的金额
        BargainGoodsDTO bargainGoods = new BargainGoodsDTO();
        bargainGoods.setBuyerOrgId(orgId);
        bargainGoods.setLiveGoodsId(liveGoodsId);
        bargainGoods.setBidPrice(bidPrice);
        BidToFrozenDTO bidToFrozenDTO = DepositUtils.getBidToFrozenDTO(bargainGoods);

        // 同一个商品只有第一次出价才需要校验，后续出价不校验
        boolean ifBidden = DepositUtils.ifBid(bidToFrozenDTO.getGoodsInfo().getGoodsId(), orgSeat.getOrgId());
        if (ifBidden) {
            return result.setBidToFrozen(bidToFrozenDTO).setSuccess(true);
        }

        // 校验保证金门槛，如果要校验的话
        Boolean validate = orgSeat.getIfDepositValidate();
        if(Boolean.TRUE.equals(validate)) {
            BigDecimal minDeposit = orgSeat.getMinDeposit();
            if (minDeposit.compareTo(bidToFrozenDTO.getFrozenDeposit()) < 0) {
                return result
                        .setRechargePopup("账户余额", buyerTotalDeposit, StrUtil.format("额度不足{}元，可能影响后续商品{}", minBidDeposit.toPlainString(), actionName));
            }
        }

        // 检查可用余额
        BigDecimal availableDeposit = organization.getBuyerManualTotalDeposit().add(organization.getBuyerVisualTotalDeposit()).subtract(getOrgFrozenDeposit(orgId));
        if (availableDeposit.compareTo(bidToFrozenDTO.getFrozenDeposit()) < 0) {
            return result.setRechargePopup("可用余额", availableDeposit, StrUtil.format("本拍品出价余额需大于{}", bidToFrozenDTO.getFrozenDeposit()));
        }

        return result.setBidToFrozen(bidToFrozenDTO).setSuccess(true);
    }

    private BigDecimal getOrgFrozenDeposit(Long orgId) {
        GetFrozenDepositDTO getFrozenDepositDTO = new GetFrozenDepositDTO();
        UserInfoDTO userInfoDTO = new UserInfoDTO();
        userInfoDTO.setId(orgId);
        getFrozenDepositDTO.setUserInfo(userInfoDTO);
        return depositService.getFrozenDeposit(getFrozenDepositDTO);
    }

    /**
     * 检查是否有采购权益
     * 这里直接查表，避免feign调用产生的额外时间损耗
     */
    @Override
    public boolean hasBuyerRights(Long seatId, Long orgId) {
        GlobalBuyerRight buyerRight = globalBuyerRightMapper.selectOne(Wrappers.lambdaQuery(GlobalBuyerRight.class)
                .select(GlobalBuyerRight::getEndAt, GlobalBuyerRight::getId)
                .eq(GlobalBuyerRight::getLiveSeatId, seatId)
                .eq(GlobalBuyerRight::getOrgId, orgId)
                .orderByDesc(GlobalBuyerRight::getId)
                .last("limit 1")
        );
        if (buyerRight == null || buyerRight.getEndAt() == null) {
            return false;
        }
        // 过期的也返回false
        return buyerRight.getEndAt().getTime() > System.currentTimeMillis();
    }

    /**
     * 主体下可用的采购权益数量
     */
    private Long getBuyerRightsSeatCount(Long orgId) {
        return globalBuyerRightMapper.selectCount(Wrappers
                .lambdaQuery(GlobalBuyerRight.class)
                .eq(GlobalBuyerRight::getOrgId, orgId)
        );
    }

    /**
     * 检查是否有销售权益
     * 这里直接查表，避免feign调用产生的额外时间损耗
     */
    @Override
    public boolean hasSellerRights(Long orgId) {
        if (!Boolean.TRUE.equals(liveServiceProperties.getCheckSellerRightsPermission())) {
            return true;
        }
        GlobalSellerRight sellerRight = globalSellerRightMapper.selectOne(Wrappers.lambdaQuery(GlobalSellerRight.class)
                .select(GlobalSellerRight::getId, GlobalSellerRight::getEndAt)
                .eq(GlobalSellerRight::getOrgId, orgId)
                .orderByDesc(GlobalSellerRight::getId)
                .last("limit 1")
        );
        if (sellerRight == null || sellerRight.getEndAt() == null) {
            return false;
        }
        // 过期的也返回false
        return sellerRight.getEndAt().getTime() > System.currentTimeMillis();
    }

    /**
     * 商家是否签约
     *
     * @param organization
     */
    @Override
    public boolean checkOrgIsContract(GlobalOrganization organization) {
        if (organization == null) {
            return false;
        }
        return organization.getOrgContractId() != null && organization.getOrgContractId() != 0 && organization.getIfOrgContract() == 1;
    }

    /**
     * 商家是否有结算账户
     *
     * @param organization
     */
    @Override
    public boolean checkOrgHasSettlementBank(GlobalOrganization organization) {
        if (organization == null) {
            return false;
        }
        return globalOrgSettlementBankService
                .lambdaQuery()
                .ne(GlobalOrgSettlementBank::getPlatMerchantNo,"")
                .eq(GlobalOrgSettlementBank::getOrgId, organization.getId())
                .exists();
    }
}
