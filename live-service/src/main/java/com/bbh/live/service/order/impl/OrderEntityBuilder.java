package com.bbh.live.service.order.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.bbh.enums.GlobalOrderAppEnum;
import com.bbh.enums.GlobalOrderStatusEnum;
import com.bbh.enums.GlobalOrderTypeEnum;
import com.bbh.enums.GlobalPayTypeEnum;
import com.bbh.live.dao.dto.AggregatedUserDTO;
import com.bbh.live.dao.dto.livegoods.LiveGoodsDTO;
import com.bbh.live.service.order.dto.OrderBuilderContext;
import com.bbh.live.service.order.dto.PreparedOrderDTO;
import com.bbh.model.GlobalOrder;
import com.bbh.model.GlobalOrderItem;
import com.bbh.model.GlobalOrderSub;
import com.bbh.secure.AuthUtil;
import com.bbh.util.AssertUtil;
import com.bbh.util.OrderNoGenerator;
import com.bbh.vo.AuthUser;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 用于构建订单实体类
 *
 * <AUTHOR>
 */
@Service
@AllArgsConstructor
@Slf4j
public class OrderEntityBuilder {

    private final FenbeiCalculator fenbeiCalculator;
    private final OrderCalculator orderCalculator;

    /**
     * 将订单项列表按商户ID分组
     *
     * @param orderItemList 订单项列表
     * @return 以商户ID为键，对应订单项列表为值的Map
     */
    public static Map<Long, List<GlobalOrderItem>> buildOrderItemMap(List<GlobalOrderItem> orderItemList) {
        // 按商户ID分组
        return orderItemList.stream().collect(Collectors.groupingBy(
                // 键为商户ID，值为订单项列表
                GlobalOrderItem::getSellerOrgId, HashMap::new, Collectors.toCollection(ArrayList::new)));
    }

    /**
     * 通用的组装
     *
     * @param context 入参
     * @return 组装好的准备数据，包含商品列表、订单列表等
     */
    public PreparedOrderDTO buildOrderResult(OrderBuilderContext context) {
        List<LiveGoodsDTO> liveGoodsList = context.getLiveGoodsList();
        AssertUtil.assertFalse(CollUtil.isEmpty(liveGoodsList), "商品列表不能为空");

        AuthUser user = AuthUtil.getUser();
        Date now = new Date();
        // 用户完整信息
        AggregatedUserDTO aggregatedUserDTO = context.getBuyerUser();

        // 先遍历商品，组装出order_item列表和order_sub列表
        List<GlobalOrderItem> orderItemList = new ArrayList<>();
        for (LiveGoodsDTO liveGoods : liveGoodsList) {
            // 生成订单项，此时没有order_sub_id 和 order_id
            GlobalOrderItem orderItem = buildOrderItem(liveGoods, context);
            orderItemList.add(orderItem);
        }

        FenbeiCalculator.OnlineResult onlineResult = null;
        FenbeiCalculator.OfflineResult offlineResult = null;
        if (context.getPayType() == GlobalPayTypeEnum.OFFLINE) {
            // 线下转账，每场都要扣除固定数量
            offlineResult = fenbeiCalculator.calculateOfflineDeduction(orderItemList, aggregatedUserDTO);
        } else {
            // 计算分贝，如果是会员会自动均摊
            // 在里面会进行应支付金额的计算，并且会set抵扣的分贝数量与金额
            onlineResult = fenbeiCalculator.calculateFenbeiDeduction(orderItemList, aggregatedUserDTO, context.getIfUseFenbei());
        }

        // 根据商户分组，key是商户id，value是商品订单
        Map<Long, List<GlobalOrderItem>> orderItemMap = buildOrderItemMap(orderItemList);

        // 将根据商户分组后的商品订单，进行金额累加等处理，组装出order_sub列表
        List<GlobalOrderSub> orderSubList = new ArrayList<>();
        orderItemMap.forEach((k, v) -> {
            GlobalOrderSub orderSub = buildOrderSub(k, v, context);
            orderSubList.add(orderSub);
        });

        // 生成全局订单，注意回头更新order_item和order_sub的列表，录入全局订单数据
        GlobalOrder order = buildGlobalOrder(orderSubList, context);

        return new PreparedOrderDTO().setUser(user).setNow(now).setLiveGoodsList(liveGoodsList).setOrderItemList(orderItemList).setOrderSubList(orderSubList).setGlobalOrder(order).setOnlineResult(onlineResult).setOfflineResult(offlineResult).setOrderItemMap(orderItemMap);
    }

    /**
     * 生成总订单
     *
     * @param orderSubList 子订单列表
     * @param context      统一上下文
     * @return 组装好的总订单
     */
    public GlobalOrder buildGlobalOrder(List<GlobalOrderSub> orderSubList, OrderBuilderContext context) {
        GlobalOrder globalOrder = new GlobalOrder();
        // 成交人相关
        globalOrder.setBuyerOrgId(context.getBuyerUser().getOrgId());
        globalOrder.setBuyerSeatId(context.getBuyerUser().getSeatId());
        // 生成指定规则的订单号
        globalOrder.setOrderNo(buildOrderNo(false));
        // 待付款
        globalOrder.setOrderStatus(GlobalOrderStatusEnum.TO_BE_PAID);
        globalOrder.setApp(GlobalOrderAppEnum.LIVE);
        globalOrder.setOrderType(GlobalOrderTypeEnum.LIVE);
        // 支付截止时间，线上支付：永不过期；线下支付：48小时
        if (context.getPayType() == GlobalPayTypeEnum.OFFLINE) {
            // 48小时
            String futureTime = DateUtil.format(DateUtil.offsetHour(DateUtil.date(), 48), "yyyy-MM-dd 03:10:00");
            globalOrder.setLastPayAt(DateUtil.parse(futureTime));
        } else {
            // 永不过期
            globalOrder.setLastPayAt(DateUtil.parse("2099-12-31 23:59:59", "yyyy-MM-dd HH:mm:ss"));
        }
        // 总金额
        globalOrder.setTotalGoodsPrice(orderSubList.stream().map(GlobalOrderSub::getTotalGoodsPrice).reduce(BigDecimal.ZERO, BigDecimal::add));
        // 应支付金额
        globalOrder.setNeedPayAmount(orderSubList.stream().map(GlobalOrderSub::getNeedPayAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
        // 抵扣的分贝数量
        globalOrder.setFenbeiDeductionCount(orderSubList.stream().map(GlobalOrderSub::getFenbeiDeductionCount).reduce(0, Integer::sum));
        // 抵扣的分贝金额
        globalOrder.setFenbeiDeductionAmount(orderSubList.stream().map(GlobalOrderSub::getFenbeiDeductionAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
        // 商品数量
        globalOrder.setGoodsNum(orderSubList.stream().map(GlobalOrderSub::getGoodsNum).reduce(0, Integer::sum));
        // 收货地址
        globalOrder.setReceiveAddressId(context.getReceiveAddressId());
        globalOrder.setReceiveAddressInfo(context.getReceiveAddressInfo());
        // 备注
        globalOrder.setOrderRemark(null);

        globalOrder.setIfInspect(context.getIfQualityInspect());

        return globalOrder;
    }

    /**
     * 子订单
     *
     * @param orderItemList 订单项列表
     * @return 组装好的子订单
     */
    public GlobalOrderSub buildOrderSub(Long sellerOrgId, List<GlobalOrderItem> orderItemList, OrderBuilderContext context) {
        GlobalOrderSub subOrder = new GlobalOrderSub();
        // 成交人
        subOrder.setBuyerOrgId(context.getBuyerUser().getOrgId());
        subOrder.setBuyerSeatId(context.getBuyerUser().getSeatId());
        subOrder.setBuyerUserId(context.getBuyerUser().getUserId());
        // 售出人商户号
        subOrder.setSellerOrgId(sellerOrgId);
        // 总订单信息，这里不需要，后面补充
        subOrder.setGlobalOrderId(null);
        subOrder.setOrderNo(null);
        // 子订单流水号：在调用中台支付时生成并更新
        subOrder.setOrderSubNo("");
        // 来源和订单类型
        subOrder.setApp(GlobalOrderAppEnum.LIVE);
        subOrder.setOrderType(GlobalOrderTypeEnum.LIVE);
        // 子订单内商品的总金额
        subOrder.setTotalGoodsPrice(orderItemList.stream().map(GlobalOrderItem::getGoodsPrice).reduce(BigDecimal.ZERO, BigDecimal::add));
        // 子订单应支付金额
        subOrder.setNeedPayAmount(orderItemList.stream().map(GlobalOrderItem::getNeedPayAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
        // 商家费率总额
        subOrder.setServiceAmount(orderItemList.stream().map(GlobalOrderItem::getServiceAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
        // 买手服务费率总额
        subOrder.setBuyerServiceAmount(orderItemList.stream().map(GlobalOrderItem::getBuyerServiceAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
        // 通道费总额
        subOrder.setChannelAmount(orderItemList.stream().map(GlobalOrderItem::getChannelAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
        // 抵扣的分贝数量
        subOrder.setFenbeiDeductionCount(orderItemList.stream().map(GlobalOrderItem::getFenbeiDeductionCount).reduce(0, Integer::sum));
        // 抵扣的分贝金额
        subOrder.setFenbeiDeductionAmount(orderItemList.stream().map(GlobalOrderItem::getFenbeiDeductionAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
        // 商品数量
        subOrder.setGoodsNum(orderItemList.size());
        // 订单状态：待付款
        subOrder.setOrderStatus(GlobalOrderStatusEnum.TO_BE_PAID);
        // 支付方式
        subOrder.setPayType(context.getPayType());
        // 2025-03-25 新增是否质检
        subOrder.setIfInspect(context.getIfQualityInspect());
        return subOrder;
    }

    /**
     * 订单商品项 <br>
     * 这边不算费率，在提现时计算费率
     *
     * @param goods 直播商品
     * @return 订单商品
     */
    public GlobalOrderItem buildOrderItem(LiveGoodsDTO goods, OrderBuilderContext context) {
        GlobalOrderItem orderItem = new GlobalOrderItem();
        // 成交人
        orderItem.setBuyerOrgId(context.getBuyerUser().getOrgId());
        orderItem.setBuyerSeatId(context.getBuyerUser().getSeatId());
        orderItem.setBuyerUserId(context.getBuyerUser().getUserId());
        orderItem.setAddCartSeatId(context.getBuyerUser().getSeatId());
        // 售出人
        orderItem.setSellerOrgId(goods.getOrgId());
        // 对应的商品ID，这里存ERP货品ID
        orderItem.setTargetId(goods.getGlobalGoodsId());
        orderItem.setTargetTableName("erp_goods");
        // 总订单信息、子订单信息
        orderItem.setGlobalOrderId(null);
        orderItem.setGlobalOrderSubId(null);
        // 商品价格
        orderItem.setGoodsPrice(goods.getSellPrice());
        // 直播的商品id
        orderItem.setBizGoodsId(goods.getId());
        orderItem.setBizType(GlobalOrderTypeEnum.LIVE);
        orderItem.setBizId(goods.getLiveRoomId());
        // 商家服务费不用管，直接设置为0，提现时会计算
        orderItem.setServiceAmount(BigDecimal.ZERO);
        orderItem.setBuyerServiceAmount(BigDecimal.ZERO);
        // 支付方式
        orderItem.setPayType(context.getPayType());

        // 如果是线上支付，要计算通道费，订单状态要改为待付款 抵扣金额
        if (!GlobalPayTypeEnum.OFFLINE.equals(orderItem.getPayType())) {
            orderItem.setChannelAmount(orderCalculator.computeChannelAmount(goods.getSellPrice()));
        } else {
            orderItem.setChannelAmount(BigDecimal.ZERO);
        }
        // 订单状态改为待付款
        orderItem.setOrderStatus(GlobalOrderStatusEnum.TO_BE_PAID);
        // 买家服务费 = 直播间的买家服务费率 * 商品价格
        BigDecimal buyerServiceRate = goods.getLiveRoomBuyerServiceRate();
        BigDecimal buyerServiceAmount = orderCalculator.computeBuyerServiceAmount(goods.getSellPrice(), buyerServiceRate);
        orderItem.setBuyerServiceRate(buyerServiceRate);
        orderItem.setBuyerServiceAmount(buyerServiceAmount);

        // 应支付金额 = 商品金额 + 通道费 + 买家服务费 - 分贝抵扣（在生成完后单独计算处理，因此这里不计算）
        orderItem.setNeedPayAmount(computeItemNeedPayAmount(orderItem));
        // 平台分类id
        orderItem.setPlatformClassifyId(Objects.toString(goods.getPlatformClassifyId(), ""));
        // 分贝抵扣默认值，如果是线上充值后面会再重新set，如果是线下就是零
        orderItem.setFenbeiDeductionCount(0);
        orderItem.setFenbeiDeductionAmount(BigDecimal.ZERO);
        // 2025-03-25 新增是否质检
        orderItem.setIfInspect(context.getIfQualityInspect());
        return orderItem;
    }

    // 计算应支付金额
    private BigDecimal computeItemNeedPayAmount(GlobalOrderItem orderItem) {
        BigDecimal goodsPrice = orderItem.getGoodsPrice();
        return goodsPrice.add(orderItem.getChannelAmount()).add(orderItem.getBuyerServiceAmount());
    }

    /**
     * 生成订单号
     *
     * @param isSubOrder 是否子订单
     * @return 订单号
     */
    public String buildOrderNo(boolean isSubOrder) {
        return OrderNoGenerator.generateIdNumber(OrderNoGenerator.Service.LIVE, OrderNoGenerator.Function.GOODS, OrderNoGenerator.Business.ORDER, isSubOrder);
    }

}
