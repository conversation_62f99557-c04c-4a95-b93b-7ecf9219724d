package com.bbh.live.service.msg.notify.chatroom.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.bbh.live.dao.dto.ChatroomStatusSyncDTO;
import com.bbh.live.dao.service.LiveRoomEntryRecordService;
import com.bbh.live.service.buyer.vip.BuyerVipService;
import com.bbh.live.service.buyer.vip.vo.UserBuyerVipInfoVO;
import com.bbh.live.service.msg.MsgService;
import com.bbh.live.service.msg.notify.chatroom.Status0SubProcessor;
import com.bbh.live.service.msg.notify.chatroom.enums.ChatroomActionType;
import com.bbh.live.service.room.LiveRoomCacheService;
import com.bbh.model.LiveRoomEntryRecord;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * 用户进入聊天室
 * <AUTHOR>
 */
@Service
@AllArgsConstructor
@Slf4j
public class UserJoinRoomProcessor implements Status0SubProcessor {

    private final LiveRoomEntryRecordService liveRoomEntryRecordService;
    private final LiveRoomCacheService liveRoomCacheService;
    private final BuyerVipService buyerVipService;
    private final MsgService msgService;

    @Override
    public void process(ChatroomStatusSyncDTO dto, Long liveRoomId) {
        var userList = dto.parseUserInfo();
        if (userList == null) {
            log.info("userList is null, chatRoomId: {}", dto.getChatRoomId());
            return;
        }
        userList.forEach(user -> saveEntry(liveRoomId, user.getSeatId(), user.getOrgId(),user.getUserId()));
    }

    private void saveEntry(Long liveRoomId, Long seatId, Long orgId,Long userId) {
        // 检查有没有未处理的数据
        List<LiveRoomEntryRecord> entryRecordList = liveRoomEntryRecordService.list(Wrappers.lambdaQuery(LiveRoomEntryRecord.class)
                .eq(LiveRoomEntryRecord::getSeatId, seatId)
                .eq(LiveRoomEntryRecord::getLiveRoomId, liveRoomId)
                .isNull(LiveRoomEntryRecord::getLeaveAt)
        );
        if (CollUtil.isNotEmpty(entryRecordList)) {
            return;
        }
        // 是否之前已进入过当前直播间
        boolean hasEnterBefore = liveRoomEntryRecordService.exists(Wrappers.lambdaQuery(LiveRoomEntryRecord.class)
                .eq(LiveRoomEntryRecord::getSeatId, seatId)
                .eq(LiveRoomEntryRecord::getLiveRoomId, liveRoomId)
        );

        // 保存新的记录
        LiveRoomEntryRecord entryRecord = new LiveRoomEntryRecord();
        entryRecord.setJoinAt(new Date());
        entryRecord.setCreatedAt(new Date());
        entryRecord.setOrgId(orgId);
        entryRecord.setSeatId(seatId);
        entryRecord.setLiveRoomId(liveRoomId);
        liveRoomEntryRecordService.save(entryRecord);

        // 更新实时人数缓存
        liveRoomCacheService.incrementRealtimeCount(liveRoomId, 1);
        liveRoomCacheService.addRoomUser(liveRoomId, seatId);
        log.info("userEnter消息处理");
        // 消息提醒，开启了潜水的年费会员不发消息，其他情况都发
        UserBuyerVipInfoVO vipInfo = buyerVipService.getUserBuyerVipInfoBySeatId(seatId);
        log.info("vipInfo:{}", JSONUtil.toJsonStr(vipInfo));
        if (vipInfo == null ||
                !vipInfo.getIsVip() ||
                !vipInfo.getIsAnnualFeeVip() ||
                !Boolean.TRUE.equals(vipInfo.getUnderwater())) {
            log.info("开始发送userEnter");
            msgService.userEnterRoom(liveRoomId, userId, seatId);
        }
        // 观看人次+1
        liveRoomCacheService.incrementViewCount(liveRoomId, 1);

        if(!hasEnterBefore){
            // 累计观看人数 +1
            liveRoomCacheService.incrementRealCount(liveRoomId, 1);
        }
    }

    @Override
    public boolean canHandle(ChatroomActionType type) {
        return type == ChatroomActionType.JOIN;
    }
}
