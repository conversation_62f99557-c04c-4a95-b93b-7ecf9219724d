package com.bbh.live.service.msg.dto.base;

import com.bbh.live.service.msg.MsgCacheService;
import org.springframework.data.redis.core.StringRedisTemplate;

import java.util.List;

/**
 * 消息查询处理接口
 * <AUTHOR>
 */
public interface IMsgRetrieveProcess {

    /**
     * 前置函数，如果返回true则继续执行，否则直接中断
     * @return true 继续
     */
    default boolean preRetrieve(StringRedisTemplate redisTemplate, MsgCacheService msgCacheService) {
        return true;
    }

    /**
     * 后置函数，消息存储完成后执行
     */
    default void postRetrieve(StringRedisTemplate redisTemplate, MsgCacheService msgCacheService, List<?> resultList) {}

}
