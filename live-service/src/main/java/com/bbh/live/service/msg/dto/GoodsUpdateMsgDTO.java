package com.bbh.live.service.msg.dto;

import com.bbh.live.core.msg.MsgType;
import com.bbh.live.service.msg.dto.base.BaseGoods;
import com.bbh.live.service.msg.dto.base.BaseMsg;
import com.bbh.live.service.msg.dto.base.IMsg;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 商品更新消息
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class GoodsUpdateMsgDTO extends BaseMsg implements IMsg {

    private Long liveRoomId;

    private BaseGoods goods;

    private Integer tradeType;

    /**
     * 每个消息对应一个类型
     *
     * @return {@link MsgType}
     */
    @Override
    public String type() {
        return MsgType.GOODS_UPDATE;
    }
}
