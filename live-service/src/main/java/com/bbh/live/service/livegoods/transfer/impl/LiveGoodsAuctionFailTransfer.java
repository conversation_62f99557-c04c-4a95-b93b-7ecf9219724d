package com.bbh.live.service.livegoods.transfer.impl;

import com.bbh.enums.LiveGoodsStatusEnum;
import com.bbh.live.dao.dto.livegoods.LiveGoodsDTO;
import com.bbh.live.service.livegoods.context.AuctionFailLiveGoodsContext;
import com.bbh.live.util.DepositUtils;
import com.bbh.model.LiveGoods;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * <AUTHOR> dsy
 * @Date: 2024/7/29
 * @Description:
 */
@Service("liveGoodsAuctionFailTransfer")
public class LiveGoodsAuctionFailTransfer extends AbstractLiveGoodsTransfer<AuctionFailLiveGoodsContext>{

    @Override
    protected void doCheck(LiveGoodsDTO liveGoods, AuctionFailLiveGoodsContext context) {

    }

    @Override
    protected void doAction(LiveGoodsDTO liveGoods, AuctionFailLiveGoodsContext context) {
        Date now = new Date();

        liveGoods.setGoodsStatus(LiveGoodsStatusEnum.ABORTIVE_AUCTION);
        liveGoods.setEndAt(now);
        //实际竞拍时长 = 结束竞拍时间 - 开始竞拍时间
        // 流拍 商品实际竞拍时长 = 竞拍持续时间
        liveGoods.setActualAuctionDuration(liveGoods.getAuctionDuration());

        LiveGoods updateGoods = new LiveGoods();
        updateGoods.setId(liveGoods.getId());
        updateGoods.setGoodsStatus(liveGoods.getGoodsStatus());
        updateGoods.setEndAt(liveGoods.getEndAt());
        updateGoods.setActualAuctionDuration(liveGoods.getActualAuctionDuration());
        liveGoodsDetailService.getLiveGoodsService().updateById(updateGoods);

        // 流拍数量加一
        liveRoomCacheService.incrementUnsoldCount(liveGoods.getLiveRoomId(), 1);
        // 发弹幕消息 商品已流拍
        msgService.goodsAbortedAuction(liveGoods.getLiveRoomId(), liveGoods.getId());

        // 成交异常 转流拍。需要解冻保证金
        if(context.isIfAbnormal()){
            DepositUtils.bidSuccessBackFrozen(liveGoods.getId(), null);
        }
    }
}
