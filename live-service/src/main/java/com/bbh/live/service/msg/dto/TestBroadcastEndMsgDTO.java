package com.bbh.live.service.msg.dto;

import com.bbh.live.core.msg.MsgType;
import com.bbh.live.service.msg.dto.base.BaseMsg;
import com.bbh.live.service.msg.dto.base.IMsg;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> dsy
 * @Date: 2024/9/4
 * @Description:
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class TestBroadcastEndMsgDTO extends BaseMsg implements IMsg {

    private String liveRoomName;

    private Long liveRoomId;

    @Override
    public String type() {
        return MsgType.TEST_BROADCAST_END;
    }
}
