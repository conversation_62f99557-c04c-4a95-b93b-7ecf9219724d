package com.bbh.live.service.livegoods.cache.redis;

import com.bbh.live.service.livegoods.cache.LiveGoodsCacheConstant;
import com.bbh.live.service.livegoods.cache.LiveGoodsInfoCacheService;
import com.bbh.live.service.livegoods.cache.info.LiveGoodsInfoCacheInfo;
import org.redisson.api.RBitSet;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR> dsy
 * @Date: 2024/7/27
 * @Description:
 *
 * redis 商品信息缓存 hash
 * key ： live_goods_info:roomId:${liveRoomId}
 *      hashKey : ${liveGoodsId}
 *      value: {
 *          goodsStatus: 1
 *      }
 *
 *  商品订阅信息缓存 bitset。
 *  key ：live_goods_subscribe:${liveRoomId}:${liveGoodsId}
 */
@Component
public class RedisLiveGoodsInfoCacheServiceImpl extends BaseRedisLiveGoodsCache implements LiveGoodsInfoCacheService {

    private static final ConcurrentHashMap<Long, Map<Long, RBitSet>> subscribeMap = new ConcurrentHashMap<>(64);

    @Override
    public boolean insertLiveGoodsInfo(Long liveRoomId, Long liveGoodsId, LiveGoodsInfoCacheInfo context) {
        String key = getKey(liveRoomId.toString());
        Long expireTime = getLiveRoomEndLeft(liveRoomId);
        redisHashService.putWithExpire(key, liveGoodsId.toString(), context, expireTime, TimeUnit.SECONDS);
        return true;
    }

    @Override
    public boolean removeLiveGoodsInfo(Long liveRoomId, Long liveGoodsId) {
        String key = getKey(liveRoomId.toString());
        redisHashService.remove(key, liveGoodsId.toString());
        return false;
    }

    @Override
    public LiveGoodsInfoCacheInfo getLiveGoodsInfo(Long liveRoomId, Long liveGoodsId) {
        String key = getKey(liveRoomId.toString());
        return redisHashService.get(key, liveGoodsId.toString(), LiveGoodsInfoCacheInfo.class);
    }

    @Override
    public void clearAll(Long liveRoomId) {
        //清理直播间所有商品缓存
        String key = getKey(liveRoomId.toString());
        redisHashService.remove(key);
        //清理订阅信息
        Map<Long, RBitSet> remove = subscribeMap.remove(liveRoomId);
        if (remove != null) {
            remove.forEach((k, v) -> v.delete());
        }
    }

    private String getKey(String liveRoomId){
        return builderKey(LiveGoodsCacheConstant.LIVE_GOODS_INFO_KEY, liveRoomId);
    }
}
