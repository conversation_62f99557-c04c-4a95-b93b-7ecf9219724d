package com.bbh.live.service.buyerCancel.dto;

import com.bbh.enums.GlobalOrderTypeEnum;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2025/4/2
 * @description:
 */
@Data
public class BuyerCancelRecordDTO implements BuyerCancelBiz {

   private Long buyerCancelRecordId;

    private Long orderId;

    private GlobalOrderTypeEnum bizType;

    @Override
    public GlobalOrderTypeEnum getBizType() {
        return this.bizType;
    }

    @Override
    public void setBizType(GlobalOrderTypeEnum bizType) {
        this.bizType=bizType;
    }

    public Long getBuyerCancelRecordId() {
        return null != orderId ? orderId : buyerCancelRecordId;
    }
}
