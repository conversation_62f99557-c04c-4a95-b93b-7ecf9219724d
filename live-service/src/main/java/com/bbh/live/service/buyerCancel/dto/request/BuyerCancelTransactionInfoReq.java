package com.bbh.live.service.buyerCancel.dto.request;

import com.bbh.enums.GlobalOrderTypeEnum;
import com.bbh.live.service.buyerCancel.dto.BuyerCancelBiz;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2025/4/2
 * @description:
 */
@Data
public class BuyerCancelTransactionInfoReq implements BuyerCancelBiz {

    private  Long liveGoodsId;


    private GlobalOrderTypeEnum bizType;

    @Override
    public GlobalOrderTypeEnum getBizType() {
        return this.bizType;
    }

    @Override
    public void setBizType(GlobalOrderTypeEnum bizType) {
        this.bizType=bizType;
    }
}
