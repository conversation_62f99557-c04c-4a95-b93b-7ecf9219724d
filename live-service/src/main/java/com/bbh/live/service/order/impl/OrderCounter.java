package com.bbh.live.service.order.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.bbh.enums.*;
import com.bbh.live.dao.dto.LiveShoppingCartDTO;
import com.bbh.live.dao.dto.QueryOrderCounterDTO;
import com.bbh.live.dao.dto.QueryShoppingCartDTO;
import com.bbh.live.dao.mapper.GlobalOrderItemMapper;
import com.bbh.live.dao.mapper.GlobalOrderSubMapper;
import com.bbh.live.dao.mapper.LiveGoodsBuyerCancelRecordMapper;
import com.bbh.live.dao.mapper.LiveGoodsMapper;
import com.bbh.model.GlobalOrderItem;
import com.bbh.model.LiveGoodsBuyerCancelRecord;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * 订单计数器
 * <AUTHOR>
 */
@Getter
@Slf4j
@Service
@AllArgsConstructor
public class OrderCounter {

    /**
     * 销售
     */
    public final Purchase purchase;

    /**
     * 采购
     */
    public final Sale sale;

    @Service
    @AllArgsConstructor
    public static class Purchase {

        private final GlobalOrderItemMapper globalOrderItemMapper;
        private final GlobalOrderSubMapper globalOrderSubMapper;
        private final LiveGoodsMapper liveGoodsMapper;
        private final LiveGoodsBuyerCancelRecordMapper liveGoodsBuyerCancelRecordMapper;

        /** 采购-收银台商品数量 */
        public Long getCashierProductCount(boolean ifOrg, Long seatId, Long orgId, Date updateAtStart) {
            QueryShoppingCartDTO query = new QueryShoppingCartDTO();
            query.setBizType(GlobalBizTypeEnum.LIVE.getCode());
            if (ifOrg) {
                query.setBuyerOrgId(orgId);
            } else {
                query.setBuyerSeatId(seatId);
            }
            query.setLiveSellTimeStart(updateAtStart);
            List<LiveShoppingCartDTO> shoppingCartList = liveGoodsMapper.selectLiveShoppingCartList(query);
            return shoppingCartList == null ? 0L : shoppingCartList.size();
        }

        /**
         * 待付款 10
         */
        public Long getPendingPaymentCount(boolean ifOrg, Long seatId, Long orgId, Date updateAtStart) {
            return getOrderSubCount(ifOrg, seatId, orgId, GlobalOrderStatusEnum.TO_BE_PAID, updateAtStart);
        }

        /**
         * 线下转账待审核 20
         */
        public Long getOfflineTransferPendingReviewCount(boolean ifOrg, Long seatId, Long orgId, Date updateAtStart) {
            return getOrderSubCount(ifOrg, seatId, orgId, GlobalOrderStatusEnum.TRANSFER_AUDIT, updateAtStart);
        }

        /**
         * 待发货 30
         */
        public Long getPendingShipmentCount(boolean ifOrg, Long seatId, Long orgId, Date updateAtStart) {
            return getOrderItemCount(ifOrg, seatId, orgId, GlobalOrderStatusEnum.TO_BE_DELIVERED, updateAtStart);
        }

        /**
         * 待收货数量 40
         */
        public Long getPendingReceiptCount(boolean ifOrg, Long seatId, Long orgId, Date updateAtStart) {
            return getOrderItemCount(ifOrg, seatId, orgId, GlobalOrderStatusEnum.TO_BE_RECEIVED, updateAtStart);
        }

        /**
         * 子订单数量
         */
        public Long getOrderSubCount(boolean ifOrg, Long seatId, Long orgId, GlobalOrderStatusEnum orderStatusEnum, Date updateAtStart) {
            return getOrderCount(false, ifOrg, seatId, orgId, orderStatusEnum, updateAtStart);
        }

        private Long getOrderCount(boolean isGetItem, boolean ifOrg, Long seatId, Long orgId, GlobalOrderStatusEnum orderStatusEnum, Date updateAtStart) {
            QueryOrderCounterDTO query = new QueryOrderCounterDTO()
                    .setOrderStatus(orderStatusEnum.getCode())
                    .setBizType(GlobalBizTypeEnum.LIVE.getCode());
            if (ifOrg) {
                query.setBuyerOrgId(orgId);
            } else {
                query.setBuyerSeatId(seatId);
            }
            // 如果是待付款，要查询未达到支付截止时间的，超过的不查询
            if (orderStatusEnum == GlobalOrderStatusEnum.TO_BE_PAID) {
                query.setLastPayAtGt(new Date());
            }
            // 如果有时间，要带上
            if (updateAtStart != null) {
                query.setUpdateAtGt(updateAtStart);
            }
            return isGetItem ? globalOrderItemMapper.countOrder(query) : globalOrderSubMapper.countOrder(query);
        }

        /**
         * 订单商品数量
         * @param ifOrg             是否商户
         * @param seatId            席位id
         * @param orgId             商户id
         * @param orderStatusEnum   订单状态
         * @return  订单数量
         */
        public Long getOrderItemCount(boolean ifOrg, Long seatId, Long orgId, GlobalOrderStatusEnum orderStatusEnum, Date updateAtStart) {
            return getOrderCount(true, ifOrg, seatId, orgId, orderStatusEnum, updateAtStart);
        }

        /**
         * 退款
         */
        public Long getPurchaseRefundCount(boolean ifOrg, Long seatId, Long orgId, Date updateAtStart) {
            return globalOrderItemMapper.selectCount(Wrappers.lambdaQuery(GlobalOrderItem.class)
                    .select(GlobalOrderItem::getId)
                    .eq(!ifOrg, GlobalOrderItem::getBuyerSeatId, seatId)
                    .eq(ifOrg, GlobalOrderItem::getBuyerOrgId, orgId)
                    .eq(GlobalOrderItem::getBizType, GlobalBizTypeEnum.LIVE)
                    .gt(updateAtStart != null, GlobalOrderItem::getUpdatedAt, updateAtStart)
                    .in(GlobalOrderItem::getRefundStatus, GlobalOrderItemRefundStatusEnum.BUYER_REFUND, GlobalOrderItemRefundStatusEnum.SELLER_AGREE_REFUND, GlobalOrderItemRefundStatusEnum.BUYER_DELIVERED)
            );
        }

        /**
         * 取消成交处理中的商品数量
         */
        public Long getGoodsCancelCount(boolean ifOrg, Long seatId, Long orgId) {
            return liveGoodsBuyerCancelRecordMapper.selectCount(Wrappers.lambdaQuery(LiveGoodsBuyerCancelRecord.class)
                    .select(LiveGoodsBuyerCancelRecord::getId)
                    .eq(ifOrg, LiveGoodsBuyerCancelRecord::getBuyerOrgId, orgId)
                    .eq(!ifOrg, LiveGoodsBuyerCancelRecord::getBuyerSeatId, seatId)
                    .ne(LiveGoodsBuyerCancelRecord::getCancelStatus, LiveGoodsBuyerCancelRecordStatusEnum.COMPLETED)
            );
        }

        /**
         * 采购，取消成交跳转路由状态
         *
         */
        @SuppressWarnings("all")
        public LiveGoodsBuyerCancelRecordStatusEnum getGoodsCancelRouteStatus(boolean ifOrg, Long seatId, Long orgId) {
            // 先查出来所有的列表，后面根据状态进行不同的统计
            List<LiveGoodsBuyerCancelRecord> cancelRecordList = liveGoodsBuyerCancelRecordMapper.selectList(Wrappers.lambdaQuery(LiveGoodsBuyerCancelRecord.class)
                    .eq(ifOrg, LiveGoodsBuyerCancelRecord::getBuyerOrgId, orgId)
                    .eq(!ifOrg, LiveGoodsBuyerCancelRecord::getBuyerSeatId, seatId)
            );

            // 优先判断「待商家处理」
            long waitSellerProcessCount = cancelRecordList.stream()
                    .filter(record -> record.getCancelStatus() == LiveGoodsBuyerCancelRecordStatusEnum.WAITING_SELLER)
                    .count();
            // 如果有「待商家处理」，那路由到待商家处理
            if (waitSellerProcessCount > 0) {
                return LiveGoodsBuyerCancelRecordStatusEnum.WAITING_SELLER;
            }

            // 其次判断「平台审核中」
            long platformReviewingCount = cancelRecordList.stream()
                    .filter(record -> record.getCancelStatus() == LiveGoodsBuyerCancelRecordStatusEnum.PLATFORM_REVIEWING)
                    .count();
            // 如果有「平台审核中」，那路由到平台审核中
            if (platformReviewingCount > 0) {
                return LiveGoodsBuyerCancelRecordStatusEnum.PLATFORM_REVIEWING;
            }

            // 以上都没有，跳转到「已完成」
            return LiveGoodsBuyerCancelRecordStatusEnum.COMPLETED;
        }

    }

    @Service
    @AllArgsConstructor
    public static class Sale {

        private final GlobalOrderItemMapper globalOrderItemMapper;
        private final GlobalOrderSubMapper globalOrderSubMapper;
        private final LiveGoodsMapper liveGoodsMapper;
        private final LiveGoodsBuyerCancelRecordMapper liveGoodsBuyerCancelRecordMapper;

        /** 收银台商品数量 */
        public Long getCashierProductCount(Long orgId, Date updateAtStart) {
            QueryShoppingCartDTO query = new QueryShoppingCartDTO();
            query.setBizType(GlobalBizTypeEnum.LIVE.getCode());
            query.setSellerOrgId(orgId);
            query.setLiveSellTimeStart(updateAtStart);
            List<LiveShoppingCartDTO> shoppingCartList = liveGoodsMapper.selectLiveShoppingCartList(query);
            return shoppingCartList == null ? 0L : shoppingCartList.size();
        }

        /**
         * 待付款 10
         */
        public Long getPendingPaymentCount(Long orgId, Date updateAtStart) {
            return getOrderSubCount(orgId, GlobalOrderStatusEnum.TO_BE_PAID, updateAtStart);
        }

        /** 线下转账待审核数量 20 */
        public Long getOfflineTransferPendingReviewCount(Long orgId, Date updateAtStart) {
            return getOrderSubCount(orgId, GlobalOrderStatusEnum.TRANSFER_AUDIT, updateAtStart);
        }

        /** 待发货数量 */
        public Long getPendingShipmentCount(Long orgId, Date updateAtStart) {
            return getOrderItemCount(orgId, GlobalOrderStatusEnum.TO_BE_DELIVERED, updateAtStart);
        }

        /** 待收货数量 */
        public Long getPendingReceiptCount(Long orgId, Date updateAtStart) {
            return getOrderItemCount(orgId, GlobalOrderStatusEnum.TO_BE_RECEIVED, updateAtStart);
        }

        /** 退款数量 */
        public Long getSaleRefundCount(Long orgId, Date updateAtStart) {
            return globalOrderItemMapper.selectCount(Wrappers.lambdaQuery(GlobalOrderItem.class)
                    .select(GlobalOrderItem::getId)
                    .eq(GlobalOrderItem::getSellerOrgId, orgId)
                    .eq(GlobalOrderItem::getBizType, GlobalBizTypeEnum.LIVE)
                    .gt(updateAtStart != null, GlobalOrderItem::getUpdatedAt, updateAtStart)
                    .in(GlobalOrderItem::getRefundStatus, GlobalOrderItemRefundStatusEnum.BUYER_REFUND, GlobalOrderItemRefundStatusEnum.SELLER_AGREE_REFUND, GlobalOrderItemRefundStatusEnum.BUYER_DELIVERED)
            );
        }

        /**
         * 子订单数量
         */
        public Long getOrderSubCount(Long orgId, GlobalOrderStatusEnum orderStatusEnum, Date updateAtStart) {
            return getOrderCount(false, orgId, orderStatusEnum, updateAtStart);
        }

        private Long getOrderCount(boolean isGetItem, Long orgId, GlobalOrderStatusEnum orderStatusEnum, Date updateAtStart) {
            QueryOrderCounterDTO query = new QueryOrderCounterDTO()
                    .setOrderStatus(orderStatusEnum.getCode())
                    .setBizType(GlobalBizTypeEnum.LIVE.getCode());
            query.setSellerOrgId(orgId);
            // 如果是待付款，要查询未达到支付截止时间的，超过的不查询
            if (orderStatusEnum == GlobalOrderStatusEnum.TO_BE_PAID) {
                query.setLastPayAtGt(new Date());
            }
            // 如果有时间，要带上
            if (updateAtStart != null) {
                query.setUpdateAtGt(updateAtStart);
            }
            return isGetItem ? globalOrderItemMapper.countOrder(query) : globalOrderSubMapper.countOrder(query);
        }

        /**
         * 订单商品数量
         * @param orgId             商户id
         * @param orderStatusEnum   订单状态
         * @return  订单数量
         */
        public Long getOrderItemCount(Long orgId, GlobalOrderStatusEnum orderStatusEnum, Date updateAtStart) {
            return getOrderCount(true, orgId, orderStatusEnum, updateAtStart);
        }

        /**
         * 取消成交，等待商家处理的商品数量
         */
        public Long getGoodsCancelCount(Long orgId) {
            return liveGoodsBuyerCancelRecordMapper.selectCount(Wrappers.lambdaQuery(LiveGoodsBuyerCancelRecord.class)
                    .select(LiveGoodsBuyerCancelRecord::getId)
                    .eq(LiveGoodsBuyerCancelRecord::getSellerOrgId, orgId)
                    .eq(LiveGoodsBuyerCancelRecord::getCancelStatus, LiveGoodsBuyerCancelRecordStatusEnum.WAITING_SELLER)
            );
        }

        /**
         * 取消成交，等待商家处理的商品路由状态
         */
        @SuppressWarnings("all")
        public LiveGoodsBuyerCancelRecordStatusEnum getGoodsCancelRouteStatus(Long orgId) {
            // 先全部查出来，然后再判断
            List<LiveGoodsBuyerCancelRecord> cancelRecordList = liveGoodsBuyerCancelRecordMapper.selectList(Wrappers.lambdaQuery(LiveGoodsBuyerCancelRecord.class)
                    .select(LiveGoodsBuyerCancelRecord::getCancelStatus)
                    .eq(LiveGoodsBuyerCancelRecord::getSellerOrgId, orgId)
            );

            // 如果有商家待处理，就跳商家待处理
            long waitingSellerCount = cancelRecordList.stream().filter(record -> record.getCancelStatus() == LiveGoodsBuyerCancelRecordStatusEnum.WAITING_SELLER).count();
            if (waitingSellerCount > 0) {
                return LiveGoodsBuyerCancelRecordStatusEnum.WAITING_SELLER;
            }
            // 其次看平台审核中
            long platformReviewingCount = cancelRecordList.stream().filter(record -> record.getCancelStatus() == LiveGoodsBuyerCancelRecordStatusEnum.PLATFORM_REVIEWING).count();
            if (platformReviewingCount > 0) {
                return LiveGoodsBuyerCancelRecordStatusEnum.PLATFORM_REVIEWING;
            }
            // 都没有，跳已完成
            return LiveGoodsBuyerCancelRecordStatusEnum.COMPLETED;
        }
    }

}
