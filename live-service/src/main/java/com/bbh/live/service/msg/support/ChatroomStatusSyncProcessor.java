package com.bbh.live.service.msg.support;

import com.bbh.live.dao.dto.ChatroomStatusSyncDTO;
import com.bbh.live.service.msg.notify.chatroom.StatusProcessorFactory;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 聊天室状态同步
 * <AUTHOR>
 */
@Service
@AllArgsConstructor
@Slf4j
public class ChatroomStatusSyncProcessor {

    private final StatusProcessorFactory statusProcessorFactory;

    /**
     * 批量处理聊天室状态同步
     * @param chatroomStatusSyncDTOList 聊天室状态同步DTO列表
     */
    public void processStatusSync(List<ChatroomStatusSyncDTO> chatroomStatusSyncDTOList) {
        for (ChatroomStatusSyncDTO dto : chatroomStatusSyncDTOList) {
            processStatusSync(dto);
        }
    }

    /**
     * 处理聊天室状态同步
     */
    private void processStatusSync(ChatroomStatusSyncDTO chatroomStatusSyncDTO) {
        // 根据聊天室状态获取对应的处理器并进行处理
        statusProcessorFactory.getProcessor(chatroomStatusSyncDTO.getStatus()).process(chatroomStatusSyncDTO);
    }

}
