package com.bbh.live.service.msg.dto;

import cn.hutool.core.date.DatePattern;
import com.bbh.live.core.msg.MsgType;
import com.bbh.live.service.msg.dto.base.BaseMsg;
import com.bbh.live.service.msg.dto.base.IMsg;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 直播结束的消息
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
public class LiveEndMsgDTO extends BaseMsg implements IMsg {
    /**
     * 每个消息对应一个类型
     *
     * @return {@link MsgType}
     */
    @Override
    public String type() {
        return MsgType.END_LIVE;
    }

    private String liveRoomName;

    private Long liveRoomId;

    /** 直播间状态 */
    private Integer roomStatus;

    /**
     * 实际的开播时间
     */
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN, timezone = "GMT+8")
    private Date actualStartAt;

    /**
     * 实际的关播时间
     */
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN, timezone = "GMT+8")
    private Date actualEndAt;

    /**
     * 实际的商品清单结束时间
     */
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN, timezone = "GMT+8")
    private Date actualGoodsListEndAt;

    /**
     * 设置的开播时间
     */
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN, timezone = "GMT+8")
    private Date startAt;

    /**
     * 设置的关播时间
     */
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN, timezone = "GMT+8")
    private Date endAt;

    /**
     * 商品列表截止时间
     */
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN, timezone = "GMT+8")
    private Date goodsListExpiredAt;

    /**
     * 设置的商品清单保持时长<br>
     * 单位:(小时)
     */
    private Integer goodsListDuration;
}
