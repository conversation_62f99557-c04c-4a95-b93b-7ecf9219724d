package com.bbh.live.service.livegoods.context;

import com.bbh.live.enums.LiveGoodsTradeCompletedTypeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <AUTHOR> dsy
 * @Date: 2024/7/29
 * @Description:
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
public class TradeLiveGoodsContext extends LiveGoodsContext{

    /**
     * 当前竞拍最终价格
     */
    private BigDecimal finalPrice;

    /**
     * 成交人席位id
     */
    private Long buyerSeatId;

    /**
     * 成交方式：0-竞拍，1-议价，2-传送
     */
    private LiveGoodsTradeCompletedTypeEnum tradeType;

}
