package com.bbh.live.service.organization.resource;

import com.bbh.base.ListBase;
import com.bbh.live.service.organization.dto.OrgAuctionNumberResourcesDTO;
import com.bbh.live.service.organization.dto.OrgVipResourcesDTO;

/**
 * <AUTHOR>
 * @date 2024/9/19 20:18
 * @description
 */
public interface OrgResourcesService {

    /**
     * 获取店铺所有拍号资源
     * @param orgId 店铺id
     * @return ListBase 店铺拍号列表
     */
    ListBase<OrgAuctionNumberResourcesDTO> getAuctionNumberResources(Long orgId);

    /**
     * 给店铺下的员工绑定席位
     * @param auctionNumber 拍号
     * @param seatId 员工席位 id
     */
    void bindAuctionNumber(String auctionNumber, Long seatId);

    /**
     * 解绑拍号
     * @param auctionNumber 拍号
     */
    void unbindAuctionNumber(String auctionNumber);

    /**
     * 换绑拍号
     * @param auctionNumber 拍号
     * @param newSeatId     新席位 id
     */
    void changeBindAuctionNumber(String auctionNumber, Long newSeatId);

    /**
     * 获取店铺所有vip资源
     * @param orgId 店铺 id
     * @return ListBase vip列表
     */
    ListBase<OrgVipResourcesDTO> getVipResources(Long orgId);

    /**
     *  给店铺下的员工绑定vip
     * @param vipId vip id
     * @param seatId 席位 id
     */
    void bindVip(Long vipId, Long seatId);

    /**
     * 解绑vip
     * @param vipId vip id
     */
    void unbindVip(Long vipId);

}
