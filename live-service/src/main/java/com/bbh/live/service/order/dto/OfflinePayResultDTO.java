package com.bbh.live.service.order.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class OfflinePayResultDTO {

    private BankInfo bankInfo;
    private OrderInfo orderInfo;
    private List<String> offlineTransferImgList = List.of();

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Accessors(chain = true)
    public static class BankInfo {
        private String payeeName;
        private String payeeCard;
        private String revBankName;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Accessors(chain = true)
    public static class OrderInfo {
        private Long orderId;
        private String orderNo;
        private BigDecimal needPayAmount;
    }

}
