package com.bbh.live.service.order.dto;

import com.bbh.enums.GlobalPayTypeEnum;
import com.bbh.live.dao.dto.AggregatedUserDTO;
import com.bbh.live.dao.dto.livegoods.LiveGoodsDTO;
import com.bbh.model.GlobalOrgSettlementBank;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;

/**
 * 订单构建上下文
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class OrderBuilderContext {

    /**
     * 前端传参：阿里支付账号ID
     */
    private String aliPayUserId;

    /**
     * 前端传参：微信OPEN_ID
     */
    private String wxOpenId;

    /**
     * 支付方式：1微信 ,2支付宝，3线下
     */
    private GlobalPayTypeEnum payType;

    /**
     * 收货地址
     */
    private Long receiveAddressId;

    /**
     * 收货地址补充信息 收货地址信息
     */
    private String receiveAddressInfo;

    /**
     * 是否使用分贝抵扣通道费，默认不使用
     */
    private Boolean ifUseFenbei = Boolean.FALSE;

    /**
     * 商品ID列表
     */
    private List<Long> liveGoodsIdList;

    /**
     * 商品列表，在检查商品状态时写入
     */
    private List<LiveGoodsDTO> liveGoodsList;

    /**
     * 聚合的买手信息
     */
    private AggregatedUserDTO buyerUser;

    /**
     * 订单流水号
     */
    private String orderNo;

    /**
     * 订单ID
     */
    private Long globalOrderId;

    /**
     * 调起支付的流水号，和order_no不同
     */
    private String paymentTradeNo;

    /**
     * 商户的结算账号，在初始化检查是写入，在调起中台支付时使用
     */
    private List<GlobalOrgSettlementBank> orgSettlementBankList;

    private Integer ifQualityInspect;

    private Long vipDeductionId;
    /**
     * 渠道费总金额
     */
    private BigDecimal totalChannelAmount;
}
