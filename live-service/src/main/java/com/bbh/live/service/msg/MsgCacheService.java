package com.bbh.live.service.msg;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONUtil;
import com.bbh.live.constant.RedisKey;
import com.bbh.live.enums.MsgCacheKeys;
import com.bbh.live.handler.queue.DelayJob;
import com.bbh.live.handler.queue.DelayQueueManager;
import com.bbh.live.service.msg.dto.base.*;
import com.bbh.live.util.EnvironmentUtils;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

/**
 * 消息缓存
 *
 * <AUTHOR>
 */
@Service
@AllArgsConstructor
@Slf4j
public class MsgCacheService implements RedisKey {

    private final StringRedisTemplate redisTemplate;

    private static final Map<Class<?>, IMsgRetrieveProcess> RETRIEVE_PROCESS_MAP = new ConcurrentHashMap<>();

    public String buildMsgKey(String uniqueKey) {
        return buildKey(MsgCacheKeys.MSG.getKey(), uniqueKey);
    }

    public String buildMsgExpiryKey(String uniqueKey) {
        return buildKey(MsgCacheKeys.MSG_EXPIRY.getKey(), uniqueKey);
    }

    /**
     * 存储带有过期时间的消息
     * @param expiredAt 过期时间
     * @param t         消息体
     * @param <T>       消息类型
     */
    public <T extends IMsg> void saveExpiringMessage(Date expiredAt, T t) {
        // 前置执行
        if (t instanceof IMsgStoreProcess) {
            boolean ifProcess = ((IMsgStoreProcess) t).preStore(redisTemplate, this);
            // 如果前置函数的返回值是false，就直接中断了
            if (!ifProcess) {
                return;
            }
        }

        List<String> uniqueKeys;
        if (t instanceof IMultiExpiringMsg) {
            uniqueKeys = ((IMultiExpiringMsg) t).uniqueKeys();
        } else if (t instanceof IExpiringMsg) {
            uniqueKeys = Collections.singletonList(((IExpiringMsg) t).uniqueKey());
        } else {
            throw new IllegalArgumentException("Message must implement IMsg or MultiKeyMessage");
        }

        String messageJson = JSONUtil.toJsonStr(t);
        for (String uniqueKey : uniqueKeys) {
            saveExpiringMessage(t.type(), uniqueKey, expiredAt.getTime() + "", expiredAt, messageJson);
        }

        // 后置函数
        if (t instanceof IMsgStoreProcess) {
            ((IMsgStoreProcess) t).postStore(redisTemplate, this);
        }
    }

    public void saveExpiringMessage(String msgType, String uniqueKey, String hashKey, Date expiredAt, String messageJson) {
        String messageHashKey = buildMsgKey(uniqueKey);
        String expiryKey = buildMsgExpiryKey(uniqueKey);
        long expiryTimestamp = expiredAt.getTime();

        // 存储消息
        redisTemplate.opsForHash().put(messageHashKey, hashKey, messageJson);

        // 存储过期时间
        redisTemplate.opsForZSet().add(expiryKey, hashKey, expiryTimestamp);

        // 设置最终的过期时间逻辑
        setFinalExpiryTime(messageHashKey, expiryKey);

        // 启动延迟队列
        DelayJob<String> delayJob = new DelayJob<>();
        delayJob.setData(messageJson);
        delayJob.setDelay(expiryTimestamp - System.currentTimeMillis());
        delayJob.setTimeUnit(TimeUnit.MILLISECONDS);
        delayJob.setTopic(EnvironmentUtils.getActiveProfile() + msgType);
        delayJob.setJobId(hashKey);
        SpringUtil.getBean(DelayQueueManager.class).addToQueue(delayJob);
    }

    /**
     * 设置最终的过期时间，避免消息长时间不销毁
     * 取最大的过期时间追加5分钟
     * @param messageHashKey    消息的hash key
     * @param expiryKey         过期时间的zSet key
     */
    private void setFinalExpiryTime(String messageHashKey, String expiryKey) {
        Set<String> lastMessage = redisTemplate.opsForZSet().reverseRange(expiryKey, 0, 0);
        if (CollUtil.isNotEmpty(lastMessage)) {
            Double maxScore = redisTemplate.opsForZSet().score(expiryKey, lastMessage.iterator().next());
            if (maxScore != null) {
                // 设置最终的过期时间，追加5分钟
                long newExpiry = maxScore.longValue() + TimeUnit.MINUTES.toMillis(5);
                long ttl = newExpiry - System.currentTimeMillis();
                if (ttl > 0) {
                    redisTemplate.expire(messageHashKey, ttl, TimeUnit.MILLISECONDS);
                    redisTemplate.expire(expiryKey, ttl, TimeUnit.MILLISECONDS);
                }
            }
        }
    }

    /**
     * 获取指定唯一标识的消息，过期的消息不参与，默认按创建时间升序
     * @param uniqueKey 消息唯一标识
     * @param clazz     消息类型
     * @return          消息体
     * @param <T>       消息类型
     */
    public <T> List<T> getActiveMessages(String uniqueKey, Class<T> clazz) {
        return getActiveMessages(uniqueKey, clazz, true);
    }

    /**
     * 获取指定唯一标识的消息，过期的消息不参与
     * @param uniqueKey 消息唯一标识
     * @param clazz     消息类型
     * @param isAsc    是否按创建时间的升序
     * @return          消息体
     * @param <T>       消息类型
     */
    public <T> List<T> getActiveMessages(String uniqueKey, Class<T> clazz, boolean isAsc) {
        IMsgRetrieveProcess process = getOrCreateRetrieveProcess(clazz);
        if (process != null) {
            boolean ifProcess = process.preRetrieve(redisTemplate, this);
            if (!ifProcess) {
                return List.of();
            }
        }

        String messageKey = buildMsgKey(uniqueKey);
        String expiryKey = buildMsgExpiryKey(uniqueKey);
        long currentTime = System.currentTimeMillis();

        // 获取未过期的HashKey
        Set<String> activeMessageIds = isAsc ?
                // 按分数升序
                redisTemplate.opsForZSet().rangeByScore(expiryKey, currentTime, Double.POSITIVE_INFINITY) :
                // 按分数降序
                redisTemplate.opsForZSet().reverseRangeByScore(expiryKey, currentTime, Double.POSITIVE_INFINITY);

        // 删除过期的消息
        Set<String> expiredMessageIds = redisTemplate.opsForZSet().rangeByScore(expiryKey, Double.NEGATIVE_INFINITY, currentTime);
        if (CollUtil.isNotEmpty(expiredMessageIds)) {
            redisTemplate.opsForHash().delete(messageKey, expiredMessageIds.toArray());
            redisTemplate.opsForZSet().removeRangeByScore(expiryKey, Double.NEGATIVE_INFINITY, currentTime);
        }

        if (activeMessageIds == null || activeMessageIds.isEmpty()) {
            return new ArrayList<>(0);
        }

        // 获取活跃消息的内容，返回的对象列表的顺序与传入的 ID 列表 activeMessageIds 的顺序是对应的
        List<Object> objects = redisTemplate.opsForHash().multiGet(messageKey, Arrays.asList(activeMessageIds.toArray()));
        List<T> result = objects.stream().map(x -> {
            if (x == null) {
                return null;
            }
            return JSONUtil.toBean(x.toString(), clazz);
        }).toList();

        // 查询后置执行函数
        if (process != null) {
            process.postRetrieve(redisTemplate, this, result);
        }
        return result;
    }

    private <T> IMsgRetrieveProcess getOrCreateRetrieveProcess(Class<T> clazz) {
        return RETRIEVE_PROCESS_MAP.computeIfAbsent(clazz, this::createRetrieveProcess);
    }

    private IMsgRetrieveProcess createRetrieveProcess(Class<?> clazz) {
        // 这里实现创建 IMsgRetrieveProcess 实例的逻辑
        // 可以使用反射、工厂方法或其他方式来创建实例
        // 如果无法创建或者该类型不需要处理器，返回 null
        if (IMsgRetrieveProcess.class.isAssignableFrom(clazz)) {
            try {
                return (IMsgRetrieveProcess) clazz.getDeclaredConstructor().newInstance();
            } catch (Exception e) {
                log.error("Error creating retrieve process for class: {}", clazz.getName(), e);
            }
        }
        return null;
    }

    /**
     * 获取指定唯一标识的所有活跃消息，支持 MultiKey
     * @param uniqueKeys 消息唯一标识列表
     * @param clazz      消息类型
     * @return           消息体列表
     * @param <T>        消息类型
     */
    public <T> List<T> getActiveMessagesForMultiKey(List<String> uniqueKeys, Class<T> clazz) {
        List<T> result = new ArrayList<>();
        for (String uniqueKey : uniqueKeys) {
            result.addAll(getActiveMessages(uniqueKey, clazz));
        }
        return result;
    }

    /**
     * 获取指定唯一标识的消息，过期的消息不参与
     * @param uniqueKey 消息唯一标识
     * @param clazz     消息类型
     * @return          消息体
     * @param <T>       消息类型
     */
    public <T> Optional<T> getActiveMessage(String uniqueKey, Class<T> clazz) {
        List<T> list = getActiveMessages(uniqueKey, clazz);
        if (CollUtil.isEmpty(list)) {
            return Optional.empty();
        }
        return Optional.of(list.getFirst());
    }

    /**
     * 删除指定类型的所有消息，不检查是否过期，直接删除
     * @param uniqueKey 消息唯一标识
     */
    public void remove(String uniqueKey) {
        Set<String> messageIds = redisTemplate.opsForZSet().rangeByScore(uniqueKey, Double.NEGATIVE_INFINITY, Double.POSITIVE_INFINITY);
        if (CollUtil.isEmpty(messageIds)) {
            return;
        }
        redisTemplate.opsForHash().delete(uniqueKey, messageIds.toArray());
        redisTemplate.opsForZSet().removeRangeByScore(uniqueKey, Double.NEGATIVE_INFINITY, Double.POSITIVE_INFINITY);
    }

    /**
     * 删除指定类型中的特定消息，不检查是否过期，直接删除
     * @param uniqueKey 消息唯一标识
     * @param value     消息ID
     */
    public void remove(String uniqueKey, String value) {
        redisTemplate.opsForHash().delete(buildMsgKey(uniqueKey), value);
        redisTemplate.opsForZSet().remove(buildMsgExpiryKey(uniqueKey), value);
    }

}
