package com.bbh.live.service.msg.push;

import com.alibaba.fastjson2.JSONObject;
import com.bbh.feign.IGlobalApiTaskClient;
import com.bbh.feign.dto.LivePushDTO2;
import com.bbh.live.dao.service.ErpOrgMessageService;
import com.bbh.live.service.msg.dto.PushMsgDTO;
import com.bbh.live.util.JSONUtils;
import com.bbh.model.ErpOrgMessage;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024/10/23 13:55
 * @description
 */
@Slf4j
@Component
@AllArgsConstructor
public class AppMessagePushService {

    private final IGlobalApiTaskClient iGlobalApiTaskClient;
    private final ErpOrgMessageService erpOrgMessageService;
    private final ObjectMapper objectMapper;


    public void pushMessage(PushMsgDTO pushMsg){
        try {
            // 保存推送日志
            ErpOrgMessage erpOrgMessage = erpOrgMessageService.create(pushMsg);

            LivePushTypeEnum pushType = pushMsg.getPushType();
            LivePushDTO2 livePushDTO = new LivePushDTO2();
            livePushDTO.setTitle(pushType.getTitle());
            livePushDTO.setContent(pushType.getContent(pushMsg.getContentArgs()));
            livePushDTO.setSeatId(pushMsg.getSeatId());
            livePushDTO.setOrgId(pushMsg.getOrgId());
            livePushDTO.setLiveRoomId(pushMsg.getLiveRoomId());
            livePushDTO.setType(pushType.getType());
            livePushDTO.setGoodsImage(pushMsg.getGoodsImage());
            livePushDTO.setAction(6);
            livePushDTO.setWgtSkipPath(pushType.getPath());
            livePushDTO.setWgtPageParams(JSONObject.toJSONString(pushMsg.getParams()));
            livePushDTO.setAfterOpen(pushType.getAfterOpen());
            // 用下划线
            erpOrgMessage.setMessageInfo(JSONUtils.toUnderlineJsonStr(livePushDTO));
            erpOrgMessageService.save(erpOrgMessage);
            livePushDTO.setId(erpOrgMessage.getId());
            iGlobalApiTaskClient.livePush2(livePushDTO);
        }catch (Exception e){
            log.error("Error sending push message, type: {}, error : {}", pushMsg.getPushType().name(), e.getMessage(), e);
        }

    }
}
