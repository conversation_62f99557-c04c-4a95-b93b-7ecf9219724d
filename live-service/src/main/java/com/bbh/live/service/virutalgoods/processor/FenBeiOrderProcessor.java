package com.bbh.live.service.virutalgoods.processor;

import com.bbh.live.dao.service.GlobalFenbeiDetailService;
import com.bbh.live.dao.service.IGlobalOrganizationService;
import com.bbh.live.service.buyer.vip.vo.UserBuyerVipInfoVO;
import com.bbh.model.GlobalOrganization;
import com.bbh.model.GlobalVirtualGoodsOrder;
import com.bbh.vo.Result;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR> dsy
 * @Date: 2024/9/12
 * @Description:
 */
@Service
@AllArgsConstructor
public class FenBeiOrderProcessor implements IVirtualOrderProcessor{

    private final IGlobalOrganizationService globalOrganizationService;
    private final GlobalFenbeiDetailService globalFenbeiDetailService;

    @Override
    public Result process(VirtualOrderProcessContext context) {
        // 虚拟商品
        GlobalVirtualGoodsOrder order = context.getOrder();

        // 分贝数量
        Integer fbNumber = order.getGoodsNumber();

        UserBuyerVipInfoVO vipInfo = context.getVipInfo();
        globalOrganizationService.lambdaUpdate().eq(GlobalOrganization::getId, order.getBuyerOrgId())
                .setIncrBy(GlobalOrganization::getFenbei, fbNumber)
                .update();

        // 分贝变更明细
        globalFenbeiDetailService.savePayFenbeiDetail(order, globalOrganizationService.getOrgFenbeiNumber(vipInfo.getOrgId()));
        return Result.ok();
    }
}
