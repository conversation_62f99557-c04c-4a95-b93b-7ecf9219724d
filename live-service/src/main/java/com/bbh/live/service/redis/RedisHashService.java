package com.bbh.live.service.redis;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR> dsy
 * @Date: 2024/7/26
 * @Description:
 */
public interface RedisHashService {

    <T> void put(String key, String hashKey, T value);

    <T> void putWithExpire(String key, String hashKey, T value, long expire, TimeUnit timeUnit);

    <T> void putWithExpireAt(String key, String hashKey, T value, Date expireAt);

    <T> T get(String key, String hashKey, Class<T> clazz);

    <T> List<T> getList(String key, String hashKey, Class<T> clazz);

    <T> Map<String, T> entities(String key, Class<T> clazz);

    void remove(String key, String hashKey);

    boolean remove(String key);
}
