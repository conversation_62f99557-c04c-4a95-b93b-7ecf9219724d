package com.bbh.live.service.virutalgoods.processor;

import cn.hutool.extra.spring.SpringUtil;
import com.bbh.enums.GlobalVirtualGoodsSourceEnum;
import com.bbh.model.GlobalOrderOtherGoodsDic;
import com.bbh.model.GlobalOrgSeat;
import com.bbh.util.LogExUtil;
import com.bbh.vo.Result;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR> dsy
 * @Date: 2024/9/12
 * @Description:
 */
@Service
public class CompositeOrderProcessor implements IVirtualOrderProcessor {
    /**
     * 前置处理方法，用于拦截器校验
     *
     * @param buyerSeat    买手信息
     * @param virtualGoods 虚拟商品信息
     * @return 是否允许继续执行
     */
    @Override
    public boolean preProcess(GlobalOrgSeat buyerSeat, GlobalOrderOtherGoodsDic virtualGoods) {
        return getRealOrderProcessor(virtualGoods.getGoodsSource()).preProcess(buyerSeat, virtualGoods);
    }

    @Override
    public Result process(VirtualOrderProcessContext context) {
        try {
            return getRealOrderProcessor(context.getGoods().getGoodsSource()).process(context);
        }catch (RuntimeException e){
            LogExUtil.errorLog("虚拟商品订单支付完成回调失败，原因：" + e.getMessage(), e);
            return Result.fail(e.getMessage());
        }
    }

    private IVirtualOrderProcessor getRealOrderProcessor(GlobalVirtualGoodsSourceEnum goodsSource) {
        return switch (goodsSource) {
            // 购买和续费会员
            case BUYER_VIP -> SpringUtil.getBean(BuyerVipOrderProcessor.class);
            // 购买分贝
            case FEN_BEI -> SpringUtil.getBean(FenBeiOrderProcessor.class);
            // 买家保证金充值
            case BOND_BUYER -> SpringUtil.getBean(RechargeDepositOrderProcessor.class);
            // 卖家保证金充值
            case BOND_SELLER -> SpringUtil.getBean(ContractOrderProcessor.class);
            // 违规扣款-补缴
            case BOND_OF -> SpringUtil.getBean(LateFeeDepositOrderProcessor.class);
            default -> throw new RuntimeException("暂不支持该商品类型");
        };
    }
}
