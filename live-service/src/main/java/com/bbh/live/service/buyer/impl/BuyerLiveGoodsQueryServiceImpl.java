package com.bbh.live.service.buyer.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bbh.base.ListBase;
import com.bbh.enums.ErpGoodsSaleStatusEnum;
import com.bbh.enums.LiveGoodsStatusEnum;
import com.bbh.enums.LiveRoomInteractiveMessageTypeEnum;
import com.bbh.exception.ServiceException;
import com.bbh.live.config.LiveBizProperties;
import com.bbh.live.controller.req.LiveGoodsQueryReq;
import com.bbh.live.controller.req.PlaybackGoodsQueryReq;
import com.bbh.live.dao.dto.AbortiveAuctionGoodsDTO;
import com.bbh.live.dao.dto.AuctionFailGoodsListDTO;
import com.bbh.live.dao.dto.BuyerLiveGoodsDTO;
import com.bbh.live.dao.dto.LiveRoomAuctionFailDTO;
import com.bbh.live.dao.dto.livegoods.LiveGoodsDTO;
import com.bbh.live.dao.dto.livegoods.LiveGoodsStatisticsDTO;
import com.bbh.live.dao.dto.vo.BuyerLiveGoodsStatisticsDTO;
import com.bbh.live.dao.dto.vo.LiveRoomPlaybackGoodsVO;
import com.bbh.live.dao.dto.vo.PlayBackLiveRoomVO;
import com.bbh.live.dao.mapper.LiveGoodsBuyerCancelRecordMapper;
import com.bbh.live.dao.mapper.LiveGoodsMapper;
import com.bbh.live.dao.mapper.LiveRoomMapper;
import com.bbh.live.dao.service.IGlobalOrganizationService;
import com.bbh.live.dao.service.LiveGoodsSubscribeService;
import com.bbh.live.dao.service.LiveRoomInteractiveMessageService;
import com.bbh.live.enums.LiveRoomEnhancedStatusEnum;
import com.bbh.live.service.buyer.BuyerLiveGoodsQueryService;
import com.bbh.live.service.livegoods.LiveGoodsDetailService;
import com.bbh.live.service.livegoods.LiveGoodsListService;
import com.bbh.live.service.livegoods.cache.LiveGoodsCacheManager;
import com.bbh.live.service.livegoods.cache.LiveGoodsSubscribeCacheService;
import com.bbh.live.service.room.LiveRoomBizService;
import com.bbh.live.service.room.LiveRoomCacheService;
import com.bbh.live.service.room.context.LiveRoomContextHolder;
import com.bbh.live.service.track.TrackService;
import com.bbh.model.*;
import com.bbh.secure.AuthUtil;
import com.bbh.util.AssertUtil;
import com.bbh.util.EncryptUtil;
import com.bbh.vo.AuthUser;
import lombok.AllArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR> dsy
 * @Date: 2024/7/31
 * @Description:
 */
@Service
@AllArgsConstructor
public class BuyerLiveGoodsQueryServiceImpl implements BuyerLiveGoodsQueryService {

    private final LiveGoodsDetailService liveGoodsDetailService;
    private final IGlobalOrganizationService globalOrganizationService;
    private final LiveRoomBizService liveRoomService;
    private final LiveRoomCacheService liveRoomCacheService;
    private final LiveGoodsCacheManager liveGoodsCacheManager;
    private final LiveGoodsListService liveGoodsListService;
    private final LiveBizProperties liveBizProperties;
    private final LiveGoodsSubscribeService liveGoodsSubscribeService;
    private final TrackService trackService;
    private final LiveRoomInteractiveMessageService liveRoomInteractiveMessageService;
    private final LiveRoomMapper liveRoomMapper;
    private final LiveGoodsMapper liveGoodsMapper;
    private final LiveGoodsBuyerCancelRecordMapper liveGoodsBuyerCancelRecordMapper;

    @Override
    public BuyerLiveGoodsDTO getLiveGoodsDetailInfo(Long liveGoodsId) {
        LiveGoodsDTO goodsDetailInfo = liveGoodsDetailService.getLiveGoodsDetailInfo(liveGoodsId);
        AssertUtil.assertNotNull(goodsDetailInfo, "商品不存在");

        BuyerLiveGoodsDTO buyerLiveGoods = new BuyerLiveGoodsDTO();
        BeanUtils.copyProperties(goodsDetailInfo, buyerLiveGoods);

        // 商家信息 订阅信息 议价信息
        richBuyerLiveGoods(List.of(buyerLiveGoods));

        // 获取直播间状态
        LiveRoomEnhancedStatusEnum roomStatus = liveRoomService.computedLiveRoomEnhancedStatus(goodsDetailInfo.getLiveRoomId());
        buyerLiveGoods.setRoomStatus(roomStatus);

        // 商品点击记录
        trackService.clickGoods(liveGoodsId, goodsDetailInfo.getGlobalGoodsId(), goodsDetailInfo.getLiveRoomId(), goodsDetailInfo.getOrgId());

        if (buyerLiveGoods.getOrgId() != null) {
            // 加密后的商户ID
            buyerLiveGoods.setEncryptedOrgId(EncryptUtil.encrypt(buyerLiveGoods.getOrgId().toString()));
            // 商家的融云ID: live_{orgId}_kefu_01
            buyerLiveGoods.setOrgRongCloudId(StrUtil.format("live_{}_kefu_01", buyerLiveGoods.getOrgId()));
        }

        // 买手主动取消成交的状态
        AuthUser authUser = AuthUtil.getUser(null);
        if (authUser != null) {
            LiveGoodsBuyerCancelRecord buyerCancelRecord = liveGoodsBuyerCancelRecordMapper.selectOne(Wrappers.lambdaQuery(LiveGoodsBuyerCancelRecord.class)
                    .eq(LiveGoodsBuyerCancelRecord::getLiveGoodsId, liveGoodsId)
                    .eq(LiveGoodsBuyerCancelRecord::getBuyerOrgId, authUser.getOrgId())
                    .orderByDesc(LiveGoodsBuyerCancelRecord::getId)
                    .last("limit 1")
            );
            if (buyerCancelRecord != null) {
                buyerLiveGoods.setIsApplyCancel(true);
                buyerLiveGoods.setCancelOrderId(buyerCancelRecord.getId());
            }
        }

        return buyerLiveGoods;
    }

    @Override
    public void checkLiveGoodsValid(Long liveGoodsId) {
        LiveGoodsDTO goodsDetailInfo = liveGoodsDetailService.getLiveGoodsDetailInfo(liveGoodsId);
        AssertUtil.assertNotNull(goodsDetailInfo, "商品不存在");

        if (goodsDetailInfo.getGoodsStatus() != LiveGoodsStatusEnum.TRADED && goodsDetailInfo.getBelongSeatId() == null){
            if(goodsDetailInfo.getGlobalGoodsId() == null || goodsDetailInfo.getErpIfLocked() || goodsDetailInfo.getErpPlaceOrderStatus() || goodsDetailInfo.getErpSaleStatus().equals(ErpGoodsSaleStatusEnum.SOLD_OUT.getType())){
                throw new ServiceException("商品已失效，请刷新列表");
            }
        }
    }

    @Override
    public ListBase<BuyerLiveGoodsDTO> getLiveGoodsList(LiveGoodsQueryReq queryParam) {
        // 直播开始前 只查询待上架商品列表
        if(LiveRoomContextHolder.liveRoomIsTestBroadcast()){
            queryParam.setLiveGoodsStatusList(List.of(LiveGoodsStatusEnum.WAIT_PUT_AWAY.getCode()));
        }
        IPage<LiveGoodsDTO> goodsList = liveGoodsListService.getLiveGoodsList(queryParam);
        if (CollectionUtil.isEmpty(goodsList.getRecords())) {
            ListBase listBase = new ListBase<>(new ArrayList<>(), 0L, null, null);
            listBase.setMap(statisticsMap(queryParam, queryParam.getLiveRoomId()));
            return listBase;
        }

        List<BuyerLiveGoodsDTO> buyerLiveGoodsList = new ArrayList<>();
        goodsList.getRecords().forEach(goods -> {
            BuyerLiveGoodsDTO buyerLiveGoods = new BuyerLiveGoodsDTO();
            BeanUtils.copyProperties(goods, buyerLiveGoods);
            buyerLiveGoodsList.add(buyerLiveGoods);
        });
        // 商家信息 订阅信息 议价信息
        richBuyerLiveGoods(buyerLiveGoodsList);
        ListBase<BuyerLiveGoodsDTO> listBase = new ListBase<>(buyerLiveGoodsList, goodsList.getTotal(), goodsList.getCurrent(), goodsList.getSize());

        listBase.setMap(statisticsMap(queryParam, queryParam.getLiveRoomId()));
        return listBase;
    }

    private Map<String, Object> statisticsMap(LiveGoodsQueryReq queryParam, Long liveRoomId){
        // 统计信息
        queryParam.setFilterLockedOrSoldOutGoods(true);
        LiveGoodsStatisticsDTO liveGoodsStatistics = liveGoodsListService.getBuyerLiveGoodsCount(queryParam);
        Map<String, Object> statisticsMap = new HashMap<>(8);
        // 待拍数量=讲解中+待上架+竞拍中
        Long waitAuctionCount = liveGoodsStatistics.getWaitPutAwayCount() + liveGoodsStatistics.getPutAwayCount() + liveGoodsStatistics.getAuctionCount();
        statisticsMap.put("wait_auction_count", waitAuctionCount);
        // 流拍数量
        statisticsMap.put("abortive_auction_count", liveGoodsStatistics.getAbortiveAuctionCount());
        // 成交数量
        Long tradeCount = liveGoodsStatistics.getTradeCount();
        statisticsMap.put("trade_count", tradeCount);
        // 总数量
        statisticsMap.put("total_count", waitAuctionCount + tradeCount + liveGoodsStatistics.getAbortiveAuctionCount());
        return statisticsMap;
    }

    @Override
    public BuyerLiveGoodsStatisticsDTO getLiveGoodsCount(LiveGoodsQueryReq queryParam) {

        queryParam.setFilterLockedOrSoldOutGoods(true);
        LiveGoodsStatisticsDTO buyerLiveGoodsCount = liveGoodsListService.getBuyerLiveGoodsCount(queryParam);

        // 待拍商品 = 竞拍商品 + 上架讲解商品 + 待上架商品  （过滤失效商品）
        Long waitAuctionCount = buyerLiveGoodsCount.getWaitPutAwayCount() + buyerLiveGoodsCount.getPutAwayCount() + buyerLiveGoodsCount.getAuctionCount();

        // 成交商品不过滤失效，直接从直播间缓存取
        Long tradeCount = buyerLiveGoodsCount.getTradeCount();
        return new BuyerLiveGoodsStatisticsDTO()
                .setWaitAuctionCount(waitAuctionCount)
                .setAbortiveAuctionCount(buyerLiveGoodsCount.getAbortiveAuctionCount())
                .setTradeCount(tradeCount)
                .setTotalCount(waitAuctionCount + tradeCount + buyerLiveGoodsCount.getAbortiveAuctionCount());
    }

    @Override
    public AuctionFailGoodsListDTO getAbortiveAuctionGoodsList() {
        AuctionFailGoodsListDTO auctionFailGoodsList = new AuctionFailGoodsListDTO();
        //所有正在直播的直播间
        List<LiveRoom> liveRooms = liveRoomService.getInLiveRoomList(AuthUtil.getUser());

        if (liveRooms.isEmpty()) {return auctionFailGoodsList;}

        Map<Long, LiveRoom> liveRoomMap = liveRooms.stream()
                .collect(Collectors.toMap(
                        LiveRoom::getId,
                        Function.identity(),
                        // 在出现重复键时保留第一个值
                        (existing, replacement) -> existing,
                        // 使用LinkedHashMap保证顺序
                        LinkedHashMap::new
                ));

        //所有正在直播的直播间流拍商品信息
        List<AbortiveAuctionGoodsDTO> goodsList = liveGoodsListService.getAuctionFailLiveGoodsListByLiveRoomId(liveRoomMap.keySet(), liveBizProperties.getAuctionFailListDisplaySize());

        //按直播间分组
        Map<Long, List<AbortiveAuctionGoodsDTO>> liveRoomGoodsMap = goodsList.stream().collect(Collectors.groupingBy(AbortiveAuctionGoodsDTO::getLiveRoomId));

        List<LiveRoomAuctionFailDTO> liveRoomAuctionFailList = new ArrayList<>();
        auctionFailGoodsList.setAuctionFailGoodsList(liveRoomAuctionFailList);
        if(CollectionUtil.isEmpty(liveRoomGoodsMap)){
            return auctionFailGoodsList;
        }

        // 流拍商品数量map
        Map<Long, Long> effectiveAuctionFailLiveGoodsCountByRoomId = liveGoodsListService.getEffectiveAuctionFailLiveGoodsCount(liveRoomGoodsMap.keySet());

        liveRoomGoodsMap.forEach((liveRoomId, goodsListByLiveRoom) -> {
            LiveRoomAuctionFailDTO liveRoomAuctionFail = new LiveRoomAuctionFailDTO();

            liveRoomAuctionFail.setLiveRoomId(liveRoomId)
                    .setOrgId(liveRoomMap.get(liveRoomId).getOrgId())
                    .setLiveRoomName(liveRoomMap.get(liveRoomId).getRoomName())
                    .setLiveRoomIfSpecial(liveRoomMap.get(liveRoomId).getIfSpecial())
                    .setTradeCount(liveRoomCacheService.getGoodsTradeCount(liveRoomId))
                    .setTotalCount(effectiveAuctionFailLiveGoodsCountByRoomId.get(liveRoomId))
                    .setLiveGoodsList(goodsListByLiveRoom);
            liveRoomAuctionFailList.add(liveRoomAuctionFail);
        });

        List<Long> orgIdList = liveRoomAuctionFailList.stream().map(LiveRoomAuctionFailDTO::getOrgId).toList();
        //商家信息补充
        richOrgInfo(orgIdList, organizationMap -> {
            //商家名称，log信息
            liveRoomAuctionFailList.forEach(liveRoomAuctionFail -> Optional.ofNullable(organizationMap.get(liveRoomAuctionFail.getOrgId()))
                    .ifPresent(organization -> {
                        liveRoomAuctionFail.setOrgName(organization.getName());
                        liveRoomAuctionFail.setLogoUrl(organization.getLogoUrl());
                    }));
        });
        return auctionFailGoodsList;
    }


    /**
     * 查询结果的数据封装类
     * @param liveRoomMap 直播间映射，key为直播间ID，value为直播间信息
     * @param playbackLiveGoodsList 回放商品列表
     */
    private record LiveRoomGoodsQueryResult(
            Map<Long, PlayBackLiveRoomVO> liveRoomMap,
            List<LiveRoomPlaybackGoodsVO> playbackLiveGoodsList
    ) {}

    /**
     * 查询直播间和商品信息的通用方法
     *
     * @return LiveRoomGoodsQueryResult 包含直播间映射和商品列表的查询结果
     *         如果没有查询到数据，返回空的映射和空列表
     */
    private LiveRoomGoodsQueryResult queryLiveRoomAndGoods() {
        // 先查到回放的直播间
        LiveRoomBizService liveRoomBizService = SpringUtil.getBean(LiveRoomBizService.class);
        List<PlayBackLiveRoomVO> roomList = liveRoomMapper.selectPlaybackRoomVOPage(
                Page.of(1, -1),
                AuthUtil.getUserId(),
                AuthUtil.getSeatId(),
                liveRoomBizService.ifFilterBlacklist(AuthUtil.getUser())
        );
        if (CollUtil.isEmpty(roomList)) {
            return new LiveRoomGoodsQueryResult(Collections.emptyMap(), Collections.emptyList());
        }

        // 获取直播间商品信息
        Map<Long, PlayBackLiveRoomVO> liveRoomMap = roomList.stream()
                .collect(Collectors.toMap(
                        PlayBackLiveRoomVO::getRoomId,
                        Function.identity(),
                        (existing, replacement) -> existing,
                        LinkedHashMap::new
                ));

        PlaybackGoodsQueryReq playbackGoodsQueryReq = new PlaybackGoodsQueryReq()
                .setRoomIdList(liveRoomMap.keySet().stream().toList());
        List<LiveRoomPlaybackGoodsVO> playbackLiveGoodsList = liveGoodsMapper.getPlaybackLiveGoodsList(
                playbackGoodsQueryReq,
                AuthUtil.getSeatId(),
                AuthUtil.getOrgId()
        );

        return new LiveRoomGoodsQueryResult(liveRoomMap, playbackLiveGoodsList);
    }

    /**
     * 获取有流拍商品的直播间数量
     *
     * @return Long 有流拍商品的直播间数量
     *         如果没有符合条件的直播间，返回0
     */
    public Long getAbortiveAuctionRoomCount() {
        LiveRoomGoodsQueryResult queryResult = queryLiveRoomAndGoods();
        if (CollectionUtil.isEmpty(queryResult.playbackLiveGoodsList())) {
            return 0L;
        }

        return queryResult.playbackLiveGoodsList().stream()
                .map(LiveRoomPlaybackGoodsVO::getLiveRoomId)
                .distinct()
                .count();
    }

    /**
     * 获取回放商品列表(V2版本)
     * 处理流程：
     * 1. 获取回放直播间列表
     * 2. 查询直播间对应的商品信息
     * 3. 按直播间分组整理数据
     * 4. 补充商家信息
     * @return 流拍商品列表数据
     */
    @Override
    public AuctionFailGoodsListDTO getAbortiveAuctionGoodsListV2() {
        AuctionFailGoodsListDTO auctionFailGoodsList = new AuctionFailGoodsListDTO();

        LiveRoomGoodsQueryResult queryResult = queryLiveRoomAndGoods();
        if (CollectionUtil.isEmpty(queryResult.playbackLiveGoodsList())) {
            return auctionFailGoodsList;
        }

        // 构建流拍商品列表
        List<AbortiveAuctionGoodsDTO> goodsList = queryResult.playbackLiveGoodsList().stream().map(x -> {
            AbortiveAuctionGoodsDTO abortiveAuctionGoodsDTO = new AbortiveAuctionGoodsDTO();
            abortiveAuctionGoodsDTO.setId(x.getLiveGoodsId())
                    .setLiveRoomId(x.getLiveRoomId())
                    .setLiveGoodsCode(x.getGoodsCode())
                    .setImgUrl(CollUtil.getFirst(x.getImgUrlList()));
            return abortiveAuctionGoodsDTO;
        }).toList();

        // 按直播间分组
        Map<Long, List<AbortiveAuctionGoodsDTO>> liveRoomGoodsMap = goodsList.stream()
                .collect(Collectors.groupingBy(AbortiveAuctionGoodsDTO::getLiveRoomId));
        List<LiveRoomAuctionFailDTO> liveRoomAuctionFailList = new ArrayList<>();
        liveRoomGoodsMap.forEach((liveRoomId, goodsListByLiveRoom) -> {
            LiveRoomAuctionFailDTO liveRoomAuctionFail = new LiveRoomAuctionFailDTO();
            PlayBackLiveRoomVO liveRoom = queryResult.liveRoomMap().get(liveRoomId);
            liveRoomAuctionFail.setLiveRoomId(liveRoomId)
                    .setOrgId(liveRoom.getOrgId())
                    .setLiveRoomName(liveRoom.getRoomName())
                    .setLiveRoomIfSpecial(liveRoom.getIfSpecial())
                    .setTradeCount(liveRoomCacheService.getGoodsTradeCount(liveRoomId))
                    .setTotalCount((long)goodsListByLiveRoom.size())
                    .setLiveGoodsList(goodsListByLiveRoom);
            liveRoomAuctionFailList.add(liveRoomAuctionFail);
        });

        // 补充商家信息
        List<Long> orgIdList = liveRoomAuctionFailList.stream()
                .map(LiveRoomAuctionFailDTO::getOrgId)
                .toList();
        richOrgInfo(orgIdList, organizationMap -> {
            liveRoomAuctionFailList.forEach(liveRoomAuctionFail ->
                    Optional.ofNullable(organizationMap.get(liveRoomAuctionFail.getOrgId()))
                            .ifPresent(organization -> {
                                liveRoomAuctionFail.setOrgName(organization.getName());
                                liveRoomAuctionFail.setLogoUrl(organization.getLogoUrl());
                            }));
        });

        auctionFailGoodsList.setAuctionFailGoodsList(liveRoomAuctionFailList);
        return auctionFailGoodsList;
    }

    private void richOrgInfo(List<Long> orgIdList, Consumer<Map<Long, GlobalOrganization>> consumer) {
        if (CollectionUtil.isEmpty(orgIdList)) {
            return;
        }
        // 去重
        orgIdList = orgIdList.stream().distinct().toList();

        //直播间商家信息
        Map<Long, GlobalOrganization> liveRoomOrganizationMap = globalOrganizationService.lambdaQuery()
                .in(GlobalOrganization::getId, orgIdList)
                .select(GlobalOrganization::getId, GlobalOrganization::getName, GlobalOrganization::getLogoUrl)
                .list().stream().collect(Collectors.toMap(GlobalOrganization::getId, Function.identity()));

        consumer.accept(liveRoomOrganizationMap);
    }

    private void richBuyerLiveGoods(List<BuyerLiveGoodsDTO> liveGoodsList) {
        if (CollectionUtil.isEmpty(liveGoodsList)) {
            return;
        }
        // 商品所属店铺 id 列表
        List<Long> orgIdList = new ArrayList<>();
        // 预约商品 id 列表
        List<Long> subscribeLiveGoodsIdList = new ArrayList<>();
        // 流拍商品 id 列表
        List<Long> abortiveAuctionLiveGoodsIdList = new ArrayList<>();

        Map<Long, BuyerLiveGoodsDTO> liveGoodsMap = new HashMap<>(liveGoodsList.size());

        LiveGoodsSubscribeCacheService cache = liveGoodsCacheManager.getLiveGoodsSubscribeCache();
        liveGoodsList.stream()
                .filter(liveGoods -> liveGoods.getLiveGoodsId() != null && liveGoods.getLiveRoomId() != null)
                .forEach(liveGoods -> {
                    orgIdList.add(liveGoods.getOrgId());
                    //是否预约
                    boolean subscribe;
                    try {
                        subscribe = cache.isSubscribe(liveGoods.getLiveRoomId(), liveGoods.getLiveGoodsId(), AuthUtil.getSeatId());
                    }catch (Exception e){
                        subscribe = false;
                    }
                    liveGoods.setHasSubscribed(subscribe);
                    if(subscribe){
                        subscribeLiveGoodsIdList.add(liveGoods.getLiveGoodsId());
                    }
                    // 是否流拍
                    if(liveGoods.getGoodsStatus() == LiveGoodsStatusEnum.ABORTIVE_AUCTION){
                        abortiveAuctionLiveGoodsIdList.add(liveGoods.getLiveGoodsId());
                    }
                    liveGoodsMap.put(liveGoods.getLiveGoodsId(), liveGoods);
        });

        // 补充商家信息
        richOrgInfo(orgIdList, liveRoomOrganizationMap -> {
            //商家名称，logo信息
            liveGoodsList.forEach(liveGoods -> Optional.ofNullable(liveRoomOrganizationMap.get(liveGoods.getOrgId()))
                    .ifPresent(organization -> {
                        liveGoods.setOrgName(organization.getName());
                        liveGoods.setLogoUrl(organization.getLogoUrl());
                    }));
        });
        // 预约备注
        richSubscribeMark(subscribeLiveGoodsIdList, liveGoodsMap);
        // 流拍商品 议价信息
        richBargainInfo(abortiveAuctionLiveGoodsIdList, liveGoodsMap);
    }

    /**
     * 预约备注
     * @param subscribeLiveGoodsIdList 订阅的商品id集合
     * @param liveGoodsMap 所有商品映射
     */
    private void richSubscribeMark(List<Long> subscribeLiveGoodsIdList, Map<Long, BuyerLiveGoodsDTO> liveGoodsMap) {
        if(CollectionUtil.isNotEmpty(subscribeLiveGoodsIdList) && AuthUtil.getUser(null) != null){
            List<LiveGoodsSubscribe> goodsSubscribes = liveGoodsSubscribeService.lambdaQuery()
                    .in(LiveGoodsSubscribe::getLiveGoodsId, subscribeLiveGoodsIdList)
                    .eq(LiveGoodsSubscribe::getLiveRoomId, LiveRoomContextHolder.getRoomId())
                    .eq(LiveGoodsSubscribe::getCreateSeatId, AuthUtil.getSeatId())
                    .isNotNull(LiveGoodsSubscribe::getRemark)
                    .ne(LiveGoodsSubscribe::getRemark, "")
                    .select(LiveGoodsSubscribe::getLiveGoodsId, LiveGoodsSubscribe::getRemark)
                    .list();
            // 设置备注
            goodsSubscribes.forEach(goodsSubscribe -> liveGoodsMap.get(goodsSubscribe.getLiveGoodsId()).setSubscribeMark(goodsSubscribe.getRemark()));
        }
    }

    /**
     * 议价信息
     * @param abortiveAuctionLiveGoodsIdList
     * @param liveGoodsMap
     */
    private void richBargainInfo(List<Long> abortiveAuctionLiveGoodsIdList, Map<Long, BuyerLiveGoodsDTO> liveGoodsMap) {
        if(CollectionUtil.isNotEmpty(abortiveAuctionLiveGoodsIdList) && AuthUtil.getUser(null) != null){
            // 议价消息
            List<LiveRoomInteractiveMessage> interactiveMessageList = liveRoomInteractiveMessageService.lambdaQuery()
                    .in(LiveRoomInteractiveMessage::getLiveGoodsId, abortiveAuctionLiveGoodsIdList)
                    .eq(LiveRoomInteractiveMessage::getLiveRoomId, LiveRoomContextHolder.getRoomId())
                    .eq(LiveRoomInteractiveMessage::getType, LiveRoomInteractiveMessageTypeEnum.BARGAIN)
                    .eq(LiveRoomInteractiveMessage::getCreateSeatId, AuthUtil.getSeatId())
                    .select(LiveRoomInteractiveMessage::getLiveGoodsId, LiveRoomInteractiveMessage::getHandleStatus, LiveRoomInteractiveMessage::getBargainPrice, LiveRoomInteractiveMessage::getCreatedAt)
                    .list();

            // 根据商品 ID 对议价消息进行分组
            Map<Long, List<LiveRoomInteractiveMessage>> bargainMsgMap = interactiveMessageList.stream().collect(Collectors.groupingBy(LiveRoomInteractiveMessage::getLiveGoodsId));

            bargainMsgMap.forEach((liveGoodsId, bargainMsgList) -> {
                if(CollectionUtil.isNotEmpty(bargainMsgList)){
                    // 获取最新的一条议价消息
                    LiveRoomInteractiveMessage lastBargainMsg = bargainMsgList.stream().max(Comparator.comparing(LiveRoomInteractiveMessage::getCreatedAt)).get();
                    liveGoodsMap.get(liveGoodsId).setBargainPrice(lastBargainMsg.getBargainPrice());
                    liveGoodsMap.get(liveGoodsId).setBargainStatus(lastBargainMsg.getHandleStatus());
                }
            });
        }
    }

    @Override
    public LiveGoodsDTO getCheckedGoodsInfo(Long globalGoodsId) {
        LiveGoodsDTO goodsDetailInfo = liveGoodsDetailService.getCheckedGoodsInfo(globalGoodsId);
        AssertUtil.assertNotNull(goodsDetailInfo, "商品不存在");
        return goodsDetailInfo;
    }
}
