package com.bbh.live.service.msg.dto;

import com.bbh.live.core.msg.MsgType;
import com.bbh.live.service.msg.dto.base.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <AUTHOR> dsy
 * @Date: 2024/9/2
 * @Description:
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
public class GoodsTransferAgreedMsgDTO extends BaseAuctionLimitMsg implements IMsg {

    @Override
    public String type() {
        return MsgType.TRANSFER_AGREE;
    }

    /**
     * 商品信息
     */
    private BaseGoods goods;

    /**
     * 用户信息
     */
    private BaseSeat user;

    private Integer tradeType;
}
