package com.bbh.live.service.user.permission;

import com.bbh.model.GlobalOrgRole;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/9/20 16:27
 * @description 直播权限
 */
public class LivePermissionCheckServiceImpl extends AbstractPermissionCheckService {

    @Override
    protected Integer getPermissionIfAll(GlobalOrgRole globalOrgRole) {
        return globalOrgRole.getLivePermissionIfAll();
    }

    @Override
    protected List<String> getPermissionCodeList(GlobalOrgRole globalOrgRole) {
        return globalOrgRole.getLivePermissionCodeList();
    }
}
