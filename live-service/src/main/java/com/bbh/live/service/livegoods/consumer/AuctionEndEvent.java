package com.bbh.live.service.livegoods.consumer;

import com.bbh.live.service.livegoods.cache.info.LiveGoodsAuctionInfo;
import org.springframework.context.ApplicationEvent;

import java.time.Clock;

/**
 * <AUTHOR>
 * @date 2024/10/31 11:38
 * @description
 */
public class AuctionEndEvent extends ApplicationEvent {

    private LiveGoodsAuctionInfo auctionInfo;

    public AuctionEndEvent(Object source) {
        super(source);
    }

    public AuctionEndEvent(Object source, Clock clock) {
        super(source, clock);
    }

    @Override
    public LiveGoodsAuctionInfo getSource() {
        return (LiveGoodsAuctionInfo) super.getSource();
    }
}
