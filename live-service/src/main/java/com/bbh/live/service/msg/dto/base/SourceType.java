package com.bbh.live.service.msg.dto.base;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 消息来源类型: 0-买手、1-导播、2-主播、3-通知、4-系统公告
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum SourceType {


    /**
     * 买手
     */
    BUYER(0),

    /**
     * 导播
     */
    LIVE_DIRECTOR(1),

    /**
     * 主播
     */
    LIVE_ANCHOR(2),

    /**
     * 通知
     */
    NOTICE(3),

    /**
     * 系统公告
     */
    SYSTEM_NOTICE(4);

    private final Integer code;

    public static SourceType getSourceType(Integer code) {
        for (SourceType sourceType : SourceType.values()) {
            if (sourceType.getCode().equals(code)) {
                return sourceType;
            }
        }
        return null;
    }


}
