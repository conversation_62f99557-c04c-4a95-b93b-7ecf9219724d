package com.bbh.live.service.msg.dto;

import com.bbh.live.core.msg.MsgType;
import com.bbh.live.service.livegoods.cache.info.LiveGoodsAuctionBidInfo;
import com.bbh.live.service.msg.dto.base.BaseAuctionLimitMsg;
import com.bbh.live.service.msg.dto.base.BaseGoods;
import com.bbh.live.service.msg.dto.base.IMsg;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 商品开始竞拍的消息
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
public class GoodsAuctionStartMsgDTO extends BaseAuctionLimitMsg implements IMsg {

    private BaseGoods goods;

    /**
     * 竞拍剩余时间，单位：秒
     */
    private Long remainTime;

    /**
     * 竞拍剩余时间，单位：毫秒
     */
    private Long remainTimeMs;

    /**
     * 该商品出过价的人的席位ID集合
     */
    private List<LiveGoodsAuctionBidInfo> biddenList;

    private Integer tradeType;

    /**
     * 每个消息对应一个类型
     *
     * @return {@link MsgType}
     */
    @Override
    public String type() {
        return MsgType.AUCTION_BID_START;
    }

}
