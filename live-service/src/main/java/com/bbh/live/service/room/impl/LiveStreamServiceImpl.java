package com.bbh.live.service.room.impl;


import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Dict;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.bbh.enums.LiveRoomStreamStatusEnum;
import com.bbh.feign.IAuthCenterFeign;
import com.bbh.feign.dto.*;
import com.bbh.live.config.LiveServiceProperties;
import com.bbh.live.dao.dto.LiveRoomEntryStatisticsDTO;
import com.bbh.live.dao.dto.vo.LiveRoomVO;
import com.bbh.live.dao.service.*;
import com.bbh.live.enums.LiveRoomEnhancedStatusEnum;
import com.bbh.live.service.msg.MsgService;
import com.bbh.live.service.permission.PermissionService;
import com.bbh.live.service.room.LiveRoomBizService;
import com.bbh.live.service.room.LiveRoomCacheService;
import com.bbh.live.service.room.LiveStreamService;
import com.bbh.live.util.LivePermissionChecker;
import com.bbh.live.util.SignUtil;
import com.bbh.model.GlobalOrganization;
import com.bbh.model.LiveRoom;
import com.bbh.service.mq.enums.MqTopicEnum;
import com.bbh.service.mq.service.CoreMqService;
import com.bbh.util.AssertUtil;
import jakarta.annotation.Resource;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;

/**
 * 直播流相关 sdk
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@AllArgsConstructor
@EnableConfigurationProperties(LiveServiceProperties.class)
public class LiveStreamServiceImpl implements LiveStreamService {

    @Resource
    private IAuthCenterFeign iAuthCenterFeign;

    private final LiveServiceProperties liveServiceProperties;
    private final LiveRoomBizService liveRoomBizService;
    private final LiveRoomService liveRoomService;
    private final LiveRoomRecordService liveRoomRecordService;
    private final MsgService msgService;
    private final LiveRoomCacheService liveRoomCacheService;
    private final CoreMqService coreMqService;
    private final LiveRoomEntryRecordService liveRoomEntryRecordService;
    private final GlobalDepositAuditService globalDepositAuditService;
    private final IGlobalOrganizationService globalOrganizationService;
    private final PermissionService permissionService;

    /**
     * 创建直播间 推流配置
     */
    @Override
    public LiveConfigDTO createLiveConfig(LiveDTO dto) {
        RequestDTO<LiveDTO> requestDTO = this.createRequest(dto);
        DataCenterFeignResult<LiveConfigDTO> liveConfigDTO = iAuthCenterFeign.createLiveStream(requestDTO);
        return liveConfigDTO.getResult();
    }

    /**
     * 获取直播间回放
     */
    @Override
    public RecordVideoDTO getVideoRecord(LiveRecordDTO dto) {
        RequestDTO<LiveRecordDTO> requestDTO = this.createRequest(dto);
        DataCenterFeignResult<RecordVideoDTO> videoRecord = iAuthCenterFeign.getVideoRecord(requestDTO);
        return videoRecord.getResult();
    }

    /**
     * 禁止推流
     */
    @Override
    public void forbidLiveStream(LiveDTO dto) {
        RequestDTO<LiveDTO> requestDTO = this.createRequest(dto);
        iAuthCenterFeign.forbidLiveStream(requestDTO);
    }

    /**
     * 恢复推流
     */
    @Override
    public void resumeLiveStream(LiveDTO dto) {
        RequestDTO<LiveDTO> requestDTO = this.createRequest(dto);
        iAuthCenterFeign.resumeLiveStream(requestDTO);
    }

    /**
     * 开始直播
     *
     * @param roomId 直播间ID
     */
    @Override
    public void startLive(Long roomId) {
        // 验证直播间ID并获取直播间信息
        // 确保直播间存在且可用，防止后续操作出现空指针异常
        LiveRoom liveRoom = validateAndGetLiveRoom(roomId);
        // 防止过期的直播被重新开启
        AssertUtil.assertFalse(liveRoom.getEndAt() != null && liveRoom.getEndAt().before(new Date()), "直播时间已结束，无法开播");
        // 检查用户权限，确保只有主播才能开启直播
        LivePermissionChecker.assertAnchor(liveRoom);
        // 检查是否有违规记录，如果有违规记录，也禁止开播
        AssertUtil.assertFalse(globalDepositAuditService.hasIllegalRecord(liveRoom.getOrgId()), "店铺已违规，无法开启直播，请及时处理");
        // 检查是否有销售权益
//        AssertUtil.assertTrue(permissionService.hasSellerRights(liveRoom.getOrgId()), "商家未开通销售权益，请开通后开播");
        // 检查商家签约状态和结算账户
        GlobalOrganization organization = globalOrganizationService.getById(liveRoom.getOrgId());
        AssertUtil.assertTrue(permissionService.checkOrgIsContract(organization), "商家未签约，请签约后开播");
        AssertUtil.assertTrue(permissionService.checkOrgHasSettlementBank(organization), "商家未开通结算账户，请开通后开播");

        // 如果当前时间早于计划开始时间，则进入测试直播模式
        // 允许主播在正式直播前进行测试
        Date now = new Date();
        if (liveRoomService.ifLiveRoomTestBroadcast(liveRoom)) {
            testBroadcastBegin(liveRoom);
        }

        // 执行通用的开始直播操作
        startLiveCommon(roomId, liveRoom, now);
    }

    /**
     * 通过流回调开始直播
     *
     * @param roomId 直播间ID
     */
    @Override
    public void startLiveByStreamCallback(Long roomId) {
        // 确保直播间存在且可用，防止后续操作出现空指针异常
        LiveRoom liveRoom = validateAndGetLiveRoom(roomId);
        // 幂等性检查，防止重复开播
        if (liveRoom.getStreamStatus() == LiveRoomStreamStatusEnum.ON) {
            return;
        }

        // 试播
        if (liveRoomService.ifLiveRoomTestBroadcast(liveRoom)) {
            testBroadcastBegin(liveRoom);
        }
        // 更新
        liveRoomService.update(Wrappers.lambdaUpdate(LiveRoom.class)
                .eq(LiveRoom::getId, roomId)
                .set(LiveRoom::getStreamStatus, LiveRoomStreamStatusEnum.ON));
        msgService.liveStart(roomId);
        updateLiveRoomCache(liveRoom);
    }

    /**
     * 停止直播
     *
     * @param roomId 直播间ID
     */
    @Override
    @Transactional
    public void stopLive(Long roomId) {
        // 验证直播间ID并获取直播间信息
        // 这个步骤确保了直播间存在且可用，防止后续操作出现空指针异常
        LiveRoom liveRoom = validateAndGetLiveRoom(roomId);

        // 检查主播权限
        LivePermissionChecker.assertAnchor(liveRoom);

        // 幂等性检查，防止重复关闭直播
        if (liveRoom.getStreamStatus() == LiveRoomStreamStatusEnum.OFF) {
            return;
        }

        // 执行通用的停止直播操作
        stopLiveCommon(roomId, liveRoom);
    }

    /**
     * 通过流回调停止直播
     *
     * @param roomId 直播间ID
     */
    @Override
    public void stopLiveByStreamCallback(Long roomId) {
        LiveRoom liveRoom = validateAndGetLiveRoom(roomId);
        // 幂等性检查，防止重复关闭直播
        if (liveRoom.getStreamStatus() == LiveRoomStreamStatusEnum.OFF) {
            return;
        }
        stopLiveCommon(roomId, liveRoom);
    }

    /**
     * 试播的开播处理
     * @param liveRoom 直播间
     */
    private void testBroadcastBegin(LiveRoom liveRoom) {
        // 发送试播消息
        msgService.sendTestBroadcastBeginMsg(liveRoom);
    }

    public void testBroadcastEnd(LiveRoom liveRoom) {
        // 发送试播消息
        msgService.sendTestBroadcastEndMsg(liveRoom);
    }

    /**
     * 验证并获取直播间信息
     *
     * @param roomId 直播间ID
     * @return 直播间对象
     */
    private LiveRoom validateAndGetLiveRoom(Long roomId) {
        AssertUtil.assertNotNull(roomId, "直播间ID不能为空");
        LiveRoom liveRoom = liveRoomService.getById(roomId);
        AssertUtil.assertNotNull(liveRoom, "直播间不存在");
        return liveRoom;
    }

    /**
     * 开始直播的通用操作
     *
     * @param roomId 直播间ID
     * @param liveRoom 直播间对象
     * @param now 当前时间
     */
    private void startLiveCommon(Long roomId, LiveRoom liveRoom, Date now) {
        // 添加直播开始记录
        // 记录直播真实的历史数据，正好记录直播流的意外中断与打开
        liveRoomRecordService.addLiveRoomStartRecord(roomId, now);

        // 更新直播间数据
        LambdaUpdateWrapper<LiveRoom> updateWrapper = Wrappers.lambdaUpdate(LiveRoom.class)
                .eq(LiveRoom::getId, roomId)
                .set(LiveRoom::getActualStartAt, now)
                // 更新直播间状态为开播状态
                .set(LiveRoom::getStreamStatus, LiveRoomStreamStatusEnum.ON);

        liveRoomService.update(updateWrapper);

        // 生产关播和倒计时的延迟队列
        produceLiveDelayQueue(liveRoom, true);

        // 发送直播开始消息
        // 这会触发一系列后续操作，如通知用户、更新UI显示等
        msgService.liveStart(roomId);

        // 更新直播间缓存
        // 缓存的更新确保了后续的快速访问和读取，提高系统性能
        // 同时也保证了缓存与数据库的一致性
        updateLiveRoomCache(liveRoom);
    }

    /**
     * 停止直播的通用操作
     *
     * @param roomId 直播间ID
     * @param liveRoom 直播间对象
     */
    private void stopLiveCommon(Long roomId, LiveRoom liveRoom) {
        // 获取当前时间，用于记录直播结束时间
        Date now = new Date();

        // 如果是试播，结束试播
        if (liveRoomService.ifLiveRoomTestBroadcast(liveRoom)) {
            this.testBroadcastEnd(liveRoom);
        }
        // 更新直播结束记录
        // 这是为了记录直播的历史数据，方便后续统计和分析直播时长、结束时间等信息，也能记录意外中断的特殊情况
        liveRoomRecordService.updateLiveRoomStopRecord(roomId, now);

        // 从缓存中获取最新的直播间信息
        // 使用缓存数据可以确保获取到最新的直播状态，包括可能在直播过程中更新的字段
        LiveRoom cachedLiveRoom = liveRoomCacheService.getLiveRoom(roomId);
        // 设置直播间ID
        cachedLiveRoom.setId(roomId);
        // 设置实际结束时间，用于准确计算直播时长
        cachedLiveRoom.setActualEndAt(now);
        // 更新直播流状态为关闭
        cachedLiveRoom.setStreamStatus(LiveRoomStreamStatusEnum.OFF);
        // 获取进出记录相关的统计信息
        LiveRoomEntryStatisticsDTO statisticsDTO = liveRoomEntryRecordService.statisticsEntry(roomId);
        // 真实观看人数
        cachedLiveRoom.setRealCount(statisticsDTO.getTotalUserCount());
        // 人均观看时长
        cachedLiveRoom.setViewAvgDuration(statisticsDTO.getAverageDurationPerUser());
        // 如果超过了结束时间，就用当前时间+duration，如果没到结束时间，就用endAt+duration
        cachedLiveRoom.setActualGoodsListEndAt(DateUtil.offsetHour(
                now.after(liveRoom.getEndAt()) ? now : liveRoom.getEndAt(),
                liveRoom.getGoodsListDuration()
        ));
        // 将更新后的直播间信息保存到数据库，确保了数据库中存储的是最新的直播间数据
        liveRoomService.updateById(cachedLiveRoom);

        // 计算直播间的真实播放状态
        LiveRoomEnhancedStatusEnum statusEnum = liveRoomBizService.computedLiveRoomEnhancedStatus(createLiveRoomVO(roomId, liveRoom, cachedLiveRoom));
        // 把播放状态提供给前端，直接能展示出来
        msgService.liveEnd(liveRoom, statusEnum);

        // 直播间缓存设置为结束时间+7天。不主动删除 因为实时人数没落库 中途关播就丢失了
        //liveRoomCacheService.deleteLiveRoom(roomId);
        // todo 直播结束 清空商品缓存
    }

    /**
     * 更新直播间缓存
     *
     * @param liveRoom 直播间对象
     */
    private void updateLiveRoomCache(LiveRoom liveRoom) {
        liveRoomCacheService.setLiveRoomAllCountValue(liveRoom.getId(), liveRoom);
    }

    /**
     * 创建LiveRoomVO对象
     *
     * @param roomId 直播间ID
     * @param liveRoom 原始直播间对象
     * @param cachedLiveRoom 缓存中的直播间对象
     * @return LiveRoomVO对象
     */
    private LiveRoomVO createLiveRoomVO(Long roomId, LiveRoom liveRoom, LiveRoom cachedLiveRoom) {
        LiveRoomVO liveRoomVO = new LiveRoomVO();
        liveRoomVO.setId(roomId);
        liveRoomVO.setStreamStatus(cachedLiveRoom.getStreamStatus());
        liveRoomVO.setStartAt(liveRoom.getStartAt());
        liveRoomVO.setEndAt(liveRoom.getEndAt());
        // 试播通过时间判断，小于开始时间就是试播
        liveRoomVO.setIfTestBroadcast(liveRoomService.ifLiveRoomTestBroadcast(liveRoom));
        return liveRoomVO;
    }

    /**
     * 默认不检查是否首次开播
     * 首次开播时，生产两条延迟队列： <br>
     * 1. 结束前3分钟的开始倒计时 <br>
     * 2. 到时间后的强制关播 <br>
     *
     * @param liveRoom 直播间
     */
    @Override
    public void produceLiveDelayQueue(LiveRoom liveRoom) {
        produceLiveDelayQueue(liveRoom, false);
    }

    /**
     * 生产结束倒计时和关播的MQ消息
     * @param liveRoom              直播间详情
     * @param validActualStartAt    是否检查实际的开播时间，如果为true，就仅允许首次开播时生产
     */
    public void produceLiveDelayQueue(LiveRoom liveRoom, boolean validActualStartAt) {
        // 仅首次开播，也就是没有实际开播时间的时候，才会生产
        if (validActualStartAt && liveRoom.getActualStartAt() != null) {
            return;
        }

        // 获取结束时间
        Date endAt = liveRoom.getEndAt();
        DateTime now = DateUtil.date();

        // 计算结束前N分钟到现在的时间差
        log.info("获取配置成功，提前{}秒开始倒计时", liveServiceProperties.getCountdownBeforeLiveEnd());
        DateTime countdownStartAt = DateUtil.offsetSecond(endAt, -liveServiceProperties.getCountdownBeforeLiveEnd());
        // 如果倒计时开始时间小于当前时间，要立即进行消费
        long countdownDelayMillis = countdownStartAt.getTime() - now.getTime();
        countdownDelayMillis = countdownDelayMillis < 0 ? 0 : countdownDelayMillis;
        // 只要一个直播间id就行，在消费时还要做时间幂等
        var content = JSONUtil.toJsonStr(new Dict().set("liveRoomId", liveRoom.getId()));
        // 结束前3分钟的开始倒计时
        coreMqService.send(MqTopicEnum.LIVE_CLOSE_COUNTDOWN_DELAY, content, countdownDelayMillis);
        log.info("倒计时队列生产成功，延迟时间:{}ms（{}），直播间ID:{}", countdownDelayMillis, countdownStartAt, liveRoom.getId());

        // 计算结束到现在的时间差
        long endDelayMillis = endAt.getTime() - now.getTime();
        // 提前半秒，避免前端卡在0秒
        endDelayMillis -= 500;
        endDelayMillis = endDelayMillis < 0 ? 0 : endDelayMillis;
        // 到时间后的强制关播
        coreMqService.send(MqTopicEnum.LIVE_CLOSE_DELAY, content, endDelayMillis);
        log.info("强制关播队列生产成功，延迟时间:{}ms（{}），直播间ID:{}", endDelayMillis, DateUtil.formatDateTime(endAt), liveRoom.getId());
    }


    /**
     * 参数签名
     * @param t
     * @return
     * @param <T>
     */
    @SuppressWarnings("all")
    private <T> RequestDTO<T> createRequest(T t) {
        String sign = SignUtil.encodeSign(t, liveServiceProperties.getSalt());
        RequestDTO requestData = new RequestDTO<>();
        requestData.setData(t);
        requestData.setSign(sign);
        requestData.setBbhMerchantNo(liveServiceProperties.getBbhMerchantNo());
        return requestData;
    }
}
