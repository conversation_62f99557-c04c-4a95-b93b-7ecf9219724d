package com.bbh.live.service.buyer.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.bbh.base.ListBase;
import com.bbh.live.controller.req.KeywordsReq;
import com.bbh.live.dao.dto.vo.AttentionOrgVO;
import com.bbh.live.dao.dto.vo.LiveRoomVO;
import com.bbh.live.dao.dto.vo.SubscribeLiveRoomVO;
import com.bbh.live.dao.service.GlobalOrgSeatService;
import com.bbh.live.dao.service.IGlobalOrganizationService;
import com.bbh.live.dao.service.LiveRoomSubscribeService;
import com.bbh.live.service.room.LiveRoomBizService;
import com.bbh.model.GlobalOrgSeat;
import com.bbh.util.EncryptUtil;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR> dsy
 * @Date: 2024/8/28
 * @Description: 买手个人中心service
 */
@Service
@AllArgsConstructor
public class BuyerCenterService {

    private final LiveRoomSubscribeService liveRoomSubscribeService;
    private final LiveRoomBizService liveRoomService;
    private final IGlobalOrganizationService globalOrganizationService;
    private final GlobalOrgSeatService globalOrgSeatService;

    /**
     * 获取订阅直播列表
     * @param buyerSeatId
     * @return
     */
    public ListBase<SubscribeLiveRoomVO> getSubscribeLiveRoomList(Long buyerSeatId) {
        var subscribeLiveRoomList = liveRoomSubscribeService.getSubscribeLiveRoomBySeatId(buyerSeatId);

        // 导播列表
        Map<Long, List<Long>> liveRoomDirectorMap = liveRoomService.computedLiveRoomDirectorMap(subscribeLiveRoomList.stream().map(SubscribeLiveRoomVO::getRoomId).toList());
        // 查询同商户的员工
        List<Long> liveRoomOrgIds = subscribeLiveRoomList.stream().map(SubscribeLiveRoomVO::getOrgId).filter(Objects::nonNull).distinct().toList();
        List<GlobalOrgSeat> roomOrgSeatList = globalOrgSeatService.selectListByOrgIdList(liveRoomOrgIds);
        List<Long> roomOrgSeatIds = roomOrgSeatList.stream().map(GlobalOrgSeat::getId).toList();

        LiveRoomVO liveRoomVO = new LiveRoomVO();
        subscribeLiveRoomList.forEach(subscribeLiveRoomVO -> {
            liveRoomVO.setId(subscribeLiveRoomVO.getRoomId());
            liveRoomVO.setAnchorSeatId(subscribeLiveRoomVO.getAnchorSeatId());
            subscribeLiveRoomVO.setSeatRole(liveRoomService.computedSeatRole(liveRoomVO, liveRoomDirectorMap, buyerSeatId));
            subscribeLiveRoomVO.setIfRelatedStaff(liveRoomService.computedIfRelatedStaff(roomOrgSeatIds, buyerSeatId));
        });
        return new ListBase<>(subscribeLiveRoomList, 0L, 0L, null);
    }

    public ListBase<AttentionOrgVO> getAttentionOrgList(KeywordsReq keywordsReq, Long buyerSeatId){
        //关注的商家信息
        List<AttentionOrgVO> attentionOrgList = liveRoomSubscribeService.getAttentionOrgListBySeatId(keywordsReq.getKeywords(), buyerSeatId);
        if(CollectionUtil.isEmpty(attentionOrgList)){
            return new ListBase<>(attentionOrgList, null, null, null);
        }
        var orgMap = attentionOrgList.stream().collect(Collectors.toMap(AttentionOrgVO::getOrgId, Function.identity()));

        // 2024.12.02新需求，去掉上新标识，因为直播里面判断ERP商品，概念比较模糊
//        // 商家是否有上新 7天内有商品更新
//        var hasNewGoodsMap = globalOrganizationService.ifOrgHasNewGoods(orgMap.keySet());
//
//        //是否有上新
//        hasNewGoodsMap.forEach((orgId, hasNewGoods) -> {
//            var attentionOrgVO = orgMap.get(orgId);
//            attentionOrgVO.setHasNewGoods(hasNewGoods);
//        });

        // 商家的直播列表
        var liveByOrgList = liveRoomSubscribeService.getAttentionOrgLiveByOrgList(orgMap.keySet());

        liveByOrgList.forEach(live -> {
            var attentionOrgVO = orgMap.get(live.getOrgId());
            attentionOrgVO.setRoomId(live.getRoomId());
            attentionOrgVO.setRoomName(live.getRoomName());
            attentionOrgVO.setInLive(live.getInLive());
            attentionOrgVO.setIfSpecial(live.getIfSpecial());
            attentionOrgVO.setStartAt(live.getStartAt());
        });
        // 加密orgid
        attentionOrgList.forEach(x -> x.setEncryptOrgId(EncryptUtil.encrypt(x.getOrgId().toString())));
        return new ListBase<>(attentionOrgList, null, null, null);
    }
}
