package com.bbh.live.service.livegoods.cache.redis;

import com.bbh.live.service.livegoods.cache.LiveGoodsAuctionCacheService;
import com.bbh.live.service.livegoods.cache.LiveGoodsCacheManager;
import com.bbh.live.service.livegoods.cache.LiveGoodsInfoCacheService;
import com.bbh.live.service.livegoods.cache.LiveGoodsSubscribeCacheService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> dsy
 * @Date: 2024/7/26
 * @Description:
 */
@Component
@AllArgsConstructor
public class RedisLiveGoodsCacheManager implements LiveGoodsCacheManager {

    private final RedisLiveGoodsAuctionCacheServiceImpl redisLiveGoodsAuctionCacheServiceImpl;
    private final RedisLiveGoodsInfoCacheServiceImpl redisLiveGoodsInfoCacheServiceImpl;
    private final RedisLiveGoodsSubscribeCacheServiceImpl redisLiveGoodsSubscribeCacheServiceImpl;


    @Override
    public LiveGoodsAuctionCacheService getLiveGoodsAuctionCache() {
        return redisLiveGoodsAuctionCacheServiceImpl;
    }

    @Override
    public LiveGoodsInfoCacheService getLiveGoodsInfoCache() {
        return redisLiveGoodsInfoCacheServiceImpl;
    }

    @Override
    public LiveGoodsSubscribeCacheService getLiveGoodsSubscribeCache() {
        return redisLiveGoodsSubscribeCacheServiceImpl;
    }

    @Override
    public void clearGoodsCache(Long liveRoomId) {
        //竞拍商品缓存清理
        getLiveGoodsAuctionCache().clearAll(liveRoomId);
        //订阅消息缓存清理
        getLiveGoodsSubscribeCache().clearAll(liveRoomId);

    }
}
