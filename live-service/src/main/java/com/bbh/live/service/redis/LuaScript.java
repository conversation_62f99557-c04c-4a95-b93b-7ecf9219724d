package com.bbh.live.service.redis;

import lombok.Getter;

/**
 * <AUTHOR> dsy
 * @Date: 2024/8/6
 * @Description:
 */
@Getter
public enum LuaScript {

    /**
     * 竞拍价格修改脚本
     */
    AUCTION_LIVE_GOODS_UPDATE_PRICE("AuctionLiveGoodsUpdatePrice"),
    /**
     * 获取竞拍商品属性值脚本
     */
    AUCTION_LIVE_GOODS_FIELD_VALUE("AuctionLiveGoodsFieldValue"),
    /**
     * 设置竞拍商品属性脚本
     */
    AUCTION_LIVE_GOODS_SET_FIELD_VALUE("AuctionLiveGoodsSetFieldValue"),

    /**
     * 设置竞拍商品正在撤回脚本
     */
    AUCTION_LIVE_GOODS_SET_REVOKE("AuctionLiveGoodsSetRevoke"),

    /**
     * 设置竞拍商品第一出价者脚本
     */
    AUCTION_LIVE_GOODS_UPDATE_FIRST_BIDDER("AuctionLiveGoodsUpdateFirstBidder"),
    ;

    private final String code;

    LuaScript(String code) {
        this.code = code;
    }
}
