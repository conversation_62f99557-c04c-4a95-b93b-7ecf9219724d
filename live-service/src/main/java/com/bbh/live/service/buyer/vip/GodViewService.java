package com.bbh.live.service.buyer.vip;


import com.bbh.live.dao.dto.GodViewTrialStatusDTO;

/**
 * 上帝视角
 * <AUTHOR>
 */
public interface GodViewService {

    /**
     * 获取用户的上帝视角体验状态
     */
    GodViewTrialStatusDTO getGodViewTrialStatus();

    /**
     * 获取全局的上帝视角体验状态
     */
    Boolean getGodViewGlobalTrial();

    /**
     * 添加点击商品次数
     * @return 剩余次数
     */
    Integer addGodViewUseCount(Long liveGoodsId, Long liveRoomId);
}
