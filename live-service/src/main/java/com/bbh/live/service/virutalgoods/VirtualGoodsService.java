package com.bbh.live.service.virutalgoods;

import com.bbh.live.dao.dto.PayDTO;
import com.bbh.live.dao.dto.vo.CreateOrderV2VO;
import com.bbh.live.dao.dto.vo.VirtualGoodsBuyVO;

/**
 * <AUTHOR> dsy
 * @Date: 2024/9/12
 * @Description:
 */
public interface VirtualGoodsService {

    /**
     * 创建虚拟商品订单
     *
     * @param virtualGoodsBuyVO 虚拟商品购买参数
     * @return orderNo 订单号
     */
    CreateOrderV2VO createOrder(VirtualGoodsBuyVO virtualGoodsBuyVO);

    /**
     *  支付成功回调
     * @param outTradeNo 支付流水号
     */
    boolean orderPayCallback(String outTradeNo);

    /**
     * 检查支付是否成功
     * @param orderId   订单号
     * @return          支付是否成功: true/false
     */
    boolean checkPaySuccess(Long orderId);

    /**
     * 单独的调起支付接口
     *
     * @param payDTO 流水号
     * @return 调起支付参数
     */
    Object pay(PayDTO payDTO);
}
