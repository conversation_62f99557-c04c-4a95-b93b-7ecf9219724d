package com.bbh.live.service.room.consumer;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import com.bbh.live.config.LiveServiceProperties;
import com.bbh.live.service.msg.MsgService;
import com.bbh.model.LiveRoom;
import com.bbh.service.mq.constants.MqConstant;
import com.bbh.util.LogExUtil;
import com.rabbitmq.client.Channel;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * 直播间关闭倒计时
 * <AUTHOR>
 */
@Component
@Slf4j
public class LiveCloseCountdownConsumer extends AbstractRoomConsumer {

    @Resource
    private MsgService msgService;
    @Resource
    private LiveServiceProperties liveServiceProperties;

    @Override
    protected String getConsumerName() {
        return "直播间结束前倒计时";
    }

    @Override
    protected void processLiveRoom(LiveRoom liveRoom, String body) {
        // 检查结束时间，如果不在时间内，就认为是无效的消息
        Date now = DateUtil.date();
        Date endAt = liveRoom.getEndAt();
        var countdownStartTime = DateUtil.offsetSecond(endAt, -liveServiceProperties.getCountdownBeforeLiveEnd());
        var isInCountdown = DateUtil.isIn(now, countdownStartTime, endAt);
        if (!isInCountdown) {
            LogExUtil.infoLog(getConsumerName() + "出错，直播间不在倒计时时间内:" + body + " ");
            return;
        }
        // 发送开始倒计时的消息
        msgService.startLiveCountdown(liveRoom.getId(), DateUtil.between(now, endAt, DateUnit.SECOND));
    }

    @RabbitListener(queuesToDeclare = @Queue(name = MqConstant.LIVE_CLOSE_COUNTDOWN_DELAY, durable = "true"))
    @Override
    public void handle(Message message, Channel channel) {
        super.handle(message, channel);
    }
}
