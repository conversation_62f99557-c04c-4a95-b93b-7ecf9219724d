package com.bbh.live.service.buyer.vip.vo;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2024/9/21
 * @description: 买手vip整个时间过程使用累计情况
 */
@Data
public class VipBuyerWholeTimeUsedVO {

	/***
	 * 到目前为止所有开通一共使用了多少天
	 */
	private Long allUsedDays;

	/***
	 * 累计已省金额
	 */
	private BigDecimal saveMoney;

	/**
	 * 累计已省分贝
	 */
	private Long saveFenbei;


	/**
	 * 累计获得分贝
	 */
	private Long totalGetFenbei;


	/**
	 * 累计收到行情周报
	 */
	private Long totalWeekly;


	/**
	 * 累计查看出价人次数
	 */
	private Long totalPeep;


	/**
	 * 累计上帝视角使用次数
	 */
	private Long totalGodView;

}
