package com.bbh.live.service.buyer.orgmap.geo;

import com.bbh.base.Sort;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public final class GeoSearchParams {

    private Double longitude;
    private Double latitude;
    private Double radius;
    private GeoUnit unit;
    private Integer count;
    private boolean countAny;
    private Sort.Direction order;
    private String keywords;

    public GeoSearchParams(double longitude, double latitude) {
        this.longitude = longitude;
        this.latitude = latitude;
    }
}
