package com.bbh.live.service.livegoods.transfer.impl;

import cn.hutool.core.util.NumberUtil;
import com.bbh.enums.ErpGoodsSaleStatusEnum;
import com.bbh.enums.LiveGoodsStatusEnum;
import com.bbh.enums.LiveGoodsTradeTypeEnum;
import com.bbh.live.config.LiveServiceProperties;
import com.bbh.live.dao.dto.livegoods.LiveGoodsDTO;
import com.bbh.live.dao.mapper.LiveGoodsMapper;
import com.bbh.live.dao.service.GlobalOrgSeatService;
import com.bbh.live.handler.queue.DelayQueueManager;
import com.bbh.live.service.buyer.BuyerLiveGoodsOpService;
import com.bbh.live.service.livegoods.LiveGoodsDetailService;
import com.bbh.live.service.livegoods.LiveGoodsListService;
import com.bbh.live.service.livegoods.cache.LiveGoodsCacheManager;
import com.bbh.live.service.livegoods.context.LiveGoodsContext;
import com.bbh.live.service.livegoods.transfer.LiveGoodsTransfer;
import com.bbh.live.service.msg.MsgService;
import com.bbh.live.service.room.LiveRoomCacheService;
import com.bbh.live.service.room.context.LiveRoomContextHolder;
import com.bbh.util.AssertUtil;
import com.bbh.util.EnvironmentUtil;
import jakarta.annotation.Resource;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;
import java.util.Optional;

/**
 * <AUTHOR> dsy
 * @Date: 2024/7/29
 * @Description:
 */
public abstract class AbstractLiveGoodsTransfer<T extends LiveGoodsContext> implements LiveGoodsTransfer<T> {

    @Resource
    protected LiveGoodsDetailService liveGoodsDetailService;
    @Resource
    protected LiveGoodsListService liveGoodsListService;
    @Resource
    protected LiveGoodsCacheManager liveGoodsCacheManager;
    @Resource
    protected DelayQueueManager delayQueueManager;
    @Resource
    protected GlobalOrgSeatService globalOrgSeatService;
    @Resource
    protected LiveRoomCacheService liveRoomCacheService;
    @Resource
    protected BuyerLiveGoodsOpService buyerLiveGoodsOpService;
    @Resource
    protected MsgService msgService;
    @Resource
    protected LiveGoodsMapper liveGoodsMapper;
    @Resource
    protected LiveServiceProperties liveServiceProperties;

    @Override
    public void check(LiveGoodsDTO liveGoodsDTO, T context) {
        AssertUtil.assertNotNull(liveGoodsDTO, "商品不存在");

        doCheck(liveGoodsDTO, context);
    }

    /**
     * 子类校验
     * @param liveGoods
     * @param context
     */
    protected abstract void doCheck(LiveGoodsDTO liveGoods, T context);

    /**
     * 竞拍必须信息校验
     * @param liveGoods
     */
    protected void liveGoodsAuctionCanTransferCheck(LiveGoodsDTO liveGoods) {
        if (EnvironmentUtil.hasProfile("local")) {
            return;
        }
        AssertUtil.assertTrue(LiveRoomContextHolder.liveRoomIsTestBroadcast() || LiveRoomContextHolder.liveRoomIsUnderway(), "直播未开启，无法上架或竞拍商品");

        if(liveGoods.getTradeType() == LiveGoodsTradeTypeEnum.AUCTION){
            //拍卖时长校验
            AssertUtil.assertFalse(liveGoods.getAuctionDuration() == null || liveGoods.getAuctionDuration() <= 0, "拍卖时长须大于0秒");
            //加价幅度
            AssertUtil.assertFalse(liveGoods.getIncreasePrice() == null || liveGoods.getIncreasePrice().compareTo(BigDecimal.ZERO) <= 0, "加价幅度须大于0元");
        }

        //起拍价
        AssertUtil.assertFalse(liveGoods.getStartPrice() == null || liveGoods.getStartPrice().compareTo(BigDecimal.ZERO) < 0 || liveGoods.getStartPrice().compareTo(BigDecimal.valueOf(99999999)) > 0, "请输入有效的商品价格");

        AssertUtil.assertFalse(liveGoods.getErpIfLocked(), "商品已锁单，无法上架");
        AssertUtil.assertFalse(liveGoods.getErpPlaceOrderStatus() || liveGoods.getErpSaleStatus().equals(ErpGoodsSaleStatusEnum.SOLD_OUT.getType()), "商品已售出");
        //判断商品状态
        AssertUtil.assertFalse(liveGoods.getGoodsStatus().equals(LiveGoodsStatusEnum.TRADED), "商品已售出");
    }

    protected void canTradeCheck(LiveGoodsDTO liveGoods, T context) {
        //检查商品是否处于可交易状态
        liveGoodsDetailService.checkLiveGoodsCanAddToCashierDesk(liveGoods, true);
    }


    @Override
    public void action(LiveGoodsDTO liveGoodsDTO, T context) {
        doAction(liveGoodsDTO, context);
    }

    /**
     * 商品流转动作
     * @param liveGoods
     * @param context
     */
    protected abstract void doAction(LiveGoodsDTO liveGoods, T context);


    protected int actualAuctionTime(LiveGoodsDTO liveGoods) {
        //实际竞拍时长 = 竞拍结束时间 - 竞拍开始时间
        //结束时间
        Date auctionGoodsEndTime = Optional.ofNullable(liveGoodsCacheManager.getLiveGoodsAuctionCache().getAuctionGoodsEndTime(liveGoods.getLiveRoomId(), liveGoods.getId()))
                .orElse(new Date());
        //差值ms
        long actualAuctionTime = auctionGoodsEndTime.getTime() - liveGoods.getAuctionStartAt().getTime();
        //转秒，向上取整
        BigDecimal actualAuctionTimeSeconds = NumberUtil.div(new BigDecimal(actualAuctionTime), new BigDecimal(1000), 0, RoundingMode.UP);
        return actualAuctionTimeSeconds.intValue();
    }
}
