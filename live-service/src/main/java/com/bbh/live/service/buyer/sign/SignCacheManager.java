package com.bbh.live.service.buyer.sign;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.bbh.live.dao.service.GlobalSignRecordService;
import com.bbh.model.GlobalSignRecord;
import lombok.AllArgsConstructor;
import org.redisson.api.RBitSet;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.ArrayList;
import java.util.BitSet;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> dsy
 * @Date: 2024/8/15
 * @Description:
 */
@Component
@AllArgsConstructor
public class SignCacheManager {

    private final RedissonClient redisson;
    private final GlobalSignRecordService globalSignRecordService;

    private static final String SIGN_IN_KEY = "buyer_sign_key:%s:%s";
    private static final Integer DAYS_OF_WEEK = 7;
    public void todaySignIn(Long buyerSeatId) {
        signIn(new Date(), buyerSeatId);
    }

    public void signIn(Date date, Long buyerSeatId){
        int dayOfMonth = DateUtil.dayOfMonth(date);
        RBitSet bitSet = this.getMonthBitSet(date, buyerSeatId);
        bitSet.set(dayOfMonth, true);

        //设置过期时间，过期时间每个月末+7天，+7天用于星期跨月计算
        int lastDayOfMonth = DateUtil.getLastDayOfMonth(date);
        int expireDays = lastDayOfMonth - dayOfMonth + DAYS_OF_WEEK;
        bitSet.expire(Duration.ofDays(expireDays));
    }

    public boolean isTodaySignIn(Long buyerSeatId) {
        return isSign(new Date(), buyerSeatId);
    }

    public boolean isSign(Date date, Long buyerSeatId){
        return this.getMonthBitSet(date, buyerSeatId).get(DateUtil.dayOfMonth(date));
    }

    /**
     * 本月签到中，是否今天之前都已签到
     * @param date
     * @param buyerSeatId
     * @return
     */
    public boolean isSignEveryDayForMonthBeforeToday(Date date, Long buyerSeatId) {
        int dayOfMonth = DateUtil.dayOfMonth(date);
        long count = this.getMonthBitSet(date, buyerSeatId).cardinality();
        return count == (dayOfMonth - 1);
    }

    /**
     * 当前月签到日期集合
     * @param buyerSeatId 席位id
     * @return 日期集合，1：1号签到，31：31号签到
     */
    public List<Integer> signDayThisMonth(Date date, Long buyerSeatId){
        BitSet bitSet = this.getMonthBitSet(date, buyerSeatId).asBitSet();
        List<Integer> result = new ArrayList<>();
        int daysForMonth = DateUtil.dayOfMonth(date);
        for (int i = 0; i <= daysForMonth; i++) {
            if (bitSet.get(i)) {
                result.add(i);
            }
        }
        return result;
    }

    /**
     * 获取连续签到天数 不限制本周
     * @param now
     * @param buyerSeatId
     * @return
     */
    public int getConsecutiveSignDays(Date now, Long buyerSeatId){
        Date yesterday = DateUtil.offsetDay(now, -1);
        GlobalSignRecord yesterdaySignRecord = globalSignRecordService.lambdaQuery()
                .eq(GlobalSignRecord::getCreateSeatId, buyerSeatId)
                .ge(GlobalSignRecord::getCreatedAt, DateUtil.beginOfDay(yesterday))
                .le(GlobalSignRecord::getCreatedAt, DateUtil.endOfDay(yesterday))
                .one();
        if(yesterdaySignRecord == null || (yesterdaySignRecord.getConsecutiveDays() == 7)){
            return  1;
        }
        return yesterdaySignRecord.getConsecutiveDays()+1;
    }

    /**
     * 本周连续签到天数
     * @param now
     * @param buyerSeatId
     * @return
     */
    public int getConsecutiveSignDaysThisWeek(Date now, Long buyerSeatId){
        //一为周一，七为周日
        int dayOfWeek = DateUtil.dayOfWeek(now);
        dayOfWeek = dayOfWeek == 1 ? 7 : dayOfWeek - 1;
        //连续签到天数
        int consecutiveSignDaysThisWeek = 1;
        //这周连续签到天数
        List<Integer> signDaysThisWeek = this.signDayThisWeek(now, buyerSeatId);
        while(dayOfWeek > 0 && signDaysThisWeek.contains(--dayOfWeek)){
            consecutiveSignDaysThisWeek++;
        }
        return consecutiveSignDaysThisWeek;
    }

    /**
     * 这一周签到的星期集合
     * @param date 本周
     * @param buyerSeatId
     * @return
     */
    public List<Integer> signDayThisWeek(Date date, Long buyerSeatId){
        List<Integer> result = new ArrayList<>();
        BitSet bitSet = this.getMonthBitSet(date, buyerSeatId).asBitSet();
        //今天在月中的天数
        int daysOfMonth = DateUtil.dayOfMonth(date);
        // 今天是这个星期的第几天，星期一是第一天
        int dayOfWeek = DateUtil.dayOfWeek(date);
        dayOfWeek = dayOfWeek == 1 ? 7 : dayOfWeek - 1;

        BitSet weekBitSet;
        // 今天在月中的位置大于今天在周中的位置，则代表整个星期都在本月
        // 则只需要从当前月的bitset中取签到记录，否则需要从上个月bitset中取签到记录
        if(daysOfMonth > dayOfWeek){
            //截取当前周的bitset，endIndex不包含，所以需要+1
            weekBitSet = bitSet.get(daysOfMonth - dayOfWeek, daysOfMonth + 1);
        }else {
            /**
             * 上个月中本周的天数 例如今天周五 1号 diff = 5 - 1 = 4
             *   一   二  三  四  五  六  日
             *   28  29  30  31  1  2   3
             */
            int diff = dayOfWeek - daysOfMonth;
            //上个月签到记录
            Date lastMonth = DateUtil.offsetMonth(date, -1);
            BitSet lastMonthBitSet = getMonthBitSet(lastMonth, buyerSeatId).asBitSet();
            //上个月天数
            int daysOfLastMonth = DateUtil.getLastDayOfMonth(lastMonth);
            //上个月剩余星期bitset
            BitSet weekOfLastMonthBitSet = lastMonthBitSet.get(daysOfLastMonth - diff, daysOfLastMonth + 1);
            weekBitSet = bitSet.get(0, daysOfMonth + 1);
            weekBitSet = combinedBitSet(weekOfLastMonthBitSet, weekBitSet, diff);
        }
        for (int i = 1; i <= dayOfWeek; i++) {
            if (weekBitSet.get(i)) {
                result.add(i);
            }
        }

        return result;
    }

    private RBitSet getMonthBitSet(Date date, Long buyerSeatId){
        String signKey = buildSignKey(date, buyerSeatId);
        if(!redisson.getBitSet(signKey).isExists()){
            loadMonthSignBitSet(date, buyerSeatId, signKey);
        }
        return redisson.getBitSet(signKey);
    }

    private void loadMonthSignBitSet(Date date, Long buyerSeatId, String signKey) {
        // 指定月开始日期
        Date monthBegin = DateUtil.beginOfMonth(date);
        // 指定月结束日期
        Date monthEnd = DateUtil.endOfMonth(date);
        // 指定月签到记录
        List<GlobalSignRecord> signRecords = globalSignRecordService.lambdaQuery()
                .eq(GlobalSignRecord::getCreateSeatId, buyerSeatId)
                .between(GlobalSignRecord::getCreatedAt, monthBegin, monthEnd).list();

        BitSet bitSet = new BitSet();

        if(!signRecords.isEmpty()){
            for (GlobalSignRecord signRecord : signRecords) {
                int dayOfMonth = DateUtil.dayOfMonth(signRecord.getCreatedAt());
                bitSet.set(dayOfMonth, true);
            }
        }
        RBitSet rBitSet = redisson.getBitSet(signKey);
        rBitSet.set(bitSet);
        rBitSet.expire(Duration.ofDays(DateUtil.dayOfMonth(date)));
    }

    private BitSet combinedBitSet(BitSet first, BitSet second, int diff){
        BitSet newBitSet = new BitSet();
        for (int i = 1; i <= diff; i++) {
            newBitSet.set(i, first.get(i));
        }
        for (int i = second.nextSetBit(0); i >= 0; i = second.nextSetBit(i + 1)) {
            newBitSet.set(i + diff);
        }
        return newBitSet;
    }

    private String buildSignKey(Date date, Long buyerSeatId) {
        String yearAndMonthFormat = DateUtil.format(date, DatePattern.SIMPLE_MONTH_PATTERN);
        return String.format(SIGN_IN_KEY, yearAndMonthFormat, buyerSeatId);
    }
}
