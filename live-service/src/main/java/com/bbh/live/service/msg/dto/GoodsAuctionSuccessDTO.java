package com.bbh.live.service.msg.dto;

import com.bbh.live.core.msg.MsgType;
import com.bbh.live.service.msg.dto.base.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;

/**
 * 竞拍成交
 * <AUTHOR> dsy
 * @Date: 2024/8/1
 * @Description:
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
public class GoodsAuctionSuccessDTO extends BaseAuctionLimitMsg implements IGodViewMsg {

    @Override
    public String type() {
        return MsgType.AUCTION_SUCCESS;
    }

    private BaseGoods goods;

    private BaseSeat user;

    /**
     * 试播客服id集合
     */
    private List<Long> testBroadcastSeatIdList;

    /**
     * 直播间是否正在试播
     */
    private Boolean ifTestBroadcast;

    /**
     * 成交价格
     */
    private BigDecimal belongPrice;

    private List<?> godViewList;

    private Integer tradeType;

    @Override
    public void setGodViewList(List<?> godViewList) {
        this.godViewList = godViewList;
    }

}
