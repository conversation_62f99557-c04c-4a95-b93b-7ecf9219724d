package com.bbh.live.service.livegoods;

import com.bbh.live.dao.dto.GoodsTransferDTO;
import com.bbh.live.dao.dto.vo.AuctionBidVO;

/**
 * <AUTHOR>
 * @date 2024/9/27 09:28
 * @description
 */
public interface LiveGoodsTransferBizService {

    /**
     * 商品传送
     *
     * @param goodsTransferDTO
     * @return
     */
    AuctionBidVO goodsTransfer(GoodsTransferDTO goodsTransferDTO);

    /**
     * 取消商品传送
     * @param goodsTransferDTO
     */
    void cancel(GoodsTransferDTO goodsTransferDTO);

    /**
     * 时效检查 状态检查 不能 一货多传
     *
     * @param goodsTransferDTO 商品转移的DTO（数据传输对象），包含了商品转移的相关信息
     */
    void timesAndStatusCheck(GoodsTransferDTO goodsTransferDTO);
}
