package com.bbh.live.service.virutalgoods.processor;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.bbh.enums.AuditTypeEnum;
import com.bbh.enums.ResponseCode;
import com.bbh.exception.ServiceException;
import com.bbh.feign.IDepositApiClient;
import com.bbh.live.dao.service.GlobalOrgSeatService;
import com.bbh.model.GlobalOrderOtherGoodsDic;
import com.bbh.model.GlobalVirtualGoodsOrder;
import com.bbh.service.deposit.dto.RechargeDepositDTO;
import com.bbh.util.LogExUtil;
import com.bbh.vo.Result;
import jakarta.annotation.Resource;
import lombok.AllArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

/**
 * 充值保证金
 * <AUTHOR>
 */
@Service
@AllArgsConstructor
public class RechargeDepositOrderProcessor implements IVirtualOrderProcessor {

    private final Logger logger = LoggerFactory.getLogger(RechargeDepositOrderProcessor.class);

    @Resource
    private IDepositApiClient depositApiClient;
    private final GlobalOrgSeatService globalOrgSeatService;

    @SuppressWarnings("all")
    @Override
    public Result process(VirtualOrderProcessContext context) {
        GlobalVirtualGoodsOrder order = context.getOrder();
        GlobalOrderOtherGoodsDic goodsInfo = context.getGoods();

        // 充值金额
        RechargeDepositDTO param = new RechargeDepositDTO();
        param.setDeposit(order.getOrderPrice());
        param.setOsn(order.getOrderNo());
        param.setInOrgId(order.getOrgId());
        param.setInType(getInType(order.getExtraData()));
        param.setCreateId(context.getOrder().getBuyerSeatId());
        param.setCreateName(globalOrgSeatService.getSeatName(context.getOrder().getBuyerSeatId()));
        Result result = depositApiClient.recharge(param);
        if (result == null) {
            LogExUtil.errorLog("充值保证金失败", new ServiceException("充值保证金失败"), logger);
            throw new ServiceException("充值保证金失败");
        }
        if (result.getCode() != ResponseCode.SUCCESS.getCode()) {
            LogExUtil.errorLog(result.getMsg(), new ServiceException("充值保证金失败: " + result.getMsg()), logger);
            throw new ServiceException("充值保证金失败: " + result.getMsg());
        }
        return Result.ok();
    }

    private AuditTypeEnum getInType(String extraData) {
        if (StrUtil.isNotEmpty(extraData) && JSONUtil.isTypeJSON(extraData)) {
            int type = JSONUtil.parseObj(extraData).getInt("type");
            // 10 买家 20卖家
            switch (type) {
                case 10:
                    return AuditTypeEnum.BUYER;
                case 20:
                    return AuditTypeEnum.SELLER;
            }
        }
        return AuditTypeEnum.BUYER;
    }
}
