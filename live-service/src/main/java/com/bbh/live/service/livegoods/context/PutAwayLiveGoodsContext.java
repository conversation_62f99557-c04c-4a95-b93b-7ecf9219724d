package com.bbh.live.service.livegoods.context;

import com.bbh.enums.LiveGoodsTradeTypeEnum;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <AUTHOR> dsy
 * @Date: 2024/7/29
 * @Description:
 */
@Data
@Accessors(chain = true)
public class PutAwayLiveGoodsContext extends LiveGoodsContext{

    /**
     * 起拍价
     */
    private BigDecimal startPrice;

    /**
     * 加价幅度
     */
    private BigDecimal increasePrice;

    /**
     * 竞拍时长，上架时设置
     */
    private Integer auctionDuration;

    /**
     * 导播备注
     */
    private String directorRemark;

    /**
     * 贸易方式
     */
    private LiveGoodsTradeTypeEnum tradeType;
}
