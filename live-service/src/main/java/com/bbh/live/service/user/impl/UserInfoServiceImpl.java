package com.bbh.live.service.user.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bbh.live.dao.dto.AggregatedUserDTO;
import com.bbh.live.dao.dto.QuerySimpleUserDTO;
import com.bbh.live.dao.dto.SimpleUserInfoDTO;
import com.bbh.live.dao.mapper.GlobalOrganizationMapper;
import com.bbh.live.dao.service.GlobalOrgSeatService;
import com.bbh.live.service.buyer.vip.BuyerVipService;
import com.bbh.live.service.buyer.vip.vo.UserBuyerVipInfoVO;
import com.bbh.live.service.user.UserInfoService;
import com.bbh.model.GlobalOrgSeat;
import com.bbh.model.GlobalOrganization;
import com.bbh.secure.AuthUtil;
import com.bbh.vo.AuthUser;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
@Slf4j
public class UserInfoServiceImpl implements UserInfoService {

    private final GlobalOrganizationMapper globalOrganizationMapper;
    private final GlobalOrgSeatService globalOrgSeatService;
    private final BuyerVipService buyerVipService;

    /**
     * 当前登录用户的信息
     *
     * @return 完整的用户信息
     */
    @Override
    public AggregatedUserDTO current() {
        AuthUser user = AuthUtil.getUser();

        AggregatedUserDTO aggregatedUserDTO = new AggregatedUserDTO();
        aggregatedUserDTO.setSeatId(user.getSeatId());
        aggregatedUserDTO.setUserId(user.getUserId());
        aggregatedUserDTO.setOrgId(user.getOrgId());

        // 获取商户的分贝余额
        GlobalOrganization organization = globalOrganizationMapper.selectById(user.getOrgId());
        aggregatedUserDTO.setOrganization(organization);

        // 买手会员权限
        GlobalOrgSeat seat = globalOrgSeatService.getById(user.getSeatId());
        aggregatedUserDTO.setSeat(seat);

        // 买手会员
        UserBuyerVipInfoVO buyerVip = buyerVipService.getUserBuyerVipInfoBySeatId(user.getSeatId());
        aggregatedUserDTO.setBuyerVip(buyerVip);

        return aggregatedUserDTO;
    }

    /**
     * 分页查询用户信息
     *
     * @param page  分页
     * @param query 查询条件
     * @return 分页结果
     */

    /**
     * @param seatId    席位id
     * @return          用户信息
     */
    @Override
    public SimpleUserInfoDTO getSimpleInfoBySeatId(Long seatId) {
        QuerySimpleUserDTO query = new QuerySimpleUserDTO();
        query.setSeatId(seatId);
        IPage<SimpleUserInfoDTO> page = globalOrgSeatService.selectPageWithVip(Page.of(1, 1), query);
        if (page.getTotal() > 0) {
            return page.getRecords().getFirst();
        }
        return null;
    }
}
