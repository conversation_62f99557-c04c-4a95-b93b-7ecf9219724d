package com.bbh.live.service.buyer.vip.impl;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.bbh.enums.GlobalBizTypeEnum;
import com.bbh.live.config.GlobalBizProperties;
import com.bbh.live.dao.dto.GodViewTrialStatusDTO;
import com.bbh.live.dao.mapper.GlobalOrgSeatMapper;
import com.bbh.live.dao.mapper.VipBuyerPutAwayFreeViewMapper;
import com.bbh.live.dao.mapper.VipBuyerPutAwayViewLogMapper;
import com.bbh.live.enums.BuyerVipTypeEnum;
import com.bbh.live.service.buyer.vip.BuyerVipService;
import com.bbh.live.service.buyer.vip.GodViewService;
import com.bbh.live.service.buyer.vip.vo.UserBuyerVipInfoVO;
import com.bbh.model.GlobalOrgSeat;
import com.bbh.model.VipBuyerPutAwayFreeView;
import com.bbh.model.VipBuyerPutAwayViewLog;
import com.bbh.secure.AuthUtil;
import com.bbh.vo.AuthUser;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.Objects;

/**
 * 上帝视角
 * <AUTHOR>
 */
@Service
@AllArgsConstructor
public class GodViewServiceImpl implements GodViewService {

    private final GlobalBizProperties globalBizProperties;
    private final GlobalOrgSeatMapper globalOrgSeatMapper;
    private final VipBuyerPutAwayFreeViewMapper vipBuyerPutAwayFreeViewMapper;
    private final VipBuyerPutAwayViewLogMapper vipBuyerPutAwayViewLogMapper;
    private final BuyerVipService buyerVipService;

    /**
     * 获取用户的上帝视角体验状态
     */
    @Override
    public GodViewTrialStatusDTO getGodViewTrialStatus() {
        var user = AuthUtil.getUser();
        GlobalOrgSeat seat = globalOrgSeatMapper.selectById(user.getSeatId());

        // 试用截止时间
        Date godViewTrialExpireAt = Objects.requireNonNullElse(seat.getGodViewTrialExpireAt(), DateUtil.offsetSecond(new Date(), -1));
        // 试用剩余次数
        int godViewTrialRemainCount = Objects.requireNonNullElse(seat.getGodViewTrialRemainCount(), 0);

        // 查询席位上的试用时间和试用次数，只要有一个达到限制就不允许试用
        boolean canFreeView = godViewTrialRemainCount > 0 && godViewTrialExpireAt.after(new Date());

        // 查询首次点击的时间
        VipBuyerPutAwayFreeView putAwayFreeView = vipBuyerPutAwayFreeViewMapper.selectOne(Wrappers.lambdaQuery(VipBuyerPutAwayFreeView.class)
                .eq(VipBuyerPutAwayFreeView::getSeatId, user.getSeatId())
        );
        Date firstClickTime = putAwayFreeView == null ? null : putAwayFreeView.getCreatedAt();

        // 组装成试用状态
        var godViewTrialStatusDTO = new GodViewTrialStatusDTO();
        godViewTrialStatusDTO.setCanFreeView(canFreeView);
        godViewTrialStatusDTO.setCreateTime(firstClickTime);
        godViewTrialStatusDTO.setFreeViewUnUsedTimes(godViewTrialRemainCount);
        godViewTrialStatusDTO.setFreeCountSecondsCountdown(DateUtil.between(godViewTrialExpireAt, new Date(), DateUnit.SECOND));
        return godViewTrialStatusDTO;
    }

    /**
     * 获取全局的上帝视角体验状态
     */
    @Override
    public Boolean getGodViewGlobalTrial() {
        return globalBizProperties.getPutAwayAllFreeViewSwitch();
    }

    /**
     * 添加点击商品次数
     *
     * @return 剩余次数
     */
    @Transactional
    @Override
    public Integer addGodViewUseCount(Long liveGoodsId, Long liveRoomId) {
        var user = AuthUtil.getUser();

        // 查询会员信息
        var vipInfo = buyerVipService.getUserBuyerVipInfoBySeatId(user.getSeatId());

        // 如果不是会员，再判断是否在试用范围内
        int remainCount = 0;
        if (vipInfo.getBuyerVipType() != BuyerVipTypeEnum.VIP) {
            var status = getGodViewTrialStatus();
            if (status.getCanFreeView()) {
                remainCount = logFreeTrialGodView(user);
            }
        }

        // 所有人都要记录日志
        logVipGodView(user, vipInfo, liveGoodsId, liveRoomId);

        // 返回剩余次数
        return vipInfo.getBuyerVipType() == BuyerVipTypeEnum.VIP ? Integer.MAX_VALUE : remainCount;
    }

    /**
     * 记录免费试用用户的商品查看日志，并返回剩余查看次数。
     *
     * @param user 当前认证用户
     * @return 剩余的免费试用查看次数
     */
    private Integer logFreeTrialGodView(AuthUser user) {
        // 商品点击次数 查看记录表
        VipBuyerPutAwayFreeView putAwayFreeView = vipBuyerPutAwayFreeViewMapper.selectOne(Wrappers.lambdaQuery(VipBuyerPutAwayFreeView.class)
                .eq(VipBuyerPutAwayFreeView::getSeatId, user.getSeatId())
        );

        // 首次查看，要进行创建，并且默认为1次
        if (putAwayFreeView == null) {
            putAwayFreeView = new VipBuyerPutAwayFreeView();
            putAwayFreeView.setSeatId(user.getSeatId());
            putAwayFreeView.setCreatedAt(new Date());
            putAwayFreeView.setFreeViewUsedTimes(1L);
            vipBuyerPutAwayFreeViewMapper.insert(putAwayFreeView);
        } else {
            // 更新记录
            LambdaUpdateWrapper<VipBuyerPutAwayFreeView> luw = new LambdaUpdateWrapper<>();
            luw.eq(VipBuyerPutAwayFreeView::getSeatId, user.getSeatId())
                    .setIncrBy(VipBuyerPutAwayFreeView::getFreeViewUsedTimes, 1);
            vipBuyerPutAwayFreeViewMapper.update(luw);

            // 更新剩余次数
            globalOrgSeatMapper.update(Wrappers.lambdaUpdate(GlobalOrgSeat.class)
                    .eq(GlobalOrgSeat::getId, user.getSeatId())
                    .setDecrBy(GlobalOrgSeat::getGodViewTrialRemainCount, 1)
            );
        }

        // 查询试用剩余次数
        GlobalOrgSeat seat = globalOrgSeatMapper.selectById(user.getSeatId());
        return Objects.requireNonNullElse(seat.getGodViewTrialRemainCount(), 0);
    }

    /**
     * 记录VIP买家查看商品的日志，并返回剩余查看次数。
     *
     * @param user 当前认证用户
     * @param vipInfo VIP买家信息
     * @param liveGoodsId 直播商品ID
     * @param liveRoomId 直播间ID
     */
    private void logVipGodView(AuthUser user, UserBuyerVipInfoVO vipInfo, Long liveGoodsId, Long liveRoomId) {
        var viewLog = new VipBuyerPutAwayViewLog();
        viewLog.setSeatId(user.getSeatId());
        viewLog.setOrgId(user.getOrgId());
        // 非会员也要存日志
        viewLog.setVipBuyerCardId(vipInfo == null || vipInfo.getId() == null || vipInfo.getBuyerVipType() != BuyerVipTypeEnum.VIP ? 0L : vipInfo.getId());
        viewLog.setBizGoodsId(liveGoodsId);
        viewLog.setBizId(liveRoomId);
        viewLog.setBizType(GlobalBizTypeEnum.LIVE);
        viewLog.setCreatedAt(new Date());
        vipBuyerPutAwayViewLogMapper.insert(viewLog);
    }
}
