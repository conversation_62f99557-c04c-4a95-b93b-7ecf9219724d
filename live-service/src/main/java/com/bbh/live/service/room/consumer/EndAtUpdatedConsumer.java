package com.bbh.live.service.room.consumer;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import com.bbh.live.service.msg.MsgService;
import com.bbh.live.service.room.LiveStreamService;
import com.bbh.model.LiveRoom;
import com.bbh.service.mq.constants.MqConstant;
import com.rabbitmq.client.Channel;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;

/**
 * 直播间结束时间更新
 * <AUTHOR>
 */
@Component(MqConstant.ADMIN_UPDATE_LIVE_ROOM_END_AT)
@Slf4j
public class EndAtUpdatedConsumer extends AbstractRoomConsumer {

    @Resource
    private MsgService msgService;
    @Resource
    private LiveStreamService liveStreamService;

    @Override
    protected String getConsumerName() {
        return "直播间关闭时间更新";
    }

    @Override
    protected void processLiveRoom(LiveRoom liveRoom, String body) {
        // 然后拿到最新的结束时间
        var endAt = liveRoom.getEndAt();
        var now = DateUtil.date();

        // 先发送结束倒计时消息
        msgService.closeLiveCountdown(liveRoom.getId());

        // 判断结束时间是否还在3分钟内，如果还在就发送开始倒计时消息
        var countdownStartTime = DateUtil.offsetSecond(endAt, -liveServiceProperties.getCountdownBeforeLiveEnd());
        // 如果在倒计时和结束时间之间，就要显示倒计时
        var isInCountdown = DateUtil.isIn(now, countdownStartTime, endAt);
        if (isInCountdown) {
            msgService.startLiveCountdown(liveRoom.getId(), DateUtil.between(now, endAt, DateUnit.SECOND));
        }

        // 重新生产延迟队列
        liveStreamService.produceLiveDelayQueue(liveRoom);
    }

    @RabbitListener(queuesToDeclare = @Queue(name = MqConstant.ADMIN_UPDATE_LIVE_ROOM_END_AT, durable = "true"))
    @Override
    public void handle(Message message, Channel channel) {
        super.handle(message, channel);
    }
}
