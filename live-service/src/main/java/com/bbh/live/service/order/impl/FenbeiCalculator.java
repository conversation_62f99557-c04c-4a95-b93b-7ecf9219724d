package com.bbh.live.service.order.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.bbh.live.config.LiveServiceProperties;
import com.bbh.live.dao.dto.AggregatedUserDTO;
import com.bbh.live.dao.dto.LiveShoppingCartDTO;
import com.bbh.live.dao.mapper.GlobalOrgSeatMapper;
import com.bbh.live.dao.mapper.GlobalOrganizationMapper;
import com.bbh.live.dao.service.VipBuyerConfigService;
import com.bbh.live.service.buyer.vip.BuyerVipService;
import com.bbh.live.service.order.support.FenbeiCalcUtil;
import com.bbh.live.service.vipdeduction.GlobalVipDeductionService;
import com.bbh.model.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.RequiredArgsConstructor;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * 分贝计算器 1.买家支付商品时,可用分贝进行抵扣(如您的商品金额>125元,最大抵扣为该商品价格的0.2%元,也就是说1万块 钱的商品,可以使用80分贝,抵扣最大数量为20元)。
 * 2.普通用户可以用分贝抵扣部分线上支付第三方通道费(0.2%),VIP买手可以用分贝全额抵扣线上支付第三方通道 费 (0.6%)。 3. 超额抵扣：vip比普通用户多抵扣的部分
 *
 * <AUTHOR>
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class FenbeiCalculator {

    private final GlobalOrgSeatMapper globalOrgSeatMapper;
    private final GlobalOrganizationMapper globalOrganizationMapper;
    private final VipBuyerConfigService vipBuyerConfigService;
    private final LiveServiceProperties liveServiceProperties;
    private final GlobalVipDeductionService globalVipDeductionService;

    @Value("${htb.annual_vip_deduction:6188}")
    private BigDecimal annualVipDeduction;

    /**
     * 根据购物车项目计算分贝抵扣。
     *
     * @param items 购物车项目列表
     * @param user  聚合用户信息
     * @return 分贝计算器DTO，包含抵扣信息
     */
    public OnlineResult calculateOnlineDeductionFromCart(List<LiveShoppingCartDTO> items, AggregatedUserDTO user) {
        List<GlobalOrderItem> orderItemList = items.stream().map(x -> {
            GlobalOrderItem orderItem = new GlobalOrderItem();
            orderItem.setChannelAmount(x.getChannelAmount());
            orderItem.setNeedPayAmount(x.getSellPrice().add(x.getChannelAmount()).add(x.getBuyerServiceAmount()));
            return orderItem;
        }).collect(Collectors.toList());
        return calculateFenbeiDeduction(orderItemList, user, true);
    }

    public OfflineResult calculateOfflineDeductionFromCart(List<LiveShoppingCartDTO> items, AggregatedUserDTO user) {
        // 统计出有几场
        long count = items.stream().map(LiveShoppingCartDTO::getLiveRoomId).distinct().count();
        return calculateOfflineDeduction((int) count, user);
    }

    /**
     * 计算线下支付需要扣除的分贝数量
     *
     * @param roomCount 直播间数量
     * @param user      用户信息，包含会员状态和配置信息
     * @return OfflineResult 包含计算结果的对象
     */
    public OfflineResult calculateOfflineDeduction(long roomCount, AggregatedUserDTO user) {
        // 如果BuyerVip为null，视为普通用户
        boolean isVip = user.getBuyerVip() != null && Boolean.TRUE.equals(user.getBuyerVip().getIsVip());
        // 每场抵扣数量：普通用户固定50，会员是会员的
        int standardDeductNum = isVip ? user.getBuyerVip().getVipConfig().getOfflineFbDeductNum()
                : liveServiceProperties.getDefaultFenbeiDeductNum();

        // 开通会员后的每场抵扣数量
        VipBuyerConfig commonV0Config = vipBuyerConfigService.getCommonV0Config();
        int vipDeductFenbeiPerMatch = commonV0Config.getOfflineFbDeductNum();

        // 本次应该扣除的分贝总数 = 场次数量 * 每场会扣除的数量
        long totalDeductFenbei = roomCount * standardDeductNum;

        return new OfflineResult().setMatchCount(roomCount).setDeductFenbeiPerMatch(standardDeductNum)
                .setTotalDeductFenbei(totalDeductFenbei)
                .setDefaultDeductFenbeiPerMatch(liveServiceProperties.getDefaultFenbeiDeductNum())
                .setVipDeductFenbeiPerMatch(vipDeductFenbeiPerMatch);
    }

    /**
     * 根据订单项目计算分贝抵扣。
     *
     * @param items  全局订单项目列表
     * @param seatId 席位ID
     * @return 分贝计算器DTO，包含抵扣信息
     */
    public OnlineResult calculateFenbeiDeductionFromOrderItems(List<GlobalOrderItem> items, Long seatId) {
        GlobalOrgSeat orgSeat = globalOrgSeatMapper.selectById(seatId);
        globalOrganizationMapper.selectById(orgSeat.getOrgId());

        AggregatedUserDTO user = new AggregatedUserDTO();
        user.setUserId(orgSeat.getUserId());
        user.setNickName(orgSeat.getNickName());
        user.setShowName(orgSeat.getShowName());
        user.setSeatId(seatId);
        user.setOrgId(orgSeat.getOrgId());
        user.setBuyerVip(SpringUtil.getBean(BuyerVipService.class).getUserBuyerVipInfoBySeatId(seatId));
        user.setSeat(orgSeat);
        return calculateFenbeiDeduction(items, user, true);
    }

    /**
     * 计算线下转账的分贝扣减
     *
     * @param orderItemList 订单项列表
     * @param user          用户信息
     * @return 分贝计算结果DTO
     */
    public OfflineResult calculateOfflineDeduction(List<GlobalOrderItem> orderItemList, AggregatedUserDTO user) {
        long roomCount = orderItemList.stream().map(GlobalOrderItem::getBizId).distinct().count();
        return calculateOfflineDeduction(roomCount, user);
    }

    /**
     * 计算线上支付的分贝抵扣，同时会更新OrderItem
     *
     * @param items       订单项列表
     * @param user        用户信息
     * @param ifUseFenbei 是否使用分贝抵扣
     * @return 分贝计算结果DTO
     */
    public OnlineResult calculateFenbeiDeduction(List<GlobalOrderItem> items, AggregatedUserDTO user,
                                                 boolean ifUseFenbei) {
        GlobalOrganization organization = user.getOrganization();

        // 计算总通道费 需要 一个一个下去找 能抵扣的 vip抵扣金
        BigDecimal channelAmount =
                items.stream().map(GlobalOrderItem::getChannelAmount).reduce(BigDecimal.ZERO, BigDecimal::add);

        // 分贝余额
        int fenbeiBalance = organization.getFenbei();
        // 判断用户是否为买手会员 分贝可以全额抵扣通道费（非VIP维持目前抵扣0.2%）
        boolean isBuyerVip = user.getBuyerVip() != null && Boolean.TRUE.equals(user.getBuyerVip().getIsVip());

        Long vipDeductionId = 0L;
        GlobalVipDeduction newDeduction = null;
        // 年会抵扣金
        if (isBuyerVip && user.getBuyerVip().getIsAnnualFeeVip()) {
            newDeduction = globalVipDeductionService.getOne(
                    Wrappers.lambdaQuery(GlobalVipDeduction.class).eq(GlobalVipDeduction::getOrgId, organization.getId())
                            .ge(GlobalVipDeduction::getEndAt, DateUtil.now())
                            .lt(GlobalVipDeduction::getUsedVipDeduction, NumberUtil.sub(annualVipDeduction, channelAmount))
                            .orderByAsc(GlobalVipDeduction::getId).select(GlobalVipDeduction::getId, GlobalVipDeduction::getUsedVipDeduction, GlobalVipDeduction::getVipDeduction)
                            .last("limit 1"),
                    false);

            if (ObjUtil.isNotNull(newDeduction)) {
                fenbeiBalance =
                        NumberUtil.mul(NumberUtil.sub(newDeduction.getVipDeduction(), newDeduction.getUsedVipDeduction()), BigDecimal.valueOf(4)).intValue();
                vipDeductionId = newDeduction.getId();
            }
        }

        // 分配分贝抵扣
        FenbeiCalcUtil.FenbeiDeductionResult distributed =
                FenbeiCalcUtil.distributeDeduction(items, fenbeiBalance, isBuyerVip, ifUseFenbei);

        // 实际抵扣的
        AtomicInteger actualDeductFenbeiCount = new AtomicInteger(0);
        BigDecimal actualDeductFenbeiAmount = BigDecimal.ZERO;

        OnlineResult result = new OnlineResult();

        if (ifUseFenbei) {
            // 如果下单时使用了分贝抵扣，还得给应支付金额减去分贝抵扣
            for (GlobalOrderItem x : items) {
                BigDecimal fenbeiDeductionAmount =
                        Optional.ofNullable(x.getFenbeiDeductionAmount()).orElse(BigDecimal.ZERO);
                int fenbeiDeductionCount = Optional.ofNullable(x.getFenbeiDeductionCount()).orElse(0);
                BigDecimal needPayAmount = x.getNeedPayAmount().subtract(fenbeiDeductionAmount);
                x.setNeedPayAmount(needPayAmount);
                x.setFenbeiDeductionAmount(fenbeiDeductionAmount);
                x.setFenbeiDeductionCount(fenbeiDeductionCount);
                // 累加分贝抵扣
                actualDeductFenbeiCount.addAndGet(fenbeiDeductionCount);
                actualDeductFenbeiAmount = actualDeductFenbeiAmount.add(fenbeiDeductionAmount);
            }

            if (vipDeductionId > 0L) {
                result.setVipDeduction(NumberUtil.div(fenbeiBalance, BigDecimal.valueOf(4), 2));
                result.setUsedVipDeduction(newDeduction.getUsedVipDeduction());
            }
        }


        result.setCanDeductionFenbei(actualDeductFenbeiCount.get() > 0);
        result.setTotalFenbeiDeductionCount(distributed.getTotalFenbeiDeductionCount());
        result.setTotalFenbeiDeductionAmount(distributed.getTotalFenbeiDeductionAmount());
        result.setVipMaxFenbeiCount(distributed.getVipMaxFenbeiCount());
        result.setVipMaxFenbeiAmount(distributed.getVipMaxFenbeiAmount());

        result.setVipDeductionId(vipDeductionId);

        if (isBuyerVip) {
            // 计算超额费用
            BigDecimal overAmount = getOverAmount(items);
            result.setOverDeductionFenbeiAmount(overAmount);
        }

        // 计算实际抵扣的
        result.setActualDeductFenbeiAmount(actualDeductFenbeiAmount);
        result.setActualDeductFenbeiCount(actualDeductFenbeiCount.get());

        log.info("计算后的OrderItemList = {}", JSONUtil.toJsonStr(items));
        log.info("计算后的OnlineResult = {}", JSONUtil.toJsonStr(result));

        return result;
    }

    /**
     * 计算超额的费用 = 总抵扣金额 / 3 * 2
     */
    private BigDecimal getOverAmount(List<GlobalOrderItem> orderItemList) {
        BigDecimal totalFenbeiDiscountMoney =
                orderItemList.stream().map(x -> Optional.ofNullable(x.getFenbeiDeductionAmount()).orElse(BigDecimal.ZERO))
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
        return totalFenbeiDiscountMoney.divide(BigDecimal.valueOf(3), 2, RoundingMode.UP)
                .multiply(BigDecimal.valueOf(2));
    }

    @EqualsAndHashCode(callSuper = true)
    @Data
    public static class OnlineResult extends FenbeiCalcUtil.FenbeiDeductionResult {

        /**
         * 能否使用分贝抵扣
         */
        private Boolean canDeductionFenbei = false;

        /**
         * 超额抵扣的金额
         */
        private BigDecimal overDeductionFenbeiAmount = BigDecimal.ZERO;

        /**
         * 实际扣除的分贝数量
         */
        private Integer actualDeductFenbeiCount = 0;

        /**
         * 实际扣除的分贝金额
         */
        private BigDecimal actualDeductFenbeiAmount = BigDecimal.ZERO;

        /**
         * 购买时使用的VIP抵扣ID
         */
        private Long vipDeductionId = 0L;

        private BigDecimal vipDeduction = BigDecimal.ZERO;
        private BigDecimal usedVipDeduction = BigDecimal.ZERO;

    }

    /**
     * 线下转账分贝计算结果
     */
    @Data
    @Accessors(chain = true)
    public static class OfflineResult {

        /**
         * 当前每场会扣除的数量，会员和普通用户共用这个
         */
        private long deductFenbeiPerMatch = 0L;

        /**
         * 本次应该扣除的分贝总数 = 场次数量 * 每场会扣除的数量
         */
        private long totalDeductFenbei = 0L;

        /**
         * 场次数量
         */
        private long matchCount = 0;

        /**
         * 普通用户，每场会扣除的数量
         */
        private long defaultDeductFenbeiPerMatch = 0L;

        /**
         * 开通会员后，每场会扣除的数量
         */
        private long vipDeductFenbeiPerMatch = 0L;
    }

}
