package com.bbh.live.service.permission;

import com.bbh.live.dao.dto.vo.AuctionBidVO;
import com.bbh.model.GlobalOrganization;

import java.math.BigDecimal;

/**
 * 权限
 * <AUTHOR>
 */
public interface PermissionService {

    /**
     * 获取当前用户的权限code列表，Map的key是不同模块，Map的value是code集合
     */
    Object getOwnList();

    /**
     * 当前用户是否导播或主播
     */
    boolean isDirectorOrAnchor();

    /**
     * 是否认证
     * @param orgId 商户ID
     * @return  是否认证
     */
    boolean isAuth(Long orgId);

    /**
     * 检查出价的权限
     * 检查商户是否认证、商户是否有结算账户
     */
    AuctionBidVO checkBidPermission(Long orgId, String actionName);

    AuctionBidVO checkBidPermission(Long orgId, Long seatId, String actionName);

    AuctionBidVO checkBidPermission(Long orgId);

    /**
     * 检查出价的权限
     * 检查当前登录的商户是否认证、商户是否有结算账户
     */
    AuctionBidVO checkBidPermission();

    /**
     * 检查出价的权限
     * 1. 商户是否认证
     * 3. 商户余额是否大于100（配置的最小检查单元）
     * 5. 商户可用余额（不含冻结）是否充足
     * @param orgId             出价人的商户ID
     * @param bidPrice          出价金额，允许为null
     * @param seatId            出价人的席位ID，当出价金额为null时，允许为null
     * @param liveGoodsId       商品ID，当出价金额为null时，允许为null
     * @param actionName        操作名称
     * @return                  出价结果
     */
    AuctionBidVO checkBidPermission(Long orgId, BigDecimal bidPrice, Long seatId, Long liveGoodsId, String actionName);

    /** 商家是否签约 */
    boolean checkOrgIsContract(GlobalOrganization organization);

    /** 商家是否有结算账户 */
    boolean checkOrgHasSettlementBank(GlobalOrganization organization);

    boolean hasSellerRights(Long orgId);

    boolean hasBuyerRights(Long seatId, Long orgId);
}
