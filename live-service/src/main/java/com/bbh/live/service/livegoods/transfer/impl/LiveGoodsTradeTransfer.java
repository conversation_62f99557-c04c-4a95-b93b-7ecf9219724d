package com.bbh.live.service.livegoods.transfer.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.bbh.enums.*;
import com.bbh.exception.ServiceException;
import com.bbh.live.dao.dto.BargainGoodsDTO;
import com.bbh.live.dao.dto.RateTemplateItemDTO;
import com.bbh.live.dao.dto.RoomRateTemplateDTO;
import com.bbh.live.dao.dto.livegoods.LiveGoodsDTO;
import com.bbh.live.dao.service.ErpGoodsService;
import com.bbh.live.dao.service.LiveRateTemplateService;
import com.bbh.live.dao.service.LiveRoomInteractiveMessageService;
import com.bbh.live.enums.LiveGoodsTradeCompletedTypeEnum;
import com.bbh.live.feign.SyncLiveGoodsToDataCenterService;
import com.bbh.live.service.livegoods.context.TradeLiveGoodsContext;
import com.bbh.live.service.order.CartService;
import com.bbh.live.service.room.LiveRoomBizService;
import com.bbh.live.thread.ThreadPoolManager;
import com.bbh.live.util.DepositUtils;
import com.bbh.model.ErpGoods;
import com.bbh.model.GlobalOrderItem;
import com.bbh.model.LiveGoods;
import com.bbh.model.LiveRoomInteractiveMessage;
import com.bbh.util.AssertUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR> dsy
 * @Date: 2024/7/29
 * @Description: 直播商品成交处理类
 */
@Slf4j
@Service("liveGoodsTradeTransfer")
public class LiveGoodsTradeTransfer extends AbstractLiveGoodsTransfer<TradeLiveGoodsContext> {

    @Resource
    private ErpGoodsService erpGoodsService;
    @Resource
    private CartService cartService;
    @Resource
    private SyncLiveGoodsToDataCenterService syncLiveGoodsToDataCenterService;
    @Resource
    private LiveRoomInteractiveMessageService liveRoomInteractiveMessageService;
    @Resource
    private LiveRoomBizService liveRoomBizService;

    @Resource
    private LiveRateTemplateService liveRateTemplateService;


    @Override
    protected void doCheck(LiveGoodsDTO liveGoods, TradeLiveGoodsContext context) {
        super.canTradeCheck(liveGoods, context);
    }

    @Override
    protected void doAction(LiveGoodsDTO liveGoods, TradeLiveGoodsContext context) {
        Date now = new Date();
        LiveGoodsStatusEnum goodsStatus = liveGoods.getGoodsStatus();
        LiveGoodsTradeCompletedTypeEnum tradeType = context.getTradeType();
        switch (tradeType) {
            case AUCTION -> auctionTrade(liveGoods, context);
            case BARGAIN -> bargainTrade(liveGoods, context);
            case TRANSFER -> transferTrade(liveGoods, context);
            default -> throw new ServiceException("不支持的交易类型:" + tradeType);
        }

        // 只要该商品成交后，它所有的待处理议价消息都要更新为过期状态
        liveRoomInteractiveMessageService.lambdaUpdate()
                .eq(LiveRoomInteractiveMessage::getLiveGoodsId, liveGoods.getId())
                .eq(LiveRoomInteractiveMessage::getHandleStatus, LiveRoomInteractiveMessageHandleStatusEnum.UNHANDLED)
                .set(LiveRoomInteractiveMessage::getHandleStatus, LiveRoomInteractiveMessageHandleStatusEnum.EXPIRED)
                .set(LiveRoomInteractiveMessage::getUpdatedAt, new Date())
                .update();

        // 更新直播商品，加到买手收银台
        this.updateLiveGoods(liveGoods, context, now);
        // 更新erp商品
        this.updateErpGoods(liveGoods);

        //直播间成交数量 + 1
        liveRoomCacheService.incrementGoodsTradeCount(liveGoods.getLiveRoomId(), 1);
        //直播间成交额 + 成交金额
        liveRoomCacheService.incrLiveGoodsSoldAmount(liveGoods.getLiveRoomId(), context.getFinalPrice().longValue());
        // 直播间流拍数量减一
        if(goodsStatus == LiveGoodsStatusEnum.ABORTIVE_AUCTION){
            liveRoomCacheService.incrementUnsoldCount(liveGoods.getLiveRoomId(), -1);
        }

        ThreadPoolManager.getGlobalBizExecutor().execute(() -> {
            // 归还其他人的保证金 ，记录成交人保证金冻结明细
            DepositUtils.bidSuccessBackFrozen(liveGoods.getId(), context.getBuyerSeatId());

            // 同步成交数据到中台
            syncLiveGoodsToDataCenterService.syncGoodsToDataCenter(liveGoods);

            // 实时更新直播间成交额，兼容后台的取消成交，因此关播后不需要再更新成交金额和成交数量
            liveRoomBizService.incrLiveRoomTradeAmount(liveGoods.getLiveRoomId(), context.getFinalPrice(), 1);

            //设置卖家服务费
            setSellerServiceAmount(liveGoods, context.getFinalPrice());
        });
    }

    private void auctionTrade(LiveGoodsDTO liveGoods, TradeLiveGoodsContext context) {
        //实际竞拍时长
        liveGoods.setActualAuctionDuration(super.actualAuctionTime(liveGoods));
        liveGoods.setBelongType(LiveGoodsBelongTypeEnum.AUCTION_TRANSACTION);
        //发成交消息： 恭喜xxx中拍.
        msgService.goodsAuctionSuccess(liveGoods.getLiveRoomId(), liveGoods.getId(), context.getBuyerSeatId(), context.getFinalPrice());
    }

    private void bargainTrade(LiveGoodsDTO liveGoods, TradeLiveGoodsContext context) {
        // 议价成交
        liveGoods.setBelongType(LiveGoodsBelongTypeEnum.BARGAIN_TRANSACTION);
        //卖家已同意您的议价，商品已自动加入您的收银台
        BargainGoodsDTO bargainGoodsDTO = new BargainGoodsDTO()
                .setLiveGoodsId(liveGoods.getId())
                .setLiveRoomId(liveGoods.getLiveRoomId())
                .setBuyerSeatId(context.getBuyerSeatId())
                .setBidPrice(context.getFinalPrice());
        msgService.userBargainAgreed(bargainGoodsDTO);
        frozenDeposit(liveGoods, context);
    }

    private void transferTrade(LiveGoodsDTO liveGoods, TradeLiveGoodsContext context) {
        //传送商品成交
        liveGoods.setBelongType(LiveGoodsBelongTypeEnum.TRANSFER_TRANSACTION);
        //成交消息
        msgService.goodsTransferAgreed(liveGoods.getLiveRoomId(), liveGoods.getId(), context.getBuyerSeatId());
        //关闭传送卡片
        msgService.goodsTransferClose(liveGoods.getLiveRoomId(), context.getBuyerSeatId(), liveGoods.getId());
        frozenDeposit(liveGoods, context);
    }

    /**
     * 冻结保证金 传送｜议价成交，出价成交的保证金已在出价时冻结
     */
    private void frozenDeposit(LiveGoodsDTO liveGoods, TradeLiveGoodsContext context){
        // 冻结保证金 - 议价或传送成交，先缓存冻结，后入库
        Long orgId = globalOrgSeatService.getOrgIdBySeatId(context.getBuyerSeatId());
        var bargainGoodsDTO = new BargainGoodsDTO()
                .setLiveGoodsId(liveGoods.getId())
                .setBidPrice(context.getFinalPrice())
                .setBuyerSeatId(context.getBuyerSeatId())
                .setBuyerOrgId(orgId);
        DepositUtils.bidToFrozen(bargainGoodsDTO);
    }

    private void updateLiveGoods(LiveGoodsDTO liveGoods, TradeLiveGoodsContext context, Date now) {
        //更新商品结束，竞拍时间
        liveGoodsDetailService.getLiveGoodsService().lambdaUpdate()
                .eq(LiveGoods::getId, liveGoods.getId())
                .set(LiveGoods::getEndAt, now)
                .set(LiveGoods::getActualAuctionDuration, liveGoods.getActualAuctionDuration())
                .set(LiveGoods::getBelongType, liveGoods.getBelongType())
                .update();

        //加到买手收银台
        BargainGoodsDTO liveGoodsTrade = new BargainGoodsDTO()
                .setLiveGoodsId(liveGoods.getId())
                .setBuyerSeatId(context.getBuyerSeatId())
                .setBidPrice(context.getFinalPrice());
        buyerLiveGoodsOpService.addGoodsToCashierDesk(liveGoodsTrade);

        // 成交时再同步一次商品信息
        // 如果没有商品没有冗余的名称等信息，这里要再冗余一遍，主要用于传送成交
        String imgListJson = null;
        try {
            imgListJson = JSONUtil.toJsonStr(liveGoods.getImgUrlList());
        } catch (Exception e) {
            log.error("更新商品冗余信息失败，转换商品图片失败", e);
        }
        liveGoodsDetailService.getLiveGoodsService().lambdaUpdate()
                .eq(LiveGoods::getId, liveGoods.getId())
                .set(LiveGoods::getGoodsName, liveGoods.getGlobalGoodsName())
                .set(LiveGoods::getGoodsQuality, liveGoods.getQuality())
                .set(LiveGoods::getGoodsImgUrlList, imgListJson)
                .set(LiveGoods::getGoodsDescription, liveGoods.getGlobalGoodsDescription())
                .update();
    }

    private void updateErpGoods(LiveGoodsDTO liveGoods){
        LambdaUpdateWrapper<ErpGoods> erpGoodsUpdater = new LambdaUpdateWrapper<>(ErpGoods.class)
                .eq(ErpGoods::getId, liveGoods.getGlobalGoodsId())
                // 商品状态=售出
                .set(ErpGoods::getSaleStatus, ErpGoodsSaleStatusEnum.SOLD_OUT.getType())
                // 售出时间
                .set(ErpGoods::getSaleAt, new Date())
                // 售出渠道=直播
                .set(ErpGoods::getSaleType, ErpGoodsSaleTypeEnum.LIVE.getType());
        erpGoodsService.update(erpGoodsUpdater);
    }


    private void setSellerServiceAmount(LiveGoodsDTO liveGoods,BigDecimal contextFinalPrice) {
        // 查询出各个直播间绑定的模板费率列表，方便后面匹配计算
        List<RoomRateTemplateDTO> rateTemplateList = liveRateTemplateService.getRoomRateTemplateList(Arrays.asList(liveGoods.getLiveRoomId()));
        // 匹配到模板费率
        List<RateTemplateItemDTO> itemList = rateTemplateList.stream().filter(template -> template.getRoomId().equals(liveGoods.getLiveRoomId())).findFirst().orElse(new RoomRateTemplateDTO()).getItemList();
        // 现在从pt_goods表获取分类id
        Integer oneClassifyId = liveGoods.getPlatformClassifyId();
        if (oneClassifyId != null && oneClassifyId != 0) {
            // 要用商品售价去匹配费率
            BigDecimal sellerServiceRate = this.matchSellerServiceRate(itemList, contextFinalPrice, oneClassifyId);
            // 计算卖家服务费，也要用商品售价去计算
            BigDecimal sellerServiceAmount = this.computeSellerServiceAmount(contextFinalPrice, sellerServiceRate);
            Date createdAt = new Date();
            erpGoodsService.update(Wrappers.lambdaUpdate(ErpGoods.class).eq(ErpGoods::getId, liveGoods.getGlobalGoodsId())
                    .set(ErpGoods::getUpdatedAt, createdAt)
                    .set(ErpGoods::getPlatformClassifyId, oneClassifyId));
            liveGoodsMapper.update(Wrappers.lambdaUpdate(LiveGoods.class).eq(LiveGoods::getId, liveGoods.getId())
                    .set(LiveGoods::getUpdatedAt, createdAt)
                    .set(LiveGoods::getServiceRate, sellerServiceRate)
                    .set(LiveGoods::getServiceAmount, sellerServiceAmount)
                    .set(LiveGoods::getPlatformClassifyId, oneClassifyId));
        }
    }


    /**
     * 匹配卖家服务费率
     *
     * @param rateTemplateItemList 费率单元列表
     * @param payAmount                商品金额
     * @param oneClassifyId        一级分类ID
     * @return 费率
     */
    public BigDecimal matchSellerServiceRate(List<RateTemplateItemDTO> rateTemplateItemList, BigDecimal payAmount, Integer oneClassifyId) {
        if (CollUtil.isEmpty(rateTemplateItemList)) {
            return BigDecimal.ZERO;
        }
        for (RateTemplateItemDTO rateTemplateItem : rateTemplateItemList) {
            // 满足最小值～最大值，且匹配到一级分类ID
            if (Objects.equals(oneClassifyId, rateTemplateItem.getOneClassifyId())
                    && payAmount.compareTo(new BigDecimal(rateTemplateItem.getMinValue())) >= 0
                    && payAmount.compareTo(new BigDecimal(rateTemplateItem.getMaxValue())) < 0) {
                return rateTemplateItem.getRate();
            }
        }
        // 没匹配到就直接零
        return BigDecimal.ZERO;
    }

    /**
     * 计算卖家服务费
     * @param goodsPrice        商品售价
     * @param rate              费率
     * @return  卖家服务费
     */
    public BigDecimal computeSellerServiceAmount(BigDecimal goodsPrice, BigDecimal rate) {
        // 计算卖家服务费，保留2位，四舍五入
        return goodsPrice.multiply(rate).setScale(2, RoundingMode.HALF_UP);
    }

}
