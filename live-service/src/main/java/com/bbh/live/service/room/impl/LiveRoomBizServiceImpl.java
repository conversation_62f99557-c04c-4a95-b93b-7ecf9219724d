package com.bbh.live.service.room.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bbh.base.ListBase;
import com.bbh.base.PageBase;
import com.bbh.base.Sort;
import com.bbh.enums.*;
import com.bbh.exception.ServiceException;
import com.bbh.feign.dto.LiveConfigDTO;
import com.bbh.feign.dto.LiveDTO;
import com.bbh.live.config.LiveBizProperties;
import com.bbh.live.config.LiveServiceProperties;
import com.bbh.live.constant.RedisKey;
import com.bbh.live.controller.req.LiveGoodsQueryReq;
import com.bbh.live.core.msg.ImProperties;
import com.bbh.live.core.msg.MsgDTO;
import com.bbh.live.dao.dto.*;
import com.bbh.live.dao.dto.livegoods.LiveGoodsDTO;
import com.bbh.live.dao.dto.livegoods.LiveGoodsStatisticsDTO;
import com.bbh.live.dao.dto.vo.*;
import com.bbh.live.dao.mapper.LiveGoodsBidMapper;
import com.bbh.live.dao.mapper.LiveOrgFilterMapper;
import com.bbh.live.dao.mapper.LiveRoomMapper;
import com.bbh.live.dao.mapper.LiveRoomSubscribeMapper;
import com.bbh.live.dao.service.*;
import com.bbh.live.enums.BuyerVipTypeEnum;
import com.bbh.live.enums.LiveRoomEnhancedStatusEnum;
import com.bbh.live.enums.SeatRoleInRoomEnum;
import com.bbh.live.service.buyer.vip.BuyerVipService;
import com.bbh.live.service.buyer.vip.vo.BuyerVipMsgTransferVO;
import com.bbh.live.service.buyer.vip.vo.UserBuyerVipInfoVO;
import com.bbh.live.service.livegoods.cache.LiveGoodsCacheManager;
import com.bbh.live.service.livegoods.cache.info.LiveGoodsAuctionBidInfo;
import com.bbh.live.service.msg.ChatroomService;
import com.bbh.live.service.msg.MsgCacheBizService;
import com.bbh.live.service.msg.MsgContext;
import com.bbh.live.service.msg.MsgService;
import com.bbh.live.service.msg.dto.*;
import com.bbh.live.service.msg.dto.base.BaseGoods;
import com.bbh.live.service.msg.dto.base.SourceType;
import com.bbh.live.service.permission.PermissionService;
import com.bbh.live.service.room.LiveRoomBizService;
import com.bbh.live.service.room.LiveRoomCacheService;
import com.bbh.live.service.room.LiveStreamService;
import com.bbh.live.util.MapUtils;
import com.bbh.live.util.PageUtils;
import com.bbh.model.*;
import com.bbh.secure.AuthUtil;
import com.bbh.service.lock.HtbLockService;
import com.bbh.service.lock.bean.HtbLock;
import com.bbh.service.weixin.miniapp.WeiXinQrCodeClient;
import com.bbh.util.AssertUtil;
import com.bbh.util.EncryptUtil;
import com.bbh.util.LogExUtil;
import com.bbh.vo.AuthUser;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.bbh.live.enums.LiveRoomCacheKeys.ROOM;
import static java.util.stream.Collectors.*;

/**
 * 直播间业务
 * <AUTHOR>
 */
@Service
@AllArgsConstructor
@Slf4j
public class LiveRoomBizServiceImpl implements LiveRoomBizService, RedisKey {

    private final LiveRoomService liveRoomService;
    private final LiveRoomMapper liveRoomMapper;
    private final LiveRoomSubscribeMapper liveRoomSubscribeMapper;
    private final LiveRoomCacheService liveRoomCacheService;
    private final LiveGoodsService liveGoodsService;
    private final LiveGoodsBidMapper liveGoodsBidMapper;
    private final LiveOrgFilterMapper liveOrgFilterMapper;
    private final MsgService msgService;
    private final BuyerVipService buyerVipService;
    private final ChatroomService chatroomService;
    private final MsgCacheBizService msgCacheBizService;
    private final LiveServiceProperties liveServiceProperties;
    private final LiveRoomInteractiveMessageService liveRoomInteractiveMessageService;
    private final LiveGoodsCacheManager liveGoodsCacheManager;
    private final LiveRoomDirectorService liveRoomDirectorService;
    private final GlobalOrgSeatService globalOrgSeatService;
    private final GlobalDepositAuditService globalDepositAuditService;
    private final WeiXinQrCodeClient weiXinQrCodeClient;
    private final IGlobalOrganizationService globalOrganizationService;
    private final HtbLockService htbLockService;
    private final PermissionService permissionService;
    private final LiveBizProperties liveBizProperties;
    /**
     * 获取直播间卡片消息列表
     *
     * @param roomId 直播间ID
     * @return 需要显示的卡片消息列表
     */
    @Override
    public List<MsgDTO<?>> getBuyerRoomCardMessageList(Long roomId) {
        AssertUtil.assertNotNull(roomId, "直播间ID不能为空");

        AuthUser user = AuthUtil.getUser();
        List<MsgDTO<?>> messageList = new ArrayList<>();
        loadCommonRoomCardMessage(roomId, messageList, user, false);
        return messageList;
    }

    private void loadCommonRoomCardMessage(Long roomId, List<MsgDTO<?>> messageList,  AuthUser user, boolean isRoomDirector) {
        // 当前上架讲解的商品
        liveGoodsService.lambdaQuery().eq(LiveGoods::getLiveRoomId, roomId)
                .eq(LiveGoods::getGoodsStatus, LiveGoodsStatusEnum.PUT_AWAY)
                .select(LiveGoods::getId).last(" limit 1")
                .oneOpt().ifPresent(liveGoods -> {
                    BaseGoods goods = msgService.retrieveGoodsInfo(liveGoods.getId());
                    GoodsPutawayMsgDTO dto = new GoodsPutawayMsgDTO();
                    dto.setLiveRoomId(roomId);
                    dto.setGoods(goods);
                    dto.setSourceType(SourceType.NOTICE.getCode());
                    dto.setTradeType(goods.getTradeType());
                    messageList.add(msgService.buildMsgDTO(dto, new MsgContext().setSenderUserId(user.getUserId()).setSenderSeatId(user.getSeatId()).setLiveRoomId(roomId)));
                });

        // 当前竞拍中的商品
        liveGoodsService.lambdaQuery()
                .eq(LiveGoods::getLiveRoomId, roomId)
                .eq(LiveGoods::getGoodsStatus, LiveGoodsStatusEnum.AUCTION)
                .select(LiveGoods::getId)
                .last(" limit 1")
                .oneOpt()
                .ifPresent(auctionGoods -> {
                    //竞拍信息
                    var auctionLiveGoods = liveGoodsCacheManager.getLiveGoodsAuctionCache().getAuctionLiveGoodsBeforeExpire(roomId, auctionGoods.getId());
                    if(auctionLiveGoods == null){
                        return;
                    }

                    // 检查是否有人出过价
                    List<LiveGoodsAuctionBidInfo> auctionGoodsBidInfo = liveGoodsCacheManager.getLiveGoodsAuctionCache().getAuctionGoodsBidInfo(roomId, auctionGoods.getId());
                    if (CollUtil.isEmpty(auctionGoodsBidInfo) || auctionLiveGoods.getTradeType() == LiveGoodsTradeTypeEnum.SEC_KILL) {
                        // 没人出价就用AuctionStart
                        BaseGoods goods = msgService.retrieveGoodsInfo(auctionGoods.getId());
                        GoodsAuctionStartMsgDTO dto = new GoodsAuctionStartMsgDTO();
                        dto.setLiveRoomId(roomId);
                        dto.setGoods(goods);
                        long betweenMs = DateUtil.between(DateUtil.date(), auctionLiveGoods.getAuctionEndTime(), DateUnit.MS);
                        dto.setRemainTime(NumberUtil.round((double) betweenMs / 1000, 0, RoundingMode.UP).longValue());
                        dto.setRemainTimeMs(betweenMs);
                        dto.setTradeType(goods.getTradeType());
                        messageList.add(msgService.buildMsgDTO(dto, new MsgContext().setSenderUserId(user.getUserId()).setSenderSeatId(user.getSeatId()).setLiveRoomId(roomId)));
                    } else {
                        // 出过价就用BidSuccess
                        BaseGoods goods = msgService.retrieveGoodsInfo(auctionGoods.getId());
                        UserBidSuccessMsgDTO dto = new UserBidSuccessMsgDTO();
                        dto.setLiveRoomId(roomId);
                        dto.setGoods(goods);
                        if(auctionLiveGoods.getBuyerSeatId() != null){
                            dto.setUser(msgService.retrieveSeatInfo(auctionLiveGoods.getBuyerSeatId()));
                        }
                        long betweenMs = DateUtil.between(DateUtil.date(), auctionLiveGoods.getAuctionEndTime(), DateUnit.MS);
                        dto.setRemainTime(NumberUtil.round((double) betweenMs / 1000, 0, RoundingMode.UP).longValue());
                        dto.setRemainTimeMs(betweenMs);
                        dto.setBidAmount(auctionLiveGoods.getCurrentPrice());
                        dto.setBiddenList(liveGoodsCacheManager.getLiveGoodsAuctionCache().getAuctionGoodsBidInfo(roomId, auctionGoods.getId()));
                        dto.setTradeType(goods.getTradeType());
                        messageList.add(msgService.buildMsgDTO(dto, new MsgContext().setSenderUserId(user.getUserId()).setSenderSeatId(user.getSeatId()).setLiveRoomId(roomId)));
                    }
                });
    }

    /**
     * 获取导播的直播间内卡片消息列表
     *
     * @param roomId 直播间ID
     * @return 需要显示的卡片消息
     */
    @Override
    public List<MsgDTO<?>> getDirectorRoomCardMessageList(Long roomId) {
        AssertUtil.assertNotNull(roomId, "直播间ID不能为空");

        AuthUser user = AuthUtil.getUser();
        AssertUtil.assertTrue(liveRoomDirectorService.isLiveRoomDirector(roomId, user.getSeatId()), "您不是导播, 无权限查看");

        List<MsgDTO<?>> messageList = new ArrayList<>();
        loadCommonRoomCardMessage(roomId, messageList, user, true);

        // 全局的传送提醒
        List<GoodsTransferMsgDTO> transferMsgDTOList = msgCacheBizService.getGoodsTransferMsgListForDirector(roomId);
        messageList.addAll(transferMsgDTOList.stream()
                .map(msg -> msgService.buildMsgDTO(
                        msg,
                        new MsgContext().setSenderUserId(user.getUserId()).setSenderSeatId(user.getSeatId()).setLiveRoomId(roomId)
                ))
                .toList());
        return messageList;
    }

    /**
     * 预约直播间
     *
     * @param roomId 直播间id
     */
    @Override
    public void subscribe(Long roomId) {
        AssertUtil.assertNotNull(roomId, "直播间ID不能为空");
        Long userId = AuthUtil.getUserId();

        String lockKey = buildKey(ROOM.getKey(), roomId.toString(), userId.toString(), "subscribe_limit");
        HtbLock lock = htbLockService.getLock(lockKey);

        boolean locked = lock.tryLock(1, TimeUnit.SECONDS);
        if (!locked) {
            throw new ServiceException("操作太频繁，请稍后再试");
        }
        try {
            // 检查是否已经预约
            LambdaQueryWrapper<LiveRoomSubscribe> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(LiveRoomSubscribe::getLiveRoomId, roomId)
                    .eq(LiveRoomSubscribe::getCreateUserId, userId);
            Long count = liveRoomSubscribeMapper.selectCount(queryWrapper);
            AssertUtil.assertTrue(count == 0, "您已经预约过了");

            // 创建预约记录
            LiveRoomSubscribe liveRoomSubscribe = new LiveRoomSubscribe();
            liveRoomSubscribe.setLiveRoomId(roomId);
            // 定时任务将获取没有推送时间的记录进行推送
            liveRoomSubscribe.setPushedAt(null);
            liveRoomSubscribeMapper.insert(liveRoomSubscribe);

            // 更新缓存中预约数量
            liveRoomCacheService.incrementSubscribeCount(roomId, 1);
        } finally {
            lock.unlock();
        }
    }

    /**
     * 取消预约直播间
     *
     * @param roomId 直播间id
     */
    @Override
    public void cancelSubscribe(Long roomId) {
        AssertUtil.assertNotNull(roomId, "直播间ID不能为空");

        // 先检查是否已经预约
        Long userId = AuthUtil.getUserId();
        LambdaQueryWrapper<LiveRoomSubscribe> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(LiveRoomSubscribe::getLiveRoomId, roomId)
                .eq(LiveRoomSubscribe::getCreateUserId, userId);
        Long count = liveRoomSubscribeMapper.selectCount(queryWrapper);
        AssertUtil.assertTrue(count > 0, "您还没有预约过");

        // 删除该直播间的预约记录
        liveRoomSubscribeMapper.delete(queryWrapper);

        // 更新缓存中预约数量
        liveRoomCacheService.incrementSubscribeCount(roomId, -1);
    }

    /**
     * 创建直播间
     *
     * @param createLiveRoomDTO 直播间基础信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createLiveRoom(CreateLiveRoomDTO createLiveRoomDTO) {
        //校验当前时间是否有其他直播场次
//        if(createLiveRoomDTO.getStartAt() != null && createLiveRoomDTO.getEndAt() != null){
//            var conflictLiveCount = liveRoomMapper..getConflictLiveCount(AuthUtil.getOrgId(), createLiveRoomDTO.getStartAt(), createLiveRoomDTO.getEndAt());
//            AssertUtil.assertTrue(conflictLiveCount == 0, "当前时间段有直播场次，请重新选择时间");
//        }

        LiveRoom liveRoom = new LiveRoom();
        BeanUtil.copyProperties(createLiveRoomDTO, liveRoom);
        liveRoomService.save(liveRoom);

        // 创建直播间推流信息
        LiveDTO liveDTO = new LiveDTO();
        liveDTO.setLiveId(liveRoom.getId().toString());
        LiveConfigDTO liveStreamConfig = SpringUtil.getBean(LiveStreamService.class).createLiveConfig(liveDTO);

        // 更新推流信息
        liveRoom.setStreamPushUrl(liveStreamConfig.getStreamUrl());
        JSONObject pullUrlObj = new JSONObject();
        pullUrlObj.set("rtmp", liveStreamConfig.getPullStreamRtmp())
                .set("m3u8", liveStreamConfig.getPullStreamM3u8())
                .set("flv", liveStreamConfig.getPullStreamFlv())
                .set("rts", liveStreamConfig.getPullStreamRts());
        liveRoom.setStreamPullUrlJson(pullUrlObj);
        liveRoomService.updateById(liveRoom);

        // 创建融云聊天室
        chatroomService.createChatroom(liveRoom.getId());
    }

    private LiveRoomVO getLiveRoomById(Long roomId){
        AuthUser user = AuthUtil.getUser(null);
        QueryLiveRoomDTO query = new QueryLiveRoomDTO();
        query.setRoomId(roomId);
        query.setFilterBlacklist(false);
        query.setFilterWhitelist(false);
        query.setStatus(null);
        query.setCurrentPage(1);
        query.setPerPage(1);
        ListBase<LiveRoomVO> roomList = new ListBase<>();
        if(user == null){
            roomList = getMiniLiveRoomList(query);
        }else{
            roomList = getLiveRoomList(query);
        }
        AssertUtil.assertTrue(CollUtil.isNotEmpty(roomList.getRecords()), "未查询到直播间: " + roomId);
        return roomList.getRecords().getFirst();
    }

    /**
     * 更新直播公告
     *
     * @param roomId 直播间ID
     * @param notice 公告
     */
    @Override
    public void updateNotice(Long roomId, String notice) {
        AssertUtil.assertNotNull(roomId, "直播间ID不能为空");
        AssertUtil.assertNotNull(notice, "公告不能为空");
        // 字数限制20个字
        AssertUtil.assertTrue(notice.length() <= 20, "公告不能超过20个字");

        // 查询直播间
        LiveRoom liveRoom = liveRoomService.getById(roomId);
        AssertUtil.assertNotNull(liveRoom, "直播间不存在");

        // 更新公告
        LambdaUpdateWrapper<LiveRoom> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(LiveRoom::getNotice, notice)
                .eq(LiveRoom::getId, roomId);
        liveRoomService.update(updateWrapper);

        // 发送通知消息
        msgService.roomNoticeChanged(roomId, notice);
    }

    @Override
    public ListBase<LiveRoomVO> getFinishedTradeRoomList(QueryLiveRoomDTO queryLiveRoomDTO) {
        AuthUser user = AuthUtil.getUser();

        // 当前商家的
        var orgId = user.getOrgId();
        queryLiveRoomDTO.setOrgId(orgId);

        // 不处理黑白名单
        queryLiveRoomDTO.setFilterWhitelist(false);
        queryLiveRoomDTO.setFilterBlacklist(false);

        // 不排除相关人员
        queryLiveRoomDTO.setExcludeRelatedStaff(false);
        queryLiveRoomDTO.setIfDirector(false);

        // 已结束的，默认排序规则就是开始时间倒序
        queryLiveRoomDTO.setStatus(2);

        // 有成交过的
        queryLiveRoomDTO.setHasDeal(true);

        ListBase<LiveRoomVO> liveRoomList = getLiveRoomList(queryLiveRoomDTO);
        if (liveRoomList == null || liveRoomList.getRecords() == null) {
            return ListBase.of();
        }

        List<FinishedTradeRoomFilterListVO> convertedList = liveRoomList.getRecords().stream()
                .map(liveRoom -> {
                    FinishedTradeRoomFilterListVO filterVO = new FinishedTradeRoomFilterListVO();
                    // 复制所有 LiveRoomVO 的属性
                    BeanUtils.copyProperties(liveRoom, filterVO);

                    // 设置格式化后的过滤名称
                    // 这里可以根据业务需求组装显示的名称，例如：直播间名称 + 开播时间
                    String startAt = DateUtil.format(liveRoom.getStartAt(), DatePattern.NORM_DATE_PATTERN);
                    filterVO.setFormattedFilterName(startAt + " 直播场");

                    return filterVO;
                })
                .collect(Collectors.toList());

        return new ListBase(
                convertedList,
                liveRoomList.getTotal(),
                liveRoomList.getCurrentPage(),
                liveRoomList.getPerPage(),
                liveRoomList.getMap()
        );

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void submitSyncSce(RoomSyncSceDTO roomSyncSceDTO) {
        //修改直播间
        liveRoomService.lambdaUpdate()
                .eq(LiveRoom::getId, roomSyncSceDTO.getRoomId())
                .set(LiveRoom::getIfNeedSyncCe, roomSyncSceDTO.getIfNeedSyncCe())
                .update();

        //修改org
        globalOrganizationService.lambdaUpdate()
                .eq(GlobalOrganization::getId, AuthUtil.getOrgId())
                .set(GlobalOrganization::getIfNeedLiveSyncSce, roomSyncSceDTO.getIfNeedLiveSyncSce())
                .update();
    }

    /**
     * 分页查询直播间列表
     *
     * @param queryLiveRoomDTO 查询参数
     * @return 分页结果
     */
    @Override
    public ListBase<LiveRoomVO> getLiveRoomList(QueryLiveRoomDTO queryLiveRoomDTO) {
        AuthUser user = AuthUtil.getUser();
        // 需要带上当前用户ID，用于处理是否关注等业务
        queryLiveRoomDTO.setCurrentUserId(user.getUserId());
        queryLiveRoomDTO.setCurrentSeatId(user.getSeatId());

        if (queryLiveRoomDTO.getShowable() == null) {
            // 默认只查询显示中的直播间
            queryLiveRoomDTO.setShowable(true);
        }

        // 处理导播的直播管理，查询该商户的所有直播间
        if (Boolean.TRUE.equals(queryLiveRoomDTO.getIfDirector())) {
            queryLiveRoomDTO.setOrgId(AuthUtil.getOrgId());
        }
        // 看播要检查观看权限
        else {
            // 除非指定了不过滤黑名单，否则默认会过滤，同时会检查是否年费会员，只有年费会员才能过滤黑名单
            if(queryLiveRoomDTO.getFilterBlacklist() == null){
                queryLiveRoomDTO.setFilterBlacklist(true);
            }
            // 如果没传过滤白名单参数，默认要过滤
            if (queryLiveRoomDTO.getFilterWhitelist() == null) {
                queryLiveRoomDTO.setFilterWhitelist(true);
            }
            // 如果要排除直播间的相关员工
            if (Boolean.TRUE.equals(queryLiveRoomDTO.getExcludeRelatedStaff())) {
                queryLiveRoomDTO.setNotInAnchorIdList(List.of(user.getSeatId()));
                queryLiveRoomDTO.setNotInDirectorIdList(List.of(user.getSeatId()));
            }
        }

        // 分页查询直播间列表，如果结果为空就中断处理
        Page<?> page = new Page<>(queryLiveRoomDTO.getCurrentPage(), queryLiveRoomDTO.getPerPage());
        page.setSearchCount(false);
        IPage<LiveRoomVO> voPage = liveRoomMapper.selectRoomVOPage(page, queryLiveRoomDTO);

        // 处理sortRoomId，便于前端上下滑动，要把SortRoomId对应的这个直播间放第一个
        if (Objects.nonNull(queryLiveRoomDTO.getSortRoomId())) {
            LiveRoomVO liveRoomVO = voPage.getRecords().stream().filter(x -> x.getId().equals(queryLiveRoomDTO.getSortRoomId())).findFirst().orElse(null);
            // 如果列表里有，要调整顺序放到第一个
            if (liveRoomVO != null) {
                voPage.getRecords().remove(liveRoomVO);
                voPage.getRecords().addFirst(liveRoomVO);
            } else {
                // 本身列表里没有这个直播间，要单独查出来放第一个
                QueryLiveRoomDTO query = new QueryLiveRoomDTO();
                query.setRoomId(queryLiveRoomDTO.getSortRoomId());
                query.setCurrentUserId(user.getUserId());
                // 这个直播间要强制查询出来，所以黑白名单处理要忽略掉
                query.setFilterWhitelist(false);
                query.setFilterBlacklist(false);
                Page<Object> singlePage = new Page<>(1, 1);
                singlePage.setSearchCount(false);
                liveRoomMapper.selectRoomVOPage(singlePage, query)
                        .getRecords()
                        .stream()
                        .findFirst()
                        .ifPresent(room -> {
                            // 如果原列表为空，创建新的可变列表；如果不为空，创建新的可变列表并复制原有元素
                            List<LiveRoomVO> records = Optional.ofNullable(voPage.getRecords())
                                    .map(ArrayList::new)
                                    .orElseGet(ArrayList::new);

                            // 在开头添加新记录
                            records.addFirst(room);

                            // 更新记录和大小
                            voPage.setRecords(records);
                            voPage.setSize(voPage.getSize() + 1);
                        });
            }
        }

        if (CollUtil.isEmpty(voPage.getRecords())) {
            return new ListBase<>();
        }

        // 仅直播中的打乱顺序
        if (queryLiveRoomDTO.getStatus() != null && queryLiveRoomDTO.getStatus() == 1) {
            // 同类型的直播间，打乱顺序
            shuffleLiveRoomGroupBySpecial(voPage.getRecords(), queryLiveRoomDTO.getSortRoomId());
        }

        // 查询直播间的导播列表，用map存储，其中key是直播间id，value是导播席位id
        List<Long> liveRoomIds = voPage.getRecords().stream().map(LiveRoomVO::getId).distinct().toList();
        var directorMap = computedLiveRoomDirectorMap(liveRoomIds);

        // 查询直播间商户的所有席位列表
        List<String> liveRoomOrgIds = voPage.getRecords().stream().map(LiveRoomVO::getOrgId).filter(Objects::nonNull).distinct().toList();
        List<GlobalOrgSeat> roomOrgSeatList = globalOrgSeatService.selectListByOrgIdList(liveRoomOrgIds.stream().map(Long::valueOf).toList());
        Map<Long, List<Long>> roomOrgSeatIdsMap = roomOrgSeatList.stream().collect(groupingBy(GlobalOrgSeat::getOrgId, mapping(GlobalOrgSeat::getId, toList())));

        // 查询直播间商品列表,只查询上架中、竞拍中和待上架状态的商品
        List<LiveGoods> goodsList = liveGoodsService.lambdaQuery()
                .select(LiveGoods::getId, LiveGoods::getLiveRoomId, LiveGoods::getGoodsStatus)
                .in(LiveGoods::getGoodsStatus, LiveGoodsStatusEnum.WAIT_PUT_AWAY)
                .in(LiveGoods::getLiveRoomId, liveRoomIds)
                .list();

        // 按直播间ID分组,统计各状态商品数量
        Map<Long, Map<LiveGoodsStatusEnum, Long>> roomGoodsStatusCountMap = goodsList.stream()
                .collect(groupingBy(LiveGoods::getLiveRoomId,
                        groupingBy(LiveGoods::getGoodsStatus, counting())));

        voPage.getRecords().forEach(x -> {
            long roomId = x.getId();

            // 加密orgId
            String orgId = x.getOrgId();
            x.setOrgId(EncryptUtil.encrypt(orgId));
            x.setOriginalOrgId(Long.parseLong(orgId));

            // 直播间ID
            x.setChatroomId(chatroomService.buildChatroomId(roomId));

            // 直播间状态使用复合状态，前端直接用这个状态进行判断
            LiveRoomEnhancedStatusEnum enhancedStatusEnum = computedLiveRoomEnhancedStatus(x);
            x.setRoomStatus(enhancedStatusEnum);

            // 各个状态的数量
            LiveRoom roomCache = liveRoomCacheService.getLiveRoom(roomId);
            if (enhancedStatusEnum != LiveRoomEnhancedStatusEnum.COMPLETED && roomCache != null) {
                // set性能高于copy，因此这里用set
                x.setGoodsCount(roomCache.getGoodsCount());
                x.setSubscribeCount(roomCache.getSubscribeCount());
                x.setUnsoldCount(roomCache.getUnsoldCount());
                x.setViewCount(roomCache.getViewCount());
                x.setRealCount(roomCache.getRealCount());
            }

            // 待拍商品数量 = 待上架 + 上架中 + 竞拍中
            x.setWaitAuctionCount(
                Optional.ofNullable(roomGoodsStatusCountMap.get(roomId))
                    .map(statusMap -> statusMap.getOrDefault(LiveGoodsStatusEnum.WAIT_PUT_AWAY, 0L)
                        + statusMap.getOrDefault(LiveGoodsStatusEnum.PUT_AWAY, 0L)
                        + statusMap.getOrDefault(LiveGoodsStatusEnum.AUCTION, 0L))
                    .orElse(0L)
            );

            // 当前用户在直播间内的身份
            x.setSeatRole(computedSeatRole(x, directorMap, AuthUtil.getSeatId()));

            // 当前用户是否直播间的员工
            x.setIfRelatedStaff(computedIfRelatedStaff(roomOrgSeatIdsMap.get(x.getOriginalOrgId()), user.getSeatId()));

            // 商品清单结束时间: 结束时间+清单时长（小时）
            x.setGoodsListExpiredAt(DateUtil.offsetHour(x.getEndAt(), x.getGoodsListDuration()));

            // 默认的背景图
            if (StrUtil.isBlank(x.getDuringCoverImgUrl())) {
                x.setDuringCoverImgUrl(liveServiceProperties.getDefaultLiveRoomDuringCoverImgUrl());
            }

            // 试播状态：通过开始时间来判断，小于开始时间就是试播
            x.setIfTestBroadcast(liveRoomService.ifLiveRoomTestBroadcast(x.getStartAt()));

        });
        return ListBase.pageConvertToListBase(voPage);
    }

    @SuppressWarnings("all")
    private void shuffleLiveRoomGroupBySpecial(List<LiveRoomVO> list, Long excludeRoomId) {
        if (list == null || list.size() <= 1) return;

        // 打印原始顺序
        log.info("原始顺序: {}", list.stream()
                .map(room -> "(" + room.getIfSpecial() + ":" + room.getId() + ":sort=" + room.getSort() + ")")
                .collect(Collectors.joining(", ")));

        // 分组并处理，排除指定ID的直播间
        Map<Boolean, ArrayList<LiveRoomVO>> rooms = list.stream()
                .filter(room -> excludeRoomId == null || !excludeRoomId.equals(room.getId()))
                .collect(Collectors.groupingBy(
                        LiveRoomVO::getIfSpecial,
                        Collectors.collectingAndThen(
                                Collectors.toCollection(ArrayList::new),
                                grouped -> {
                                    // 先按sort降序排序整个列表
                                    List<LiveRoomVO> sortedRooms = grouped.stream()
                                            .sorted((a, b) -> {
                                                Long sortA = a.getSort() != null ? a.getSort() : 0L;
                                                Long sortB = b.getSort() != null ? b.getSort() : 0L;
                                                return sortB.compareTo(sortA); // 降序排序
                                            })
                                            .collect(Collectors.toList());

                                    // 按sort值分组
                                    Map<Long, List<LiveRoomVO>> roomsBySort = sortedRooms.stream()
                                            .collect(Collectors.groupingBy(
                                                    room -> room.getSort() != null ? room.getSort() : 0L,
                                                    LinkedHashMap::new, // 保持原来的排序顺序
                                                    Collectors.toList()
                                            ));

                                    // 对每组相同sort值的房间进行随机打乱
                                    roomsBySort.forEach((sort, sameSortRooms) -> {
                                        Collections.shuffle(sameSortRooms);
                                    });

                                    // 将所有组扁平化成最终结果
                                    return roomsBySort.values().stream()
                                            .flatMap(Collection::stream)
                                            .collect(Collectors.toCollection(ArrayList::new));
                                }
                        )
                ));

        // 打印分组后的情况
        rooms.forEach((isSpecial, groupList) -> {
            log.info("{}: {}",
                    isSpecial ? "专场直播间处理后" : "普通直播间处理后",
                    groupList.stream()
                            .map(room -> room.getId() + "(sort=" + room.getSort() + ")")
                            .collect(Collectors.joining(", ")));
        });

        // 保留需要排除的直播间
        List<LiveRoomVO> excludedRooms = excludeRoomId == null ? Collections.emptyList() :
                list.stream()
                        .filter(room -> excludeRoomId.equals(room.getId()))
                        .collect(Collectors.toList());

        list.clear();
        // 首先添加排除的直播间
        list.addAll(excludedRooms);
        // 然后添加其他处理后的直播间
        list.addAll(Stream.of(Boolean.TRUE, Boolean.FALSE)
                .flatMap(key -> rooms.getOrDefault(key, new ArrayList<>()).stream())
                .collect(Collectors.toList()));

        // 打印最终顺序
        log.info("最终顺序: {}", list.stream()
                .map(room -> "(" + room.getIfSpecial() + ":" + room.getId() + ":sort=" + room.getSort() + ")")
                .collect(Collectors.joining(", ")));
    }

    /**
     * 获取直播间列表（用于上下滑动切换）
     *
     * @param queryDTO 查询参数（仅支持sort_room_id, perPage, currentPage, status）
     * @return 直播间列表
     */
    @Override
    public ListBase<LiveRoomVO> getLiveRoomListForSlide(QueryLiveRoomDTO queryDTO) {
        // 创建新的查询对象，避免修改原始参数
        QueryLiveRoomDTO finalQuery = new QueryLiveRoomDTO();

        // 设置固定参数
        finalQuery.setFilterModeList(List.of(LiveRoomFilterModeEnum.WHITE_LIST.getCode(), LiveRoomFilterModeEnum.BLACKLIST.getCode()));
        finalQuery.setFilterBlacklist(true);
        finalQuery.setFilterWhitelist(true);
        finalQuery.setExcludeRelatedStaff(false);

        // 可见不可进，同时不在白名单的，滑不动
        // 但是如果在白名单，要能滑到
        finalQuery.setFilterWhitelistShow(false);

        // 设置允许前端传入的参数
        finalQuery.setSortRoomId(queryDTO.getSortRoomId());
        finalQuery.setPerPage(queryDTO.getPerPage() > 0 ? queryDTO.getPerPage() : 50);
        finalQuery.setCurrentPage(queryDTO.getCurrentPage() > 0 ? queryDTO.getCurrentPage() : 1);
        // 使用传入的状态，默认为1（进行中）
        finalQuery.setStatus(queryDTO.getStatus() != null ? queryDTO.getStatus() : 1);
        if (finalQuery.getStatus() == 1) {
            // 默认排除掉断流的直播间，只显示有流的
            finalQuery.setStreamStatus(LiveRoomStreamStatusEnum.ON.getCode());
        }

        // 复用原有的获取直播间列表方法
        return getLiveRoomList(finalQuery);
    }

    /**
     * 计算直播间状态 <br>
     *
     * - 等待开播
     * 	- now() < start_at
     * - 直播中
     * 	- (now() >= start_at && now() <= end_at) || (now() > end_at && stream_status == 10)
     * - 直播中主播离开
     * 	- now() >= start_at && now() <= end_at && stream_status == 0
     * - 直播结束
     * 	- now() > end_at
     *
     * @param liveRoom 直播间
     * @return 直播间状态
     */
    @Override
    public LiveRoomEnhancedStatusEnum computedLiveRoomEnhancedStatus(LiveRoomVO liveRoom) {
        LiveRoomStreamStatusEnum streamStatus = liveRoom.getStreamStatus();
        Date startAt = liveRoom.getStartAt();
        Date endAt = liveRoom.getEndAt();
        Date now = new Date();

        if (now.before(startAt)) {
            if(liveRoomService.ifLiveRoomTestBroadcast(startAt)){
                return LiveRoomEnhancedStatusEnum.TEST_BROADCAST;
            }
            return LiveRoomEnhancedStatusEnum.WAITING;
        }

        if (now.after(endAt)) {
            if (streamStatus == LiveRoomStreamStatusEnum.ON) {
                return LiveRoomEnhancedStatusEnum.LIVING;
            } else {
                return LiveRoomEnhancedStatusEnum.COMPLETED;
            }
        }

        if (now.compareTo(startAt) >= 0 && now.compareTo(endAt) <= 0) {
            if (streamStatus == LiveRoomStreamStatusEnum.ON) {
                return LiveRoomEnhancedStatusEnum.LIVING;
            } else if (streamStatus == LiveRoomStreamStatusEnum.OFF) {
                return LiveRoomEnhancedStatusEnum.PAUSED;
            }
        }

        return LiveRoomEnhancedStatusEnum.UNKNOWN;
    }

    /**
     * 根据时间和流的状态，计算直播间真实状态
     *
     * @param liveRoomId 直播间id
     * @return 直播间真实状态
     */
    @Override
    public LiveRoomEnhancedStatusEnum computedLiveRoomEnhancedStatus(Long liveRoomId) {
        QueryLiveRoomDTO query = new QueryLiveRoomDTO();
        query.setRoomId(liveRoomId);
        Page<Object> objectPage = Page.of(1, -1);
        objectPage.setSearchCount(false);
        var page = liveRoomMapper.selectRoomVOPage(objectPage, query);
        if (CollUtil.isEmpty(page.getRecords())) {
            return LiveRoomEnhancedStatusEnum.COMPLETED;
        }
        return computedLiveRoomEnhancedStatus(page.getRecords().getFirst());
    }

    /**
     * 计算用户在该直播间内的身份
     * @param liveRoomVO    直播间对象
     * @param directorMap   导播map
     * @param seatId        用户在直播间内的身份
     * @return             用户在直播间内的身份
     */
    @Override
    public SeatRoleInRoomEnum computedSeatRole(LiveRoomVO liveRoomVO, Map<Long, List<Long>> directorMap, Long seatId) {
        // 先判断是否主播
        if (liveRoomVO.getAnchorSeatId() != null && liveRoomVO.getAnchorSeatId().equals(seatId)) {
            return SeatRoleInRoomEnum.ANCHOR;
        }
        // 如果没有配置导播，那默认返回看播身份
        if (CollUtil.isEmpty(directorMap) || CollUtil.isEmpty(directorMap.get(liveRoomVO.getId()))) {
            return SeatRoleInRoomEnum.WATCHER;
        }
        // 然后判断是否导播
        boolean isDirector = directorMap.get(liveRoomVO.getId()).stream().anyMatch(x -> x.equals(seatId));
        return isDirector ? SeatRoleInRoomEnum.DIRECTOR : SeatRoleInRoomEnum.WATCHER;
    }

    /**
     * 计算是否直播间的员工
     * @param roomOrgSeatIdList 直播间商户的席位ID列表
     * @param seatId            当前人席位
     * @return                  是否直播间的员工
     */
    @Override
    public Boolean computedIfRelatedStaff(List<Long> roomOrgSeatIdList, Long seatId) {
        // 检查当前seat是否在直播间的org内
        return roomOrgSeatIdList.stream().anyMatch(x -> x.equals(seatId));
    }

    /**
     * 查询直播间的导播列表，用map存储，其中key是直播间id，value是导播席位id
     * @param liveRoomIdList    直播间id列表
     * @return                  直播间的导播列表
     */
    @Override
    public Map<Long, List<Long>> computedLiveRoomDirectorMap(List<Long> liveRoomIdList) {
        if (CollUtil.isEmpty(liveRoomIdList)) {
            return new HashMap<>(0);
        }
        List<LiveRoomDirector> liveRoomDirectorList = liveRoomDirectorService.listByRoomIds(liveRoomIdList);
        if (CollUtil.isEmpty(liveRoomDirectorList)) {
            return new HashMap<>(0);
        } else {
            return liveRoomDirectorList.stream().collect(
                    groupingBy(
                            LiveRoomDirector::getLiveRoomId, mapping(LiveRoomDirector::getDirectorSeatId, toList())
                    )
            );
        }
    }

    /**
     * 查询导播的直播间详情
     *
     * @param roomId 直播间id
     * @param isDirector 是否是导播调用
     * @return 直播间详情
     */
    @Override
    public DirectorRoomDetailVO getDirectorRoomDetail(Long roomId, Boolean isDirector) {
        AssertUtil.assertNotNull(roomId, "直播间ID不能为空");
        // 这里查直播间详情
        LiveRoomVO liveRoomVO = getLiveRoomById(roomId);
        // 查完再判断一下状态
        AssertUtil.assertNotNull(liveRoomVO, "直播间不存在");
        AssertUtil.assertNotNull(liveRoomVO.getOriginalOrgId(), "直播间所属商户不存在");
        if (isDirector) {
            // 检查是否有违规记录，如果有违规记录，也禁止开播
            AssertUtil.assertFalse(globalDepositAuditService.hasIllegalRecord(liveRoomVO.getOriginalOrgId()), "店铺已违规，无法开启直播，请及时处理");
            // 检查是否有销售权益
//            AssertUtil.assertTrue(permissionService.hasSellerRights(liveRoomVO.getOriginalOrgId()), "商家未开通销售权益，请开通后开播");
        }

        DirectorRoomDetailVO vo = new DirectorRoomDetailVO();
        BeanUtils.copyProperties(liveRoomVO, vo);

        // 待上架数量
        Long waitPutAwayCount = liveGoodsService.count(Wrappers.lambdaQuery(LiveGoods.class)
                .eq(LiveGoods::getGoodsStatus, LiveGoodsStatusEnum.WAIT_PUT_AWAY)
                .eq(LiveGoods::getLiveRoomId, roomId));
        Date startAt = vo.getStartAt();
        if (startAt != null && startAt.before(new Date())) {
            vo.setWaitPutAwayCount(waitPutAwayCount);
        } else {
            vo.setWaitCompleteCount(waitPutAwayCount);
        }

        // 异步创建聊天室
        CompletableFuture.runAsync(() -> {
            try {
                chatroomService.createChatroom(vo.getId());
                chatroomService.createChatroom(ImProperties.LIVE_ROOM_CHAT_ROOM_FULL_INFO);
            } catch (Exception e) {
                LogExUtil.errorLog("创建聊天室失败: " + vo.getChatroomId(), e);
            }
        });

        // 计算显示直播倒计时
        Date now = DateUtil.date();
        Date endAt = vo.getEndAt();
        var countdownStartTime = DateUtil.offsetSecond(endAt, -liveServiceProperties.getCountdownBeforeLiveEnd());
        // 如果在倒计时和结束时间之间，就要显示倒计时
        vo.setIfShowCountdown(DateUtil.isIn(now, countdownStartTime, endAt));
        // 倒计时剩余秒数=结束时间-当前时间
        vo.setCountdownSeconds(DateUtil.between(now, endAt, DateUnit.SECOND));
        // 是否在开播后关闭过商品清单 = 最后关闭时间 != null && 最后关闭时间 > 开播时间
        vo.setIfClosedGoodsListAfterLiveStart(vo.getGoodsListLastCloseAt() != null && vo.getGoodsListLastCloseAt().after(vo.getStartAt()));

        // 如果没有背景图，要用默认的
        if (StrUtil.isBlank(vo.getDuringCoverImgUrl())) {
            vo.setDuringCoverImgUrl(liveServiceProperties.getDefaultLiveRoomDuringCoverImgUrl());
        }

        // 是否使用wgt的商品清单
        vo.setIfSwitchWgtGoodsList(liveServiceProperties.getIfSwitchWgtGoodsList());

        vo.setSystemNoticeWaitSeconds(liveServiceProperties.getSystemNoticeWaitSeconds());
        vo.setSystemNotice(buildSystemNotice(liveRoomService.getById(roomId)));

        //处理直播同步云展弹框
        handleLiveSyncSce(vo);

        return vo;
    }

    /**
     * 构建系统公告消息的 Map 表示。
     *
     * <p>此方法创建一个系统公告消息，并将其转换为下划线命名的 Map 格式。
     * 系统公告被设置为最新的消息，消息 ID 被设置为 "-1"。</p>
     *
     * @param liveRoom 直播间
     * @return 包含系统公告消息信息的 Map，键为下划线命名的字符串，值为对应的对象
     *
     * @see SystemNoticeMsgDTO
     * @see MsgDTO
     * @see MapUtils#toUnderlineMap(Object)
     */
    private Map<String, Object> buildSystemNotice(LiveRoom liveRoom) {
        // 系统公告加到第一个，作为最新的消息
        SystemNoticeMsgDTO systemNoticeMsgDTO = new SystemNoticeMsgDTO();
        systemNoticeMsgDTO.setLiveRoomId(liveRoom.getId());
        String note = StrUtil.blankToDefault(liveRoom.getNote(), liveBizProperties.getSystemNotice());
        systemNoticeMsgDTO.setContent(trimContent(note));
        systemNoticeMsgDTO.setSourceType(SourceType.SYSTEM_NOTICE.getCode());
        MsgDTO<SystemNoticeMsgDTO> noticeMsg = msgService.buildMsgDTO(systemNoticeMsgDTO, new MsgContext().setSenderUserId(0L).setSenderSeatId(0L).setLiveRoomId(liveRoom.getId()));
        noticeMsg.setMsgId("-1");
        return MapUtils.toUnderlineMap(noticeMsg);
    }

    private String trimContent(String content) {
        if (StrUtil.isBlank(content)) {
            return content;
        }
        // 正则移除首尾的"
        return content.replaceAll("^\"|\"$", "");
    }


    /**
     * <p>Title: handleLiveSyncSce</p>
     * <p>Description: 处理全局同步云展标记 </p>
     * <p>Another: yangfan</p>
     * <p>Date: 2025/5/22 15:50</p>
     * @param vo 导播数据
     * @see com.bbh.live.service.room.impl#handleLiveSyncSce
     */
    private void handleLiveSyncSce(DirectorRoomDetailVO vo) {
        //获取org
        GlobalOrganization org = globalOrganizationService.getById(vo.getOriginalOrgId());
        if (org == null) {
            return;
        }
        vo.setIfNeedLiveSyncSce(org.getIfNeedLiveSyncSce());
        vo.setSyncSceSeconds(liveServiceProperties.getSyncSceSeconds());
        //强制关闭
        if (liveServiceProperties.getSyncSceFlag()) {
            vo.setIfNeedLiveSyncSce(LiveSyncSceEnum.YES);
            vo.setIfNeedSyncCe(LiveSyncSceEnum.YES);
        }
    }

    @Override
    public DirectorRoomStatisticsVO getDirectorRoomStatistics(Long roomId) {
        DirectorRoomStatisticsVO vo = new DirectorRoomStatisticsVO();
        // 待完善数量
        var statisticsDTO = liveGoodsService.getLiveGoodsCountBeforeLiveStarted(new LiveGoodsQueryReq().setLiveRoomId(roomId));
        vo.setWaitCompleteCount(statisticsDTO.getWaitCompleteCount());
        // 待处理消息数量
        vo.setUnhandledMsgCount(liveRoomInteractiveMessageService.getUnhandledMessageCount(roomId));

        // 流拍商品数量
        LiveGoodsQueryReq queryReq = new LiveGoodsQueryReq();
        queryReq.setLiveRoomId(roomId);
        queryReq.setFilterLockedOrSoldOutGoods(true);
        LiveGoodsStatisticsDTO goodsStatisticsDTO = liveGoodsService.getLiveGoodsCountAfterLiveStarted(queryReq);
        vo.setAbortiveAuctionCount(goodsStatisticsDTO.getAbortiveAuctionCount());
        return vo;
    }

    /**
     * 查询看播的直播间详情 <br>
     * 同时返回当前用户预约的所有商品ID
     *
     * @param roomId 直播间id
     * @return 直播间详情
     */
    @Override
    public WatcherRoomDetailVO getWatcherRoomDetail(Long roomId) {
        AuthUser user = AuthUtil.getUser(null);
        //Long userId = user.getUserId();

        // 查询直播间详情，然后再拓展更新字段
        DirectorRoomDetailVO directorRoomDetail = getDirectorRoomDetail(roomId, false);

        // 先拷贝成看播的详情字段
        WatcherRoomDetailVO watcherRoomDetailVO = new WatcherRoomDetailVO();
        BeanUtil.copyProperties(directorRoomDetail, watcherRoomDetailVO);
        //无token则为小程序查询
        if(user == null) {
            return watcherRoomDetailVO;
        }
        // 检查是否在该直播间出过价
        Long bidCount = liveGoodsBidMapper.selectCount(Wrappers.lambdaQuery(LiveGoodsBid.class)
                .eq(LiveGoodsBid::getLiveRoomId, roomId)
                .eq(LiveGoodsBid::getBidSeatId, user.getSeatId()));
        watcherRoomDetailVO.setIfBidden(bidCount > 0);

        // 检查是否有试播权限
        if(watcherRoomDetailVO.getRoomStatus() == LiveRoomEnhancedStatusEnum.TEST_BROADCAST){
            watcherRoomDetailVO.setHasTestBroadcastPermission(liveServiceProperties.getLiveRoomTestSeatIdList().contains(user.getSeatId()));
        }

        // 检查用户的观看权限
        watcherRoomDetailVO.setWatcherPermission(getWatcherPermission(watcherRoomDetailVO, watcherRoomDetailVO.getOriginalOrgId(), user));

        // 发言及出价权限
        watcherRoomDetailVO.setSpeakPermission(permissionService.checkBidPermission(AuthUtil.getOrgId(), AuthUtil.getSeatId(), "发言"));

        return watcherRoomDetailVO;
    }

    /**
     * 检查看播的观看权限，白名单可见不可进，就返回203
     *
     * @param roomId 直播间id
     * @return 是否有权限
     */
    @Override
    public Boolean checkWatcherPermission(Long roomId) {
        AuthUser user = AuthUtil.getUser();
        WatcherRoomDetailVO watcherRoomDetail = getWatcherRoomDetail(roomId);
        var roomOrgId = watcherRoomDetail.getOriginalOrgId();

        // 先把这个商家的黑白名单列表查出来，避免后面频繁查询
        List<LiveOrgFilter> orgFilterList = liveOrgFilterMapper.selectList(Wrappers.lambdaQuery(LiveOrgFilter.class)
                .eq(LiveOrgFilter::getOrgId, roomOrgId)
        );

        // 检查直播间是否白名单，如果白名单模式，检查用户是否在名单内，如果不在就提示无权限
        if (watcherRoomDetail.getFilterMode() == LiveRoomFilterModeEnum.WHITE_LIST) {
            // 是否在名单内
            boolean anyMatch = orgFilterList.stream().anyMatch(x ->
                    x.getFilterMode() == LiveOrgFilterModeEnum.WHITE_LIST
                            // 白名单以前是席位，现在也是user
                            && user.getUserId().equals(x.getUserId())
            );
            // 如果不在名单内且不是会员，直接不给进
            String forbiddenTip = StrUtil.emptyToDefault(watcherRoomDetail.getShowableTips(), "您不在白名单内，无法进入");
            if(!anyMatch){
                if(!watcherRoomDetail.getIfVipDirectlyEnter()){
                    throw new ServiceException(forbiddenTip);
                }
                // 如果是会员，再判断会员能否直接进，不给进也要给提示
                UserBuyerVipInfoVO vipInfo = buyerVipService.getUserBuyerVipInfoBySeatId(user.getSeatId());
                if(vipInfo.getBuyerVipType() != BuyerVipTypeEnum.VIP){
                    throw new ServiceException(forbiddenTip);
                }
            }
        }
        return true;
    }

    private WatcherRoomDetailVO.WatcherPermission getWatcherPermission(WatcherRoomDetailVO watcherRoomDetail, Long roomOrgId, AuthUser user) {
        // 先把这个商家的黑白名单列表查出来，避免后面频繁查询
        List<LiveOrgFilter> orgFilterList = liveOrgFilterMapper.selectList(Wrappers.lambdaQuery(LiveOrgFilter.class)
                .eq(LiveOrgFilter::getOrgId, roomOrgId)
        );

        // 检查是否当前直播间的员工
//        if (Boolean.TRUE.equals(watcherRoomDetail.getIfRelatedStaff())) {
//            return new WatcherRoomDetailVO.WatcherPermission(false, "你是该直播的商家/员工", "为防止兜货，即将为您切换直播间");
//        }

        // 检查直播间是否白名单，如果白名单模式，检查用户是否在名单内，如果不在就提示无权限
        if (watcherRoomDetail.getFilterMode() == LiveRoomFilterModeEnum.WHITE_LIST) {
            // 是否在名单内
            boolean anyMatch = orgFilterList.stream().anyMatch(x ->
                    x.getFilterMode() == LiveOrgFilterModeEnum.WHITE_LIST
                            // 白名单以前是seat，现在是user
                            && user.getUserId().equals(x.getUserId())
            );

            String forbiddenTip = StrUtil.emptyToDefault(watcherRoomDetail.getShowableTips(), "您不在白名单内，无法进入");
            if(!anyMatch){
                if(!watcherRoomDetail.getIfVipDirectlyEnter()){
                    return new WatcherRoomDetailVO.WatcherPermission(false, "温馨提示", forbiddenTip);
                }
                // 获取当前席位的会员
                UserBuyerVipInfoVO vipInfo = buyerVipService.getUserBuyerVipInfoBySeatId(user.getSeatId());
                if (vipInfo.getBuyerVipType() != BuyerVipTypeEnum.VIP) {
                    return new WatcherRoomDetailVO.WatcherPermission(false, "温馨提示", forbiddenTip);
                }
            }
        }

        // 检查直播间是否黑名单，如果黑名单模式，检查是否已拉黑商家，如果拉黑了就提示已拉黑该商家
        if (watcherRoomDetail.getFilterMode() == LiveRoomFilterModeEnum.BLACKLIST) {
            boolean anyMatch = orgFilterList.stream().anyMatch(x ->
                    x.getFilterMode() == LiveOrgFilterModeEnum.BLACKLIST
                    && x.getSourceType() == LiveOrgFilterSourceTypeEnum.USER_BLOCK_MERCHANT
                    // 黑名单是绑定的用户
                    && user.getUserId().equals(x.getUserId())
            );
            if (anyMatch) {
                return new WatcherRoomDetailVO.WatcherPermission(false, "温馨提示", "您已拉黑该商家");
            }

            // 如果被商家拉黑，就提示已被拉黑
            anyMatch = orgFilterList.stream().anyMatch(x ->
                    x.getFilterMode() == LiveOrgFilterModeEnum.BLACKLIST
                    && (x.getSourceType() == LiveOrgFilterSourceTypeEnum.MERCHANT_BLOCK_USER
                            || x.getSourceType() == LiveOrgFilterSourceTypeEnum.PLATFORM_MERCHANT_BLOCK_USER)
                    // 黑名单是绑定的用户
                    && user.getUserId().equals(x.getUserId())
            );
            if (anyMatch) {
                return new WatcherRoomDetailVO.WatcherPermission(false, "温馨提示", "您已经被限制进入本场直播间");
            }
        }

        return new WatcherRoomDetailVO.WatcherPermission(true, null, null);
    }

    /**
     * 查询导播的商品上架队列
     *
     * @param roomId 直播间id
     * @return 上架队列
     */
    @Override
    public DirectorRoomGoodsQueueDTO getDirectorRoomGoodsQueue(Long roomId) {
        AssertUtil.assertNotNull(roomId, "直播间ID不能为空");

        DirectorRoomGoodsQueueDTO dto = new DirectorRoomGoodsQueueDTO();

        // 获取直播间内上架讲解中的商品，倒序排取第一个，确保状态异常时该接口不出错
        List<LiveGoodsDTO> goodsList = liveGoodsService.getLiveGoodsList(new LiveGoodsQueryReq()
                .setLiveRoomId(roomId)
                .setLiveGoodsStatusList(List.of(
                        LiveGoodsStatusEnum.PUT_AWAY.getCode(),
                        LiveGoodsStatusEnum.AUCTION.getCode(),
                        LiveGoodsStatusEnum.WAIT_PUT_AWAY.getCode()
                ))
                .setSort(new Sort("sort", Sort.Direction.ASC))
        ).getRecords();

        // 上架中或者竞拍中的
        goodsList.stream()
                .filter(x ->
                        x.getGoodsStatus() == LiveGoodsStatusEnum.PUT_AWAY
                                || x.getGoodsStatus() == LiveGoodsStatusEnum.AUCTION
                )
                .findFirst()
                .ifPresent(dto::setCurrent);

        // 再获取直播间内待上架的商品列表
        goodsList.remove(dto.getCurrent());
        List<LiveGoodsDTO> waitPutAwayList = new ArrayList<>(goodsList);
        // 组合出返回结果
        Dict next = Dict.create().set("records", waitPutAwayList).set("total", waitPutAwayList.size());
        dto.setNext(next);
        return dto;
    }

    @Override
    public List<LiveRoomRecommendVO> getRecommendRoomList() {
        return liveRoomMapper.selectRecommendLiveRoom();
    }

    @Override
    public boolean liveRoomIsVisible(Long roomId, Long userId) {
        LiveRoom one = liveRoomService.lambdaQuery().eq(LiveRoom::getId, roomId)
                .select(LiveRoom::getOrgId, LiveRoom::getWhitelistShow, LiveRoom::getFilterMode)
                .one();
        AssertUtil.assertNotNull(one, "直播间不存在");

        // 先把黑白名单列表查出来，避免后面频繁查询
        List<LiveOrgFilter> orgFilterList = liveOrgFilterMapper.selectList(Wrappers.lambdaQuery(LiveOrgFilter.class)
                .eq(LiveOrgFilter::getOrgId, one.getOrgId())
                .eq(LiveOrgFilter::getUserId, userId)
        );
        // 白名单模式
        if (one.getFilterMode() == LiveRoomFilterModeEnum.WHITE_LIST) {
            return orgFilterList.stream().anyMatch(x -> x.getFilterMode() == LiveOrgFilterModeEnum.WHITE_LIST) || Boolean.TRUE.equals(one.getWhitelistShow());
        }
        // 黑名单模式
        if (one.getFilterMode() == LiveRoomFilterModeEnum.BLACKLIST) {
            return !ifFilterBlacklist(AuthUtil.getUser()) || orgFilterList.stream().noneMatch(x -> x.getFilterMode() == LiveOrgFilterModeEnum.BLACKLIST);
        }

        return false;
    }

    @Override
    public ListBase<PlayBackLiveRoomVO> getPlaybackLiveRoom(PageBase page) {
        com.bbh.base.Page<PlayBackLiveRoomVO> roomPage = PageUtils.getPage(page, PlayBackLiveRoomVO.class);
        List<PlayBackLiveRoomVO> roomListVO = liveRoomMapper.selectPlaybackRoomVOPage(roomPage, AuthUtil.getUserId(), AuthUtil.getSeatId(), ifFilterBlacklist(AuthUtil.getUser()));
        roomPage.setRecords(roomListVO);
        return ListBase.pageConvertToListBase(roomPage);
    }

    @Override
    public String shareLiveRoom(RoomIdDTO roomId) {
        // 白名单直播间禁止分享
        var liveRoom = liveRoomService.getById(roomId.getRoomId());
        AssertUtil.assertNotNull(liveRoom, "直播间不存在");
        AssertUtil.assertTrue(liveRoom.getFilterMode() != LiveRoomFilterModeEnum.WHITE_LIST, "白名单直播间禁止分享");

        // 生成小程序码
        try {
            byte[] qrCodeBytes = weiXinQrCodeClient.getUnlimitedQrCode(
                    "id=" + roomId.getRoomId(),
                    "pages/live/liveSwiper/index"
            );
            return Base64.getEncoder().encodeToString(qrCodeBytes);
        } catch (Exception e) {
            LogExUtil.errorLog("二维码生成失败", e);
            throw new ServiceException("二维码生成失败");
        }
    }

    /**
     * 获取所有正在直播的直播间，过滤黑白名单
     */
    @Override
    public List<LiveRoom> getInLiveRoomList(AuthUser user) {
        return liveRoomService.getInLiveRoomList(user.getUserId(), user.getSeatId(), ifFilterBlacklist(user));
    }

    /**
     * 获取直播间的剩余即拍即上次数
     *
     * @param roomId    直播间id
     */
    @Override
    public Integer getOffhandRemainTimes(Long roomId) {
        var room = liveRoomService.getById(roomId);
        AssertUtil.assertNotNull(room, "直播间不存在");
        return room.getOffhandRemainTimes();
    }

    /**
     * 只有年费会员才能过滤黑名单
     * @param user
     * @return
     */
    @Override
    public Boolean ifFilterBlacklist(AuthUser user) {
        BuyerVipMsgTransferVO buyerVipInfo = buyerVipService.getBuyerVipMsgTransferVO(user.getSeatId());
        return buyerVipInfo.getBuyerVipType() == BuyerVipTypeEnum.VIP && Boolean.TRUE.equals(buyerVipInfo.getIsAnnualFeeVip());
    }

    /**
     * 分页查询小程序直播间列表
     *
     * @param queryLiveRoomDTO 查询参数
     * @return 分页结果
     */
    @Override
    public ListBase<LiveRoomVO> getMiniLiveRoomList(QueryLiveRoomDTO queryLiveRoomDTO) {
        // 默认只查询显示中的直播间
        queryLiveRoomDTO.setShowable(true);
        if (LiveRoomEnhancedStatusEnum.LIVING.getCode().equals(queryLiveRoomDTO.getStatus())) {
            // 首页列表中默认排除掉断流的直播间，只显示有流的
            queryLiveRoomDTO.setStreamStatus(LiveRoomStreamStatusEnum.ON.getCode());
        }
        // 排除掉白名单，只要黑名单模式
        queryLiveRoomDTO.setFilterModeList(List.of(LiveRoomFilterModeEnum.BLACKLIST.getCode()));
        // 分页查询直播间列表，如果结果为空就中断处理
        Page<?> page = new Page<>(queryLiveRoomDTO.getCurrentPage(), queryLiveRoomDTO.getPerPage());
        IPage<LiveRoomVO> voPage = liveRoomMapper.selectMiniRoomVOPage(page, queryLiveRoomDTO);
        if (CollUtil.isEmpty(voPage.getRecords())) {
            return new ListBase<>();
        }
        voPage.getRecords().forEach(x -> {
            long roomId = x.getId();
            // 加密orgId
            String orgId = x.getOrgId();
            x.setOrgId(EncryptUtil.encrypt(orgId));
            x.setOriginalOrgId(Long.parseLong(orgId));
            // 直播间ID
            x.setChatroomId(chatroomService.buildChatroomId(roomId));
            // 直播间状态使用复合状态，前端直接用这个状态进行判断
            LiveRoomEnhancedStatusEnum enhancedStatusEnum = computedLiveRoomEnhancedStatus(x);
            x.setRoomStatus(enhancedStatusEnum);
            // 各个状态的数量
            LiveRoom roomCache = liveRoomCacheService.getLiveRoom(roomId);
            if (enhancedStatusEnum != LiveRoomEnhancedStatusEnum.COMPLETED && roomCache != null) {
                // set性能高于copy，因此这里用set
                x.setGoodsCount(roomCache.getGoodsCount());
                x.setSubscribeCount(roomCache.getSubscribeCount());
                x.setUnsoldCount(roomCache.getUnsoldCount());
                x.setViewCount(roomCache.getViewCount());
                x.setRealCount(roomCache.getRealCount());
            }
            // 商品清单结束时间: 结束时间+清单时长（小时）
            x.setGoodsListExpiredAt(DateUtil.offsetHour(x.getEndAt(), x.getGoodsListDuration()));
        });
        return ListBase.pageConvertToListBase(voPage);
    }

    @Override
    public ListBase<LiveRoomVO> getMiniLiveRoomListForSlide(QueryLiveRoomDTO queryDTO) {
        // 创建新的查询对象，避免修改原始参数
        QueryLiveRoomDTO finalQuery = new QueryLiveRoomDTO();

        // 设置固定参数
        finalQuery.setFilterModeList(List.of(LiveRoomFilterModeEnum.BLACKLIST.getCode()));
        finalQuery.setFilterBlacklist(true);
        finalQuery.setFilterWhitelist(true);
        finalQuery.setExcludeRelatedStaff(false);

        // 设置允许前端传入的参数
        finalQuery.setSortRoomId(queryDTO.getSortRoomId());
        finalQuery.setPerPage(queryDTO.getPerPage() > 0 ? queryDTO.getPerPage() : 50);
        finalQuery.setCurrentPage(queryDTO.getCurrentPage() > 0 ? queryDTO.getCurrentPage() : 1);
        // 使用传入的状态，默认为1（进行中）
        finalQuery.setStatus(queryDTO.getStatus() != null ? queryDTO.getStatus() : 1);
        if (finalQuery.getStatus() == 1) {
            // 默认排除掉断流的直播间，只显示有流的
            finalQuery.setStreamStatus(LiveRoomStreamStatusEnum.ON.getCode());
        }

        // 复用原有的获取直播间列表方法
        return getMiniLiveRoomList(finalQuery);
    }

    /**
     * 商家是否存在违规记录
     *
     * @return true/false
     */
    @Override
    public Long countOrgIllegalRecord() {
        var orgId = AuthUtil.getUser().getOrgId();

        // 前置条件，商家未签约、商家合约过期，都无法进入直播间
//        GlobalOrganization organization = globalOrganizationService.getById(orgId);
//        AssertUtil.assertNotNull(organization, "商家不存在");
//        AssertUtil.assertTrue(organization.getIfOrgContract() == 1, "商家未签约");
//        AssertUtil.assertTrue(organization.getOrgContractEndTime() != null && organization.getOrgContractEndTime().after(new Date()), "商家合约已过期");


        return globalDepositAuditService.getIllegalCount(orgId);
    }

    /**
     * 增加成交金额
     *
     * @param roomId      直播间id
     * @param tradeAmount 成交金额
     */
    @Override
    public void incrLiveRoomTradeAmount(Long roomId, BigDecimal tradeAmount, Integer goodsCount) {
        liveRoomService.lambdaUpdate()
                .setIncrBy(LiveRoom::getSoldAmount, tradeAmount)
                .setIncrBy(LiveRoom::getSoldCount, goodsCount)
                .eq(LiveRoom::getId, roomId)
                .update();
    }
}
