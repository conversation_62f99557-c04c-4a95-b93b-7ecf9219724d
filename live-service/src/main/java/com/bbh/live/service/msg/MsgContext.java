package com.bbh.live.service.msg;

import com.bbh.model.GlobalUser;
import com.bbh.model.LiveRoom;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 消息上下文，在最终调用ImService时会获取直播间id和用户id进行发送
 * <AUTHOR>
 */
@NoArgsConstructor
@Data
@Accessors(chain = true)
public class MsgContext {

    /**
     * 直播间id
     */
    private Long liveRoomId;

    /**
     * 允许为null
     */
    private LiveRoom liveRoom;

    /**
     * 接收对象的类型：单个直播间、全局直播间、两者都发
     */
    private TargetType targetType = TargetType.BOTH;

    private boolean godViewProcessed = false;

    /**
     * 发送方ID，系统发送则传0
     */
    private Long senderUserId = 0L;

    /**
     * 发送方席位ID
     */
    private Long senderSeatId = 0L;

    /**
     * 允许为null
     */
    private GlobalUser senderUser;

    public static enum TargetType {
        SINGLE,
        GLOBAL,
        BOTH,
        ;
    }

}
