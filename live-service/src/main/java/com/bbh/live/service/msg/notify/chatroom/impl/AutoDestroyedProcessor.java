package com.bbh.live.service.msg.notify.chatroom.impl;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.bbh.live.dao.dto.ChatroomStatusSyncDTO;
import com.bbh.live.dao.mapper.LiveRoomMapper;
import com.bbh.live.service.msg.ChatroomService;
import com.bbh.live.service.msg.notify.chatroom.StatusProcessor;
import com.bbh.live.service.msg.notify.chatroom.enums.ChatroomStatus;
import com.bbh.model.LiveRoom;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * 自动销毁的处理
 * 如果聊天室被销毁后，判断是否在时间时间段的前后N天内，如果在N天内，自动重新创建
 * <AUTHOR>
 */
@Service
@AllArgsConstructor
@Slf4j
public class AutoDestroyedProcessor implements StatusProcessor {

    private final LiveRoomMapper liveRoomMapper;

    @Override
    public void process(ChatroomStatusSyncDTO dto) {
        String chatRoomId = dto.getChatRoomId();
        var liveRoomId = dto.parseLiveRoomId();
        if (liveRoomId == null) {
            log.info("融云回调，未解析到直播间ID: {}", dto.getChatRoomId());
            return;
        }
        LiveRoom liveRoom = liveRoomMapper.selectById(liveRoomId);
        if (liveRoom == null) {
            log.info("liveRoom is null, chatRoomId: {}", chatRoomId);
            return;
        }
        // 获取结束时间
        Date endTime = liveRoom.getEndAt();
        if (endTime == null) {
            log.info("liveRoom endAt is null, chatRoomId: {}", chatRoomId);
            return;
        }
        // 判断是否小于(结束时间+1天)，如果小于这个时间，就自动重新创建直播间
        Date now = new Date();
        DateTime allowableDate = DateUtil.offsetDay(endTime, 1);
        if (now.before(allowableDate)) {
            log.info("liveRoom will be auto created, chatRoomId: {}, liveRoomId: {}", chatRoomId, liveRoomId);
            SpringUtil.getBean(ChatroomService.class).createChatroom(chatRoomId);
            return;
        }
        log.info("liveRoom will be auto destroyed, chatRoomId: {}, liveRoomId: {}", chatRoomId, liveRoomId);
    }

    @Override
    public boolean canHandle(ChatroomStatus status) {
        return status == ChatroomStatus.AUTO_DESTROYED;
    }
}
