package com.bbh.live.service.livegoods.consumer;

import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.CharsetUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.bbh.live.dao.service.LiveGoodsService;
import com.bbh.live.service.livegoods.LiveGoodsPlaybackService;
import com.bbh.model.LiveGoods;
import com.bbh.service.mq.constants.MqConstant;
import com.bbh.service.mq.service.CoreMqListener;
import com.bbh.util.LogExUtil;
import com.rabbitmq.client.Channel;
import jakarta.annotation.Resource;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.time.LocalDateTime;

/**
 * 商品上架：
 * 5分钟后获取回放地址
 * <AUTHOR>
 */
@Component
public class GoodsPutawayConsumer implements CoreMqListener {

    @Resource
    private LiveGoodsService liveGoodsService;
    @Resource
    private LiveGoodsPlaybackService liveGoodsPlaybackService;

    @Override
    @RabbitListener(queuesToDeclare = @Queue(name = MqConstant.LIVE_GOODS_PUTAWAY, durable = "true"))
    public void handle(Message message, Channel channel) {
        try {
            String json = StrUtil.str(message.getBody(), CharsetUtil.UTF_8);
            LogExUtil.infoLog("=============> 商品上架，消息内容:" + json + " <=============");

            // 商品id
            JSONObject jsonObject = JSONUtil.parseObj(json);
            Long liveGoodsId = jsonObject.getLong("liveGoodsId");

            // 商品信息
            LiveGoods liveGoods = liveGoodsService.getById(liveGoodsId);
            if (liveGoods == null) {
                // 商品不存在，直接确认消息
                LogExUtil.infoLog("商品不存在，直接确认消息");
                ack(message, channel);
                return;
            }

            // 上架时间
            LocalDateTime putawayTime = LocalDateTimeUtil.of(liveGoods.getPutawayAt());
            LocalDateTime fiveMinutesAfter = putawayTime.plusMinutes(5);

            if (LocalDateTime.now().isBefore(fiveMinutesAfter)) {
                // 还没到5分钟，拒绝消息并重新入队
                LogExUtil.infoLog("还没到5分钟，拒绝消息并重新入队");
                reject(message, channel);
                return;
            }

            // 获取回放地址
            LogExUtil.infoLog("开始获取回放地址");
            liveGoodsPlaybackService.fetchPlaybackUrl(liveGoodsId);

            ack(message, channel);
        } catch (Exception e) {
            // 出现异常
            LogExUtil.errorLog("处理商品上架消息异常", e);
            ack(message, channel);
        }
    }

    private void reject(Message message, Channel channel) {
        try {
            channel.basicReject(message.getMessageProperties().getDeliveryTag(), true);
        } catch (IOException ioException) {
            LogExUtil.errorLog("Failed to REJECT message", ioException);
        }
    }

    private void ack(Message message, Channel channel) {
        try {
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
        } catch (IOException ioException) {
            LogExUtil.errorLog("Failed to ACK message", ioException);
        }
    }
}
