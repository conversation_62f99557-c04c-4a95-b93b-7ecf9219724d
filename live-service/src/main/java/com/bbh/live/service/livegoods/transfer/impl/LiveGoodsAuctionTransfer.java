package com.bbh.live.service.livegoods.transfer.impl;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import com.bbh.enums.LiveGoodsStatusEnum;
import com.bbh.enums.LiveGoodsTradeTypeEnum;
import com.bbh.live.constant.DelayQueueTopics;
import com.bbh.live.dao.dto.livegoods.LiveGoodsDTO;
import com.bbh.live.handler.queue.DelayJob;
import com.bbh.live.service.livegoods.cache.info.LiveGoodsAuctionInfo;
import com.bbh.live.service.livegoods.context.LiveGoodsContext;
import com.bbh.live.service.room.context.LiveRoomContextHolder;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Date;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR> dsy
 * @Date: 2024/7/29
 * @Description:
 */
@Service("liveGoodsAuctionTransfer")
public class LiveGoodsAuctionTransfer extends AbstractLiveGoodsTransfer<LiveGoodsContext> {

    @Override
    protected void doCheck(LiveGoodsDTO liveGoods, LiveGoodsContext context) {
        super.liveGoodsAuctionCanTransferCheck(liveGoods);
    }

    @Override
    public void doAction(LiveGoodsDTO liveGoods, LiveGoodsContext context) {
        boolean ifAuction = liveGoods.getTradeType() == LiveGoodsTradeTypeEnum.AUCTION;

        Date now = new Date();
        liveGoods.setGoodsStatus(LiveGoodsStatusEnum.AUCTION);
        liveGoods.setAuctionStartAt(now);
        // 一口价商品，0元改0.01
        if (!ifAuction && liveGoods.getStartPrice() != null && liveGoods.getStartPrice().compareTo(BigDecimal.ZERO) <= 0) {
            liveGoods.setStartPrice(BigDecimal.valueOf(0.01));
        }
        liveGoodsDetailService.updateLiveGoods(liveGoods);

        // 结束时间
        DateTime auctionEndAt = ifAuction ?
                // 竞拍用商品的竞拍时长
                DateUtil.offsetSecond(now, liveGoods.getAuctionDuration()) :
                // 一口价没有截止时间，这里为了减少代码变动，给7天
                DateUtil.offsetDay(now, 7);

        // 商品竞拍信息放入缓存
        // 竞拍和一口价，用相同的缓存信息
        putIntoCache(liveGoods, auctionEndAt);

        // 仅竞拍，加入延迟队列
        // 一口价倒计时结束后不发消息，由前端自行处理
        // 后端仅记录结束时间，在直播间初始化消息接口中返回剩余毫秒数
        if (ifAuction) {
            // 加入延迟队列
            putIntoDelayQueue(liveGoods, auctionEndAt);
        }

        //发消息，通知竞拍开始
        msgService.goodsAuctionStart(liveGoods.getLiveRoomId(), liveGoods.getId(), auctionEndAt);
    }

    /**
     * 把商品信息放入缓存
     * @param liveGoods         商品详情
     * @param auctionEndAt      结束时间
     */
    private void putIntoCache(LiveGoodsDTO liveGoods, Date auctionEndAt){
        LiveGoodsAuctionInfo auctionInfo = new LiveGoodsAuctionInfo().setLiveGoodsId(liveGoods.getId())
                // 拍卖是起拍价，一口价就是价格
                // 统一用StartPrice字段
                .setCurrentPrice(liveGoods.getStartPrice())
                .setIncreasePrice(liveGoods.getIncreasePrice())
                .setAuctionEndTime(auctionEndAt)
                .setTradeType(liveGoods.getTradeType());
        liveGoodsCacheManager.getLiveGoodsAuctionCache().insertAuctionLiveGoods(LiveRoomContextHolder.getRoomId(), auctionInfo);
    }

    /**
     * 放入延时队列，过期消费，消费者：{@link com.bbh.live.service.livegoods.consumer.AuctionLiveGoodsConsumer}
     * @param liveGoods
     */
    private void putIntoDelayQueue(LiveGoodsDTO liveGoods, Date auctionEndAt){
        if (liveGoods.getGoodsStatus() == LiveGoodsStatusEnum.AUCTION) {
            DelayJob<Long> delayJob = new DelayJob<>();
            delayJob.setTopic(DelayQueueTopics.AUCTION_TOPIC);
            delayJob.setDelay(DateUtil.between(new Date(), auctionEndAt, DateUnit.MS));
            delayJob.setTimeUnit(TimeUnit.MILLISECONDS);
            delayJob.setJobId(liveGoods.getLiveRoomId().toString());
            delayJob.setData(liveGoods.getId());
            delayQueueManager.addToQueue(delayJob);
            return;
        }
    }
}
