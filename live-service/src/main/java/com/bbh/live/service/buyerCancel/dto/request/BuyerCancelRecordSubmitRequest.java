package com.bbh.live.service.buyerCancel.dto.request;

import com.bbh.enums.GlobalOrderTypeEnum;
import com.bbh.live.service.buyerCancel.dto.BuyerCancelBiz;
import lombok.Data;

import java.util.List;

/**
 * 买家取消成交申请请求对象
 *
 * <AUTHOR>
 * @since 2025-01-10
 */
@Data
public class BuyerCancelRecordSubmitRequest implements BuyerCancelBiz {

    /**
     * 直播商品ID
     */
    private Long liveGoodsId;

    /**
     * 取消原因类型
     */
    private Integer applyReasonId;

    /**
     * 取消原因
     */
    private String applyReason;

    /**
     * 取消说明
     */
    private String applyDescription;

    /**
     * 证据图片URL列表
     */
    private List<String> applyImgList;


    private GlobalOrderTypeEnum bizType;

    @Override
    public GlobalOrderTypeEnum getBizType() {
        return this.bizType;
    }

    @Override
    public void setBizType(GlobalOrderTypeEnum bizType) {
        this.bizType=bizType;
    }
}