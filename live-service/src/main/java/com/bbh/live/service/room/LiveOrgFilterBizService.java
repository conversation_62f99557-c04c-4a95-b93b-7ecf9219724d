package com.bbh.live.service.room;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.bbh.base.PageBase;
import com.bbh.enums.LiveOrgFilterSourceTypeEnum;
import com.bbh.live.dao.dto.LiveFilterSearchUserDTO;
import com.bbh.live.dao.dto.LiveOrgFilterDTO;
import com.bbh.live.dao.dto.vo.LiveOrgFilterVO;
import com.bbh.vo.AuthUser;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/9/27 09:46
 * @description
 */
public interface LiveOrgFilterBizService {


    //--------------------------商家操作用户-------------

    /**
     * 拉黑的用户列表
     * @param pageBase
     * @param user
     * @param liveOrgFilterSourceTypeEnum
     * @return
     */
    IPage<LiveOrgFilterVO> pageListUser(PageBase pageBase, AuthUser user, LiveOrgFilterSourceTypeEnum liveOrgFilterSourceTypeEnum);

    /**
     * 搜索用户
     * @param user
     * @param dto
     * @return
     */
    IPage<LiveOrgFilterVO> searchUser(AuthUser user, LiveFilterSearchUserDTO dto);

    /**
     * 添加黑名单
     * @param user
     * @param liveOrgFilter
     */
    void addUser(AuthUser user, LiveOrgFilterDTO liveOrgFilter);


    //-------------------------用户操作商家-------------

    /**
     * 拉黑的商家列表
     * @param pageBase
     * @param user
     * @param liveOrgFilterSourceTypeEnum
     * @return
     */
    IPage<LiveOrgFilterVO> pageListOrg(PageBase pageBase, AuthUser user, LiveOrgFilterSourceTypeEnum liveOrgFilterSourceTypeEnum);

    /**
     * 搜索商家
     * @param user
     * @param dto
     * @return
     */
    IPage<LiveOrgFilterVO> searchOrg(AuthUser user, LiveFilterSearchUserDTO dto);

    /**
     * 添加黑名单
     * @param user
     * @param dto
     * @return
     */
    Long addOrg(AuthUser user,  LiveOrgFilterDTO dto);

    /**
     * 移除黑名单
     * @param userId
     * @param orgId
     * @param id
     */
    void remove(Long userId,Long orgId,Long id);

    /**
     * 获取被屏蔽的商家的所有直播间id
     * @param user  当前登录用户
     * @return      直播间id列表
     */
    List<Long> getFilteredLiveRoomIdList(AuthUser user);
}
