package com.bbh.live.service.buyer.vip.impl;

import com.bbh.live.enums.BuyerVipEventEnum;
import com.bbh.live.service.buyer.vip.BuyerVipService;
import com.bbh.live.service.buyer.vip.VipRightsService;
import com.bbh.secure.AuthUtil;
import com.bbh.vo.AuthUser;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 会员权益Service
 * <AUTHOR>
 */
@Slf4j
@AllArgsConstructor
@Service
public class VipRightsServiceImpl implements VipRightsService {

    private final BuyerVipService buyerVipService;

    /**
     * 通用的消耗会员权益次数
     *
     * @param eventEnum 消耗事件
     * @return 成功/失败
     */
    @Override
    public boolean useRightsTimes(BuyerVipEventEnum eventEnum) {
        AuthUser user = AuthUtil.getUser();
        Long seatId = user.getSeatId();
        return false;
    }
}
