package com.bbh.live.controller.req;

import com.bbh.base.PageBase;
import com.bbh.base.Sort;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * <AUTHOR> dsy
 * @Date: 2024/7/26
 * @Description:
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
public class LiveGoodsQueryReq extends PageBase {

    /**
     * 关键词搜索 货品名称、描述、编码
     */
    private String keywords;

    /**
     * 直播间id
     */
    private Long liveRoomId;

    /**
     * 是否过滤锁定或已售罄商品
     */
    private boolean filterLockedOrSoldOutGoods;

    /**
     * 成交人
     */
    private Long belongUserId;

    /**
     * 成交席位
     */
    private Long belongSeatId;

    /**
     * 排序，目前仅支持单个
     */
    private Sort sort;

    /**
     * 直播商品id
     */
    private List<Long> liveGoodsIdList;

    /**
     * 支持同时查询多种状态的商品{@link com.bbh.enums.LiveGoodsStatusEnum}
     */
    private List<Integer> liveGoodsStatusList;

    /**
     * 过滤即拍即上商品
     */
    private Boolean filterOffhandGoods;

    /**
     * 商品编码搜索
     */
    private String codeKeywords;

    /**
     * 是否同步到云展
     */
    private Boolean filterIfSyncCe;

    public void parseKeywords() {
        if (StringUtils.isNotBlank(keywords)) {
            keywords = keywords.trim();
            if (StringUtils.isNumeric(keywords)) {
                try {
                    this.codeKeywords = Integer.valueOf(keywords).toString();
                } catch (NumberFormatException e) {
                    this.codeKeywords = keywords;
                }
            } else {
                this.codeKeywords = keywords;
            }
        }
    }
}
