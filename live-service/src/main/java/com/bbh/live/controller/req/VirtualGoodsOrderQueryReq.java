package com.bbh.live.controller.req;

import com.bbh.base.PageBase;
import com.bbh.base.Sort;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR> dsy
 * @Date: 2024/8/20
 * @Description:
 */
@Data
public class VirtualGoodsOrderQueryReq extends PageBase {

    /**
     * 订单id
     */
    private Long orderId;

    /**
     * 订单状态 10=等待支付 20=买家取消 30=超时取消 40=支付完成 50=完成交付
     */
    private Integer orderState;

    /**
     *  买家席位id
     */
    private Long buyerSeatId;

    /**
     * 订单开始时间
     */
    private Date startDatetime;

    /**
     * 订单结束时间
     */
    private Date endDatetime;

    /**
     * 商品类型(1=分贝，2=app原价vip会员 3=app拼团vip会员 4=小程序原价vip会员 5=保证金补缴（补缴金额不固定） 6=卖家保证金, 7=买家保证金, 8=买家升级新卖家保证金, 9=老卖家升级卖家保证金, 10=系统赠送升级卖家保证金, 11=系统赠送升级买家保证金, 12=小程序购票，包含包展票和展位票 13=小程序展位票 14=买家会员(高价)， 15=买家会员(低价)）
     */
    private Integer goodsType;

    /**
     * 排序字段
     */
    private Sort sort;
}
