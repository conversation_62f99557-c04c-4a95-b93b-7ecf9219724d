package com.bbh.live.controller;

import com.bbh.live.dao.dto.RoomIdDTO;
import com.bbh.live.service.room.LiveRoomStatisticsService;
import com.bbh.util.AssertUtil;
import com.bbh.vo.Result;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR> dsy
 * @Date: 2024/9/12
 * @Description: 直播间统计信息controller
 */
@RestController
@RequestMapping("room/statistics")
@AllArgsConstructor
public class LiveRoomStatisticsController {

    private final LiveRoomStatisticsService liveRoomStatisticsService;

    /**
     *  直播销售数据
     * @param roomId
     * @return
     */
    @PostMapping("/liveSaleInfo")
    public Result liveSaleInfo(@RequestBody RoomIdDTO roomId) {
        AssertUtil.assertNotNull(roomId.getRoomId(), "请选择直播间");
        return Result.ok(liveRoomStatisticsService.liveSaleInfo(roomId));
    }


    /**
     * 实时销售数据,导播直播间查看
     * @param roomId
     * @return
     */
    @PostMapping("/realtimeSaleInfo")
    public Result realtimeSaleInfo(@RequestBody RoomIdDTO roomId) {
        AssertUtil.assertNotNull(roomId.getRoomId(), "请选择直播间");
        return Result.ok(liveRoomStatisticsService.realTimeSaleInfo(roomId));
    }
}
