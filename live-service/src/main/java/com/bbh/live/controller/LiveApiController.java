package com.bbh.live.controller;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bbh.feign.dto.LiveConfigDTO;
import com.bbh.feign.dto.LiveDTO;
import com.bbh.feign.dto.LiveRecordDTO;
import com.bbh.feign.dto.RecordVideoDTO;
import com.bbh.live.config.LiveServiceProperties;
import com.bbh.live.dao.service.LiveGoodsService;
import com.bbh.live.dao.service.LiveRoomService;
import com.bbh.live.service.room.LiveStreamService;
import com.bbh.model.LiveGoods;
import com.bbh.model.LiveRoom;
import com.bbh.util.AssertUtil;
import com.bbh.vo.Result;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Objects;

/**
 * 对外接口
 * <AUTHOR>
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/api")
public class LiveApiController {

    private final LiveStreamService liveStreamService;

    private final LiveGoodsService liveGoodsService;

    private final LiveRoomService liveRoomService;

    private final LiveServiceProperties liveServiceProperties;

    /**
     * 创建直播推流配置
     *
     * @param dto
     * @return
     */
    @PostMapping("/createLiveStream")
    public Result createLiveStream(@RequestBody LiveDTO dto) {
        LiveConfigDTO liveConfig = liveStreamService.createLiveConfig(dto);
        return Result.ok(liveConfig);
    }

    /**
     * 根据ERP商品编号 查询直播商品回放地址
     * 先查询本地，如果不存在就请求中台直播服务
     *
     * @param globalGoodsId ERP商品编号
     * @return 返回直播回放地址
     */
    @PostMapping("/globalGoodsVideoRecord")
    public Result globalGoodsVideoRecord(Integer globalGoodsId) {
        AssertUtil.assertNotNull(globalGoodsId, "参数异常， 没有globalGoodsId");

        // 先查询本地是否存在LiveVideoUrl
        LambdaQueryWrapper<LiveGoods> globalGoodsLqw = new LambdaQueryWrapper<>();
        globalGoodsLqw.eq(LiveGoods::getGlobalGoodsId, globalGoodsId)
                .isNotNull(LiveGoods::getPutawayAt)
                .isNotNull(LiveGoods::getEndAt)
                .isNull(LiveGoods::getDeletedAt);

        List<LiveGoods> listLiveGoods = liveGoodsService.list(globalGoodsLqw);
        if (CollectionUtil.isNotEmpty(listLiveGoods)) {
            LiveGoods first = listLiveGoods.getFirst();
            if (StringUtils.isNotBlank(first.getLiveVideoUrl())) {
                return Result.ok(first.getLiveVideoUrl());
            }

            LiveRecordDTO liveRecordDTO = new LiveRecordDTO();
            liveRecordDTO.setLiveId(first.getLiveRoomId().toString());

            LocalDateTime putAwayAt = LocalDateTimeUtil.of(first.getPutawayAt());
            LocalDateTime putAwayAtBefore = putAwayAt.minusMinutes(liveServiceProperties.getGoodsVideoCatchTime());
            String putAwayAtDate = putAwayAtBefore.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));

            LocalDateTime endAt = LocalDateTimeUtil.of(first.getEndAt());
            LocalDateTime endAtAfter = endAt.plusMinutes(liveServiceProperties.getGoodsVideoCatchTime());
            String endAtDate = endAtAfter.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));

            liveRecordDTO.setStartTime(putAwayAtDate);
            liveRecordDTO.setEndTime(endAtDate);

            liveRecordDTO.setRecordType(1);
            RecordVideoDTO indexFile = liveStreamService.getVideoRecord(liveRecordDTO);
            if (indexFile != null) {

                first.setLiveVideoUrl(indexFile.getVideoUrl());
                liveGoodsService.updateById(first);

                return Result.ok(indexFile.getVideoUrl());
            }
        }
        // 如果本地还没有创建LiveVideoUrl，请求直播服务
        return Result.fail("无法获取直播商品回放地址");
    }

    /**
     * 查询直播间回放地址
     *
     * @param liveRoomId 直播间编号
     * @return
     */
    @PostMapping("/liveRoomVideoRecord")
    public Result liveRoomVideoRecord(Integer liveRoomId) {
        AssertUtil.assertNotNull(liveRoomId, "参数异常， 没有liveRoomId");

        LiveRoom liveRoom = liveRoomService.getById(liveRoomId);
        AssertUtil.assertTrue(liveRoom != null
                && liveRoom.getActualStartAt() != null
                && liveRoom.getActualEndAt() != null, "无法获取直播间回放地址,直播间不存在或状态错误");

        if (StrUtil.isNotBlank(liveRoom.getLiveVideoUrl())) {
            return Result.ok(liveRoom.getLiveVideoUrl());
        } else {
            LiveRecordDTO liveRecordDTO = new LiveRecordDTO();
            liveRecordDTO.setLiveId(liveRoomId.toString());
            liveRecordDTO.setStartTime(DateUtil.formatDateTime(Objects.requireNonNull(liveRoom).getStartAt()));
            liveRecordDTO.setEndTime(DateUtil.formatDateTime(liveRoom.getActualEndAt()));
            liveRecordDTO.setRecordType(0);

            RecordVideoDTO indexFile = liveStreamService.getVideoRecord(liveRecordDTO);
            if (indexFile != null) {
                liveRoom.setLiveVideoUrl(indexFile.getVideoUrl());
                liveRoomService.updateById(liveRoom);
                return Result.ok(indexFile.getVideoUrl());
            }
        }


        return Result.fail("无法获取直播间回放地址");
    }

}
