package com.bbh.live.controller.miniapp;

import com.bbh.base.ListBase;
import com.bbh.live.dao.dto.QueryLiveRoomDTO;
import com.bbh.live.dao.dto.RoomIdDTO;
import com.bbh.live.dao.dto.vo.LiveRoomVO;
import com.bbh.live.dao.dto.vo.WatcherRoomDetailVO;
import com.bbh.live.service.room.LiveRoomBizService;
import com.bbh.vo.Result;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 小程序-直播间
 * <AUTHOR>
 */
@RestController
@RequestMapping("/miniapp/room")
@Slf4j
@AllArgsConstructor
public class MpLiveRoomController {

    private final LiveRoomBizService liveRoomService;

    /**
     * 查询小程序直播间列表
     *
     * @param queryLiveRoomDTO 查询参数
     * @return 直播间列表
     */
    @PostMapping("getMiniLiveRoomList")
    public Result getMiniLiveRoomList(@RequestBody QueryLiveRoomDTO queryLiveRoomDTO) {
        ListBase<LiveRoomVO> list = liveRoomService.getMiniLiveRoomList(queryLiveRoomDTO);
        return Result.ok(list);
    }

    /**
     * 获取直播间列表（用于上下滑动切换）
     *
     * @param queryDTO 查询参数（仅支持sort_room_id, perPage, currentPage, status）
     * @return 直播间列表
     */
    @PostMapping("getLiveRoomListForSlide")
    public Result getLiveRoomListForSlide(@RequestBody QueryLiveRoomDTO queryDTO) {
        ListBase<LiveRoomVO> list = liveRoomService.getMiniLiveRoomListForSlide(queryDTO);
        return Result.ok(list);
    }

    /**
     * 用于看播的直播间详情
     * @param roomIdDTO 直播间ID
     * @return 详情信息
     */
    @PostMapping("getWatcherRoomDetail")
    public Result getWatcherRoomDetail(@RequestBody RoomIdDTO roomIdDTO) {
        WatcherRoomDetailVO dto = liveRoomService.getWatcherRoomDetail(roomIdDTO.getRoomId());
        return Result.ok(dto);
    }


}
