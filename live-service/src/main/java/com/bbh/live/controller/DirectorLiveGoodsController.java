package com.bbh.live.controller;

import cn.hutool.core.collection.CollectionUtil;
import com.bbh.live.controller.req.LiveGoodsQueryReq;
import com.bbh.live.dao.dto.InteractiveMsgDTO;
import com.bbh.live.dao.dto.OffhandPutAwayDTO;
import com.bbh.live.dao.dto.VisibleDTO;
import com.bbh.live.dao.dto.livegoods.LiveGoodsIdListDTO;
import com.bbh.live.dao.dto.livegoods.LiveGoodsPutAwayDTO;
import com.bbh.live.dao.dto.livegoods.LiveGoodsSortUpdateDTO;
import com.bbh.live.dao.dto.livegoods.LiveGoodsSyncCeDTO;
import com.bbh.live.service.director.DirectorLiveGoodsOpService;
import com.bbh.live.service.director.DirectorLiveGoodsQueryService;
import com.bbh.log.ApiLog;
import com.bbh.util.AssertUtil;
import com.bbh.vo.Result;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR> dsy
 * @Date: 2024/8/5
 * @Description: 导播-商品 controller
 */
@RestController
@RequestMapping("director/liveGoods")
@AllArgsConstructor
public class DirectorLiveGoodsController {

    private final DirectorLiveGoodsOpService directorLiveGoodsOpService;
    private final DirectorLiveGoodsQueryService directorLiveGoodsQueryService;

    /**
     * 从erp仓库添加商品到直播清单
     * @param liveGoodsIdListDTO 所有erp商品id集合，包括原来已添加的和本次新添加的
     * @return  Result
     */
    @ApiLog
    @PostMapping("/addGoodsList")
    public Result addLiveGoodsList(@RequestBody LiveGoodsIdListDTO liveGoodsIdListDTO) {
        AssertUtil.assertNotNull(liveGoodsIdListDTO.getLiveRoomId(), "直播间不存在");
        String msg = directorLiveGoodsOpService.addLiveGoodsList(liveGoodsIdListDTO.getLiveRoomId(), liveGoodsIdListDTO.getGoodsIdList());
        return Result.ok(null, msg);
    }

    /**
     * 从直播清单中移除商品
     * @param liveGoodsIdListDTO
     * @return
     */
    @ApiLog
    @PostMapping("/removeGoodsList")
    public Result removeLiveGoodsList(@RequestBody LiveGoodsIdListDTO liveGoodsIdListDTO) {
        AssertUtil.assertNotNull(liveGoodsIdListDTO.getLiveRoomId(), "直播间不存在");
        AssertUtil.assertFalse(liveGoodsIdListDTO.getGoodsIdList().isEmpty(), "请选择移除商品");
        directorLiveGoodsOpService.removeLiveGoodsList(liveGoodsIdListDTO.getGoodsIdList());
        return Result.ok();
    }

    /**
     * 开启或隐藏商品清单
     * @return
     */
    @PostMapping("/updateLiveGoodsVisible")
    public Result updateLiveGoodsVisible(@RequestBody(required = false) VisibleDTO visibleDTO) {
        return Result.ok(directorLiveGoodsOpService.updateLiveGoodsVisible(visibleDTO));
    }

    /**
     * 获取直播清单接口，所有上架，成交，流拍商品均可使用
     * @param liveGoodsQueryReq
     * @return
     */
    @PostMapping("/getGoodsList")
    public Result getLiveGoods(@RequestBody LiveGoodsQueryReq liveGoodsQueryReq){
        AssertUtil.assertNotNull(liveGoodsQueryReq.getLiveRoomId(), "直播间不存在");
        return Result.ok(directorLiveGoodsQueryService.getLiveGoodsList(liveGoodsQueryReq));
    }


    /**
     * 获取直播清单接口，所有上架，成交，流拍商品均可使用 同步到新云展全选接口
     * @param liveGoodsQueryReq
     * @return
     */
    @PostMapping("/getGoodsList/syncToSceSelectAll")
    public Result getLiveGoodsSyncToSceSelectAll(@RequestBody LiveGoodsQueryReq liveGoodsQueryReq){
        AssertUtil.assertNotNull(liveGoodsQueryReq.getLiveRoomId(), "直播间不存在");
        return Result.ok(directorLiveGoodsQueryService.getLiveGoodsSyncToSceSelectAll(liveGoodsQueryReq));
    }


    /**
     *  同步到新云展查询接口
     * @param liveGoodsQueryReq
     * @return
     */
    @PostMapping("/getGoodsList/getLiveGoodsToSceGoods")
    public Result getLiveGoodsToSceGoods(@RequestBody LiveGoodsQueryReq liveGoodsQueryReq){
        AssertUtil.assertNotNull(liveGoodsQueryReq.getLiveRoomId(), "直播间不存在");
        return Result.ok(directorLiveGoodsQueryService.getLiveGoodsToSceGoods(liveGoodsQueryReq));
    }


    @PostMapping("/getCheckedLiveGoods")
    public Result getCheckedLiveGoods(@RequestBody LiveGoodsQueryReq liveGoodsQueryReq){
        AssertUtil.assertNotNull(liveGoodsQueryReq.getLiveRoomId(), "直播间不存在");
        return Result.ok(directorLiveGoodsQueryService.getCheckedLiveGoodsList());
    }

    /**
     *  待上架商品列表
     * @param liveGoodsQueryReq
     * @return
     */
    @PostMapping("/getWaitPutAwayGoodsList")
    public Result getWaitPutAwayLiveGoods(@RequestBody LiveGoodsQueryReq liveGoodsQueryReq){
        AssertUtil.assertNotNull(liveGoodsQueryReq.getLiveRoomId(), "直播间不存在");
        return Result.ok(directorLiveGoodsQueryService.getWaitPutAwayLiveGoodsList(liveGoodsQueryReq.getLiveRoomId()));
    }

    /**
     * 获取上架商品详情
     * @param liveGoodsQueryReq
     * @return
     */
    @PostMapping("/getPutAwayGoods")
    public Result getPutAwayLiveGoods(@RequestBody LiveGoodsQueryReq liveGoodsQueryReq){
        AssertUtil.assertNotNull(liveGoodsQueryReq.getLiveRoomId(), "直播间不存在");
        return Result.ok(directorLiveGoodsQueryService.getPutAwayLiveGoodsDetail(liveGoodsQueryReq.getLiveRoomId()));
    }

    /**
     *  带统计信息的直播商品清单
     * @param liveGoodsQueryReq
     * @return
     */
    @PostMapping("/getGoodsCheckList")
    public Result getCheckList(@RequestBody LiveGoodsQueryReq liveGoodsQueryReq){
        AssertUtil.assertNotNull(liveGoodsQueryReq.getLiveRoomId(), "直播间不存在");
        return Result.ok(directorLiveGoodsQueryService.getLiveGoodsCheckList(liveGoodsQueryReq));
    }

    /**
     * 直播商品数量统计接口
     * @param liveGoodsQueryReq
     * @return
     */
    @PostMapping("/getGoodsCount")
    public Result getGoodsCount(@RequestBody LiveGoodsQueryReq liveGoodsQueryReq){
        AssertUtil.assertNotNull(liveGoodsQueryReq.getLiveRoomId(), "直播间不存在");
        return Result.ok(directorLiveGoodsQueryService.getGoodsCount(liveGoodsQueryReq));
    }

    /**
     * 完善上架信息，修改商品信息
     * @param putAwayInfo
     * @return
     */
    @ApiLog
    @PostMapping("/completePutAwayInfo")
    public Result completePutAwayInfo(@RequestBody LiveGoodsPutAwayDTO putAwayInfo){
        putAwayInfo.check();
        directorLiveGoodsOpService.completeLiveGoodsPutAwayInfo(putAwayInfo);
        return Result.ok();
    }

    /**
     * 直播商品排序
     * @param liveGoodsSortUpdateDTO
     * @return
     */
    @ApiLog
    @PostMapping("/batchUpdateLiveGoodsSort")
    public Result batchUpdateLiveGoodsSort(@RequestBody LiveGoodsSortUpdateDTO liveGoodsSortUpdateDTO){
        AssertUtil.assertNotNull(liveGoodsSortUpdateDTO.getLiveRoomId(), "直播间不存在");
        directorLiveGoodsOpService.batchUpdateLiveGoodsSort(liveGoodsSortUpdateDTO.getSortList());
        return Result.ok();
    }

    /**
     * 直播商品上架讲解
     * @param putAwayInfo
     * @return
     */
    @ApiLog
    @PostMapping("/putAwayAndExplain")
    public Result putAwayAndExplain(@RequestBody LiveGoodsPutAwayDTO putAwayInfo){
        AssertUtil.assertNotNull(putAwayInfo.getLiveRoomId(), "直播间不存在");
        directorLiveGoodsOpService.putAwayAndExplainLiveGoods(putAwayInfo.getLiveGoodsId());
        return Result.ok();
    }

    /**
     * 开始竞拍
     * @param putAwayInfo
     * @return
     */
    @ApiLog
    @PostMapping("/auction")
    public Result auction(@RequestBody LiveGoodsPutAwayDTO putAwayInfo){
        AssertUtil.assertNotNull(putAwayInfo.getLiveRoomId(), "直播间不存在");
        directorLiveGoodsOpService.auctionLiveGoods(putAwayInfo.getLiveGoodsId());
        return Result.ok();
    }

    /**
     * 流拍
     * @param abortInfo 直播间id、商品id
     * @return
     */
    @ApiLog
    @PostMapping("/abort")
    public Result abort(@RequestBody LiveGoodsPutAwayDTO abortInfo){
        AssertUtil.assertNotNull(abortInfo.getLiveRoomId(), "直播间不存在");
        directorLiveGoodsOpService.abortLiveGoods(abortInfo.getLiveGoodsId());
        return Result.ok();
    }

    /**
     * 商品撤回
     * @param putAwayInfo
     * @return
     */
    @ApiLog
    @PostMapping("/putAwayOff")
    public Result putAwayOff(@RequestBody LiveGoodsPutAwayDTO putAwayInfo){
        AssertUtil.assertNotNull(putAwayInfo.getLiveRoomId(), "直播间不存在");
        directorLiveGoodsOpService.putAwayOffLiveGoods(putAwayInfo.getLiveGoodsId());
        return Result.ok();
    }

    /**
     * 处理买手互动消息
     * @param interceptMsg
     * @return
     */
    @ApiLog
    @PostMapping("/messageHandle")
    public Result messageHandle(@RequestBody InteractiveMsgDTO interceptMsg){
        interceptMsg.checkProperties();
        directorLiveGoodsOpService.interactiveMessageHandle(interceptMsg);
        return Result.ok();
    }


    /**
     * 流拍商品重新上架到 待上架队列
     * @param putAwayInfo
     * @return
     */
    @ApiLog
    @PostMapping("/reShelve")
    public Result reShelve(@RequestBody LiveGoodsPutAwayDTO putAwayInfo){
        putAwayInfo.check();
        directorLiveGoodsOpService.abortiveAuctionGoodsReShelve(putAwayInfo.getLiveGoodsId());
        return Result.ok();
    }

    /**
     * 流拍商品上架（完善上架信息，并到上架中队列）
     * @param putAwayInfo
     * @return
     */
    @ApiLog
    @PostMapping("/reShelveAndComplete")
    public Result reShelveAndComplete(@RequestBody LiveGoodsPutAwayDTO putAwayInfo){
        putAwayInfo.check();
        directorLiveGoodsOpService.abortiveAuctionGoodsReShelveAndComplete(putAwayInfo);
        return Result.ok();
    }

    /**
     * 即拍即上
     * @param offhandPutAwayDTO
     * @return
     */
    @ApiLog
    @PostMapping("/offhandPutAway")
    public Result offhandPutAway(@RequestBody OffhandPutAwayDTO offhandPutAwayDTO){
        AssertUtil.assertNotNull(offhandPutAwayDTO.getLiveRoomId(), "直播间不存在");
        directorLiveGoodsOpService.offhandPutAway(offhandPutAwayDTO);
        return Result.ok();
    }

    @ApiLog
    @PostMapping("/batchSyncToCe")
    public Result batchToCe(@RequestBody LiveGoodsSyncCeDTO liveGoodsList) {
        AssertUtil.assertNotNull(liveGoodsList.getLiveRoomId(), "直播间不存在");
        AssertUtil.assertFalse(CollectionUtil.isEmpty(liveGoodsList.getLiveGoods()), "请选择同步商品");
//        liveGoodsList.getLiveGoods().forEach(liveGoods -> AssertUtil.assertNotNull(liveGoods.getPeerPrice(), "请填写同行价"));
        directorLiveGoodsOpService.batchSyncLiveVideo(liveGoodsList.getLiveGoods());
        directorLiveGoodsOpService.batchSyncToCe(liveGoodsList.getLiveGoods());
        return Result.ok();
    }
}
