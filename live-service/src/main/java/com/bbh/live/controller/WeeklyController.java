package com.bbh.live.controller;

import com.bbh.live.dao.dto.IdDTO;
import com.bbh.live.dao.dto.QueryWeeklyDTO;
import com.bbh.live.dao.dto.QueryWeeksDTO;
import com.bbh.live.service.buyer.weekly.WeeklyService;
import com.bbh.vo.Result;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 行情周报
 * <AUTHOR>
 */
@RequestMapping("weekly")
@RestController
@Slf4j
@AllArgsConstructor
public class WeeklyController {

    private final WeeklyService weeklyService;

    /**
     * 获取行情周报，其中包含SPU列表、SKU列表等
     * @param queryWeeklyDTO    查询参数
     * @return                  周报信息
     */
    @PostMapping
    public Result getWeekly(@RequestBody QueryWeeklyDTO queryWeeklyDTO) {
        return Result.ok(weeklyService.getWeekly(queryWeeklyDTO));
    }

    /**
     * 行情周报-年月周筛选列表
     * @param queryWeeksDTO 查询参数
     * @return  年、月、周列表
     */
    @PostMapping("weeks")
    public Result getWeeklySpu(@RequestBody QueryWeeksDTO queryWeeksDTO) {
        return Result.ok(weeklyService.getWeeks(queryWeeksDTO));
    }

    /**
     * 行情周报-SPU最近成交
     * @param idDTO 传weeklySpuId
     * @return SPU列表
     */
    @PostMapping("spu")
    public Result getWeeklySpuList(@RequestBody IdDTO idDTO) {
        return Result.ok(weeklyService.getWeeklySpu(idDTO.getId()));
    }

}
