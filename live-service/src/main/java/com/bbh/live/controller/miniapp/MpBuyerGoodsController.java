package com.bbh.live.controller.miniapp;

import com.bbh.live.controller.req.LiveGoodsQueryReq;
import com.bbh.live.dao.dto.BargainGoodsDTO;
import com.bbh.live.service.buyer.BuyerLiveGoodsQueryService;
import com.bbh.util.AssertUtil;
import com.bbh.vo.Result;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 小程序-买手商品
 * <AUTHOR>
 */
@RestController
@RequestMapping("/miniapp/buyer/liveGoods")
@Slf4j
@AllArgsConstructor
public class MpBuyerGoodsController {

    private final BuyerLiveGoodsQueryService buyerLiveGoodsQueryService;

    /**
     * 买手直播商品列表
     * @param liveGoodsQueryReq
     * @return
     */
    @PostMapping("/getGoodsList")
    public Result getGoodsList(@RequestBody LiveGoodsQueryReq liveGoodsQueryReq) {
        AssertUtil.assertNotNull(liveGoodsQueryReq.getLiveRoomId(), "直播间不存在");
        return Result.ok(buyerLiveGoodsQueryService.getLiveGoodsList(liveGoodsQueryReq));
    }

    /**
     * 买手直播商品详情
     * @param bargainGoods
     * @return
     */
    @PostMapping("/getGoodsInfo")
    public Result getLiveGoods(@RequestBody BargainGoodsDTO bargainGoods) {
        AssertUtil.assertNotNull(bargainGoods.getLiveRoomId(), "直播间不存在");
        return Result.ok(buyerLiveGoodsQueryService.getLiveGoodsDetailInfo(bargainGoods.getLiveGoodsId()));
    }

}
