package com.bbh.live.controller;

import com.bbh.base.ListBase;
import com.bbh.base.PageBase;
import com.bbh.enums.LiveRoomStreamStatusEnum;
import com.bbh.exception.ServiceException;
import com.bbh.live.core.msg.MsgDTO;
import com.bbh.live.dao.dto.*;
import com.bbh.live.dao.dto.vo.DirectorRoomDetailVO;
import com.bbh.live.dao.dto.vo.LiveRoomVO;
import com.bbh.live.dao.dto.vo.WatcherRoomDetailVO;
import com.bbh.live.enums.LiveRoomEnhancedStatusEnum;
import com.bbh.live.service.room.LiveRoomBizService;
import com.bbh.live.service.room.LiveStreamService;
import com.bbh.log.ApiLog;
import com.bbh.util.AssertUtil;
import com.bbh.vo.Result;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 直播间控制器
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/room")
@AllArgsConstructor
public class LiveRoomController {

    private final LiveRoomBizService liveRoomService;
    private final LiveStreamService liveStreamService;

    /**
     * 创建直播间
     * @param createLiveRoomDTO 直播间基础信息
     * @return Result
     */
    @PostMapping("create")
    public Result create(@RequestBody CreateLiveRoomDTO createLiveRoomDTO) {
        liveRoomService.createLiveRoom(createLiveRoomDTO);
        return Result.ok();
    }

    /**
     * 预约直播间
     * @param roomIdDTO 直播间ID
     * @return Result
     */
    @PostMapping("subscribe")
    public Result subscribe(@RequestBody RoomIdDTO roomIdDTO) {
        AssertUtil.assertNotNull(roomIdDTO.getRoomId(), "请选择直播间");
        liveRoomService.subscribe(roomIdDTO.getRoomId());
        return Result.ok();
    }

    /**
     * 取消预约直播间
     * @param roomIdDTO 直播间ID
     * @return Result
     */
    @PostMapping("cancelSubscribe")
    public Result cancelSubscribe(@RequestBody RoomIdDTO roomIdDTO) {
        AssertUtil.assertNotNull(roomIdDTO.getRoomId(), "请选择直播间");
        liveRoomService.cancelSubscribe(roomIdDTO.getRoomId());
        return Result.ok();
    }

    /**
     * 开始直播
     * @param roomIdDTO 直播间ID
     * @return Result
     */
    @ApiLog
    @PostMapping("startLive")
    public Result startLive(@RequestBody RoomIdDTO roomIdDTO) {
        AssertUtil.assertNotNull(roomIdDTO.getRoomId(), "请选择直播间");
        liveStreamService.startLive(roomIdDTO.getRoomId());
        return Result.ok();
    }

    /**
     * 关播 <br>
     * 主播允许多次开关播 <br>
     * 真正的直播结束状态 = stream_status=关闭 & 场次结束（当前时间 > end_at） <br>
     * 如果场次结束，但直播流还在，则依然是直播中状态
     *
     * @param roomIdDTO 直播间ID
     * @return Result
     */
    @ApiLog
    @PostMapping("stopLive")
    public Result stopLive(@RequestBody RoomIdDTO roomIdDTO) {
        AssertUtil.assertNotNull(roomIdDTO.getRoomId(), "请选择直播间");
        liveStreamService.stopLive(roomIdDTO.getRoomId());
        return Result.ok();
    }

    /**
     * 查询直播间列表
     *
     * @param queryLiveRoomDTO 查询参数
     * @return 直播间列表
     */
    @PostMapping("getLiveRoomList")
    public Result getLiveRoomList(@RequestBody QueryLiveRoomDTO queryLiveRoomDTO) {
        if (LiveRoomEnhancedStatusEnum.LIVING.getCode().equals(queryLiveRoomDTO.getStatus())) {
            // 首页列表中默认排除掉断流的直播间，只显示有流的
            queryLiveRoomDTO.setStreamStatus(LiveRoomStreamStatusEnum.ON.getCode());
        }
        ListBase<LiveRoomVO> list = liveRoomService.getLiveRoomList(queryLiveRoomDTO);
        return Result.ok(list);
    }

    /**
     * 获取商家的、已结束的、有过成交的直播列表
     */
    @PostMapping("/getFinishedTradeRoomList")
    public Result getFinishedTradeRoomList(@RequestBody(required = false) QueryLiveRoomDTO queryLiveRoomDTO) {
        if (queryLiveRoomDTO == null) {
            queryLiveRoomDTO = new QueryLiveRoomDTO();
        }
        ListBase<LiveRoomVO> list = liveRoomService.getFinishedTradeRoomList(queryLiveRoomDTO);
        return Result.ok(list);
    }

    /**
     * 获取直播间列表（用于上下滑动切换）
     *
     * @param queryDTO 查询参数（仅支持sort_room_id, perPage, currentPage, status）
     * @return 直播间列表
     */
    @PostMapping("getLiveRoomListForSlide")
    public Result getLiveRoomListForSlide(@RequestBody QueryLiveRoomDTO queryDTO) {
        ListBase<LiveRoomVO> list = liveRoomService.getLiveRoomListForSlide(queryDTO);
        return Result.ok(list);
    }

    /**
     * 用于导播的直播间详情
     * @param roomIdDTO 直播间ID
     * @return 详情信息
     */
    @PostMapping("getDirectorRoomDetail")
    public Result getDirectorRoomDetail(@RequestBody RoomIdDTO roomIdDTO) {
        DirectorRoomDetailVO dto = liveRoomService.getDirectorRoomDetail(roomIdDTO.getRoomId(), true);
        return Result.ok(dto);
    }

    /**
     * 用于导播的同步云展弹框提交
     * @param roomSyncSceDTO 传参
     * @return 详情信息
     */
    @PostMapping("submitSyncSce")
    public Result submitSyncSce(@RequestBody RoomSyncSceDTO roomSyncSceDTO) {
        liveRoomService.submitSyncSce(roomSyncSceDTO);
        return Result.ok();
    }


    /**
     * 导播直播间角标统计信息
     * @param roomIdDTO 直播间ID
     * @return 详情信息
     */
    @PostMapping("getDirectorRoomStatistics")
    public Result getDirectorRoomStatistics(@RequestBody RoomIdDTO roomIdDTO) {
        return Result.ok(liveRoomService.getDirectorRoomStatistics(roomIdDTO.getRoomId()));
    }

    /**
     * 用于看播的直播间详情
     * @param roomIdDTO 直播间ID
     * @return 详情信息
     */
    @PostMapping("getWatcherRoomDetail")
    public Result getWatcherRoomDetail(@RequestBody RoomIdDTO roomIdDTO) {
        WatcherRoomDetailVO dto = liveRoomService.getWatcherRoomDetail(roomIdDTO.getRoomId());
        return Result.ok(dto);
    }

    /**
     * 检查看播的观看权限
     * @param roomIdDTO 直播间ID
     * @return  true/false
     */
    @PostMapping("checkWatcherPermission")
    public Result checkWatcherPermission(@RequestBody RoomIdDTO roomIdDTO) {
        return Result.ok(liveRoomService.checkWatcherPermission(roomIdDTO.getRoomId()));
    }

    /**
     * 获取看播的直播间详情卡片消息提醒
     *
     * @param roomIdDTO 直播间ID
     * @return 卡片消息
     */
    @PostMapping("getCardMessageReminder")
    public Result getBuyerCardMessageReminder(@RequestBody RoomIdDTO roomIdDTO) {
        List<MsgDTO<?>> messageList = liveRoomService.getBuyerRoomCardMessageList(roomIdDTO.getRoomId());
        return Result.ok(messageList);
    }

    /**
     * 获取导播的直播间详情卡片消息提醒
     *
     * @param roomIdDTO 直播间ID
     * @return 卡片消息
     */
    @PostMapping("director/getCardMessageReminder")
    public Result getDirectorCardMessageReminder(@RequestBody RoomIdDTO roomIdDTO) {
        List<MsgDTO<?>> messageList = liveRoomService.getDirectorRoomCardMessageList(roomIdDTO.getRoomId());
        return Result.ok(messageList);
    }

    /**
     * 获取导播的商品上架队列
     * @param roomIdDTO 直播间ID
     * @return 商品上架队列
     */
    @PostMapping("getDirectorGoodsQueue")
    public Result getDirectorGoodsQueue(@RequestBody RoomIdDTO roomIdDTO) {
        DirectorRoomGoodsQueueDTO queue = liveRoomService.getDirectorRoomGoodsQueue(roomIdDTO.getRoomId());
        return Result.ok(queue);
    }

    /**
     * 更新直播间公告
     * @param updateNoticeDTO 更新公告内容
     * @return Result
     */
    @PostMapping("updateNotice")
    public Result updateNotice(@RequestBody UpdateNoticeDTO updateNoticeDTO) {
//        liveRoomService.updateNotice(updateNoticeDTO.getRoomId(), updateNoticeDTO.getNotice());
//        return Result.ok();
        throw new ServiceException("该功能已下线，请联系客服修改公告");
    }

    /**
     * 直播回放列表
     */
    @PostMapping("getPlaybackLiveRoom")
    public Result getPlaybackLiveRoom(PageBase page) {
        return Result.ok(liveRoomService.getPlaybackLiveRoom(page));
    }

    /**
     * 分享直播
     */
    @PostMapping("shareLiveRoom")
    public Result shareLiveRoom(@RequestBody RoomIdDTO roomId) {
        var qrCode = liveRoomService.shareLiveRoom(roomId);
        return Result.ok(new LiveRoomQrDTO(qrCode));
    }

    /**
     * 获取即拍即上剩余次数
     * @return 剩余次数
     */
    @PostMapping("getOffhandRemainTimes")
    public Result getOffhandRemainTimes(@RequestBody RoomIdDTO roomIdDTO) {
        return Result.ok(liveRoomService.getOffhandRemainTimes(roomIdDTO.getRoomId()));
    }

    /**
     * 查询商家是否有违规记录
     */
    @PostMapping("hasOrgIllegalRecord")
    public Result hasOrgIllegalRecord() {
        return Result.ok(liveRoomService.countOrgIllegalRecord());
    }

}
