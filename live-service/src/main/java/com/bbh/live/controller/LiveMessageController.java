package com.bbh.live.controller;

import com.bbh.base.ListBase;
import com.bbh.live.dao.dto.GlobalMsgGroupDTO;
import com.bbh.live.dao.dto.OrgAttentionDTO;
import com.bbh.live.dao.dto.QueryMessagePageDTO;
import com.bbh.live.dao.dto.UnHandleBargainGoodsMsg;
import com.bbh.live.dao.service.LiveRoomInteractiveMessageService;
import com.bbh.live.dao.service.LiveRoomMsgService;
import com.bbh.live.service.msg.MsgService;
import com.bbh.live.service.msg.dto.CommonMemeMsgDTO;
import com.bbh.live.service.msg.dto.CommonTextMsgDTO;
import com.bbh.live.service.room.LiveMessageService;
import com.bbh.log.ApiLog;
import com.bbh.vo.Result;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * 直播间消息接口，包括弹幕和待处理消息
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/room/message")
@AllArgsConstructor
public class LiveMessageController {

    private final LiveRoomMsgService liveRoomMsgService;
    private final LiveRoomInteractiveMessageService liveRoomInteractiveMessageService;
    private final MsgService msgService;
    private final LiveMessageService liveMessageService;

    /**
     * 获取直播间弹幕消息列表 <br>
     * 最终返回的结构，外层是驼峰，里面的自定义data是下划线 <br>
     *
     * 因此需要将表里的json结构原样返回，避免被jackson序列化 <br>
     * 只有Map才能绕过jackson序列化，不得已出此下策 <br>
     *
     * @param queryMessagePageDTO 查询参数
     * @return Result
     */
    @PostMapping("getMsgList")
    public Result getMsgList(@RequestBody QueryMessagePageDTO queryMessagePageDTO) {
        ListBase<Map<String, Object>> list = liveRoomMsgService.getLiveRoomMsgList(
                queryMessagePageDTO.getCurrentPage(),
                queryMessagePageDTO.getPerPage(),
                queryMessagePageDTO.getRoomId(),
                queryMessagePageDTO.getLiveGoodsId()
        );
        return Result.ok(list);
    }

    /**
     * 全局消息列表，包含预约、传送和上帝视角(上架中、竞拍中)
     * @return Result
     */
    @PostMapping("getGlobalMsgGroup")
    public Result getGlobalMsgGroup() {
        GlobalMsgGroupDTO group = liveRoomMsgService.getGlobalMsgGroup();
        return Result.ok(group);
    }

    /**
     * 查询未处理消息
     * @param queryMessagePageDTO 查询参数
     * @return Result
     */
    @PostMapping("getUnhandledMessage")
    public Result getUnhandledMessage(@RequestBody QueryMessagePageDTO queryMessagePageDTO) {
        List<UnHandleBargainGoodsMsg> list = liveRoomInteractiveMessageService.getUnhandledMessageList(queryMessagePageDTO.getRoomId());
        return Result.ok(list);
    }

    /**
     * 发送文本消息
     * @param commonTextMsgDTO {
     *                           "liveRoomId": 1,
     *                           "content": "测试",
     *                           "sourceType": 1
     * }
     * @return Result
     */
    @PostMapping("send/text")
    public Result sendText(@RequestBody CommonTextMsgDTO commonTextMsgDTO) {
        return Result.ok(liveMessageService.sendText(commonTextMsgDTO));
    }

    /**
     * 发送表情包
     * @param commonMemeMsgDTO {
     *                           "liveRoomId": 1,
     *                           "memeId": "测试",
     *                           "sourceType": 1
     * }
     * @return Result
     */
    @PostMapping("send/meme")
    public Result sendMeme(@RequestBody CommonMemeMsgDTO commonMemeMsgDTO) {
        return Result.ok(liveMessageService.sendMeme(commonMemeMsgDTO));
    }

    /**
     * 关注了商家
     */
    @ApiLog
    @PostMapping("send/org/attention")
    public Result sendOrgAttention(@RequestBody OrgAttentionDTO orgAttentionDTO) {
        msgService.orgAttention(orgAttentionDTO.getSeatId(), orgAttentionDTO.getLiveRoomId(), orgAttentionDTO.getBeAttendedOrgId());
        return Result.ok();
    }

}
