package com.bbh.live.controller;

import com.bbh.live.aspect.PermissionCheck;
import com.bbh.live.controller.req.OrgAuctionNumberReq;
import com.bbh.live.controller.req.OrgVipResourceReq;
import com.bbh.live.enums.PermissionCodeEnum;
import com.bbh.live.service.organization.resource.OrgResourcesService;
import com.bbh.secure.AuthUtil;
import com.bbh.vo.Result;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2024/9/19 20:10
 */
@RestController
@RequestMapping("org/resources")
@AllArgsConstructor
public class GlobalOrgResourceController {

    private final OrgResourcesService orgResourcesService;

    /**
     * 店铺的拍号列表
     * @return
     */
    @PostMapping("/auctionNumberList")
    public Result getAuctionNumberList() {
        return Result.ok(orgResourcesService.getAuctionNumberResources(AuthUtil.getOrgId()));
    }

    /**
     * 绑定店铺拍号 todo 权限控制
     * @param auctionNumberDTO
     * @return
     */
    @PostMapping("/bindAuctionNumber")
    @PermissionCheck(PermissionCodeEnum.BEAT_EDIT)
    public Result bindAuctionNumber(@RequestBody OrgAuctionNumberReq auctionNumberDTO) {
        orgResourcesService.bindAuctionNumber(auctionNumberDTO.getAuctionNumber(), auctionNumberDTO.getSeatId());
        return Result.ok();
    }

    /**
     * 解绑店铺拍号
     * @param auctionNumberDTO
     * @return
     */
    @PostMapping("/unbindAuctionNumber")
    @PermissionCheck(PermissionCodeEnum.BEAT_EDIT)
    public Result unbindAuctionNumber(@RequestBody OrgAuctionNumberReq auctionNumberDTO) {
        orgResourcesService.unbindAuctionNumber(auctionNumberDTO.getAuctionNumber());
        return Result.ok();
    }

    /**
     * 换绑拍号
     * @param auctionNumberDTO
     * @return
     */
    @PostMapping("/changeBindAuctionNumber")
    @PermissionCheck(PermissionCodeEnum.BEAT_EDIT)
    public Result changeBindAuctionNumber(@RequestBody OrgAuctionNumberReq auctionNumberDTO) {
        orgResourcesService.changeBindAuctionNumber(auctionNumberDTO.getAuctionNumber(), auctionNumberDTO.getSeatId());
        return Result.ok();
    }

    /**
     * 店铺的vip列表
     * @return
     */
    @PostMapping("/vipList")
    public Result vipList() {
        return Result.ok(orgResourcesService.getVipResources(AuthUtil.getOrgId()));
    }

    /**
     * 绑定店铺vip
     * @param vipResourceReq
     * @return
     */
    @PostMapping("/bindVip")
    @PermissionCheck(PermissionCodeEnum.LIVE_ALLOCATE_VIP)
    public Result bindVip(@RequestBody OrgVipResourceReq vipResourceReq) {
        orgResourcesService.bindVip(vipResourceReq.getVipId(), vipResourceReq.getSeatId());
        return Result.ok();
    }

    /**
     * 解绑店铺vip
     * @param vipResourceReq
     * @return
     */
    @PostMapping("/unbindVip")
    @PermissionCheck(PermissionCodeEnum.LIVE_ALLOCATE_VIP)
    public Result unbindVip(@RequestBody OrgVipResourceReq vipResourceReq) {
        orgResourcesService.unbindVip(vipResourceReq.getVipId());
        return Result.ok();
    }
}
