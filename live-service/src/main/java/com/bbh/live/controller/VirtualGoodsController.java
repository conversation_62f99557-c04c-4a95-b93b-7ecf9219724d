package com.bbh.live.controller;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.bbh.exception.ServiceException;
import com.bbh.live.dao.dto.*;
import com.bbh.live.dao.dto.vo.CreateOrderV2VO;
import com.bbh.live.dao.dto.vo.VirtualGoodsBuyVO;
import com.bbh.live.dao.service.GlobalOrderOtherGoodsDicService;
import com.bbh.live.service.virutalgoods.VirtualGoodsService;
import com.bbh.log.ApiLog;
import com.bbh.secure.AuthUtil;
import com.bbh.service.pay.config.PayCenterProperties;
import com.bbh.util.AssertUtil;
import com.bbh.util.LogExUtil;
import com.bbh.util.SignUtil;
import com.bbh.vo.Result;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.nio.charset.StandardCharsets;

/**
 * <AUTHOR> dsy
 * @Date: 2024/9/12
 * @Description:
 */
@Slf4j
@AllArgsConstructor
@RestController
@RequestMapping("/virtualGoods")
public class VirtualGoodsController {

    private final VirtualGoodsService virtualGoodsService;
    private final PayCenterProperties payCenterProperties;
    private final GlobalOrderOtherGoodsDicService globalOrderOtherGoodsDicService;
    private final StringRedisTemplate stringRedisTemplate;

    /**
     * 获取可开通的会员套餐
     * @return vip套餐列表
     */
    @PostMapping("/getFenbeiTypes")
    public Result getFenbeiGoods() {
        return Result.ok(globalOrderOtherGoodsDicService.getFenbeiTypeList());
    }

    /**
     * 调起支付，保证金、会员、购买分贝都调这个接口
     * @param virtualGoodsBuyVO 参数
     * @return  订单号
     */
    @ApiLog
    @PostMapping("/createOrder")
    public Result createOrder(@RequestBody VirtualGoodsBuyVO virtualGoodsBuyVO) {
        AssertUtil.assertNotNull(virtualGoodsBuyVO.getGoodsId(), "虚拟商品不存在");
        virtualGoodsBuyVO.setBuyerSeatId(AuthUtil.getSeatId());
        CreateOrderV2VO order = virtualGoodsService.createOrder(virtualGoodsBuyVO);

        // 这里做个缓存，在检查支付结果的时候，如果有这个缓存，要换个友好提示
        stringRedisTemplate.opsForValue().set("virtualGoodsOrder:" + order.getGlobalOrderId(), "1");

        // 返回字符串
        return Result.ok(order.getOrderNo());
    }

    @ApiLog
    @PostMapping("/createOrder/v2")
    public Result createOrderV2(@RequestBody VirtualGoodsBuyVO virtualGoodsBuyVO) {
        AssertUtil.assertNotNull(virtualGoodsBuyVO.getGoodsId(), "虚拟商品不存在");
        virtualGoodsBuyVO.setBuyerSeatId(AuthUtil.getSeatId());
        return Result.ok(virtualGoodsService.createOrder(virtualGoodsBuyVO));
    }

    /**
     * 调起支付
     */
    @ApiLog
    @PostMapping("pay")
    public Result pay(@RequestBody PayDTO payDTO) {
        return Result.ok(virtualGoodsService.pay(payDTO));
    }

    /**
     * 检查支付结果
     * @param orderIdDTO 订单流水号
     * @return  支付成功: true/false
     */
    @ApiLog
    @PostMapping("checkPaySuccess")
    public Result checkPaySuccess(@RequestBody OrderIdDTO orderIdDTO) {
        boolean success;
        try {
            success = virtualGoodsService.checkPaySuccess(orderIdDTO.getOrderId());
        } catch (Exception e) {
            // 如果是老接口来的，要给个友好提示
            String oldFlag = stringRedisTemplate.opsForValue().get("virtualGoodsOrder:" + orderIdDTO.getOrderId());
            if (StrUtil.isNotBlank(oldFlag)) {
                stringRedisTemplate.delete("virtualGoodsOrder:" + orderIdDTO.getOrderId());
                throw new ServiceException("拼命加载中，请刷新后重试");
            }

            log.error("检查支付结果: {}", e.getMessage(), e);
            success = false;
        }
        return Result.ok(success);
    }

    @ApiLog
    @PostMapping("/payCallback")
    public Object payCallback(@RequestBody SignDTO signDTO) {
        var sign = signDTO.getSign();
        AssertUtil.assertNotNull(sign, "签名错误");
        if (!SignUtil.checkSign(sign, payCenterProperties.getSalt())) {
            LogExUtil.errorLog("支付回调签名校验失败,sign:" + sign, new ServiceException("支付回调签名校验失败"));
        }
        String json = Base64.decodeStr(sign, StandardCharsets.UTF_8);
        PaySuccessDTO paySuccessDTO = JSONUtil.toBean(json, PaySuccessDTO.class);
        virtualGoodsService.orderPayCallback(paySuccessDTO.getOutTradeNo());
        return "success";
    }
}
