package com.bbh.live.controller;

import com.bbh.live.controller.req.KeywordsReq;
import com.bbh.live.dao.dto.VipIdDTO;
import com.bbh.live.dao.dto.vo.BuyerVipInfoVO;
import com.bbh.live.dao.service.GlobalOrgSeatService;
import com.bbh.live.enums.BuyerVipTypeEnum;
import com.bbh.live.service.buyer.impl.BuyerCenterService;
import com.bbh.live.service.buyer.sign.BuyerSignService;
import com.bbh.live.service.buyer.sign.vo.SignInfoVo;
import com.bbh.live.service.buyer.vip.BuyerVipService;
import com.bbh.live.service.buyer.vip.vo.UserBuyerVipInfoVO;
import com.bbh.live.service.order.CartService;
import com.bbh.model.GlobalOrgSeat;
import com.bbh.secure.AuthUtil;
import com.bbh.vo.Result;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;

/**
 * <AUTHOR> dsy
 * @Date: 2024/8/16
 * @Description: 买手基本服务接口
 */
@RestController
@RequestMapping("buyer")
@AllArgsConstructor
@Slf4j
public class BuyerController {

    private final BuyerSignService buyerSignService;
    private final BuyerVipService buyerVipService;
    private final GlobalOrgSeatService globalOrgSeatService;
    private final CartService cartService;
    private final BuyerCenterService buyerCenterService;

    /**
     * 用户签到
     *
     * @return 操作结果
     */
    @PostMapping("/sign")
    public Result sign() {
        return Result.ok(buyerSignService.sign(new Date(), AuthUtil.getSeatId()));
    }

    /**
     * 用户签到统计信息
     *
     * @return
     */
    @PostMapping("/signInfo")
    public Result signInfo() {
        SignInfoVo signInfoVo = buyerSignService.signInfo(new Date(), AuthUtil.getSeatId());
        return Result.ok(signInfoVo);
    }

    /**
     * 买手会员信息
     *
     * @return
     */
    @PostMapping("/getVipInfo")
    public Result getVipInfo(@RequestBody(required = false) VipIdDTO vipIdDTO) {
        UserBuyerVipInfoVO vipInfoBySeatId;
        if (vipIdDTO == null || vipIdDTO.getVipId() == null) {
            vipInfoBySeatId = buyerVipService.getUserBuyerVipInfoBySeatId(AuthUtil.getSeatId());
        } else {
            vipInfoBySeatId = buyerVipService.getUserBuyerVipInfoByVipId(vipIdDTO.getVipId());
        }
        BuyerVipInfoVO buyerVipInfo = new BuyerVipInfoVO();
        BeanUtils.copyProperties(vipInfoBySeatId, buyerVipInfo);

        //拍号，昵称，头像
        globalOrgSeatService.lambdaQuery().eq(GlobalOrgSeat::getId, vipInfoBySeatId.getSeatId())
                .select(GlobalOrgSeat::getId, GlobalOrgSeat::getAuctionCode, GlobalOrgSeat::getAvatar, GlobalOrgSeat::getShowName)
                .oneOpt()
                .ifPresent(orgSeat -> {
                    buyerVipInfo.setAuctionCode(orgSeat.getAuctionCode());
                    buyerVipInfo.setAvatar(orgSeat.getAvatar());
                    buyerVipInfo.setNickName(orgSeat.getShowName());
                    buyerVipInfo.setIsVip(vipInfoBySeatId.getBuyerVipType() == BuyerVipTypeEnum.VIP);
                    buyerVipInfo.setCreateFrom(vipInfoBySeatId.getCreateFrom());
                });
        return Result.ok(buyerVipInfo);
    }

    /**
     * 获取买手的收银台商品数量
     *
     * @return 商品数量
     */
    @PostMapping("/getCartCount")
    public Result getCartCount() {
        return Result.ok(cartService.getBuyerCartCount());
    }


    /**
     * 预约的直播间列表列表
     * @return
     */
    @PostMapping("/getSubscribeLiveRoomList")
    public Result getSubscribeLiveRoomList() {
        return Result.ok(buyerCenterService.getSubscribeLiveRoomList(AuthUtil.getSeatId()));
    }

    /**
     * 关注的商家列表
     * @return
     */
    @PostMapping("/getAttentionOrgList")
    public Result getAttentionOrgList(@RequestBody KeywordsReq keywordsReq) {
        return Result.ok(buyerCenterService.getAttentionOrgList(keywordsReq, AuthUtil.getSeatId()));
    }
}
