package com.bbh.live.controller;

import cn.hutool.core.util.StrUtil;
import com.bbh.live.controller.req.RunScriptReq;
import com.bbh.live.service.script.ScriptRunnerEngine;
import com.bbh.secure.AuthUtil;
import com.bbh.vo.Result;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2024/12/3 13:40
 * @description
 */
@RestController
@RequestMapping
@AllArgsConstructor
@Slf4j
public class ScriptRunController {

    private final static Long DEFAULT_USER = 123456L;

    private final ScriptRunnerEngine scriptRunnerEngine;

    @PostMapping("runScript")
    public Result runScript(@RequestBody RunScriptReq runScriptReq) {
        Long seatId = AuthUtil.getSeatId();
        //AssertUtil.assertTrue(seatId.equals(DEFAULT_USER), "权限不足");
        if(StrUtil.isBlank(runScriptReq.getSrcCode())){
            return null;
        }
        return Result.ok(scriptRunnerEngine.runScript(runScriptReq));
    }
}
