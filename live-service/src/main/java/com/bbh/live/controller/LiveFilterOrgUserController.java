package com.bbh.live.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.bbh.base.ListBase;
import com.bbh.base.PageBase;
import com.bbh.live.dao.dto.IdDTO;
import com.bbh.live.dao.dto.LiveFilterSearchUserDTO;
import com.bbh.live.dao.dto.LiveOrgFilterDTO;
import com.bbh.live.dao.dto.vo.LiveOrgFilterVO;
import com.bbh.live.service.room.LiveOrgFilterBizService;
import com.bbh.secure.AuthUtil;
import com.bbh.util.AssertUtil;
import com.bbh.vo.AuthUser;
import com.bbh.vo.Result;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @Date 2024/7/26
 * @description: 直播黑白名单功能 商家拉黑用户
 */
@RestController
@RequestMapping("/filter/org/user")
@RequiredArgsConstructor
public class LiveFilterOrgUserController {

    private final LiveOrgFilterBizService liveOrgFilterBizService;


    /***
     * 黑白名单 搜索用户
     * @param dto
     * @return
     */
    @PostMapping("/search/user")
    public Result searchUser(@RequestBody LiveFilterSearchUserDTO dto) {
        AuthUser user = AuthUtil.getUser();
        IPage<LiveOrgFilterVO> searchUser = liveOrgFilterBizService.searchUser(user, dto);
        return Result.ok(ListBase.pageConvertToListBase(searchUser));
    }


    /****
     * 黑名单列表
     * @return
     */
    @PostMapping("/pageList")
    public Result pageList(@RequestBody PageBase pageBase) {
        AuthUser user = AuthUtil.getUser();
        IPage<LiveOrgFilterVO> pages = liveOrgFilterBizService.pageListUser(pageBase, user, null);
        return Result.ok(pages);
    }


    @PostMapping("remove")
    public Result remove(@RequestBody IdDTO idDTO) {
        AuthUser user = AuthUtil.getUser();
        Long id = idDTO.getId();
        liveOrgFilterBizService.remove(null,user.getOrgId(),id);
        return Result.ok("操作成功");
    }


    /****
     * 添加黑名单
     * @return
     */
    @PostMapping("/add")
    public Result addUser(@RequestBody LiveOrgFilterDTO liveOrgFilter) {
        AuthUser user = AuthUtil.getUser();
        AssertUtil.assertFalse(liveOrgFilter.getSeatId().equals(user.getSeatId()), "不能拉黑自己");
        liveOrgFilterBizService.addUser(user,liveOrgFilter);
        return Result.ok("操作成功");
    }


}
