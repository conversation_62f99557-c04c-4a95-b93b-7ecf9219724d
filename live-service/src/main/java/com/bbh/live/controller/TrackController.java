package com.bbh.live.controller;

import com.bbh.live.service.track.TrackService;
import com.bbh.model.VipBuyerClickRecord;
import com.bbh.vo.Result;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 埋点跟踪
 * <AUTHOR>
 */
@Slf4j
@AllArgsConstructor
@RestController
@RequestMapping("track")
public class TrackController {

    private final TrackService trackService;

    /**
     * 会员相关功能的点击数据上报
     * 1 直播间广告 2 我的  3 首页轮播图 4 首页广告 5 闪屏图 6收银台 7 签到 8分贝商城  9修改昵称 10招财虎 11商家地图
     */
    @PostMapping("vip/click")
    public Result clickVip(@RequestBody VipBuyerClickRecord vipBuyerClickRecord) {
        trackService.clickVip(vipBuyerClickRecord);
        return Result.ok();
    }

}
