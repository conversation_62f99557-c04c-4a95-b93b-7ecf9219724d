package com.bbh.live.controller;

import com.bbh.base.PageQuery;
import com.bbh.config.LockPlus;
import com.bbh.enums.CommitLockPlusKeyEnum;
import com.bbh.live.service.buyerCancel.BuyerCancelRecordServiceContext;
import com.bbh.live.service.buyerCancel.dto.BuyerCancelRecordDTO;
import com.bbh.live.service.buyerCancel.dto.request.BuyerCancelRecordQueryRequest;
import com.bbh.live.service.buyerCancel.dto.request.BuyerCancelRecordSubmitRequest;
import com.bbh.live.service.buyerCancel.dto.request.BuyerCancelTransactionInfoReq;
import com.bbh.live.service.buyerCancel.dto.request.SellerCancelRecordApproveRequest;
import com.bbh.vo.Result;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/buyer-cancel-record")
@Validated
@AllArgsConstructor
public class BuyerCancelRecordController {

   private final BuyerCancelRecordServiceContext buyerCancelRecordServiceContext;

    /**
     * 买家取消原因枚举
     * 用php接口： http://47.103.47.114:4999/web/#/120/2061 type=50
     */
    @Deprecated
    @PostMapping("/cancel-reason/enum/list")
    public Result getCancelReasonList() {
        return Result.ok();
    }

    /**
     * 获取交易信息
     */
    @PostMapping("/transaction/info")
    public Result getTransactionInfo(@RequestBody BuyerCancelTransactionInfoReq dto) {
        return Result.ok(buyerCancelRecordServiceContext.strategy(dto).getTransactionInfo(dto.getLiveGoodsId()));
    }

    /**
     * 买家提交审核
     */
    @LockPlus(value = "buyer-cancel-record-submit-lock", lockPlusKeyType = CommitLockPlusKeyEnum.COMMIT_LOCK_PLUS_USER)
    @PostMapping("/submit")
    public Result submit(@RequestBody BuyerCancelRecordSubmitRequest request) {
        return Result.ok(buyerCancelRecordServiceContext.strategy(request).submit(request));
    }

    /**
     * 买家撤销审核
     */
    @LockPlus(value = "buyer-cancel-record-revoke-lock", lockPlusKeyType = CommitLockPlusKeyEnum.COMMIT_LOCK_PLUS_USER)
    @PostMapping("/revoke")
    public Result revoke(@RequestBody BuyerCancelRecordDTO dto) {
        buyerCancelRecordServiceContext.strategy(dto).revoke(dto.getBuyerCancelRecordId());
        return Result.ok();
    }

    /**
     * 买家查看审核详情
     */
    @PostMapping("/buyer/record/detail")
    public Result getBuyerRecordDetail(@RequestBody BuyerCancelRecordDTO dto) {
        return Result.ok(buyerCancelRecordServiceContext.strategy(dto).getRecordDetail(dto.getBuyerCancelRecordId(), true));
    }

    /**
     * 卖家查看审核详情
     */
    @PostMapping("/seller/record/detail")
    public Result getSellerRecordDetail(@RequestBody BuyerCancelRecordDTO dto) {
        return Result.ok(buyerCancelRecordServiceContext.strategy(dto).getRecordDetail(dto.getBuyerCancelRecordId(), false));
    }


    /**
     * 买家审核列表
     */
    @PostMapping("/buyer/record/list")
    public Result getBuyerRecordList(@RequestBody BuyerCancelRecordQueryRequest request,@RequestBody PageQuery pageQuery) {
        request.setIfBuyer(true);
        return Result.ok(buyerCancelRecordServiceContext.strategy(request).getRecordList(request,pageQuery));
    }

    /**
     * 卖家审核列表
     */
    @PostMapping("/seller/record/list")
    public Result getSellerRecordList(@RequestBody BuyerCancelRecordQueryRequest request,@RequestBody PageQuery pageQuery) {
        request.setIfBuyer(false);
        return Result.ok(buyerCancelRecordServiceContext.strategy(request).getRecordList(request,pageQuery));
    }

    /**
     * 卖家审核通过
     */
    @LockPlus(value = "seller-cancel-record-approve-lock", lockPlusKeyType = CommitLockPlusKeyEnum.COMMIT_LOCK_PLUS_USER)
    @PostMapping("/seller/approve")
    public Result sellerApprove(@RequestBody SellerCancelRecordApproveRequest request) {
        buyerCancelRecordServiceContext.strategy(request).sellerApprove(request);
        return Result.ok();
    }

}
