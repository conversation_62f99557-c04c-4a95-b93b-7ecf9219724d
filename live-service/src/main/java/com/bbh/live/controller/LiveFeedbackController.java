package com.bbh.live.controller;

import com.bbh.live.dao.service.LiveFeedbackService;
import com.bbh.model.LiveFeedback;
import com.bbh.secure.AuthUtil;
import com.bbh.vo.AuthUser;
import com.bbh.vo.Result;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @Date 2024/7/30
 * @description:
 */
@RestController
@AllArgsConstructor
@RequestMapping("/feedback")
public class LiveFeedbackController {

    private final LiveFeedbackService liveFeedbackService;

    @PostMapping("add")
    public Result add(@RequestBody LiveFeedback liveFeedback) {
        AuthUser user = AuthUtil.getUser();
        liveFeedbackService.add(user,liveFeedback);
        return Result.ok("操作成功");
    }

    /**
     * 反馈举报的选项列表
     */
    @PostMapping("optionList")
    public Result getOptionList() {
        var list = liveFeedbackService.getOptionList();
        return Result.ok(list);
    }

}
