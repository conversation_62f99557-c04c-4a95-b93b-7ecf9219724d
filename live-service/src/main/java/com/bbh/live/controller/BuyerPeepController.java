package com.bbh.live.controller;

import com.bbh.live.dao.dto.VipPeepDTO;
import com.bbh.live.service.buyer.vip.impl.VipBuyerPeepService;
import com.bbh.vo.Result;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 会员查看出价人
 * <AUTHOR>
 */
@RestController
@RequestMapping("/vip/peep")
@Slf4j
@AllArgsConstructor
public class BuyerPeepController {

    private final VipBuyerPeepService vipBuyerPeepService;

    /**
     * 创建查看记录
     * @param vipPeepDTO 查看参数
     * @return 操作结果
     */
    @PostMapping
    public Result createPoopLog(@RequestBody VipPeepDTO vipPeepDTO) {
        vipBuyerPeepService.createPoopLog(vipPeepDTO);
        return Result.ok();
    }

}
