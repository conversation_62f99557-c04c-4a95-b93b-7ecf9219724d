package com.bbh.live.controller.req;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024/10/9 13:41
 * @description
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class OrgMapSearchReq {

    /**
     * 店铺id
     */
    private Long orgId;

    /**
     * 纬度
     */
    private BigDecimal latitude;

    /**
     * 经度
     */
    private BigDecimal longitude;

    /**
     * 关键词搜索，店铺名
     */
    private String keywords;
}
