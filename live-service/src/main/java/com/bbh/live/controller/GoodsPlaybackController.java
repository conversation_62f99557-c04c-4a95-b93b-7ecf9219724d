package com.bbh.live.controller;

import com.bbh.live.controller.req.PlaybackGoodsQueryReq;
import com.bbh.live.dao.dto.IdDTO;
import com.bbh.live.dao.dto.RoomIdDTO;
import com.bbh.live.service.livegoods.LiveGoodsPlaybackService;
import com.bbh.util.AssertUtil;
import com.bbh.vo.Result;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 商品回放
 * <AUTHOR>
 */
@RequestMapping("goods/playback")
@RestController
@Slf4j
@AllArgsConstructor
public class GoodsPlaybackController {

    private final LiveGoodsPlaybackService liveGoodsPlaybackService;

    @PostMapping("list")
    public Result goodsList(@RequestBody PlaybackGoodsQueryReq queryReq) {
        AssertUtil.assertNotNull(queryReq.getRoomId(), "请选择直播间");
        return Result.ok(liveGoodsPlaybackService.getLiveGoodsList(queryReq));
    }

    /**
     * 移出购物车
     * @param idDTO ERP货品id
     * @return Result
     */
    @PostMapping("removeCeShoppingCart")
    public Result removeCeShoppingCart(@RequestBody IdDTO idDTO) {
        liveGoodsPlaybackService.removeCeShoppingCart(idDTO.getId());
        return Result.ok();
    }

    /**
     * 查询直播商品回放地址
     * 先查询本地，如果不存在就请求中台直播服务
     *
     * @param idDTO 直播商品编号
     * @return 返回直播回放地址
     */
    @PostMapping("get")
    public Result liveGoodsVideoRecord(@RequestBody IdDTO idDTO) {
        return Result.ok(liveGoodsPlaybackService.getLiveVideoUrl(idDTO.getId()));
    }

    /**
     * 返回最近一场、没有全部同步到云展的直播间
     * @return
     */
    @PostMapping("getLatestToCeRoom")
    public Result getLatestToCeRoom(@RequestBody RoomIdDTO roomIdDTO) {
        return Result.ok(liveGoodsPlaybackService.getLatestToCeRoom(roomIdDTO.getRoomId()));
    }

}
