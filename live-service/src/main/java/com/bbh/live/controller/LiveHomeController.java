package com.bbh.live.controller;

import cn.hutool.json.JSONArray;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.bbh.live.config.LiveBizProperties;
import com.bbh.live.dao.dto.vo.LiveRoomRecommendVO;
import com.bbh.live.dao.service.GlobalBizConfigService;
import com.bbh.live.enums.GlobalBizConfigEnum;
import com.bbh.live.service.room.LiveRoomBizService;
import com.bbh.model.GlobalBizConfig;
import com.bbh.vo.Result;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;
import java.util.List;

/**
 * 直播首页控制器
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/home")
@AllArgsConstructor
public class LiveHomeController {

    private final GlobalBizConfigService globalBizConfigService;

    private final LiveBizProperties liveBizProperties;

    private final LiveRoomBizService liveRoomService;

    /**
     * 获取首页轮播图
     *
     * @return Result
     */
    @PostMapping("/getCarouselImg")
    public Result getCarouselImg() {
        JSONArray jsonArray = liveBizProperties.getHomeCarouselImgs();
        if (jsonArray != null) {
            Object[] array = jsonArray.toArray();
            return Result.ok(array);
        }

        return Result.ok();
    }

    /**
     * 更新首页轮播图
     *
     * @return Result
     */
    @PostMapping("/updateCarouselImg")
    public Result updateCarouselImg(@RequestBody String bizValue) {
        LambdaUpdateWrapper<GlobalBizConfig> eq = new LambdaUpdateWrapper<GlobalBizConfig>()
                .eq(GlobalBizConfig::getBizKey, GlobalBizConfigEnum.LIVE_HOME_CAROUSEL_IMGS.getBizKey());
        GlobalBizConfig config = globalBizConfigService.getOne(eq);
        if (config != null) {
            config.setBizValue(bizValue);
            config.setUpdatedAt(new Date());
            globalBizConfigService.updateById(config);
        }
        return Result.ok();
    }

    /**
     * 首页推荐主播
     *
     * @return Result
     */
    @PostMapping("/getRecommend")
    public Result getRecommend() {
        List<LiveRoomRecommendVO> recommendRoomList = this.liveRoomService.getRecommendRoomList();
        return Result.ok(recommendRoomList);
    }


}
