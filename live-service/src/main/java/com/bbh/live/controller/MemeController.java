package com.bbh.live.controller;

import com.bbh.live.dao.dto.MemeClassDTO;
import com.bbh.live.service.msg.dto.CommonMemeMsgDTO;
import com.bbh.vo.Result;
import com.bbh.live.dao.service.VipMemeClassService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 表情包的控制器
 * <AUTHOR>
 */
@RequestMapping("meme")
@RestController
@Slf4j
@AllArgsConstructor
public class MemeController {

    private final VipMemeClassService vipMemeClassService;

    @RequestMapping("list")
    public Result list() {
        List<MemeClassDTO> memeClassDTOList = vipMemeClassService.getMemeList();
        return Result.ok(memeClassDTOList);
    }

    /**
     * @deprecated
     * @see LiveMessageController#sendMeme(CommonMemeMsgDTO)
     */
    @Deprecated
    @PostMapping("send")
    public Result send() {
        return Result.ok();
    }

}
