package com.bbh.live.controller;

import com.bbh.live.dao.dto.LiveStreamNotifyDTO;
import com.bbh.live.service.room.LiveRoomNotifyService;
import com.bbh.log.ApiLog;
import com.bbh.vo.Result;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 直播服务回调接口, 接口加入白名单免鉴权
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/live")
@AllArgsConstructor
public class LiveStreamNotifyController {

    private final LiveRoomNotifyService liveRoomNotifyService;

    /**
     * 直播服务回调
     */
    @ApiLog
    @PostMapping("/notify")
    public Result notify(@RequestBody LiveStreamNotifyDTO dto) {
        liveRoomNotifyService.handleLiveNotify(dto);
        return Result.ok();
    }

}
