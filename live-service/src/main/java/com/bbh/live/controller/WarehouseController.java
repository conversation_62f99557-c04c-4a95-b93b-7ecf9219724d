package com.bbh.live.controller;

import com.bbh.feign.dto.WarehouseGoodsQueryDTO;
import com.bbh.live.service.warehouse.WarehouseService;
import com.bbh.vo.Result;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @date 2024/10/25 13:53
 * @description
 */
@Slf4j
@AllArgsConstructor
@RestController
@RequestMapping("/warehouse/storehouse")
public class WarehouseController {

    private final WarehouseService warehouseService;

    @GetMapping("/page")
    public Object getStorehouseList() {
        return Result.ok(warehouseService.getStorehouseList());
    }


    @PostMapping("/goods/page")
    public Object getStorehouseGoodsList(@RequestBody WarehouseGoodsQueryDTO goodsQueryDTO) {
        return Result.ok(warehouseService.getWarehouseGoodsList(goodsQueryDTO));
    }
}
