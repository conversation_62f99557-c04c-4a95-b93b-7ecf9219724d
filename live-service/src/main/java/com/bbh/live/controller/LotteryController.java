package com.bbh.live.controller;

import com.bbh.base.ListBase;
import com.bbh.live.dao.dto.LotteryCreateDTO;
import com.bbh.live.dao.dto.LotteryWinningRecordDTO;
import com.bbh.live.dao.dto.LotteryWinningRecordPO;
import com.bbh.live.service.lottery.LotteryService;
import com.bbh.log.ApiLog;
import com.bbh.vo.Result;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 抽奖
 * <AUTHOR>
 */
@RestController
@Slf4j
@AllArgsConstructor
@RequestMapping("lottery")
public class LotteryController {

    private final LotteryService lotteryService;

    /**
     * 抽奖页面信息
     */
    @PostMapping("index")
    public Result lottery() {
        return Result.ok(lotteryService.getLottery());
    }

    /**
     * 中奖结果
     */
    @PostMapping("winningRecord")
    public Result winningRecord(@RequestBody LotteryWinningRecordPO pageBase) {
        ListBase<LotteryWinningRecordDTO> list = lotteryService.getWinningRecordList(pageBase);
        return Result.ok(list);
    }

    /**
     * 创建抽奖
     */
    @ApiLog
    @PostMapping("create")
    public Result createLottery(@RequestBody LotteryCreateDTO createLotteryDTO) {
        return Result.ok(lotteryService.createLottery(createLotteryDTO));
    }

}
