package com.bbh.live.controller;

import com.bbh.live.service.admin.AdminService;
import com.bbh.vo.Result;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2024/12/5 16:37
 * @description
 */
@RestController
@RequestMapping("/admin")
@AllArgsConstructor
public class AdminController {

    private final AdminService adminService;

    /**
     * 关闭进程，不再处理请求，任务
     * @return
     */
    @PostMapping("/shutdown")
    public Result shutdown() {
        adminService.shutdown();
        return Result.ok();
    }
}
