package com.bbh.live.controller;

import com.bbh.live.dao.dto.ChatroomStatusSyncDTO;
import com.bbh.live.service.msg.ChatroomService;
import com.bbh.log.ApiLog;
import com.bbh.vo.Result;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 融云服务回调接口
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/rongcloud/notify")
@AllArgsConstructor
public class RongCloudNotifyController {

    private final ChatroomService chatroomService;

    /**
     * 聊天室状态同步
     * <a href="https://doc.rongcloud.cn/imserver/server/v1/chatroom/status#%E5%9B%9E%E8%B0%83%E4%BB%A3%E7%A0%81%E7%A4%BA%E4%BE%8B">融云开发文档</a>
     */
    @ApiLog
    @RequestMapping("/chatroom/status/sync")
    public Result chatroomStatusSync(@RequestBody List<ChatroomStatusSyncDTO> dto) {
        log.info("融云服务回调接口-----");
        chatroomService.chatroomStatusSync(dto);
        return Result.ok();
    }

}
