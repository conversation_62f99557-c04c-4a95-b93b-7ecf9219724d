package com.bbh.live.controller;

import cn.hutool.core.lang.Dict;
import com.bbh.live.dao.dto.*;
import com.bbh.live.dao.dto.vo.CreateOrderV2VO;
import com.bbh.live.service.order.CartService;
import com.bbh.live.service.order.OrderV2Service;
import com.bbh.log.ApiLog;
import com.bbh.vo.Result;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 订单
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/order")
@AllArgsConstructor
@Slf4j
public class OrderController {

    private final OrderV2Service orderV2Service;
    private final CartService cartService;

    /**
     * 获取收银台信息
     */
    @PostMapping("getCashierDeskInfo")
    public Result getCashierDeskInfo(@RequestBody ShoppingCartDTO shoppingCartDTO) {
        return Result.ok(cartService.getCashierDeskInfo(shoppingCartDTO.getIfOrg()));
    }

    /**
     * 收银台显示的数量，微商显示个人的数量，企业显示全店数量
     * @return 数量
     */
    @PostMapping(value = {"getCashierDeskCount"})
    public Result getCashierDeskCount() {
        Long count = cartService.getCashierDeskCount();
        return Result.ok(count);
    }

    /**
     * 准备订单信息
     */
    @ApiLog
    @PostMapping("prepareOrderInfo")
    public Result prepareOrderInfo(@RequestBody PrepareOrderDTO prepareOrderDTO) {
        return Result.ok(orderV2Service.prepareOrderInfoV2(prepareOrderDTO));
    }

    /**
     * 创建订单
     */
    @ApiLog
    @PostMapping("create")
    public Result createOrder(@RequestBody CreateOrderV2DTO createOrderDTO) {
        CreateOrderV2VO order = orderV2Service.createOrderV2(createOrderDTO);
        return Result.ok(order);
    }

    /**
     * 调起支付
     */
    @ApiLog
    @PostMapping("pay")
    public Result pay(@RequestBody PayDTO payDTO) {
        return Result.ok(orderV2Service.pay(payDTO));
    }

    /**
     * 上传转账凭证
     */
    @ApiLog
    @PostMapping("offlineTransfer")
    public Result offlineTransfer(@RequestBody OfflineTransferDTO offlineTransferDTO) {
        orderV2Service.offlineTransfer(offlineTransferDTO);
        return Result.ok();
    }

    /**
     * 取消支付，把商品退回购物车
     * @param cancelPayDTO 订单流水号
     * @return Result
     */
    @ApiLog
    @PostMapping("pay/cancel")
    public Result cancelPay(@RequestBody CancelPayDTO cancelPayDTO) {
        orderV2Service.cancelPay(cancelPayDTO);
        return Result.ok();
    }

    /**
     * 支付回调，在创建订单-调用中台支付时使用 {@link com.bbh.live.service.order.impl.PayServiceImpl#orderPay}
     * @return Result
     */
    @ApiLog
    @RequestMapping("pay/notify")
    public String payNotify(@RequestBody SignDTO signDTO) {
        return orderV2Service.paySuccessCallBack(signDTO);
    }

    @ApiLog
    @RequestMapping("pay/notify/direct")
    public String payNotifyDirect(@RequestBody Dict dict) {
        orderV2Service.paySuccess(dict.getStr("out_trade_no"));
        return "success";
    }

    /**
     * 检查支付结果
     * @param orderIdDTO 订单ID
     * @return  支付成功: true/false
     */
    @ApiLog
    @PostMapping("checkPaySuccess")
    public Result checkPaySuccess(@RequestBody OrderIdDTO orderIdDTO) {
        boolean success;
        try {
            success = orderV2Service.checkPaySuccess(orderIdDTO.getOrderId());
        } catch (Exception e) {
            log.error("检查支付结果: {}", e.getMessage(), e);
            success = false;
        }
        return Result.ok(success);
    }

    /**
     * 获取订单状态数量
     * @return  分组后的订单数量
     */
    @PostMapping("getStatusCountGroup")
    public Result getStatusCountGroup(@RequestBody OrderCenterDTO orderCenterDTO) {
        return Result.ok(orderV2Service.getStatusCountGroup(orderCenterDTO));
    }

    /**
     * 获取未读消息角标
     */
    @PostMapping("getUnreadMessage")
    public Result getUnreadMessage(@RequestBody OrderCenterDTO orderCenterDTO) {
        return Result.ok(orderV2Service.getUnreadMessage(orderCenterDTO));
    }

    /**
     * 卖家收银台商品列表，按成交商家分页
     * @deprecated 现在由前端直接分页，用 #getSellerCashierOrderListV2
     */
    @Deprecated
    @PostMapping("seller/cashier/order/list")
    public Result getSellerCashierOrderList(@RequestBody SellerCashierDeskDTO sellerCashierDeskDTO) {
        var groupList = cartService.getSellerCashierDeskInfo(sellerCashierDeskDTO);
        return Result.ok(groupList);
    }

    /**
     * 卖家收银台商品列表，按商品分页，不分组
     */
    @PostMapping("seller/cashier/order/list/v2")
    public Result getSellerCashierOrderListV2(@RequestBody SellerCashierDeskDTO sellerCashierDeskDTO) {
        var groupList = cartService.getSellerCashierGoodsPageList(sellerCashierDeskDTO);
        return Result.ok(groupList);
    }

}
