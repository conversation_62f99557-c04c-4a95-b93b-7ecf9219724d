package com.bbh.live.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.bbh.base.ListBase;
import com.bbh.base.PageBase;
import com.bbh.enums.LiveOrgFilterSourceTypeEnum;
import com.bbh.live.dao.dto.IdDTO;
import com.bbh.live.dao.dto.LiveFilterSearchUserDTO;
import com.bbh.live.dao.dto.LiveOrgFilterDTO;
import com.bbh.live.dao.dto.vo.LiveOrgFilterVO;
import com.bbh.live.service.room.LiveOrgFilterBizService;
import com.bbh.secure.AuthUtil;
import com.bbh.util.AssertUtil;
import com.bbh.vo.AuthUser;
import com.bbh.vo.Result;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/7/26
 * @description: 直播黑白名单功能  用户拉黑商家
 */
@RestController
@RequestMapping("/filter/user/org")
@RequiredArgsConstructor
public class LiveFilterUserOrgController {

    private final LiveOrgFilterBizService liveOrgFilterBizService;

    /***
     * 黑白名单 搜索用户
     * @param dto
     * @return
     */
    @PostMapping("/search/org")
    public Result searchUser(@RequestBody LiveFilterSearchUserDTO dto) {
        AuthUser user = AuthUtil.getUser();
        IPage<LiveOrgFilterVO> searchOrg = liveOrgFilterBizService.searchOrg(user, dto);
        return Result.ok(ListBase.pageConvertToListBase(searchOrg));
    }

    /****
     * 黑名单列表
     * @return
     */
    @PostMapping("/pageList")
    public Result pageList(@RequestBody PageBase pageBase) {
        AuthUser user = AuthUtil.getUser();
        IPage<LiveOrgFilterVO> pages = liveOrgFilterBizService.pageListOrg(pageBase, user, LiveOrgFilterSourceTypeEnum.USER_BLOCK_MERCHANT);
        return Result.ok(pages);
    }

    /****
     * 添加黑名单
     * @return
     */
    @PostMapping("/add")
    public Result addOrg(@RequestBody LiveOrgFilterDTO liveOrgFilter) {
        AuthUser user = AuthUtil.getUser();
        AssertUtil.assertFalse(liveOrgFilter.getOrgId().equals(user.getOrgId()), "不能拉黑自己");
        return Result.ok(liveOrgFilterBizService.addOrg(user,liveOrgFilter));
    }

    @PostMapping("remove")
    public Result remove(@RequestBody IdDTO idDTO) {
        AuthUser user = AuthUtil.getUser();
        Long id = idDTO.getId();
        liveOrgFilterBizService.remove(user.getUserId(),null,id);
        return Result.ok("操作成功");
    }

    @PostMapping("liveRoomIdList")
    public Result getFilteredLiveRoomIdList() {
        var user = AuthUtil.getUser();
        List<Long> liveRoomIdList = liveOrgFilterBizService.getFilteredLiveRoomIdList(user);
        return Result.ok(liveRoomIdList);
    }

}
