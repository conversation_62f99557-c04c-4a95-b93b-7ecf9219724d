package com.bbh.live.controller;

import com.bbh.annotations.ExcludeProd;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * <AUTHOR>
 */
@Controller
@Slf4j
@RequiredArgsConstructor
@ExcludeProd
public class MockController {

    @GetMapping("mock")
    public String index() {
        return "index";
    }

    @GetMapping({"", "/", "status"})
    @ResponseBody
    public String success() {
        return "success";
    }

}