package com.bbh.live.controller.req;

import com.bbh.base.PageBase;
import com.bbh.live.enums.VipRightsTypeEnum;
import com.bbh.util.AssertUtil;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/9/23 15:53
 * @description
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class VipRightsUseRecordsReq extends PageBase {

    private VipRightsTypeEnum rightsType;

    private Long vipId;

    private Date startAt;

    private Date endAt;

    public void check() {
        AssertUtil.assertNotNull(rightsType, "权益类型不能为空");
        AssertUtil.assertTrue(startAt != null && endAt != null, "请指定时间范围");
    }
}
