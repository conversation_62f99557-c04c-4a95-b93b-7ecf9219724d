package com.bbh.live.controller;

import com.bbh.live.controller.req.OrgMapSearchReq;
import com.bbh.live.service.buyer.orgmap.BuyerOrgMapService;
import com.bbh.util.AssertUtil;
import com.bbh.vo.Result;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2024/10/9 14:30
 * @description
 */
@RestController
@RequestMapping("buyer/homepage")
@AllArgsConstructor
public class BuyerHomePageController {

    private final BuyerOrgMapService buyerOrgMapService;

    @PostMapping("/getNearByOrg")
    public Result getNearByOrg(@RequestBody OrgMapSearchReq searchReq) {
        AssertUtil.assertNotNull(searchReq.getLatitude() == null || searchReq.getLongitude() == null, "参数异常");
        return Result.ok(buyerOrgMapService.getNearbyOrgSortedByDistanceAsc(searchReq));
    }

    @PostMapping("/getAllOrgPosition")
    public Result getAllOrgPosition(@RequestBody OrgMapSearchReq searchReq) {
        AssertUtil.assertNotNull(searchReq.getLatitude() == null || searchReq.getLongitude() == null, "参数异常");
        return Result.ok(buyerOrgMapService.getAllOrgWithDistance(searchReq));
    }

    /**
     *  获取商家名片
     * @param searchReq
     * @return
     */
    @PostMapping("/getOrgBusinessCard")
    public Result getOrgBusinessCard(@RequestBody OrgMapSearchReq searchReq) {
        AssertUtil.assertNotNull(searchReq.getOrgId(), "参数异常");
        return Result.ok(buyerOrgMapService.getOrgBusinessCard(searchReq));
    }
}
