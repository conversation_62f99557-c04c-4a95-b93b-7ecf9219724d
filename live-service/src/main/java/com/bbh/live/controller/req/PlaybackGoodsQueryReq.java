package com.bbh.live.controller.req;

import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * <AUTHOR> dsy
 * @Date: 2024/9/3
 * @Description:
 */
@Data
@Accessors(chain = true)
public class PlaybackGoodsQueryReq {

    /**
     * 直播间id
     */
    private Long roomId;

    /**
     * 直播间id列表
     */
    private List<Long> roomIdList;

    /**
     * 关键词搜索
     */
    private String keywords;

    /**
     * 商品编码关键词搜索
     */
    private String codeKeywords;

    public void parseKeywords() {
        if(StringUtils.isNotBlank(keywords)){
            if(StringUtils.isNumeric(keywords)){
                try{
                    this.codeKeywords = Integer.valueOf(keywords).toString();
                }catch (NumberFormatException e){
                    this.codeKeywords = keywords;
                }
            }else {
                this.codeKeywords = keywords;
            }
        }
    }
}
