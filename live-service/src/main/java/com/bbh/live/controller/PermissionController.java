package com.bbh.live.controller;

import com.bbh.live.service.permission.PermissionService;
import com.bbh.secure.AuthUtil;
import com.bbh.vo.Result;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 权限
 * <AUTHOR>
 */
@RestController
@RequestMapping
@AllArgsConstructor
@Slf4j
public class PermissionController {

    private final PermissionService permissionService;

    /**
     * 获取当前用户的权限code集合
     */
    @GetMapping("/user/dept/permission/own_code/list")
    public Object getOwnList() {
        return permissionService.getOwnList();
    }

    /**
     * 当前用户是否导播或主播
     */
    @PostMapping("/permission/is_director_or_anchor")
    public Result isDirectorOrAnchor() {
        return Result.ok(permissionService.isDirectorOrAnchor());
    }

    /**
     * 检查发言权限
     */
    @PostMapping("/permission/get_speak_permission")
    public Result getSpeakPermission() {
        return Result.ok(permissionService.checkBidPermission(AuthUtil.getOrgId(), AuthUtil.getSeatId(), "发言"));
    }

}
