package com.bbh.live.controller;

import com.bbh.live.aspect.PermissionCheck;
import com.bbh.live.controller.req.VipRightsUseRecordsReq;
import com.bbh.live.controller.req.VirtualGoodsOrderQueryReq;
import com.bbh.live.dao.dto.GodViewTrialStatusDTO;
import com.bbh.live.dao.dto.IdDTO;
import com.bbh.live.dao.dto.VipIdDTO;
import com.bbh.live.dao.dto.livegoods.LiveGoodsDTO;
import com.bbh.live.dao.dto.vo.VipConfigByLevelVO;
import com.bbh.live.dao.dto.vo.VipConfigVO;
import com.bbh.live.dao.dto.vo.VirtualGoodsOrderVO;
import com.bbh.live.dao.service.GlobalOrderOtherGoodsDicService;
import com.bbh.live.dao.service.GlobalVirtualGoodsOrderService;
import com.bbh.live.dao.service.VipBuyerCardService;
import com.bbh.live.dao.service.VipBuyerPayRecordService;
import com.bbh.live.enums.GlobalVirtualGoodsOrderCancelSource;
import com.bbh.live.enums.PermissionCodeEnum;
import com.bbh.live.service.buyer.vip.BuyerVipQueryService;
import com.bbh.live.service.buyer.vip.BuyerVipService;
import com.bbh.live.service.buyer.vip.GodViewService;
import com.bbh.live.service.buyer.vip.vo.VipUpdateVO;
import com.bbh.model.VipBuyerConfig;
import com.bbh.secure.AuthUtil;
import com.bbh.util.AssertUtil;
import com.bbh.vo.Result;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR> dsy
 * @Date: 2024/8/20
 * @Description:
 */
@RestController
@RequestMapping("buyer/vip")
@AllArgsConstructor
@Slf4j
public class BuyerVipController {

    private final BuyerVipService buyerVipService;
    private final VipBuyerCardService vipBuyerCardService;
    private final GlobalOrderOtherGoodsDicService globalOrderOtherGoodsDicService;
    private final GlobalVirtualGoodsOrderService globalVirtualGoodsOrderService;
    private final GodViewService godViewService;
    private final BuyerVipQueryService buyerVipQueryService;
    private final VipBuyerPayRecordService vipBuyerPayRecordService;
    /**
     * 获取可开通的会员套餐
     * @return vip套餐列表
     */
    @PostMapping("/getVipTypes")
    @PermissionCheck(PermissionCodeEnum.LIVE_OPEN_VIP)
    public Result getVipGoods() {
        return Result.ok(globalOrderOtherGoodsDicService.getVipTypeList());
    }

    /**
     * 取消vip购买订单
     * @param queryReq
     * @return
     */
    @PostMapping("/cancelOrder")
    public Result cancelOrder(@RequestBody VirtualGoodsOrderQueryReq queryReq) {
        AssertUtil.assertNotNull(queryReq.getOrderId(), "订单不存在");
        globalVirtualGoodsOrderService.cancelOrder(queryReq.getOrderId(), GlobalVirtualGoodsOrderCancelSource.USER);
        return Result.ok();
    }

    /**
     * 会员信息更新 是否潜水
     * @return
     */
    @PostMapping("/update")
    public Result vipUpdate(@RequestBody VipUpdateVO vipUpdateVO) {
        if(vipUpdateVO.getBuyerSeatId() == null){
            vipUpdateVO.setBuyerSeatId(AuthUtil.getSeatId());
        }
        vipBuyerCardService.updateVipInfo(vipUpdateVO);
        return Result.ok();
    }

    /**
     * 删除会员席位
     * @param idDTO id
     * @return
     */
    @PostMapping("delete")
    public Result deleteVip(@RequestBody IdDTO idDTO) {
        vipBuyerCardService.delete(idDTO.getId());
        return Result.ok();
    }

    /**
     * 会员订单列表
     * @return
     */
    @PostMapping("/orderList")
    public Result vipOrderList(@RequestBody(required = false) VirtualGoodsOrderQueryReq queryReq) {
        if(queryReq == null) {
            queryReq = new VirtualGoodsOrderQueryReq();
        }
        if(queryReq.getBuyerSeatId() == null){
            queryReq.setBuyerSeatId(AuthUtil.getSeatId());
        }

        return Result.ok(vipBuyerPayRecordService.getVipBuyerPayRecordList(queryReq.getCurrentPage(), queryReq.getPerPage(), queryReq.getBuyerSeatId()));
    }

    /**
     * 会员订单详情
     * @return
     * @deprecated 没用到，等用到了要改成查vipBuyerPayRecord
     */
    @Deprecated
    @PostMapping("/orderInfo")
    public Result vipOrderInfo(@RequestBody VirtualGoodsOrderQueryReq queryReq) {
        if(queryReq.getBuyerSeatId() == null){
            queryReq.setBuyerSeatId(AuthUtil.getSeatId());
        }
        List<VirtualGoodsOrderVO> virtualGoodsOrderList = globalVirtualGoodsOrderService.getVirtualGoodsOrderList(queryReq);
        return Result.ok(virtualGoodsOrderList != null ? virtualGoodsOrderList.getFirst() : null);
    }

    /**
     * 会员等级详情
     * @return
     */
    @PostMapping("/vipConfig")
    public Result vipConfig() {
        //普通会员
        List<VipBuyerConfig> normalVipBuyerConfigs = buyerVipService.initBuyerVip(false);
        //年费会员
        List<VipBuyerConfig> annualVipBuyerConfigs = buyerVipService.initBuyerVip(true);

        Map<Integer, VipBuyerConfig> normalVipConfigByLevel = normalVipBuyerConfigs.stream().collect(Collectors.toMap(VipBuyerConfig::getVipLevel, Function.identity()));
        Map<Integer, VipBuyerConfig> annualVipConfigByLevel = annualVipBuyerConfigs.stream().collect(Collectors.toMap(VipBuyerConfig::getVipLevel, Function.identity()));

        List<VipConfigByLevelVO> result = new ArrayList<>();
        LinkedHashSet<Integer> levelSet = new LinkedHashSet<>(){{
            addAll(normalVipConfigByLevel.keySet());
            addAll(annualVipConfigByLevel.keySet());
        }};
        Optional<Integer> maxLevel = levelSet.stream().max(Comparator.comparing(Integer::intValue));
        if(maxLevel.isEmpty()){
            return Result.ok(new VipConfigVO().setVipConfig(result));
        }

        for (int i = 0; i <= maxLevel.get(); i++) {
            result.add(new VipConfigByLevelVO().setLevel(i).setVipConfig(normalVipConfigByLevel.get(i)).setAnnualVipConfig(annualVipConfigByLevel.get(i)));
        }
        return Result.ok(new VipConfigVO().setVipConfig(result));
    }

    /**
     * 获取上帝视角体验状态
     * @return Result
     */
    @PostMapping("/getGodViewTrialStatus")
    public Result getGodViewTrialStatus() {
        GodViewTrialStatusDTO godViewTrialStatusDTO = godViewService.getGodViewTrialStatus();
        return Result.ok(godViewTrialStatusDTO);
    }

    /**
     * 上帝视角 全民体验开关
     */
    @PostMapping("/getGodViewGlobalTrial")
    public Result getGodViewGlobalTrial() {
        return Result.ok(godViewService.getGodViewGlobalTrial());
    }

    /**
     * 上报商品点击次数
     *
     * @return 剩余次数
     */
    @PostMapping("/addGodViewUseCount")
    public Result addGodViewUseCount(@RequestBody LiveGoodsDTO goodsDTO) {
        godViewService.addGodViewUseCount(goodsDTO.getLiveGoodsId(), goodsDTO.getLiveRoomId());
        return Result.ok();
    }

    /**
     * VIP 使用次数统计
     * @return 使用次数统计
     */
    @PostMapping("/getWholeTimeUsed")
    public Result getWholeTimeUsed(@RequestBody(required = false) VipIdDTO vipIdDTO) {
        return Result.ok(buyerVipQueryService.getWholeTimeUsedInfo(vipIdDTO == null ? null : vipIdDTO.getVipId()));
    }

    /**
     * VIP 使用次数统计
     * @return 使用次数统计
     */
    @PostMapping("/getVipRightsRecords")
    public Result getVipRightsUseRecords(@RequestBody VipRightsUseRecordsReq recordsReq) {
        recordsReq.check();
        return Result.ok(buyerVipQueryService.getVipRightsUsedRecords(recordsReq));
    }

    /**
     * 激活会员卡
     */
    @PostMapping("/activateAndRenewVipCard")
    public Result activateAndRenewVipCard(@RequestBody IdDTO idDTO) {
        buyerVipService.activateAndRenewVipCard(idDTO.getId());
        return Result.ok();
    }

    /**
     * 获取会员信息
     */
    @PostMapping("/getUserBuyerVipInfoBySeatId")
    public Result getUserBuyerVipInfoBySeatId(@RequestBody IdDTO idDTO) {
        return Result.ok(buyerVipService.getUserBuyerVipInfoBySeatId(idDTO.getId()));
    }
}
