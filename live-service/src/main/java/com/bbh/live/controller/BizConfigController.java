package com.bbh.live.controller;

import cn.hutool.core.bean.BeanUtil;
import com.bbh.live.config.LiveBizProperties;
import com.bbh.live.dao.dto.vo.GlobalBizConfigVO;
import com.bbh.live.dao.dto.vo.GoodsAuctionConfigVO;
import com.bbh.vo.Result;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 业务配置
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/bizConfig")
@AllArgsConstructor
@Slf4j
public class BizConfigController {

    private final LiveBizProperties liveBizProperties;

    /**
     * 感兴趣-快捷选择-备注列表
     *
     * @return 备注数组
     */
    @PostMapping("getGoodsSubscribeFastRemarkList")
    public Result getGoodsSubscribeFastRemarkList() {
        return Result.ok(liveBizProperties.getSubscribeFastRemarkList());
    }

    /**
     * 获取全局业务配置
     * @return 业务配置对象
     */
    @PostMapping("getBizConfig")
    public Result getBizProperties() {
        GlobalBizConfigVO globalBizConfigVO = new GlobalBizConfigVO();
        BeanUtil.copyProperties(liveBizProperties, globalBizConfigVO);
        return Result.ok(globalBizConfigVO);
    }

    /**
     * 获取商品竞拍配置
     * @return
     */
    @PostMapping("getGoodsAuctionConfig")
    public Result getGoodsAuctionConfig() {
        GoodsAuctionConfigVO goodsAuctionConfigVO = new GoodsAuctionConfigVO();
        goodsAuctionConfigVO.setIncreasePrices(liveBizProperties.getGoodsAuctionIncreasePrices());
        goodsAuctionConfigVO.setAuctionDurations(liveBizProperties.getGoodsAuctionDuration());
        return Result.ok(goodsAuctionConfigVO);
    }

}
