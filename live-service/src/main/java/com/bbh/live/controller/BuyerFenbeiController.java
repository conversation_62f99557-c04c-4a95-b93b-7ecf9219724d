package com.bbh.live.controller;

import com.bbh.live.controller.req.FenbeiDetailReq;
import com.bbh.live.dao.dto.IdDTO;
import com.bbh.live.dao.dto.PresentedFenbeiDTO;
import com.bbh.live.service.buyer.BuyerFenbeiService;
import com.bbh.secure.AuthUtil;
import com.bbh.vo.Result;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2024/9/29 10:00
 * @description 买家分贝控制器
 */
@RestController
@RequestMapping("buyer/fenbei")
@AllArgsConstructor
public class BuyerFenbeiController {

    private final BuyerFenbeiService buyerFenbeiService;

    /**
     * 我的分贝信息
     * @return
     */
    @PostMapping("/info")
    public Result info() {
        return Result.ok(buyerFenbeiService.getBuyerFenbeiInfo(AuthUtil.getOrgId()));
    }

    /**
     * 分贝赠送
     * @return
     */
    @PostMapping("/presented")
    public Result presented(@RequestBody PresentedFenbeiDTO presentedFenbei) {
        if(presentedFenbei.getPresentedSeatId() == null){
            presentedFenbei.setPresentedSeatId(AuthUtil.getSeatId());
        }
        if(presentedFenbei.getPresentedOrgId() == null){
            presentedFenbei.setPresentedOrgId(AuthUtil.getOrgId());
        }
        presentedFenbei.check();
        buyerFenbeiService.presentedFenbei(presentedFenbei);
        return Result.ok();
    }

    /**
     * 分贝明细
     * @param fenbeiDetailReq
     * @return
     */
    @PostMapping("/getDetailList")
    public Result getDetailList(@RequestBody FenbeiDetailReq fenbeiDetailReq) {
        fenbeiDetailReq.setSeatId(AuthUtil.getSeatId());
        return Result.ok(buyerFenbeiService.getFenbeiDetail(fenbeiDetailReq));
    }

    /**
     * 购买分贝
     */
    @PostMapping("/buyFenbei")
    public Result buyFenbei(@RequestBody IdDTO idDTO) {
        buyerFenbeiService.buyFenbei(idDTO.getId());
        return Result.ok();
    }
}
