package com.bbh.live.controller;

import com.bbh.live.controller.req.LiveGoodsQueryReq;
import com.bbh.live.dao.dto.BargainGoodsDTO;
import com.bbh.live.dao.dto.livegoods.LiveGoodsSubscribeDTO;
import com.bbh.live.service.buyer.BuyerLiveGoodsOpService;
import com.bbh.live.service.buyer.BuyerLiveGoodsQueryService;
import com.bbh.log.ApiLog;
import com.bbh.secure.AuthUtil;
import com.bbh.util.AssertUtil;
import com.bbh.vo.Result;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 *
 * <AUTHOR> dsy
 * @Date: 2024/8/5
 * @Description:
 */
@RestController
@AllArgsConstructor
@RequestMapping("buyer/liveGoods")
public class BuyerLiveGoodsController {

    private final BuyerLiveGoodsOpService buyerLiveGoodsOpService;
    private final BuyerLiveGoodsQueryService buyerLiveGoodsQueryService;

    /**
     * 买手直播商品详情
     * @param bargainGoods
     * @return
     */
    @PostMapping("/getGoodsInfo")
    public Result getLiveGoods(@RequestBody BargainGoodsDTO bargainGoods) {
        AssertUtil.assertNotNull(bargainGoods.getLiveRoomId(), "直播间不存在");
        return Result.ok(buyerLiveGoodsQueryService.getLiveGoodsDetailInfo(bargainGoods.getLiveGoodsId()));
    }

    /**
     * 校验商品是否已失效
     * @param bargainGoods
     * @return
     */
    @PostMapping("/checkLiveGoodsValid")
    public Result checkLiveGoodsValid(@RequestBody BargainGoodsDTO bargainGoods) {
        buyerLiveGoodsQueryService.checkLiveGoodsValid(bargainGoods.getLiveGoodsId());
        return Result.ok();
    }

    /**
     * 买手已选商品详情
     * @param bargainGoods
     * @return
     */
    @PostMapping("/getCheckedGoodsInfo")
    public Result getCheckedGoodsInfo(@RequestBody BargainGoodsDTO bargainGoods) {
        return Result.ok(buyerLiveGoodsQueryService.getCheckedGoodsInfo(bargainGoods.getGlobalGoodsId()));
    }

    /**
     * 买手直播商品列表
     * @param liveGoodsQueryReq
     * @return
     */
    @PostMapping("/getGoodsList")
    public Result getGoodsList(@RequestBody LiveGoodsQueryReq liveGoodsQueryReq) {
        AssertUtil.assertNotNull(liveGoodsQueryReq.getLiveRoomId(), "直播间不存在");
        // 默认查询100个
        liveGoodsQueryReq.setPerPage(100);
        return Result.ok(buyerLiveGoodsQueryService.getLiveGoodsList(liveGoodsQueryReq));
    }

    /**
     * 买手直播商品数量统计
     * @param liveGoodsQueryReq
     * @return
     */
    @PostMapping("/getGoodsCount")
    public Result getGoodsCount(@RequestBody LiveGoodsQueryReq liveGoodsQueryReq) {
        AssertUtil.assertNotNull(liveGoodsQueryReq.getLiveRoomId(), "直播间不存在");
        return Result.ok(buyerLiveGoodsQueryService.getLiveGoodsCount(liveGoodsQueryReq));
    }

    /**
     * 所有直播间的再见列表 捡漏 - 流拍议价
     * @return
     */
    @Deprecated
    @PostMapping("/getAllAbortiveAuctionGoods")
    public Result getAllAbortiveAuctionGoods() {
        return Result.ok(buyerLiveGoodsQueryService.getAbortiveAuctionGoodsList());
    }

    /**
     * 所有直播间的再见列表 捡漏 - 流拍议价
     * @return
     */
    @PostMapping("/playback/v2")
    public Result playbackV2() {
        return Result.ok(buyerLiveGoodsQueryService.getAbortiveAuctionGoodsListV2());
    }

    /**
     * 获取有流拍商品的直播间数量
     */
    @PostMapping("/getAbortiveAuctionRoomCount")
    public Result getAbortiveAuctionRoomCount() {
        return Result.ok(buyerLiveGoodsQueryService.getAbortiveAuctionRoomCount());
    }

    /**
     * 点击直播清单议价
     * @param bargainGoods
     * @return
     */
    @ApiLog
    @PostMapping("/bargainGoods")
    public Result bargainGoods(@RequestBody BargainGoodsDTO bargainGoods) {
        if(bargainGoods.getBuyerSeatId() == null){
            bargainGoods.setBuyerSeatId(AuthUtil.getSeatId());
        }
        if (bargainGoods.getBuyerOrgId() == null) {
            bargainGoods.setBuyerOrgId(AuthUtil.getOrgId());
        }
        return Result.ok(buyerLiveGoodsOpService.bargainLiveGoods(bargainGoods));
    }

    /**
     * 商品求讲解
     * @param bargainGoods
     * @return
     */
    @PostMapping("/askForExplanation")
    public Result askForExplanation(@RequestBody BargainGoodsDTO bargainGoods) {
        if(bargainGoods.getBuyerSeatId() == null){
            bargainGoods.setBuyerSeatId(AuthUtil.getSeatId());
        }
        buyerLiveGoodsOpService.askForExplanation(bargainGoods);
        return Result.ok();
    }

    /**
     * 直播商品竞拍出价
     * @param bargainGoods
     * @return
     */
    @ApiLog
    @PostMapping("/auctionBid")
    public Result auctionBid(@RequestBody BargainGoodsDTO bargainGoods) {
        if(bargainGoods.getBuyerSeatId() == null){
            bargainGoods.setBuyerSeatId(AuthUtil.getSeatId());
        }
        if (bargainGoods.getBuyerOrgId() == null) {
            bargainGoods.setBuyerOrgId(AuthUtil.getOrgId());
        }
        return Result.ok(buyerLiveGoodsOpService.auctionBid(bargainGoods));
    }

    /**
     * 商品感兴趣
     * @param liveGoodsSubscribe
     * @return
     */
    @PostMapping("/subscribe")
    public Result subscribe(@RequestBody LiveGoodsSubscribeDTO liveGoodsSubscribe) {
        AssertUtil.assertNotNull(liveGoodsSubscribe.getLiveRoomId(), "直播间不存在");
        buyerLiveGoodsOpService.subscribe(liveGoodsSubscribe);
        return Result.ok();
    }

    /**
     * 取消感兴趣
     * @param liveGoodsSubscribe
     * @return
     */
    @PostMapping("/cancelSubscribe")
    public Result cancelSubscribe(@RequestBody LiveGoodsSubscribeDTO liveGoodsSubscribe) {
        AssertUtil.assertNotNull(liveGoodsSubscribe.getLiveRoomId(), "直播间不存在");
        buyerLiveGoodsOpService.cancelSubscribe(liveGoodsSubscribe);
        return Result.ok();
    }

    /**
     * 关闭预约商品提醒卡片
     * @param liveGoodsSubscribe
     * @return
     */
    @PostMapping("/closeSubscribeCard")
    public Result closeSubscribeCard(@RequestBody LiveGoodsSubscribeDTO liveGoodsSubscribe) {
        AssertUtil.assertNotNull(liveGoodsSubscribe.getLiveRoomId(), "直播间不存在");
        buyerLiveGoodsOpService.closeSubscribeCard(liveGoodsSubscribe);
        return Result.ok();
    }

    /**
     * 传送商品要了
     * @param bargainGoods
     * @return
     */
    @ApiLog
    @PostMapping("/takeTransferGoods")
    public Result takeTheTransferGoods(@RequestBody BargainGoodsDTO bargainGoods) {
        if(bargainGoods.getBuyerSeatId() == null){
            bargainGoods.setBuyerSeatId(AuthUtil.getSeatId());
        }
        if (bargainGoods.getBuyerOrgId() == null) {
            bargainGoods.setBuyerOrgId(AuthUtil.getOrgId());
        }
        return Result.ok(buyerLiveGoodsOpService.takeTheTransferGoods(bargainGoods));
    }

}
