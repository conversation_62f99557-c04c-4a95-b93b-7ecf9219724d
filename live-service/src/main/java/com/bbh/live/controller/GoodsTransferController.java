package com.bbh.live.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bbh.live.constant.ProjectConstant;
import com.bbh.live.dao.dto.GoodsTransferDTO;
import com.bbh.live.dao.dto.vo.GlobalOrgSeatVO;
import com.bbh.live.dao.service.GlobalOrgSeatService;
import com.bbh.live.service.livegoods.LiveGoodsTransferBizService;
import com.bbh.live.service.msg.MsgService;
import com.bbh.model.GlobalOrgSeat;
import com.bbh.util.AssertUtil;
import com.bbh.vo.Result;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2024/7/25 下午1:59
 */
@RestController
@RequestMapping("/goodsTransfer")
@RequiredArgsConstructor
public class GoodsTransferController {

    private final LiveGoodsTransferBizService liveGoodsTransferBizService;
    private final GlobalOrgSeatService globalOrgSeatService;
    private final MsgService msgService;


    /**
     * 添加商品传送接口。
     * 通过POST请求向此URL发送一个GoodsTransferDTO对象，以添加一个新的商品转出记录。
     * 此方法首先验证商品转出DTO的合法性，然后创建并保存一个LiveGoodsTransfer对象来记录传送。
     *
     * @param goodsTransferDTO 包含商品传送信息的数据传输对象。
     * @return 返回一个表示操作结果的Result对象，操作成功时，ok()方法返回true。
     */
    @PostMapping("/add")
    public Result add(@RequestBody GoodsTransferDTO goodsTransferDTO) {
        return Result.ok(liveGoodsTransferBizService.goodsTransfer(goodsTransferDTO));
    }

    /**
     * 查询竞拍码接口是否有效。
     */
    @PostMapping("/queryAuctionCode")
    public Result queryAuctionCode(@RequestBody GoodsTransferDTO goodsTransferDTO) {
        goodsTransferDTO.auctionCodeModelCheck();
        LambdaQueryWrapper<GlobalOrgSeat> globalOrgSeatLqw = new LambdaQueryWrapper<>();
        globalOrgSeatLqw.eq(GlobalOrgSeat::getAuctionCode, goodsTransferDTO.getAuctionCode())
                .eq(GlobalOrgSeat::getStatus, ProjectConstant.GlobalOrgSeatStatus.ENABLE)
                .select(GlobalOrgSeat::getId, GlobalOrgSeat::getOrgId, GlobalOrgSeat::getUserId, GlobalOrgSeat::getShowName);

        GlobalOrgSeat globalOrgSeat = globalOrgSeatService.getOne(globalOrgSeatLqw, false);
        if(globalOrgSeat == null){
            return Result.ok();
        }

        GlobalOrgSeatVO globalOrgSeatVO = new GlobalOrgSeatVO();
        globalOrgSeatVO.setAuctionCode(goodsTransferDTO.getAuctionCode());
        globalOrgSeatVO.setAuctionName(globalOrgSeat.getShowName());
        globalOrgSeatVO.setTargetOrgId(globalOrgSeat.getOrgId());
        globalOrgSeatVO.setTargetUserId(globalOrgSeat.getUserId());
        globalOrgSeatVO.setTargetSeatId(globalOrgSeat.getId());
        return Result.ok(globalOrgSeatVO);
    }

    /**
     * 导播主动取消商品传送 -接口。
     * @param goodsTransferDTO
     * @return
     */
    @PostMapping("/cancel")
    public Result cancel(@RequestBody GoodsTransferDTO goodsTransferDTO) {
        AssertUtil.assertNotNull(goodsTransferDTO.getLiveGoodsId(), "商品id不能为空");
        AssertUtil.assertNotNull(goodsTransferDTO.getLiveRoomId(), "直播间id不能为空");
        liveGoodsTransferBizService.cancel(goodsTransferDTO);
        return Result.ok();
    }
}
