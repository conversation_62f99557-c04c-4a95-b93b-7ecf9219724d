package com.bbh.live.controller;


import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.map.MapUtil;
import cn.hutool.extra.servlet.JakartaServletUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.http.useragent.UserAgent;
import cn.hutool.http.useragent.UserAgentUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.bbh.annotations.ExcludeProd;
import com.bbh.config.LockPlus;
import com.bbh.enums.CommitLockPlusKeyEnum;
import com.bbh.enums.GlobalBizTypeEnum;
import com.bbh.enums.LiveRoomInteractiveMessageHandleStatusEnum;
import com.bbh.feign.IDataCenterFeign;
import com.bbh.live.config.LiveBizProperties;
import com.bbh.live.core.msg.ImService;
import com.bbh.live.core.msg.MsgDTO;
import com.bbh.live.dao.dto.RoomIdDTO;
import com.bbh.live.dao.dto.livegoods.LiveGoodsDTO;
import com.bbh.live.dao.mapper.CeShoppingCartMapper;
import com.bbh.live.dao.mapper.GlobalOrgSettlementBankMapper;
import com.bbh.live.dao.mapper.LiveGoodsMapper;
import com.bbh.live.dao.mapper.LiveRoomMapper;
import com.bbh.live.dao.service.*;
import com.bbh.live.handler.queue.DelayQueueManager;
import com.bbh.live.service.buyer.sign.BuyerSignService;
import com.bbh.live.service.buyer.sign.vo.SignInfoVo;
import com.bbh.live.service.buyer.vip.BuyerVipService;
import com.bbh.live.service.director.DirectorLiveGoodsOpService;
import com.bbh.live.service.livegoods.LiveGoodsDetailService;
import com.bbh.live.service.msg.ChatroomService;
import com.bbh.live.service.msg.MsgService;
import com.bbh.live.service.msg.dto.base.BaseSeat;
import com.bbh.live.service.order.CartService;
import com.bbh.live.service.room.LiveRoomCacheService;
import com.bbh.live.service.room.context.LiveRoomContextHolder;
import com.bbh.live.service.room.impl.LiveStreamServiceImpl;
import com.bbh.live.thread.ThreadPoolManager;
import com.bbh.log.ApiLog;
import com.bbh.log.ApiLogTypeEnum;
import com.bbh.model.*;
import com.bbh.secure.AuthUtil;
import com.bbh.service.RedisService;
import com.bbh.service.alipay.service.AlipayService;
import com.bbh.service.mq.enums.MqTopicEnum;
import com.bbh.service.mq.service.CoreMqService;
import com.bbh.util.ParamsUtil;
import com.bbh.util.SignUtil;
import com.bbh.vo.AuthUser;
import com.bbh.vo.Result;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.ExpiredJwtException;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.io.Decoders;
import io.jsonwebtoken.security.Keys;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.crypto.SecretKey;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.*;

/**
 * 测试类，在生产环境不会生效
 *
 * <AUTHOR>
 */
@ExcludeProd
@RestController
@RequestMapping("/test")
@RequiredArgsConstructor
@Slf4j
public class TestController {

    private final RedisService redisService;

    private final RedissonClient redissonClient;

    private final IGlobalOrganizationService globalOrganizationService;

    private final GlobalOrgSeatService globalOrgSeatService;

    private final GlobalVirtualGoodsOrderService globalVirtualGoodsOrderService;

    private final DelayQueueManager delayQueueManager;

    private final ImService imService;

    private final LiveBizProperties liveBizProperties;

    private final BuyerSignService buyerSignService;

    private final MsgService msgService;

    private final ChatroomService chatroomService;

    private final BuyerVipService buyerVipService;

    private final CartService cartService;

    private final CoreMqService coreMqService;

    private final GlobalOrderService globalOrderService;

    private final LiveRoomInteractiveMessageService liveRoomInteractiveMessageService;

    private final HttpServletRequest httpServletRequest;

    @Resource
    private HttpServletRequest request;

    private final LiveGoodsMapper liveGoodsMapper;
    @Autowired
    private LiveRoomMapper liveRoomMapper;
    @Autowired
    private LiveBizProperties live;

    @LockPlus(value = "testLockPlus", lockPlusKeyType = CommitLockPlusKeyEnum.COMMIT_LOCK_PLUS_USER)
    @GetMapping("/a1")
    public Result test() {
        redisService.set("hanjd", 1, 60);
        redissonClient.getLock("");
        return Result.ok(redisService.get("hanjd", Integer.class));
    }


    @GetMapping("/a2")
    public Result updateOrgName() {
        // 这里故意写一个慢查询
        List<GlobalOrgSeat> seatList = globalOrgSeatService.list();
        log.info("seatList.size = {}", seatList.size());
        return Result.ok();
    }


    @GetMapping("/a3")
    public Result msgts() {
        imService.createChatRoom("userBuyerVipChatRoomId");
        MsgDTO<HashMap<String, LocalDateTime>> hashMapMsgDTO = new MsgDTO<>();
        hashMapMsgDTO.setMsgType("fqy");
        HashMap<String, LocalDateTime> stringLocalDateTimeHashMap = new HashMap<>(16);
        stringLocalDateTimeHashMap.put("nowDate", LocalDateTime.now());
        hashMapMsgDTO.setData(stringLocalDateTimeHashMap);
        Set<String> viPchartRomms = Set.of("userBuyerVipChatRoomId");
        hashMapMsgDTO.setChatRoomIds(viPchartRomms);
        imService.sendChatroomTxtMessage(hashMapMsgDTO);
        return Result.ok();
    }

    @GetMapping("test-something")
    public Object testSomething() {
        LiveRoomService liveRoomService = SpringUtil.getBean(LiveRoomService.class);
        LiveRoomMapper roomMapper = SpringUtil.getBean(LiveRoomMapper.class);
        LiveRoomMsgService liveRoomMsgService = SpringUtil.getBean(LiveRoomMsgService.class);
        BuyerVipService buyerVipService = SpringUtil.getBean(BuyerVipService.class);

        long userId = 26L;
        long seatId = 3719L;
        String orderNo = "LIVE_G_O_20250102114142914928";
        Date now = new Date();
        Date applyTimeoutAt = DateUtil.offset(now, DateField.SECOND, 10);
        Date systemPushAt = DateUtil.offset(applyTimeoutAt, DateField.SECOND, -1);

        log.info(JakartaServletUtil.getClientIP(httpServletRequest));
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();

        ThreadPoolManager.getGlobalBizExecutor().execute(() -> {
            try {
                // 在新线程中设置请求上下文
                RequestContextHolder.setRequestAttributes(attributes);

                // 请求ip
                String clientIP = JakartaServletUtil.getClientIP(httpServletRequest);
                log.info(clientIP);
                // ua
                String uaString = httpServletRequest.getHeader("User-Agent");
                String appVersion = httpServletRequest.getHeader("app_version");
                String appInfo = httpServletRequest.getHeader("app_info");
                UserAgent userAgent = UserAgentUtil.parse(uaString);
                log.info(userAgent.toString());
                log.info(JSONUtil.toJsonStr(userAgent));
                log.info(userAgent.getPlatform().getName());
                log.info(appInfo);
                log.info(appVersion);
            } finally {
                // 清理请求上下文
                RequestContextHolder.resetRequestAttributes();
            }
        });

        return Dict.of("ok", "ok");
    }

    @ApiLog
    @GetMapping("enum-serial")
    public Result enumSerial() {
        List<LiveRoom> list = SpringUtil.getBean(LiveRoomService.class).list();
        return Result.ok(list);
    }

    @GetMapping("getBizConfig")
    public Result getBizConfig() {
        GlobalBizConfigService service = SpringUtil.getBean(GlobalBizConfigService.class);
        Dict dict = Dict.create()
                .set("global_default_seat_name_list", service.getJSONArray("global_default_seat_name_list"))
                .set("erp_message_permission", service.getJSONArray("erp_message_permission"))
                .set("ce_auth_firm_num", service.getInteger("ce_auth_firm_num"))
                .set("erp_right_org_price", service.get("erp_right_org_price", Double.class));
        return Result.ok(dict);
    }

    @ApiLog
    @GetMapping("getBizProperties")
    public Result getBizProperties() {
        Dict dict = Dict.create()
//                .set("liveBizProperties", liveBizProperties)
//                .set("liveBizProperties2", SpringUtil.getBean(LiveBizProperties.class))
                .set("getHomeCarouselImgs", liveBizProperties.getHomeCarouselImgs());

        if (!dict.isEmpty()) {
            throw new IllegalArgumentException("测试");
        }
        return Result.ok(dict);
    }

    @ApiLog(ApiLogTypeEnum.API_CALL)
    @GetMapping("getRateTemplate")
    public Object getRateTemplate() {
        log.info(request.toString());
        return SpringUtil.getBean(LiveRateTemplateService.class).getRoomRateTemplateList(List.of(5L, 6L));
    }

    @GetMapping("addLiveGoodsListFromErpStorehouse")
    public Result addLiveGoodsListFromErpStorehouse(Long roomId) {
        LiveRoomContextHolder.initRoomContext(roomId);
        SpringUtil.getBean(DirectorLiveGoodsOpService.class)
                .addLiveGoodsList(roomId, List.of(62045L, 62050L, 62043L));
        return Result.ok();
    }

    @GetMapping("/generateJwtToken")
    public Result generateJwtToken(Long seatId) {
        GlobalOrgSeat globalOrgSeat = SpringUtil.getBean(GlobalOrgSeatService.class).getById(seatId);
        AuthUser authUser = new AuthUser();
        authUser.setUserId(globalOrgSeat.getUserId());
        authUser.setOrgId(globalOrgSeat.getOrgId());
        authUser.setSeatId(globalOrgSeat.getId());
        authUser.setIfMaster(globalOrgSeat.getIfMaster() == 1);
        String res = AuthUtil.generateJwtToken(authUser);
        return Result.ok(res);
    }

    @GetMapping("getNextWaitPutAwayLiveGoods")
    public Object getNextWaitPutAwayLiveGoods() {
        return SpringUtil.getBean(LiveGoodsDetailService.class).getNextWaitPutAwayLiveGoods(new LiveGoodsDTO().setLiveRoomId(3L));
    }

    @GetMapping("testLiveRoomGoodsIdsCache")
    public Object testLiveRoomGoodsIdsCache() {
        LiveRoomCacheService service = SpringUtil.getBean(LiveRoomCacheService.class);
        service.pushGoodsIds(1L, 3L, 4L, 5L);
        Set<Long> goodsIds = service.getGoodsIds(1L);
        log.info("{}", goodsIds);
        service.deleteGoodsId(1L, 3L);

        service.incrementUnsoldCount(1L, 30L);
        service.incrementGoodsCount(1L, -30L);

        return Result.ok(service.getGoodsIds(1L));
    }

    @GetMapping("testMq")
    public Object testMq() throws Throwable {
        coreMqService.send(MqTopicEnum.LIVE_PAY_CLOSE_DELAY, JSONUtil.toJsonStr(Dict.create().set("orderNo", "123123")), 1000L);
        return Result.ok();
    }

    @GetMapping("testGoodsLog")
    public Result testGoodsLog() {
        LiveGoodsService goodsService = SpringUtil.getBean(LiveGoodsService.class);
        goodsService.update(Wrappers
                .lambdaUpdate(LiveGoods.class)
                .eq(LiveGoods::getId, 20)
                .set(LiveGoods::getPutawayAt, new Date())
        );

        return Result.ok();
    }

    @PostMapping("signTest")
    public Result signTest(@RequestBody SignDTO signDTO) {
        long seatId = signDTO.getBuyerSeatId() == null ? AuthUtil.getSeatId() : signDTO.getBuyerSeatId();

        Date date = signDTO.getDate();
        int lastDayOfMonth = DateUtil.getLastDayOfMonth(date);
        int nowi = DateUtil.dayOfMonth(date);
        /*for (int i= 1; i <= lastDayOfMonth -nowi + 1 ; i++) {
            buyerSignService.sign(date, seatId);
            date = DateUtil.offsetDay(date, 1);
        }*/
        //Date now = DateUtil.parse("2024-09-01");
        buyerSignService.sign(signDTO.getDate(), seatId);

        /*for (int i = 0; i < 31; i++) {
            buyerSignService.sign(now, seatId);
            now = DateUtil.offsetDay(now, 1);
        }*/

        SignInfoVo signInfoVo = buyerSignService.signInfo(signDTO.getDate(), seatId);
        log.info("-----签到信息：" + signInfoVo);
        return Result.ok(signInfoVo);
    }

    @Data
    static class SignDTO{
        private Date date;
        private Long buyerSeatId;
    }

    @GetMapping("msgTest")
    public Object msgTest() {
        AuthUser user = AuthUtil.getUser();
        log.info(JSONUtil.toJsonStr(user));
        // 用户进入直播间
        msgService.userEnterRoom(8L, 1910L, 3719L);
//        msgService.roomNoticeChanged(8L, "这是新的公告，告五人。");
//        msgService.liveStart(8L);
//        msgService.liveEnd(8L);
//        msgService.goodsAuctionSuccess(8L, 20L, 692L, new BigDecimal(2100));
//        msgService.userBidSuccess(8L, 12L, 3719L, 1L);
        // 上架
//        msgService.goodsPutAway(217L, 1572L);
        // 用户订阅商品
//        msgService.goodsSubscribe(8L, 2L);
        // 开始竞拍
//        msgService.goodsAuctionStart(105L, 782L, new Date());
        // 传送
//        msgService.goodsTransfer(8L, 3719L, 1L);
        // 传送关闭
//        msgService.goodsTransferClose(8L, 3719L, 20L);

        // 撤回商品
//        msgService.goodsRevoke(8L, 20L);
        // 流拍
//        msgService.goodsAbortedAuction(8L, 20L);
        // 普通文本消息
//        msgService.text(8L, "这是普通文本消息", 3719L);
        // 表情包
//        msgService.meme(8L, 1L, 3719L);

//        msgService.letUserOutNow(8L, 3719L, 3719L);

        BaseSeat baseSeat = msgService.retrieveSeatInfo(3719L);

        return baseSeat;
    }

    @GetMapping("generateRongUserToken")
    public Object generateRongUserToken() throws Exception {
        return imService.register("wujiawei-test", "wujaiwei-test", "https://bang-file.oss-cn-shanghai.aliyuncs.com/htb/headimg/10.png");
    }

    @PostMapping("createChatroom/{roomId}")
    public Object createChatroom(@PathVariable String roomId) {
        return chatroomService.createChatroom(roomId);
    }

    @GetMapping("handleMessage")
    public Object handleMessage() {
        return liveRoomInteractiveMessageService.lambdaUpdate()
                .eq(LiveRoomInteractiveMessage::getLiveGoodsId, 954L)
                .eq(LiveRoomInteractiveMessage::getHandleStatus, LiveRoomInteractiveMessageHandleStatusEnum.UNHANDLED)
                .set(LiveRoomInteractiveMessage::getHandleStatus, LiveRoomInteractiveMessageHandleStatusEnum.EXPIRED)
                .set(LiveRoomInteractiveMessage::getUpdatedAt, new Date())
                .update();
    }

    @GetMapping("testLiveMq")
    public Object testLiveMq() {
        LiveStreamServiceImpl liveStreamService = SpringUtil.getBean(LiveStreamServiceImpl.class);

        LiveRoom liveRoom = new LiveRoom();
        liveRoom.setId(84L);
        liveRoom.setEndAt(DateUtil.offsetSecond(DateUtil.date(), 40));
        liveRoomMapper.updateById(liveRoom);
        liveStreamService.produceLiveDelayQueue(liveRoom, false);

        return Result.ok();
    }

    @Data
    public static class AddCartDTO{
        private Long liveRoomId;
        private Long liveGoodsId;
        private BigDecimal price;
        private Long buyerSeatId;
    }

    @PostMapping("clearGoods")
    public Result clearGoods(@RequestBody RoomIdDTO roomIdDTO) {
        LiveGoodsMapper liveGoodsMapper = SpringUtil.getBean(LiveGoodsMapper.class);
        liveGoodsMapper.delete(Wrappers.lambdaQuery(LiveGoods.class).eq(LiveGoods::getLiveRoomId, roomIdDTO.getRoomId()));
        return Result.ok();
    }

    @PostMapping("resetGoodsStatus")
    public Result resetGoodsStatus(@RequestBody RoomIdDTO roomIdDTO) {
        LiveGoodsMapper liveGoodsMapper = SpringUtil.getBean(LiveGoodsMapper.class);
        liveGoodsMapper.update(Wrappers.lambdaUpdate(LiveGoods.class)
                .eq(LiveGoods::getLiveRoomId, roomIdDTO.getRoomId())
                .set(LiveGoods::getPutawayAt, null)
                .set(LiveGoods::getAuctionStartAt, null)
                .set(LiveGoods::getEndAt, null)
                .set(LiveGoods::getGoodsStatus, 10)
        );
        return Result.ok();
    }

    @PostMapping("resetShoppingCart")
    public Result resetShoppingCart() {
        var mapper = SpringUtil.getBean(CeShoppingCartMapper.class);
        mapper.delete(Wrappers.lambdaQuery(CeShoppingCart.class)
                .eq(CeShoppingCart::getBizType, GlobalBizTypeEnum.LIVE)
                .eq(CeShoppingCart::getBuyerSeatId, AuthUtil.getSeatId())
        );
        return Result.ok();
    }

    @GetMapping("feignTest")
    public Result feignTest() {
        var result = SpringUtil.getBean(IDataCenterFeign.class).brandDetail("123");
        return Result.ok(result);
    }

    // 开通买手权限
    @PostMapping("openBuyerAuctionPermission")
    public Result openBuyerAuctionPermission(String seatId) {
        return Result.ok();
    }

    // 开通导播权限
    @PostMapping("openDirectorPermission")
    public Result openDirectorPermission(Long seatId, Long liveRoomId) {
        LiveRoomDirectorService liveRoomDirectorService = SpringUtil.getBean(LiveRoomDirectorService.class);
        // 如果已经开通了，就不管了
        boolean exists = liveRoomDirectorService.exists(Wrappers.lambdaQuery(LiveRoomDirector.class)
                .eq(LiveRoomDirector::getDirectorSeatId, seatId)
                .eq(LiveRoomDirector::getLiveRoomId, liveRoomId)
        );
        if (!exists) {
            LiveRoomDirector liveRoomDirector = new LiveRoomDirector();
            liveRoomDirector.setDirectorSeatId(seatId);
            liveRoomDirector.setLiveRoomId(liveRoomId);
            liveRoomDirectorService.save(liveRoomDirector);
        }
        return Result.ok();
    }

    // 开通结算账户
    @PostMapping("openSettlementBank")
    public Result openSettlementBank(Long liveGoodsId) {
        GlobalOrgSettlementBankMapper orgSettlementBankMapper = SpringUtil.getBean(GlobalOrgSettlementBankMapper.class);
        // 找到商户id
        LiveGoods liveGoods = liveGoodsMapper.selectById(liveGoodsId);
        // 找到商户开通记录
        GlobalOrgSettlementBank globalOrgSettlementBank = orgSettlementBankMapper.selectOne(Wrappers.lambdaQuery(GlobalOrgSettlementBank.class)
                .eq(GlobalOrgSettlementBank::getOrgId, liveGoods.getOrgId())
        );
        if (globalOrgSettlementBank == null) {
            globalOrgSettlementBank = new GlobalOrgSettlementBank();
            globalOrgSettlementBank.setOrgId(liveGoods.getOrgId());
            globalOrgSettlementBank.setPlatMerchantNo("**********");
            globalOrgSettlementBank.setBankSuccessId(1L);
            globalOrgSettlementBank.setAccountName("账户提示优化");
            globalOrgSettlementBank.setCardNo("123");
            orgSettlementBankMapper.insert(globalOrgSettlementBank);
        }
        return Result.ok();
    }

    // 解析中台支付sign
    @PostMapping("decodePayCenterSign")
    public Result decodePayCenterSign(@RequestBody Map<String, String> body) {
        String key = "112233";
        var obj = SignUtil.decodeSign(MapUtil.getStr(body, "sign"), key);
        return Result.ok(obj);
    }

    @PostMapping("virtualMsg")
    public Result virtualMsg() {
        String content = ParamsUtil.convertObjectToString(Map.of("virtualOrderId", 1234L));
        coreMqService.send(MqTopicEnum.VIRTUAL_ORDER_PAY_CLOSE_DELAY, content, 10000L);
        return Result.ok();
    }


    @PostMapping("testToken")
    public Result testToken() throws Exception{
        String s = "ac10a98f995c380e4dff8833c95b47htb";
        String base64Security = Base64.getEncoder().encodeToString(s.getBytes(StandardCharsets.UTF_8));
        String token = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.CZic5MdDSoJJ7teUPQHIOUnXqw7qBwH3yN5tdjqEZR8";
        Claims claims;
        try {
            byte[] bytes = Decoders.BASE64.decode(base64Security);
            SecretKey secretKey = Keys.hmacShaKeyFor(bytes);
            claims = Jwts.parser().verifyWith(secretKey).build().parseSignedClaims(token).getPayload();
        } catch (ExpiredJwtException e) {
            log.info("AuthCheck 解析token失败 ==> ExpiredJwtException, token:{}", token);
            return null;
        } catch (Exception e) {
            log.info("AuthCheck 解析token失败 ==> RuntimeException, token:{}", token);
            log.error("AuthUtil 解析token失败 token:{}, base64Security:{}, msg:{}", token, base64Security, e.getMessage(), e);
            return null;
        }
        System.out.println(claims);
        return Result.ok();
    }

    @GetMapping("getAlipayUserId")
    public String getAlipayUserId(String code) {
        log.info("getAlipayUserId code:{}", code);
        return SpringUtil.getBean(AlipayService.class).getUserIdByCode(code);
    }
}
