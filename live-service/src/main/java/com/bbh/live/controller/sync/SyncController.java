package com.bbh.live.controller.sync;

import com.bbh.live.service.admin.AdminService;
import com.bbh.live.service.director.DirectorLiveGoodsOpService;
import com.bbh.vo.Result;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 *
 * @ClassName:  SyncController
 * @Description: TODO( 同步代码 )
 * @Author: yangfan
 * @date:   2025/5/28 18:36
 *
 * @Copyright:
 *
 */
@RestController
@RequestMapping("/sync")
@AllArgsConstructor
public class SyncController {

    private final DirectorLiveGoodsOpService directorLiveGoodsOpService;

    /**
     * <p>Title: syncEndLiveRoomToSce</p>
     * <p>Description: 直播结束后商品同步新云展 上架时间往前十分钟随机</p>
     * <p>Another: yangfan</p>
     * <p>Date: 2025/5/28 18:37</p>
     *
     * @return com.bbh.vo.Result
     * @see com.bbh.live.controller.sync#syncEndLiveRoomToSce
     */
    @PostMapping("/syncEndLiveRoomToSce")
    public Result syncEndLiveRoomToSce() {
        directorLiveGoodsOpService.syncEndLiveRoomToSce();
        return Result.ok();
    }

}
