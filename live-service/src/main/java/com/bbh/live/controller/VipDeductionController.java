package com.bbh.live.controller;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bbh.base.ListBase;
import com.bbh.live.dao.dto.GlobalOrderItemVO;
import com.bbh.live.dao.dto.OrderNoDTO;
import com.bbh.live.dao.service.GlobalOrderGoodsSnapshotService;
import com.bbh.live.dao.service.GlobalOrderItemService;
import com.bbh.live.dao.service.GlobalOrderService;
import com.bbh.live.service.vipdeduction.GlobalVipDeductionLogService;
import com.bbh.live.service.vipdeduction.GlobalVipDeductionService;
import com.bbh.model.GlobalOrder;
import com.bbh.model.GlobalOrderItem;
import com.bbh.model.GlobalVipDeduction;
import com.bbh.model.GlobalVipDeductionLog;
import com.bbh.secure.AuthUtil;
import com.bbh.vo.Result;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/3/27 09:47
 */
@RestController
@RequestMapping("vip/deduction")
@RequiredArgsConstructor
public class VipDeductionController {
    private final GlobalVipDeductionLogService globalVipDeductionLogService;
    private final GlobalVipDeductionService globalVipDeductionService;
    private final GlobalOrderItemService globalOrderItemService;
    private final GlobalOrderService globalOrderService;
    private final GlobalOrderGoodsSnapshotService globalOrderGoodsSnapshotService;

    @PostMapping("/index")
    public Result index() {
        Long orgId = AuthUtil.getOrgId();
        List<GlobalVipDeduction> vipDeductionList =
                globalVipDeductionService.list(Wrappers.lambdaQuery(GlobalVipDeduction.class)
                        .eq(GlobalVipDeduction::getOrgId, orgId).gt(GlobalVipDeduction::getEndAt, DateUtil.now()));

        BigDecimal allBigDecimal =
                vipDeductionList.stream().map(GlobalVipDeduction::getVipDeduction).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal allUsedBigDecimal = vipDeductionList.stream().map(GlobalVipDeduction::getUsedVipDeduction)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal remain = NumberUtil.sub(allBigDecimal, allUsedBigDecimal);
        return Result.ok(Map.of("sum", allBigDecimal, "used", allUsedBigDecimal, "remain", remain));
    }

    @PostMapping("/logList")
    public Result list(@RequestBody OrderNoDTO dto) {
        Long orgId = AuthUtil.getOrgId();
        Page<GlobalVipDeductionLog> page = new Page<>(dto.getCurrentPage(), dto.getPerPage());
        Page<GlobalVipDeductionLog> pageInfo =
                globalVipDeductionLogService.page(page, Wrappers.lambdaQuery(GlobalVipDeductionLog.class)
                        .eq(GlobalVipDeductionLog::getOrgId, orgId)
                        .le(ObjUtil.isNotNull(dto.getEndAt()), GlobalVipDeductionLog::getCreatedAt, dto.getEndAt())
                        .ge(ObjUtil.isNotNull(dto.getStartAt()), GlobalVipDeductionLog::getCreatedAt, dto.getStartAt())
                        .orderByDesc(GlobalVipDeductionLog::getCreatedAt));
        ListBase<GlobalVipDeductionLog> listBase = ListBase.pageConvertToListBase(pageInfo);
        return Result.ok(listBase);
    }

    @PostMapping("/itemList")
    public Result itemList(@RequestBody OrderNoDTO dto) {
        Page<GlobalOrderItem> page = new Page<>(dto.getCurrentPage(), dto.getPerPage());
        GlobalOrder globalOrder = globalOrderService
                .getOne(Wrappers.lambdaQuery(GlobalOrder.class).eq(GlobalOrder::getOrderNo, dto.getOrderNo()), false);
        if (ObjUtil.isNull(globalOrder)) {
            return Result.fail("订单不存在");
        }
        Page<GlobalOrderItemVO> pageInfo = globalOrderItemService.pageList(page, globalOrder.getId());
        ListBase<GlobalOrderItemVO> listBase = ListBase.pageConvertToListBase(pageInfo);
        return Result.ok(listBase);
    }

}
