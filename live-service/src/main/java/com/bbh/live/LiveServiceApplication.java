package com.bbh.live;


import com.bbh.live.config.LiveBeanNameGenerator;
import org.dromara.dynamictp.core.spring.EnableDynamicTp;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.retry.annotation.EnableRetry;
import org.springframework.scheduling.annotation.EnableAsync;

/**
 * <AUTHOR>
 */
@SpringBootApplication(scanBasePackages = {"com.bbh.live"}, nameGenerator = LiveBeanNameGenerator.class)
@EnableDiscoveryClient
@EnableAsync
@EnableDynamicTp
@EnableRetry
@EnableAspectJAutoProxy(exposeProxy = true)
public class LiveServiceApplication {
    public static void main(String[] args) {
        SpringApplication.run(LiveServiceApplication.class, args);
    }

}
