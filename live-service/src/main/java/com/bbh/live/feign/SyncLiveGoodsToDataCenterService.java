package com.bbh.live.feign;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONUtil;
import com.bbh.feign.IDataCenterFeign;
import com.bbh.feign.dto.SyncOrderGoodsDTO;
import com.bbh.live.dao.dto.livegoods.LiveGoodsDTO;
import com.bbh.live.dao.service.ErpGoodsService;
import com.bbh.live.dao.service.GlobalOrgSeatService;
import com.bbh.live.dao.service.IGlobalOrganizationService;
import com.bbh.live.feign.dto.GlobalOrgDetailDTO;
import com.bbh.live.service.livegoods.LiveGoodsDetailService;
import com.bbh.model.ErpGoods;
import com.bbh.model.GlobalOrgSeat;
import com.bbh.util.AssertUtil;
import com.bbh.util.LogExUtil;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.Date;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2024/9/21 10:07
 * @description 同步直播成交商品到数据中心
 */
@Service
@RequiredArgsConstructor
public class SyncLiveGoodsToDataCenterService {

    private final IGlobalOrganizationService globalOrganizationService;
    private final ErpGoodsService erpGoodsService;
    private final GlobalOrgSeatService globalOrgSeatService;
    private final LiveGoodsDetailService liveGoodsDetailService;

    @Resource
    private IDataCenterFeign dataCenterFeign;

    /**
     * 同步成交数据到中台
     *
     * @param liveGoods 直播商品
     */
    public void syncGoodsToDataCenter(LiveGoodsDTO liveGoods) {
        AssertUtil.assertNotNull(liveGoods, "直播商品不能为空");
        ErpGoods erpGoods = erpGoodsService.getById(liveGoods.getGlobalGoodsId());
        liveGoods = liveGoodsDetailService.getLiveGoodsDetailInfo(liveGoods.getLiveGoodsId());
        SyncOrderGoodsDTO syncOrderGoodsDTO = new SyncOrderGoodsDTO()
                .setTargetId(erpGoods.getId())
                .setPrdName(erpGoods.getName())
                .setQuality(erpGoods.getQuality())
                // 2 - 帮帮虎
                .setResource(3)
                // 商品描述
                .setPrdDescribe(erpGoods.getDescription())
                // 成色备注
                .setQualityRemark("")
                // 同行价
                .setTonghangPrice(erpGoods.getPeerPrice() == null ? "0" : erpGoods.getPeerPrice().toString())
                // 销售价
                .setXiaoshouPrice(erpGoods.getSalePrice() == null ? "0" : erpGoods.getSalePrice().toString())
                // 成本价
                .setChenbenPrice(erpGoods.getCostPrice() == null ? "0" : erpGoods.getCostPrice().toString())
                // 1-直播
                .setSoldType("1")
                // 商家编码
                .setSkuCodeEx(erpGoods.getIndependentCode())
                // 入库时间
                .setWarehousingTime(DateUtil.format(erpGoods.getCreatedAt(), DatePattern.UTC_MS_WITH_XXX_OFFSET_PATTERN));

        // 查询商品店铺信息
        GlobalOrgDetailDTO orgDetailDTO = globalOrganizationService.getOrgDetailById(erpGoods.getOrgId());
        if (orgDetailDTO != null) {
            syncOrderGoodsDTO.setProviderName(orgDetailDTO.getSeatName())
                    .setProviderPhone(orgDetailDTO.getPhone())
                    .setProviderShopInfoName(orgDetailDTO.getOrgName());
        }

        // 成交信息
        Optional<GlobalOrgSeat> globalOrgSeat = globalOrgSeatService.lambdaQuery().eq(GlobalOrgSeat::getId, liveGoods.getBelongSeatId())
                .select(GlobalOrgSeat::getId, GlobalOrgSeat::getShowName)
                .oneOpt();
        if(globalOrgSeat.isPresent()){
             syncOrderGoodsDTO
                    .setBelongUid(globalOrgSeat.get().getId().toString())
                    .setBelongUserName(globalOrgSeat.get().getShowName())
                    .setBelongDatetime(DateUtil.format(liveGoods.getEndAt() == null ? new Date() : liveGoods.getEndAt(), DatePattern.UTC_MS_WITH_XXX_OFFSET_PATTERN))
                    .setBelongPrice(liveGoods.getSellPrice() == null ? "0" : liveGoods.getSellPrice().toString());
        }

        // 封面图和详细图
        if (CollUtil.isNotEmpty(erpGoods.getImgUrlList())) {
            syncOrderGoodsDTO.setCoverImg(erpGoods.getImgUrlList().getFirst())
                    .setIntroduceImg(JSONUtil.toJsonStr(erpGoods.getImgUrlList()));
        }

        // 调用中台接口
        try {
            dataCenterFeign.syncGoods(Collections.singletonList(syncOrderGoodsDTO));
        } catch (Exception e) {
            LogExUtil.errorLog("直播同步成交数据到中台异常", e);
        }
    }
}
