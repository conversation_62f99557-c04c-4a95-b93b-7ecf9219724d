package com.bbh.live.handler.queue;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.bbh.constant.CommonConstant;
import com.bbh.live.handler.queue.consumer.ConsumerFactory;
import com.bbh.live.thread.ThreadPoolManager;
import com.bbh.live.util.ThreadUtils;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBlockingQueue;
import org.redisson.api.RDelayedQueue;
import org.redisson.api.RedissonClient;
import org.slf4j.MDC;
import org.springframework.stereotype.Service;

import java.util.concurrent.ConcurrentHashMap;

/**
 * 延迟队列管理器
 *
 * <AUTHOR>
 */
@Service
@Slf4j
@AllArgsConstructor
@SuppressWarnings("all")
public class DelayQueueManager {

    private static final ConcurrentHashMap<String, RDelayedQueue> DELAY_QUEUE_CACHE_MAP = new ConcurrentHashMap<>(8);
    private final RedissonClient redissonClient;
    private final ConsumerFactory consumerFactory;

    /**
     * 装载
     */
    public void load() {
        startAutoConsume();
    }

    public <T> void addToQueue(DelayJob<T> delayJob) {
        addToQueue(delayJob, true);
    }

    public <T> void addToQueue(DelayJob<T> delayJob, boolean override) {
        log.info("add to queue[{}][{}], will be executed after {} {}", delayJob.getTopic(), delayJob.getJobId(), delayJob.getDelay(), delayJob.getTimeUnit());

        // 如果没有traceId，自动生成
        if (StrUtil.isEmpty(delayJob.getTraceId())) {
            String traceId = MDC.get(CommonConstant.TRACE_ID);
            if (StrUtil.isEmpty(traceId)) {
                traceId = IdUtil.fastSimpleUUID();
            }
            delayJob.setTraceId(traceId);
        }

        try {
            if (override) {
                remove(delayJob);
            }
            // 延迟发送
            getDelayedQueue(delayJob.getTopic()).offer(delayJob, delayJob.getDelay(), delayJob.getTimeUnit());
        } catch (Exception e) {
            log.error("Error adding to queue: " + delayJob.getTopic(), e);
        }
    }

    public <T> void remove(DelayJob<T> delayJob) {
        try {
            RDelayedQueue<DelayJob> delayedQueue = getDelayedQueue(delayJob.getTopic());
            delayedQueue.stream()
                    .filter(x -> x.getJobId().equals(delayJob.getJobId()))
                    .findFirst()
                    .ifPresent(x -> delayedQueue.remove(x));
        } catch (Exception e) {
            log.error("Error remove delayJob, topic:{}, data:{} ", delayJob.getTopic(), delayJob.getData(), e);
        }
    }

    public void startAutoConsume() {
        var autoConsumeExecutor = ThreadPoolManager.getAutoConsumeExecutor(consumerFactory.getTopics().size());
        for (String topic : consumerFactory.getTopics()) {
            autoConsumeExecutor.execute(() -> processComsume(topic));
        }
    }

    private void processComsume(String queueName) {
        log.info("Start delay queue auto consumer, topic name: " + queueName);
        RBlockingQueue<Object> blockingQueue = redissonClient.getBlockingQueue(queueName);
        if (blockingQueue == null) {
            log.info("Queue not found: " + queueName);
            return;
        }
        // 解决程序重启后blockingQueue无法获取队列的问题
        redissonClient.getDelayedQueue(blockingQueue);

        String originName = Thread.currentThread().getName();
        ThreadUtils.specifiedThreadName("AutoConsumerThread-" + queueName);
        try {
            while (true) {
                try {
                    DelayJob job = (DelayJob) blockingQueue.take();
                    consumerFactory.consume(job);
                } catch (InterruptedException interruptedException){
                    log.error("autoConsumerThread is interrupted : {}", Thread.currentThread().getName());
                    break;
                } catch (Exception e) {
                    log.error("Error processing job from queue: " + queueName, e);
                }
            }
        } finally {
            ThreadUtils.specifiedThreadName(originName);
        }
    }

    private RDelayedQueue<DelayJob> getDelayedQueue(String topic) {
        return DELAY_QUEUE_CACHE_MAP.computeIfAbsent(topic, key -> redissonClient.getDelayedQueue(redissonClient.getBlockingQueue(topic)));
    }
}
