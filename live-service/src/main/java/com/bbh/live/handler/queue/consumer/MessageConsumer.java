package com.bbh.live.handler.queue.consumer;

import com.bbh.live.handler.queue.DelayJob;

import java.util.concurrent.Executor;

/**
 * 消费
 * @param <T>
 *
 * <AUTHOR>
 */
public interface MessageConsumer<T> {

    /**
     * 定义topic
     */
    String topic();

    /**
     * 原则上只要topic名称匹配上就执行，但是也允许其他特殊的匹配条件
     */
    boolean match(String topic);

    /**
     * 消费
     */
    void consume(DelayJob<T> delayJob);

    /**
     * 异步消费线程池
     * @return
     */
    default Executor getExecutor(){return null;}
}
