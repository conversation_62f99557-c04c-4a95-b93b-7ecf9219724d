package com.bbh.live.handler.queue.consumer;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.thread.ThreadUtil;
import com.bbh.live.handler.queue.DelayJob;
import com.bbh.live.handler.queue.DelayQueueManager;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;

import java.util.Date;
import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;

/**
 * <AUTHOR>
 */
@Slf4j
public abstract class AbstractMessageConsumer<T> implements MessageConsumer<T> {

    @Resource
    @Lazy
    protected DelayQueueManager delayQueueManager;

    /**
     * 延时任务与实际结束时间误差值，小于这个时间直接等待。否则从新放入队列
     */
    private static final Long THREAD_WAIT_TIME_THRESHOLD = 500L;

    /**
     * 默认的规则就是Topic名称完全匹配
     * @param topic 主题名称
     * @return 匹配/不匹配
     */
    @Override
    public boolean match(String topic) {
        return topic().equals(topic);
    }


    /**
     * 判断是否已到过期时间，如果误差在500ms内，则直接等待，否则重新放入队列
     * @param expectExpireTime
     * @param supplier
     * @return
     */
    protected boolean isTimeUp(Date expectExpireTime, Supplier<DelayJob> supplier){
        Date now = new Date();
        if(expectExpireTime.before(now)){
            return true;
        }

        long timeRemaining = DateUtil.betweenMs(now, expectExpireTime);
        if(timeRemaining < THREAD_WAIT_TIME_THRESHOLD){
            ThreadUtil.sleep(timeRemaining);
            return true;
        }else {
            DelayJob delayJob = supplier.get();
            delayJob.setDelay(timeRemaining);
            delayJob.setTimeUnit(TimeUnit.MILLISECONDS);
            delayQueueManager.addToQueue(delayJob);
            return false;
        }
    }
}
