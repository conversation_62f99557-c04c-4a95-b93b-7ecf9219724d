package com.bbh.live.handler.queue.consumer;

import cn.hutool.core.util.StrUtil;
import com.bbh.constant.CommonConstant;
import com.bbh.live.handler.queue.DelayJob;
import com.bbh.live.thread.ThreadPoolManager;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.concurrent.Executor;

/**
 * 消费工厂
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@AllArgsConstructor
@SuppressWarnings("all")
public class ConsumerFactory {

    private List<MessageConsumer> messageConsumers;

    /**
     * 匹配合适的消费者
     * @param delayJob
     * @param <T>
     */
    public <T> void consume(DelayJob<T> delayJob) {
        for (MessageConsumer messageConsumer : messageConsumers) {
            if (messageConsumer.match(delayJob.getTopic())) {
                //异步消费
                Executor executor = messageConsumer.getExecutor() != null ? messageConsumer.getExecutor() : ThreadPoolManager.getGlobalBizExecutor();
                // 传递traceId
                String traceId = delayJob.getTraceId();
                executor.execute(() -> {
                    try {
                        // 设置MDC的traceId
                        if (StrUtil.isNotEmpty(traceId)) {
                            MDC.put(CommonConstant.TRACE_ID, traceId);
                        }

                        // 执行消费逻辑
                        messageConsumer.consume(delayJob);
                    } finally {
                        // 清理MDC
                        MDC.remove(CommonConstant.TRACE_ID);
                    }
                });
            }
        }
    }

    public int size() {
        return messageConsumers.size();
    }

    public List<String> getTopics() {
        return messageConsumers.stream().map(MessageConsumer::topic).toList();
    }

}
