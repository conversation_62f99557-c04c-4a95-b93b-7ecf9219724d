package com.bbh.live.handler.queue;

import cn.hutool.extra.spring.SpringUtil;
import org.jetbrains.annotations.NotNull;
import org.springframework.boot.context.event.ApplicationStartedEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
public class DelayQueueStarter implements ApplicationListener<ApplicationStartedEvent> {
    @Override
    public void onApplicationEvent(@NotNull ApplicationStartedEvent event) {
        DelayQueueManager producer = SpringUtil.getBean(DelayQueueManager.class);
        producer.load();
    }
}
