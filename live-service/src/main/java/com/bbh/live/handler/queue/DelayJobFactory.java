package com.bbh.live.handler.queue;

import com.bbh.live.constant.DelayQueueTopics;

import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR> dsy
 * @Date: 2024/8/1
 * @Description:
 */
public class DelayJobFactory {

    public static DelayJob<Long> createAuctionDelayJob(Long liveRoomId, Long liveGoodsId, Long delay) {
        return new DelayJob<Long>()
                .setTopic(DelayQueueTopics.AUCTION_TOPIC)
                .setJobId(String.valueOf(liveRoomId))
                .setDelay(delay)
                .setTimeUnit(TimeUnit.MILLISECONDS)
                .setData(liveGoodsId);
    }

    public static DelayJob<Long> createVirtualGoodsOrderDelayJob(Long orderId, Long delay) {
        return new DelayJob<Long>()
                .setTopic(DelayQueueTopics.VIRTUAL_ORDER_TOPIC)
                .setJobId(String.valueOf(orderId))
                .setDelay(delay)
                .setTimeUnit(TimeUnit.MILLISECONDS)
                .setData(null);
    }

    public static <T> DelayJob<T> createDelayJob(String topic, String jobId, Long delay, TimeUnit timeUnit, T data) {
        return new DelayJob<T>()
                .setTopic(topic)
                .setJobId(jobId)
                .setDelay(delay)
                .setTimeUnit(timeUnit)
                .setData(data);
    }
}
