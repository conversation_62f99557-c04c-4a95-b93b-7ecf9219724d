package com.bbh.live.handler.queue;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.concurrent.TimeUnit;

/**
 * 延迟队列消息
 * @param <T>
 *
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class DelayJob<T> {

    /**
     * 主题，用于匹配消费者
     */
    private String topic;

    /**
     * 任务ID，主题内保持唯一
     */
    private String jobId;

    /**
     * 延迟的时间
     */
    private Long delay;

    private TimeUnit timeUnit;

    /**
     * 任意数据格式
     */
    private T data;

    private String traceId;

}
