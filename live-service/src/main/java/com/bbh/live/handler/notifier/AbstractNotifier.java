package com.bbh.live.handler.notifier;

import java.util.Date;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
public abstract class AbstractNotifier implements Notifier {
    @Override
    public boolean enabled() {
        return true;
    }

    @Override
    public void notify(String receiver, String content, Date notifyTime) {
        long delay = buildDelayMills(notifyTime);
        scheduledExecutorService.schedule(() -> {
            notify(receiver, content);
        }, delay, TimeUnit.MILLISECONDS);
    }

    @Override
    public void notify(String receiver, String content) {
        throw new UnsupportedOperationException("notify method is not implemented");
    }
}
