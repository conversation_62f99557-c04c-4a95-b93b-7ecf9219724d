spring:
  cloud:
    nacos:
      discovery:
        server-addr: 192.168.30.224:8848
        username: nacos
        password: nacos
      config:
        enabled: true
        server-addr: 192.168.30.224:8848
        username: nacos
        password: nacos
  config:
    import:
      - nacos:${spring.application.name}.${file-extension:yaml}
      - nacos:${spring.application.name}-${spring.profiles.active}.${file-extension:yaml}
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    dynamic:
      druid:
        proxy-filters: sqlLogInterceptor
      primary: master
      datasource:
        master:
          url: ***********************************************************************************************************************************************************************
          username: bbh
          password: bbh1234!@#$2*
  data:
    redis:
      host: r-uf6384ruwso5m5efbmpd.redis.rds.aliyuncs.com
      password: bbh:Boloogo2017
      port: 6379
      database: 8
rong-yun:
  app-key: 82hegw5u86gex
  app-secret: Otnudo2fji56
bbh:
  live-service:
    live-service-url: http://htb-api-test.bangbanghu.com.cn/htb-live-api
    service-center-url: http://htb-api-test.bangbanghu.com.cn/auth-service
    time-out: 30000
    bbh-merchant-no: 1234578910
    salt: 112233
pay-center:
  serviceUrl: http://htb-api-test.bangbanghu.com.cn/pay-service/bbh
  bbhMerchantNo: 9876543210
  salt: 112233
#抵扣金 年费  6188
htb:
  annual_vip_deduction: 6188
mq:
  switch: true
  host: rabbitmq-serverless-cn-pe33jsv220q.cn-shanghai.amqp-15.net.mq.amqp.aliyuncs.com
  port: 5672
  username: MjpyYWJiaXRtcS1zZXJ2ZXJsZXNzLWNuLXBlMzNqc3YyMjBxOkxUQUk1dE5DREVBR2REVktLajN2NGY3VQ==
  password: OTg3NDQ5QjY0RDQ5NkE0RDE3NUVGQUI2NjIzMzNGREE2OEVGREE0RToxNzA0MTc2NzUwNjY0
  virtual-host: pay_center
weixin:
  miniapp:
    env-version: develop