---
--- Generated by <PERSON><PERSON><PERSON>(https://github.com/<PERSON><PERSON>ua)
--- Created by Administrator.
--- DateTime: 2024/8/6 16:18
--- 设置竞拍商品单个属性值
local key = KEYS[1];
local hashKey = KEYS[2];

local field = ARGV[1];
local newValue = ARGV[2];
local expectedValue = ARGV[3];

local goods_str = redis.call('HGET', key, hashKey);
if goods_str then
    local goods_obj = cjson.decode(goods_str);
    if expectedValue ~= nil and goods_obj[field] ~= expectedValue then
        return 0;
    end;
    goods_obj[field] = newValue;
    redis.call('HSET', key, hashKey, cjson.encode(goods_obj));
    return 1;
else
    return nil;
end;