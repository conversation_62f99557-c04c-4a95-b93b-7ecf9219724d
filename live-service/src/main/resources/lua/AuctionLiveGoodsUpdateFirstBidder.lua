--- 一口价 修改第一个出价者
local key = KEYS[1];
local hashKey = KEYS[2];

local curBuyerSeat = ARGV[1];

local goods_str = redis.call('HGET', key, hashKey);
if goods_str then
    local goods_obj = cjson.decode(goods_str);
    if tonumber(goods_obj.ifSettle) == 1 then
        return 3;
    end;
    local preBuyerSeat = goods_obj.buyerSeatId
    if preBuyerSeat ~= nil and preBuyerSeat ~= curBuyerSeat then
        return 1;
    else
        goods_obj.buyerSeatId = curBuyerSeat;
        redis.call('HSET', key, hashKey, cjson.encode(goods_obj));
        return 4;
    end;
else
    return 0;
end;
