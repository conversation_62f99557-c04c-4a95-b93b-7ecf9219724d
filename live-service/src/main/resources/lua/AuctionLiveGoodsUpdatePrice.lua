---商品竞拍 比较并修改商品竞拍价格
---  首先比较出价是否大于当前价格
---      是：修改当前价格，当前出价人
---        并 判断当前竞拍时间是否小于阈值，小于则增加竞拍时间
---  返回 0：修改失败 1：只修改了价格  2：价格和时间都修改
local key = KEYS[1];
local hashKey = KEYS[2];

local bidPrice = tonumber(ARGV[1]);
local buyerSeatId = ARGV[2];
local auctionTimeThreshold = ARGV[3];
local secondsToAdd = ARGV[4];
local currentTime = ARGV[5];

local goods_str = redis.call('HGET', key, hashKey);
if goods_str then
    local goods_obj = cjson.decode(goods_str);
    local curPrice = goods_obj.currentPrice;
    if tonumber(goods_obj.ifSettle) == 1 then
        return 3;
    end;
    if tonumber(goods_obj.ifRevoke) == 1 then
        return 5;
    end;
    if curPrice <= bidPrice then

        if goods_obj.buyerSeatId ~= nil and curPrice == bidPrice then
            return 0;
        end
        goods_obj.currentPrice = bidPrice;
        goods_obj.buyerSeatId = buyerSeatId;

        local res = 1;
        if (goods_obj.auctionEndTime - tonumber(currentTime)) < tonumber(auctionTimeThreshold) then
            goods_obj.auctionEndTime = goods_obj.auctionEndTime + tonumber(secondsToAdd)
            res = 2;
        end;
        redis.call('HSET', key, hashKey, cjson.encode(goods_obj));
        return res;
    else
        return 0;
    end;
else
    return -1;
end;
