server:
  port: 9001
mybatis-plus:
  configuration:
    shrink-whitespaces-in-sql: true
    map-underscore-to-camel-case: true
    call-setters-on-nulls: true
  global-config:
    banner: false
    db-config:
      logic-delete-field: deletedAt
      logic-delete-value: 'now()'
      logic-not-delete-value: 'null'
  mapper-locations: classpath:mapper/*.xml
  type-aliases-package: com.bbh.live.*.model
  type-handlers-package: com.bbh.handler.json
spring:
  application:
    name: htb-live-api
  cloud:
    loadbalancer:
      retry:
        enabled: true
    openfeign:
      circuitbreaker:
        enabled: true
  datasource:
    druid:
      filters: stat,wall,slf4j
      initial-size: 15
      keep-alive: true
      max-active: 100
      max-pool-prepared-statement-per-connection-size: 20
      max-wait: 60000
      min-evictable-idle-time-millis: 300000
      min-idle: 15
      pool-prepared-statements: true
      test-on-borrow: false
      test-on-return: false
      test-while-idle: true
      time-between-eviction-runs-millis: 60000
      use-global-data-source-stat: true
      validation-query: SELECT 1
      validation-query-timeout: 1000
      keep-alive-between-time-millis: 120000    # 2分钟进行一次keepAlive
      phy-timeout-millis: 30000                 # 物理连接超时时间
      remove-abandoned: true                    # 开启废弃连接回收
      remove-abandoned-timeout: 1800            # 30分钟认为是废弃连接
      log-abandoned: true
      fail-fast: true                          # 快速失败
      break-after-acquire-failure: false       # 获取连接失败后是否立即抛出异常
      connection-error-retry-attempts: 3       # 获取连接出错时重试次数
      timeBetweenConnectErrorMillis: 300000   # 连接出错后重试时间间隔
    type: com.alibaba.druid.pool.DruidDataSource
  data:
    redis:
      client-type: lettuce
      lettuce:
        pool:
          max-active: 2000
          max-wait: 10000
          max-idle: 350
          min-idle: 100
      ssl:
        enabled: false
      timeout: 10000
  jackson:
    property-naming-strategy: SNAKE_CASE
    date-format: yyyy-MM-dd HH:mm:ss
    serialization:
      write-enums-using-to-string: true
    time-zone: Asia/Shanghai
  profiles:
    active: dev
    include:
      - dynamic-tp
      - white-list
  main:
    allow-circular-references: true
    allow-bean-definition-overriding: true
management:
  endpoints:
    web:
      exposure:
        include: '*'
alipay:
  app-id: 2021004138640498
  private-key: MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQCn5zuqW2jmWhIVybSMbZP8bHUsaXqkQNsBshCDbCEQzh4OErdg1uFKH+7p5bmp4qrhvGwZmkCsP7erVcm2aP1QoFNBtpHRSQL2IXlCEicGpkjUpnJ54yBGZy4eJSJ3FTdveMKFFOOcwHC6Ti2a5+YCqgkpYbjJ8YtZRYiDJFUBVCOg1cFaqF+Ceo61a7XHpQIDPfb2gV779D1wsu9e54YLCh2eDJLr1lIIx+kdeWMFmF5ccrK4SAlzndrWZzpga9gdmo2i9biqJCR+Qlzi4cL7lj36/5AYvahZaEWR+2j5kCEQDtg0eS7sytznhdEYSCxnFeEoc9dxfxEvYab77FC3AgMBAAECggEAZcwGGrUtFmwJhvgx5OoSzPoc2H5jAu7nWVHSPTvzmDK/ehzS3X2qPby6p0jGlj99Bzur9qHVEBnZi6GJmHRzEM0DbVX5dn/jscJ9uBc1Xgy3H2aDBW81BDpOdaxiBvzwDT+UAAe4MDNkpS/86mwqwfbBcztyQ82UhN1pdMk0WIbi7Epas8ulp0jURJ7g9ivP1jcTMX5AV7nDGFKwraX3v+HQIngk1742VWAiuzpeaostUWzaO28SEpZ/ZfWh7SgiDyHegM0tlN5X4oOr7uZqyYwZI4TcBZKQYVBy62wfYb0UpLpIyPViCnO0zeo+VLIAsfUTfEGBtrQvkQYdPMWeUQKBgQDuggQWgO9kAf1X6dXTEVGgFoOFFUpoY1bvM/hkyo7bFrtnXi7NAascHEXRLI7YbmZZfYdjIqAswPH4YsdInS145xp5msARzc0xNaZ6oRxsuInUg3GvvBmk3Kqo6ViClx06DZqAuMxHAtspJC210rdfSeOcqWCc+qjlSMOScS32fQKBgQC0N52g6iZLl+Y48BVQm4S8EqUfckJ1VwaQQ1CM0cSt9SkmZ5qeCMP4Ue2s6qjW62fvA0TD+qvO7OKizmkkbJL4tkMrgWbLnEJ1DMVIXARJGxjeNr07+fIGHeI+lMXT7uMR4arTWGFjq0Zc/kbMqblUpV+xdksnB/6a4/B4O5tmQwKBgQDVJykUG4LWc9jAWuCX02CJm9VKeK6ZAD7PZDjXixeEwZoWVVuqJ5WOd84FlLbMwk2CKOd4N5fCp2UYwN6tDJt488D7cMu6nZyGIBZ6fcXzfDDh1d8iWQ7qAbQbZP+PJhIesc2kwIL/65OF/ylC1+C9rkFW3yDS6klLMG5oT/Ht2QKBgCvplVYdV+F5jNZ44ai3jFtLa8LC06DF6MqELH9zqan9n95Exm8VkbLTO8ezkhp0V+nscPgT/4M3Q24KP6JpdlJ2Dt3t9P9N/v+IchXUnhK2MJQM4IVZvPhylatMEvw2LEwyIa7HzUs887M7HkNGY+tVNIEIYv2KvCY/vlt9orsDAoGAV3Oqg6S2fUPf6/abmHZbxeeajo7lU+WvxnSrbufEC5yzyUWCqtDQGUgebIHk76gxTOiVhEoDgtWvJ3o0l9tP0fwtp7CMLSQ2O0QKMDmLk4V1GjetfSFO+jda8PXk3R9aRXHS/hF9t03l7Yem8J4Y2Z0OuDlEpXVfM46mgRpqgAA=
  public-key: MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAigPA88jhJcf/ChaO0itYArdRnjXoLWU9wmvInZHgHi/G5HmcbPylz5uDqHOn6rls0S0feHLTB21sq4WgHJAcfidTbIjQhblwxIudMBKYy79dEB2kDVcH4i/VNWxnHkL6v6Er/tZyMVQ/2lh6N8vWHbfS/hUrC38CVV39kyhzJULzloxY6CeKQNjIFqMaozH4h7mm70IX55Ocqi7yJSrQm1Y0daMOw/gEDxseT7IvDkjkBk0paFko/jCGLOJRJN8sJK14SIHlx0PGStBRfV1l7caYMnQYn661buuVGLYBZMIMclQwFFamZ03BAhJgQ5ANx6Bqs97fdFNoaEyslV8VxwIDAQAB
token:
  check-permission-change: true
weixin:
  miniapp:
    app-id: wxfdefcdfdabf59109
    app-secret: be571ac97b6332db636b233883901256
