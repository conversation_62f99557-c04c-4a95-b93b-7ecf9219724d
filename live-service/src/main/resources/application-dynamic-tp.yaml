spring:
  task:
    execution:
      thread-name-prefix: bbh-live-task-
  dynamic:
    tp:
      enabled: true
      enabledBanner: false
      enabledCollect: true
      collectorTypes: micrometer,logging
      platforms:
        - platform: lark
          platformId: 1
          urlKey: 0d63b711-c370-465c-8751-9225f899ce40
      executors: # 动态线程池配置，都有默认值，采用默认值的可以不配置该项，减少配置量
        - threadPoolName: globalBizExecutor         # 线程池名称，必填
          thread-pool-alias-name: '全局业务线程池'
          corePoolSize: 100                      # 核心线程数，默认1
          maximumPoolSize: 100                   # 最大线程数，默认cpu核数
          queueCapacity: 1024                  # 队列容量，默认1024
          queueType: VariableLinkedBlockingQueue         # 任务队列，查看源码QueueTypeEnum枚举类，默认VariableLinkedBlockingQueue
          rejectedHandlerType: CallerRunsPolicy          # 拒绝策略，查看RejectedTypeEnum枚举类，默认AbortPolicy
          keepAliveTime: 60                              # 空闲线程等待超时时间，默认60
          threadNamePrefix: bbh-global-biz               # 线程名前缀，默认dtp
          allowCoreThreadTimeOut: false                  # 是否允许核心线程池超时，默认false
          waitForTasksToCompleteOnShutdown: true         # 参考spring线程池设计，优雅关闭线程池，默认true
          awaitTerminationSeconds: 5                     # 优雅关闭线程池时，阻塞等待线程池中任务执行时间，默认3，单位（s）
          preStartAllCoreThreads: false                  # 是否预热所有核心线程，默认false
          runTimeout: 0                                # 任务执行超时阈值，单位（ms），默认0（不统计）
          queueTimeout: 0                              # 任务在队列等待超时阈值，单位（ms），默认0（不统计）
          taskWrapperNames: [ "ttl", "mdc", "user" ]               # 任务包装器名称，继承TaskWrapper接口
          notifyEnabled: true                            # 是否开启报警，默认true
          notifyItems: # 报警项，不配置自动会按默认值（查看源码NotifyItem类）配置（变更通知、容量报警、活性报警、拒绝报警、任务超时报警）
            - type: change
              enabled: true
              interval: 60
            - type: capacity               # 队列容量使用率，报警项类型，查看源码 NotifyTypeEnum枚举类
              enabled: true
              threshold: 70
              interval: 60
            - type: liveness               # 线程池活性
              enabled: true
              threshold: 80
              interval: 60
            - type: reject                 # 触发任务拒绝告警
              enabled: true
              threshold: 10
            - type: run_timeout            # 任务执行超时告警
              enabled: true
              threshold: 10
            - type: queue_timeout          # 任务排队超时告警
              enabled: true
              threshold: 10
        - threadPoolName: autoConsumeExecutor         # 线程池名称，必填
          thread-pool-alias-name: '公共消息消费线程池'
          corePoolSize: 10                      # 核心线程数，默认1
          maximumPoolSize: 10                   # 最大线程数，默认cpu核数
          queueCapacity: 1024                  # 队列容量，默认1024
          queueType: VariableLinkedBlockingQueue         # 任务队列，查看源码QueueTypeEnum枚举类，默认VariableLinkedBlockingQueue
          rejectedHandlerType: CallerRunsPolicy          # 拒绝策略，查看RejectedTypeEnum枚举类，默认AbortPolicy
          keepAliveTime: 60                              # 空闲线程等待超时时间，默认60
          threadNamePrefix: bbh-auto-consume             # 线程名前缀，默认dtp
          allowCoreThreadTimeOut: false                  # 是否允许核心线程池超时，默认false
          waitForTasksToCompleteOnShutdown: true         # 参考spring线程池设计，优雅关闭线程池，默认true
          awaitTerminationSeconds: 5                     # 优雅关闭线程池时，阻塞等待线程池中任务执行时间，默认3，单位（s）
          preStartAllCoreThreads: false                  # 是否预热所有核心线程，默认false
          runTimeout: 0                                # 任务执行超时阈值，单位（ms），默认0（不统计）
          queueTimeout: 0                              # 任务在队列等待超时阈值，单位（ms），默认0（不统计）
          taskWrapperNames: [ "ttl", "mdc", "user"]               # 任务包装器名称，继承TaskWrapper接口
          notifyEnabled: true                            # 是否开启报警，默认true
          notifyItems: # 报警项，不配置自动会按默认值（查看源码NotifyItem类）配置（变更通知、容量报警、活性报警、拒绝报警、任务超时报警）
            - type: change
              enabled: true
            - type: capacity               # 队列容量使用率，报警项类型，查看源码 NotifyTypeEnum枚举类
              enabled: true
              threshold: 70
              interval: 60
            - type: liveness               # 线程池活性
              enabled: true
              threshold: 80
              interval: 60
            - type: reject                 # 触发任务拒绝告警
              enabled: true
              threshold: 10
            - type: run_timeout            # 任务执行超时告警
              enabled: true
              threshold: 10
            - type: queue_timeout          # 任务排队超时告警
              enabled: true
              threshold: 10
        - threadPoolName: auctionMessageConsumerExecutor         # 线程池名称，必填
          thread-pool-alias-name: '商品竞拍消息消费线程池'
          corePoolSize: 30                      # 核心线程数，默认1
          maximumPoolSize: 30                   # 最大线程数，默认cpu核数
          queueCapacity: 100                  # 队列容量，默认1024
          queueType: VariableLinkedBlockingQueue         # 任务队列，查看源码QueueTypeEnum枚举类，默认VariableLinkedBlockingQueue
          rejectedHandlerType: CallerRunsPolicy          # 拒绝策略，查看RejectedTypeEnum枚举类，默认AbortPolicy
          keepAliveTime: 60                              # 空闲线程等待超时时间，默认60
          threadNamePrefix: bbh-auction-message-consumer # 线程名前缀，默认dtp
          allowCoreThreadTimeOut: false                  # 是否允许核心线程池超时，默认false
          waitForTasksToCompleteOnShutdown: true         # 参考spring线程池设计，优雅关闭线程池，默认true
          awaitTerminationSeconds: 5                     # 优雅关闭线程池时，阻塞等待线程池中任务执行时间，默认3，单位（s）
          preStartAllCoreThreads: false                  # 是否预热所有核心线程，默认false
          runTimeout: 0                                # 任务执行超时阈值，单位（ms），默认0（不统计）
          queueTimeout: 0                              # 任务在队列等待超时阈值，单位（ms），默认0（不统计）
          taskWrapperNames: [ "ttl", "mdc", "user"]               # 任务包装器名称，继承TaskWrapper接口
          notifyEnabled: true                            # 是否开启报警，默认true
          notifyItems: # 报警项，不配置自动会按默认值（查看源码NotifyItem类）配置（变更通知、容量报警、活性报警、拒绝报警、任务超时报警）
            - type: change
              enabled: true
            - type: capacity               # 队列容量使用率，报警项类型，查看源码 NotifyTypeEnum枚举类
              enabled: true
              threshold: 70
              interval: 60
            - type: liveness               # 线程池活性
              enabled: true
              threshold: 80
              interval: 60
            - type: reject                 # 触发任务拒绝告警
              enabled: true
              threshold: 10
            - type: run_timeout            # 任务执行超时告警
              enabled: true
              threshold: 10
            - type: queue_timeout          # 任务排队超时告警
              enabled: true
              threshold: 10
        - threadPoolName: rongyunMsgSendExecutor         # 线程池名称，必填
          thread-pool-alias-name: '融云消息发送线程池'
          corePoolSize: 100                      # 核心线程数，默认1
          maximumPoolSize: 100                   # 最大线程数，默认cpu核数
          queueCapacity: 1024                  # 队列容量，默认1024
          queueType: VariableLinkedBlockingQueue         # 任务队列，查看源码QueueTypeEnum枚举类，默认VariableLinkedBlockingQueue
          rejectedHandlerType: CallerRunsPolicy          # 拒绝策略，查看RejectedTypeEnum枚举类，默认AbortPolicy
          keepAliveTime: 60                              # 空闲线程等待超时时间，默认60
          threadNamePrefix: bbh-rongyun-msg-send # 线程名前缀，默认dtp
          allowCoreThreadTimeOut: false                  # 是否允许核心线程池超时，默认false
          waitForTasksToCompleteOnShutdown: true         # 参考spring线程池设计，优雅关闭线程池，默认true
          awaitTerminationSeconds: 5                     # 优雅关闭线程池时，阻塞等待线程池中任务执行时间，默认3，单位（s）
          preStartAllCoreThreads: false                  # 是否预热所有核心线程，默认false
          runTimeout: 0                                # 任务执行超时阈值，单位（ms），默认0（不统计）
          queueTimeout: 0                              # 任务在队列等待超时阈值，单位（ms），默认0（不统计）
          taskWrapperNames: [ "ttl", "mdc", "liveRoom", "user"] # 任务包装器名称，继承TaskWrapper接口
          notifyEnabled: true                            # 是否开启报警，默认true
          notifyItems: # 报警项，不配置自动会按默认值（查看源码NotifyItem类）配置（变更通知、容量报警、活性报警、拒绝报警、任务超时报警）
            - type: change
              enabled: true
            - type: capacity               # 队列容量使用率，报警项类型，查看源码 NotifyTypeEnum枚举类
              enabled: true
              threshold: 70
              interval: 60
            - type: liveness               # 线程池活性
              enabled: true
              threshold: 80
              interval: 60
            - type: reject                 # 触发任务拒绝告警
              enabled: true
              threshold: 10
            - type: run_timeout            # 任务执行超时告警
              enabled: true
              threshold: 200
            - type: queue_timeout          # 任务排队超时告警
              enabled: true
              threshold: 200