
-- 订单表
alter table global_order modify column order_type tinyint null comment '订单类型: 10-ai查价订单, 20-云展商品订单, 30-直播商品订单, 40-放心拍商品订单';
alter table global_order
    modify need_pay_amount decimal(11, 2) default 0.00 not null comment '应付金额（商品金额 + 通道费 + 买家服务费 - 分贝抵扣）';
alter table global_order
    modify real_payed_amount decimal(11, 2) default 0.00 not null comment '已付金额（商品金额 + 通道费 + 买家服务费 - 分贝抵扣）';


-- 订单item表
alter table global_order_item add column biz_goods_id bigint null comment '业务商品id：live_goods_id、pt_goods_id、ce_goods_id';
alter table global_order_item add column biz_type int null comment '业务类型: 10 ai查价  20 云展  30 直播 40 放心拍';
alter table global_order_item add column biz_id bigint null comment '业务ID：直播间ID、图文拍活动ID';
alter table global_order_item modify need_pay_amount decimal(11, 2) default 0.00 not null comment '商品支付价格（商品金额 + 通道费 + 买家服务费 - 分贝抵扣）';
alter table global_order_item
    modify real_payed_amount decimal(11, 2) default 0.00 not null comment '商品实际支付价格（商品金额 + 通道费 + 买家服务费 - 分贝抵扣）';
alter table global_order_item
    modify biz_id bigint null comment '业务表ID：直播表ID、放心拍活动表ID';


-- 商户订单表
alter table global_order_sub modify column order_no varchar(64) not null comment '订单号';
alter table global_order_sub modify column order_sub_no varchar(64) not null comment '子订单号';
alter table global_order_sub modify need_pay_amount decimal(11, 2) default 0.00 not null comment '订单金额（商品金额 + 通道费 + 买家服务费 - 分贝抵扣）';
alter table global_order_sub
    modify app tinyint default 0 not null comment '订单渠道: 10-云展, 20-直播, 30-放心拍';
alter table global_order_sub
    modify order_type tinyint null comment '订单类型 10 ai查价订单  20 云展商品订单  30 直播商品订单 40 放心拍商品订单';
alter table global_order_sub
    modify real_payed_amount decimal(11, 2) default 0.00 not null comment '实付金额（商品金额 + 通道费 + 买家服务费 - 分贝抵扣）';


-- 分贝抵扣
alter table global_order_item add column fenbei_deduction_amount double(11,4) default 0 comment '分贝抵扣金额';
alter table global_order_item add column fenbei_deduction_count int default 0 comment '分贝抵扣数量';
alter table global_order_sub add column fenbei_deduction_amount double(11,4) default 0 comment '分贝抵扣金额';
alter table global_order_sub add column fenbei_deduction_count int default 0 comment '分贝抵扣数量';
alter table global_order add column fenbei_deduction_amount double(11,4) default 0 comment '分贝抵扣金额';
alter table global_order add column fenbei_deduction_count int default 0 comment '分贝抵扣数量';


-- ERP商品表
alter table erp_goods modify add column pt_goods_id  bigint default 0   not null comment '图文拍商品清单的Id';

-- 商户表添加保证金
alter table global_organization add column deposit decimal(11,2) not null default 0 comment '保证金';
alter table global_organization add fenbei int default 0 not null comment '分贝';
alter table global_organization add column self_deposit decimal(11, 2) default 0.00 not null comment '自助保证金';
alter table global_organization add column manual_deposit decimal(11, 2) default 0.00 not null comment '人工保证金';
-- 卖家保证金
alter table global_organization add column seller_deposit decimal(11, 2) default 0.00 not null comment '卖家保证金';
-- 保证金->买家保证金
alter table global_organization change deposit buyer_deposit decimal(11, 2) default 0.00 not null comment '买家保证金';
alter table global_organization change self_deposit buyer_self_deposit decimal(11, 2) default 0.00 not null comment '买家自充值保证金';
alter table global_organization change manual_deposit buyer_manual_deposit decimal(11, 2) default 0.00 not null comment '买家从老帮帮虎迁移的保证金';
-- 保证金 2024.09.06
alter table global_organization add column buyer_visual_deposit decimal(11,2) not null default 0.00 comment '买家虚拟保证金' after buyer_manual_deposit;
alter table global_organization add column seller_self_deposit decimal(11,2) not null default 0.00 comment '卖家自充值保证金' after seller_deposit;
alter table global_organization add column seller_manual_deposit decimal(11,2) not null default 0.00 comment '卖家从帮帮虎迁移来的保证金' after seller_self_deposit;
-- 保证金 2024.09.09
alter table global_organization add column seller_visual_deposit double(11,2) default 0.00 not null comment '卖家虚拟保证金' after seller_manual_deposit;

-- 商户席位添加保证金、保证金开关、拍号
alter table global_org_seat add column deposit decimal(11,2) not null default 0 comment '保证金';
alter table global_org_seat add column if_deposit_switch int not null default 0 comment '保证金开关';
alter table global_org_seat add column auction_code varchar(128) null comment '拍号';
-- 店铺拍号
alter table global_org_seat add column org_auction_code varchar(128) null comment '店铺靓号（优先使用）';
alter table global_org_seat
    change deposit min_deposit decimal(11, 2) default 0.00 not null comment '最小保证金门槛';
alter table global_org_seat
    change if_deposit_switch if_deposit_validate int default 0 not null comment '是否校验保证金门槛';
-- 竞拍买手席位
alter table global_org_seat add column auction_buyer_start_at datetime null comment '竞拍买手席位-开始时间';
alter table global_org_seat add column auction_buyer_end_at datetime null comment '竞拍买手席位-开始时间';
-- 席位昵称
alter table global_org_seat add column nick_name varchar(128) null comment '昵称';


-- 云展购物车
alter table ce_shopping_cart add column biz_id bigint null comment '业务主表id';
alter table ce_shopping_cart add column biz_type int null comment '业务类型';
alter table ce_shopping_cart add column biz_goods_id bigint null comment '业务商品id';

-- 账户余额
alter table global_org_money_account add column live_withdraw_balance decimal(11,2) default 0 comment '直播可提现余额';
alter table global_org_money_account add column pt_withdraw_balance decimal(11,2) default 0 comment '放心拍可提现余额';
-- 提现记录
alter table global_order_withdraw add column withdraw_type int default 0 comment '提现类型: 00-云展提现, 10-直播提现, 20-放心拍提现';

-- 全局配置
INSERT INTO `global_biz_config` (`id`, `biz_key`, `biz_value`, `remark`, `created_at`, `updated_at`, `deleted_at`) VALUES (28, 'live_subscribe_fast_remark_list', '[\"成色很好\", \"下次一定\"]', '直播-感兴趣-备注-快捷选择', NULL, NULL, NULL);
INSERT INTO `global_biz_config` (`id`, `biz_key`, `biz_value`, `remark`, `created_at`, `updated_at`, `deleted_at`) VALUES (32, 'live_auction_fail_list_display_size', '2', '直播看播的再见列表每个商家默认展示流拍商品数量', NULL, NULL, NULL);
INSERT INTO `global_biz_config` (`id`, `biz_key`, `biz_value`, `remark`, `created_at`, `updated_at`, `deleted_at`) VALUES (33, 'live_auction_fail_list_remark', NULL, '直播看播的再见列表消息滚动栏内容', NULL, NULL, NULL);
INSERT INTO `global_biz_config` (`id`, `biz_key`, `biz_value`, `remark`, `created_at`, `updated_at`, `deleted_at`) VALUES (34, 'global_org_sign_reward_fb_limit_daily', '50', '商品每天签到获取分贝上限', NULL, NULL, NULL);

-- 虚拟商品订单表
CREATE TABLE `global_virtual_goods_order` (
                                              `id` int NOT NULL AUTO_INCREMENT COMMENT '订单id',
                                              `org_id` int DEFAULT NULL COMMENT '买家店铺id',
                                              `org_name` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '买家店铺名',
                                              `order_no` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '订单号',
                                              `goods_id` int DEFAULT NULL COMMENT '商品ID',
                                              `goods_type` int DEFAULT NULL COMMENT '商品类型(1=分贝，2=app原价vip会员 3=app拼团vip会员 4=小程序原价vip会员 5=保证金补缴（补缴金额不固定） 6=卖家保证金, 7=买家保证金, 8=买家升级新卖家保证金, 9=老卖家升级卖家保证金, 10=系统赠送升级卖家保证金, 11=系统赠送升级买家保证金,）',
                                              `goods_name` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '商品名称',
                                              `goods_cover_img` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '商品封面图',
                                              `goods_price` decimal(10,2) DEFAULT NULL COMMENT '商品价格',
                                              `order_price` decimal(10,2) NOT NULL COMMENT '订单金额',
                                              `order_state` tinyint NOT NULL DEFAULT '10' COMMENT '订单状态 10=WAIT_PAY=等待支付 20=BUYER_CANCEL=买家取消 30=OVERTIME_CANCEL=超时取消 40=HAVE_MONEY=支付完成 50=DONE_DELIVER=完成交付',
                                              `discount_money` decimal(10,2) DEFAULT NULL COMMENT '优惠金额',
                                              `pay_type` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '支付方式',
                                              `pay_price` decimal(10,2) DEFAULT NULL COMMENT '支付金额',
                                              `pay_datetime` datetime DEFAULT NULL COMMENT '支付时间',
                                              `pay_code` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '支付系统流水号',
                                              `pay_trade_no` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '支付平台流水号',
                                              `deliver_datetime` datetime DEFAULT NULL COMMENT '发货时间',
                                              `buyer_seat_id` int DEFAULT NULL COMMENT '买家席位id',
                                              `buyer_user_id` int DEFAULT NULL COMMENT '买家id',
                                              `order_expire_time` datetime DEFAULT NULL COMMENT '订单过期时间',
                                              `remark` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '备注',
                                              `created_at` datetime DEFAULT NULL COMMENT '创建时间',
                                              `updated_at` datetime DEFAULT NULL COMMENT '修改时间',
                                              `deleted_at` datetime DEFAULT NULL COMMENT '删除时间',
                                              PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='虚拟商品订单';