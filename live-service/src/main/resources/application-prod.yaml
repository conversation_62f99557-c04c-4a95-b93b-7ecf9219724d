spring:
  cloud:
    nacos:
      discovery:
        server-addr: 172.19.93.44:8848
        namespace: cef529bc-16f6-44a8-a442-7520d6063159
        username: nacos
        password: bbh@0519.
      config:
        enabled: true
        server-addr: 172.19.93.44:8848
        namespace: cef529bc-16f6-44a8-a442-7520d6063159
        username: nacos
        password: bbh@0519.
  config:
    import:
      - nacos:${spring.application.name}.${file-extension:yaml}
      - nacos:${spring.application.name}-${spring.profiles.active}.${file-extension:yaml}
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    dynamic:
      druid:
        proxy-filters: sqlLogInterceptor
      primary: master
      datasource:
        master:
          url: **************************************************************************************************************************************************************************************************
          username: htb_prod
          password: je7NRecWzcMpaDHE@e&n
  data:
    redis:
      host: r-uf616umw2ywbtr9cew.redis.rds.aliyuncs.com
      password: bbh:Boloogo2017
      port: 6379
      database: 8
bbh:
  live-service:
    live-service-url: https://htb-api.bangbanghu.com.cn/htb-live-api
    service-center-url: https://htb-api.bangbanghu.com.cn/auth-service
    time-out: 30000
    bbh-merchant-no: htb_auth
    salt: 4006addda6f8e4f570ead33f6d043616
rong-yun:
  app-key: e0x9wycfesw9q
  app-secret: q3ned6lFpo
pay-center:
  serviceUrl: https://htb-api.bangbanghu.com.cn/pay-service/bbh
  bbhMerchantNo: htb_pay
  salt: c2982cc05653aa7caf8bfda741ebe6e4
  platMerchantNo: 0000001201
#抵扣金 年费  6188
htb:
  annual_vip_deduction: 6188
mq:
  switch: true
  host: rabbitmq-serverless-cn-jeo3mmq3g0i.cn-shanghai.amqp-3.vpc.mq.amqp.aliyuncs.com
  port: 5672
  username: MjpyYWJiaXRtcS1zZXJ2ZXJsZXNzLWNuLWplbzNtbXEzZzBpOkxUQUk1dDl2VkRGRXVvcDlBcEtqRTNLNQ==
  password: QTI5OTU3NjYyNTVDMUVCRDQzREJGRDc1NTMwMUJCOUEwOEMzQjY4MzoxNzEzMTU1MzI0NDEw
  virtual-host: pay_center
weixin:
  miniapp:
    env-version: release
