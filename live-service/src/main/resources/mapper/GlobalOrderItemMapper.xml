<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bbh.live.dao.mapper.GlobalOrderItemMapper">

    <select id="countOrder" resultType="java.lang.Long" parameterType="com.bbh.live.dao.dto.QueryOrderCounterDTO">
        SELECT COUNT(goi.id)
        FROM global_order_item goi
        left join global_order go on go.id = goi.global_order_id
        <where>
            <if test="orderStatus != null">
                AND goi.order_status = #{orderStatus}
            </if>
            <if test="buyerSeatId != null">
                AND goi.buyer_seat_id = #{buyerSeatId}
            </if>
            <if test="buyerOrgId != null">
                AND goi.buyer_org_id = #{buyerOrgId}
            </if>
            <if test="sellerOrgId != null">
                AND goi.seller_org_id = #{sellerOrgId}
            </if>
            <if test="bizType != null">
                AND goi.biz_type = #{bizType}
            </if>
            <if test="lastPayAtGt != null">
                AND go.last_pay_at &gt; #{lastPayAtGt}
            </if>
            <if test="lastPayAtLt != null">
                AND go.last_pay_at &lt;= #{lastPayAtLt}
            </if>
            <if test="updateAtGt != null">
                AND goi.updated_at &gt; #{updateAtGt}
            </if>
        </where>
    </select>
    <select id="pageList" resultType="com.bbh.live.dao.dto.GlobalOrderItemVO">
        SELECT goi.vip_deduction_amount,
        gog.img_url_list,
        gog.quality,
        gog.`name`,
        goi.real_payed_amount
        FROM global_order_item goi
        INNER JOIN global_order_goods_snapshot gog ON goi.id = gog.global_order_item_id
        WHERE global_order_id = #{orderId}
    </select>

</mapper>
