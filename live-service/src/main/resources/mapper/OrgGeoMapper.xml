<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bbh.live.dao.mapper.OrgGeoMapper">

    <select id="getDistance" resultType="java.lang.Double">
        select
            6378.138 * 2 * ASIN(
                    SQRT(
                            POW(
                                    SIN(
                                            (
                                                #{position1.latitude} * PI() / 180 - #{position2.latitude} * PI() / 180
                                                ) / 2
                                    ),
                                    2
                            ) + COS(#{position1.latitude} * PI() / 180) * COS(#{position2.latitude} * PI() / 180) * POW(
                                    SIN(
                                            (
                                                #{position1.longitude} * PI() / 180 - #{position2.longitude} * PI() / 180
                                                ) / 2
                                    ),
                                    2
                                                                                             )
                    )
                           ) * 1000 as distance
    </select>

    <select id="searchOrgsWithDistance" resultType="com.bbh.live.service.buyer.orgmap.vo.OrgDistanceVO">
        select
            go.id as orgId,
            <include refid="DISTANCE_SQL"/> as distance
        from global_organization go
        where
            go.deleted_at is null
            and JSON_EXTRACT(go.address_coordinate, '$.lat') is not null
            and go.address_coordinate->>'$.lat' between -90.0 and 90.0
            and go.address_coordinate->>'$.lng' between -180.0 and 180.0
        <if test="args.radius != null">
            and <include refid="DISTANCE_SQL"/> &lt;= #{args.radius}
        </if>
        <if test="args.keywords != null and args.keywords != ''">
            and go.name like concat('%',#{args.keywords},'%')
        </if>
        <choose>
            <when test="args.order != null and args.order.name == 'DESC'">
                order by distance desc
            </when>
            <otherwise>
                order by distance asc
            </otherwise>
        </choose>
        <if test="args.count != null">
            limit #{args.count}
        </if>
    </select>

    <sql id="DISTANCE_SQL">
        6378.138 * 2 * ASIN(
                    SQRT(
                        POW(
                            SIN(
                                 (
                                    #{args.latitude} * PI() / 180 - go.address_coordinate->>'$.lat' * PI() / 180
                                 ) / 2
                                ),
                          2)
                    +
                    COS(#{args.latitude} * PI() / 180) * COS(go.address_coordinate->>'$.lat' * PI() / 180) * POW(
                        SIN(
                                (
                                    #{args.longitude} * PI() / 180 - go.address_coordinate->>'$.lng' * PI() / 180
                                ) / 2
                            ),
                        2)
                    )
        ) * 1000
    </sql>
</mapper>
