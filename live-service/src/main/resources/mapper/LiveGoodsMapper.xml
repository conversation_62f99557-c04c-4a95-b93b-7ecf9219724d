<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bbh.live.dao.mapper.LiveGoodsMapper">
    <update id="batchUpdateLiveGoodsSort">
        <foreach collection="sortList" item="sort" separator=";">
            UPDATE live_goods
            SET sort = #{sort.sort}
            WHERE id = #{sort.liveGoodsId}
        </foreach>
    </update>
    <update id="incrGoodsSubscribeCount">
        UPDATE live_goods
        SET subscribe_count = subscribe_count + #{delta}
        WHERE id = #{liveGoodsId}
    </update>

    <select id="getLiveGoodsList" resultType="com.bbh.live.dao.dto.livegoods.LiveGoodsDTO">
        SELECT
            lg.*,
            lg.id                   liveGoodsId,
            lg.start_price          startPrice,
            lg.subscribe_count      subscribeCount,
            lg.end_at               endAt,
            eg.deleted_at       as  erp_deleted_at,
            eg.code                 globalGoodsCode,
            eg.name                 globalGoodsName,
            eg.description          globalGoodsDescription,
            eg.img_url_list         imgUrlList,
            eg.quality              quality,
            if(lg.sce_goods_id > 0, 1 , 0) as ifSyncCe,
            eg.place_order_status   placeOrderStatus,
            eg.if_locked            erpIfLocked,
            eg.cost_price           costPrice,
            eg.peer_price           peerPrice,
            eg.place_order_status   erpPlaceOrderStatus,
            eg.sale_status          erpSaleStatus,
            eg.settlement_status    settleStatus,
            lg.platform_classify_id,
            lsc.one_classify_id,
            lsc.spu_id,
            lr.buyer_service_rate as live_room_buyer_service_rate,
            lgbcr.cancel_status as buyerCancelStatus
        FROM
            live_goods lg
        LEFT JOIN erp_goods eg ON lg.global_goods_id = eg.id
        LEFT JOIN live_room lr ON lg.live_room_id = lr.id
        left join live_spu_classify lsc on lsc.spu_id = eg.spu_id
        left join bbh_htb.live_goods_buyer_cancel_record lgbcr on lg.id = lgbcr.live_goods_id and lgbcr.deleted_at IS NULL
        WHERE
            lg.deleted_at IS NULL
        <if test="queryParam.liveRoomId != null">
            and lg.live_room_id = #{queryParam.liveRoomId}
        </if>
        <if test="queryParam.belongUserId != null">
            and lg.belong_user_id = #{queryParam.belongUserId}
        </if>
        <if test="queryParam.belongSeatId != null">
            and lg.belong_seat_id = #{queryParam.belongSeatId}
        </if>
        <if test="queryParam.keywords != null and queryParam.keywords != ''">
            AND
            (eg.name like concat('%',#{queryParam.keywords},'%')  OR lg.live_goods_code = #{queryParam.codeKeywords} OR eg.description like concat('%',#{queryParam.keywords},'%'))
        </if>
        <if test="queryParam.liveGoodsIdList != null and queryParam.liveGoodsIdList.size() > 0">
            AND lg.id in
            <foreach collection="queryParam.liveGoodsIdList" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="queryParam.liveGoodsStatusList != null and queryParam.liveGoodsStatusList.size() > 0">
            AND
            <foreach collection="queryParam.liveGoodsStatusList" open="(" close=")" separator=" or " item="status">
                lg.goods_status = #{status}
            </foreach>
        </if>
        <if test="queryParam.filterLockedOrSoldOutGoods != null and queryParam.filterLockedOrSoldOutGoods">
            AND (eg.if_locked = 0 AND eg.place_order_status = 0 AND eg.sale_status = 1)
        </if>
        <if test="queryParam.filterOffhandGoods != null and queryParam.filterOffhandGoods">
            AND lg.if_offhand_goods = 0
        </if>
        <if test="queryParam.filterIfSyncCe != null and queryParam.filterIfSyncCe == true">
            AND lg.sce_goods_id > 0
        </if>
        <if test="queryParam.filterIfSyncCe != null and queryParam.filterIfSyncCe == false">
            AND lg.sce_goods_id = 0
        </if>
    </select>

    <select id="getLiveGoodsDetailInfo" resultType="com.bbh.live.dao.dto.livegoods.LiveGoodsDTO">
        SELECT lg.*,
               lg.id                   liveGoodsId,
               eg.code                 globalGoodsCode,
               eg.id                   globalGoodsId,
               eg.name                 globalGoodsName,
               eg.description          globalGoodsDescription,
               eg.img_url_list         imgUrlList,
               eg.quality              quality,
               eg.place_order_status   placeOrderStatus,
               eg.if_locked            erpIfLocked,
               eg.cost_price           costPrice,
               eg.peer_price           peerPrice,
               eg.place_order_status   erpPlaceOrderStatus,
               eg.sale_status          erpSaleStatus,
               eg.settlement_status    settleStatus,
               lsc.one_classify_id,
               lsc.spu_id
        FROM live_goods lg
                 LEFT JOIN erp_goods eg ON lg.global_goods_id = eg.id
                 LEFT JOIN live_spu_classify lsc on lsc.spu_id = eg.spu_id
        WHERE lg.deleted_at IS NULL
        <if test="liveGoodsId != null">
            and lg.id = #{liveGoodsId}
        </if>
        <if test="globalGoodsId != null">
            and lg.global_goods_id = #{globalGoodsId}
        </if>
    </select>

    <select id="getLiveGoodsDetailInfoByGlobalGoodsId" resultType="com.bbh.live.dao.dto.livegoods.LiveGoodsDTO">
        SELECT
        eg.id                   globalGoodsId,
        eg.code                 globalGoodsCode,
        eg.name                 globalGoodsName,
        eg.description          globalGoodsDescription,
        eg.img_url_list         imgUrlList,
        eg.quality              quality,
        eg.place_order_status   placeOrderStatus,
        eg.if_locked            erpIfLocked,
        eg.cost_price           costPrice,
        eg.peer_price           peerPrice,
        eg.place_order_status   erpPlaceOrderStatus,
        eg.sale_status          erpSaleStatus,
        eg.settlement_status    settleStatus,
        lsc.one_classify_id,
        lsc.spu_id
        FROM erp_goods eg
        LEFT JOIN live_spu_classify lsc on lsc.spu_id = eg.spu_id
        WHERE eg.deleted_at IS NULL
        and eg.id = #{globalGoodsId}
    </select>

    <select id="getAuctionFailLiveGoodsListByLiveRoomId" resultType="com.bbh.live.dao.dto.AbortiveAuctionGoodsDTO">
        SELECT id,live_room_id,imgUrl FROM (
            SELECT lg.id,
                   lg.live_room_id,
                   JSON_UNQUOTE(JSON_EXTRACT(eg.img_url_list, '$[0]')) AS imgUrl,
                   ROW_NUMBER() OVER (PARTITION BY lg.live_room_id ORDER BY lg.updated_at DESC) AS rn
            FROM live_goods lg
            LEFT JOIN erp_goods eg ON lg.global_goods_id = eg.id
            WHERE
                lg.goods_status = 40
                AND lg.deleted_at IS NULL
                AND (eg.if_locked = 0 AND eg.place_order_status = 0 AND eg.sale_status = 1)
                AND lg.live_room_id in
            <foreach collection="liveRoomIdList" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        ) AS subquery
        WHERE rn &lt;= #{size};
    </select>

    <select id="getLiveGoodsCountGroupByStatus" resultType="map">
        SELECT
            lg.goods_status goodsStatus,
            count(*) count
        FROM
        live_goods lg
        LEFT JOIN erp_goods eg ON lg.global_goods_id = eg.id
        WHERE
        lg.deleted_at IS NULL
        <if test="queryParam.liveRoomId != null">
            and lg.live_room_id = #{queryParam.liveRoomId}
        </if>

        <if test="queryParam.keywords != null and queryParam.keywords != ''">
            AND
            (eg.name like concat('%',#{queryParam.keywords},'%')  OR lg.live_goods_code = #{queryParam.codeKeywords} OR eg.description like concat('%',#{queryParam.keywords},'%'))
        </if>

        <if test="queryParam.filterLockedOrSoldOutGoods != null and queryParam.filterLockedOrSoldOutGoods">
            AND (eg.if_locked = 0 AND eg.place_order_status = 0 AND eg.sale_status = 1)
        </if>
        GROUP BY goodsStatus
    </select>

    <select id="getLiveGoodsTradeCount" resultType="java.lang.Long">
        SELECT
            count(*) count
        FROM
            live_goods lg
        LEFT JOIN erp_goods eg ON lg.global_goods_id = eg.id
        WHERE
            lg.deleted_at IS NULL
            AND lg.goods_status = 50
            <if test="queryParam.liveRoomId != null">
                AND lg.live_room_id = #{queryParam.liveRoomId}
            </if>
            <if test="queryParam.keywords != null and queryParam.keywords != ''">
                AND
                (eg.name LIKE concat('%',#{queryParam.keywords},'%')  OR lg.live_goods_code = #{queryParam.codeKeywords} OR eg.description LIKE concat('%',#{queryParam.keywords},'%'))
             </if>
    </select>

    <select id="getCheckedLiveGoodsList" resultType="com.bbh.live.dao.dto.CheckedLiveGoodsDTO">
        SELECT lg.id                   liveGoodsId,
               lg.global_goods_id      globalGoodsId,
               lg.goods_status         goodsStatus,
               eg.name                 globalGoodsName,
               eg.img_url_list         imgUrlList,
               eg.quality              quality,
               eg.peer_price           peerPrice,
               eg.place_order_status as erp_place_order_status,
               eg.sale_status as erp_sale_status,
               eg.settlement_status settleStatus,
               eg.if_locked as erp_if_locked
        FROM live_goods lg
                 LEFT JOIN erp_goods eg ON lg.global_goods_id = eg.id
        WHERE lg.live_room_id = #{liveRoomId}
          AND lg.deleted_at IS NULL
    </select>

    <select id="getLiveGoodsListByLiveRoomAndStatus" resultType="com.bbh.live.dao.dto.vo.AuctionLiveGoodsVO">
        SELECT
            lg.id                   liveGoodsId,
            lg.live_room_id         liveRoomId,
            lg.increase_price       increasePrice,
            eg.name                 goodsName,
            eg.img_url_list         imgUrlList,
            eg.quality              quality,
            lg.putaway_at           putawayAt,
            lg.trade_type           tradeType
        FROM live_goods lg
                 LEFT JOIN erp_goods eg ON lg.global_goods_id = eg.id
        WHERE lg.live_room_id in
            <foreach collection="liveRoomIdList" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
          AND lg.deleted_at IS NULL
          AND lg.goods_status = #{status}
    </select>


    <select id="getPlaybackLiveGoodsList" resultType="com.bbh.live.dao.dto.vo.LiveRoomPlaybackGoodsVO">
        SELECT
            lg.id                   liveGoodsId,
            lg.live_room_id         liveRoomId,
            lg.global_goods_id      globalGoodsId,
            lg.live_goods_code      goodsCode,
            lg.live_video_url       videoUrl,
            eg.name                 goodsName,
            eg.peer_price           price,
            eg.img_url_list         imgUrlList,
            eg.quality              quality,
            eg.ce_goods_id
        FROM live_goods lg
                 LEFT JOIN erp_goods eg ON lg.global_goods_id = eg.id and eg.deleted_at is null
        <where>
            <if test="queryParam.roomId != null">
                lg.live_room_id = #{queryParam.roomId}
            </if>
            <if test="queryParam.roomIdList != null and queryParam.roomIdList.size() > 0">
                AND lg.live_room_id IN
                <foreach collection="queryParam.roomIdList" item="roomId" open="(" separator="," close=")">
                    #{roomId}
                </foreach>
            </if>
            <if test="queryParam.keywords != null and queryParam.keywords != ''">
                AND
                (eg.name like concat('%',#{queryParam.keywords},'%')  OR lg.live_goods_code = #{queryParam.codeKeywords} OR eg.description like concat('%',#{queryParam.keywords},'%'))
            </if>
          AND lg.deleted_at IS NULL
          AND lg.sce_goods_id > 0
          and eg.ce_goods_id > 0
            and not exists (
            select 1 from sce_goods sg
            where sg.global_goods_id = lg.global_goods_id
            and sg.goods_status = 20
          )
        </where>
    </select>

    <select id="getLiveGoodsCountBeforeLiveStart" resultType="java.lang.Long">
        SELECT
            count(1)
        FROM
            live_goods lg
        LEFT JOIN erp_goods eg ON lg.global_goods_id = eg.id
        WHERE
            lg.deleted_at IS NULL
          AND lg.live_room_id = #{queryParam.liveRoomId}
          AND lg.goods_status = 10
          AND ( lg.start_price IS NULL OR lg.increase_price IS NULL )
            <if test="queryParam.keywords != null and queryParam.keywords != ''">
                AND
                (eg.name like concat('%',#{queryParam.keywords},'%')  OR lg.live_goods_code = #{queryParam.codeKeywords} OR eg.description like concat('%',#{queryParam.keywords},'%'))
            </if>
            <if test="queryParam.filterLockedOrSoldOutGoods != null and queryParam.filterLockedOrSoldOutGoods">
                AND (eg.if_locked = 0 AND eg.place_order_status = 0 AND eg.sale_status = 1)
            </if>

        UNION

        SELECT
            count(1)
        FROM
            live_goods lg
        LEFT JOIN erp_goods eg ON lg.global_goods_id = eg.id
        WHERE
            lg.deleted_at IS NULL
          AND lg.live_room_id = #{queryParam.liveRoomId}
            <if test="queryParam.keywords != null and queryParam.keywords != ''">
                AND
                (eg.name like concat('%',#{queryParam.keywords},'%')  OR lg.live_goods_code = #{queryParam.codeKeywords} OR eg.description like concat('%',#{queryParam.keywords},'%'))
            </if>
            <if test="queryParam.filterLockedOrSoldOutGoods != null and queryParam.filterLockedOrSoldOutGoods">
                AND (eg.if_locked = 0 AND eg.place_order_status = 0 AND eg.sale_status = 1)
            </if>
    </select>

    <select id="getWaitCompleteLiveGoodsList" resultType="com.bbh.live.dao.dto.livegoods.LiveGoodsDTO">
        SELECT
        lg.*,
        lg.id                   liveGoodsId,
        lg.start_price          startPrice,
        lg.subscribe_count      subscribeCount,
        lg.end_at               endAt,
        eg.code                 globalGoodsCode,
        eg.name                 globalGoodsName,
        eg.description          globalGoodsDescription,
        eg.img_url_list         imgUrlList,
        eg.quality              quality,
        eg.place_order_status   placeOrderStatus,
        eg.if_locked            erpIfLocked,
        eg.cost_price           costPrice,
        eg.peer_price           peerPrice,
        eg.place_order_status   erpPlaceOrderStatus,
        eg.sale_status          erpSaleStatus,
        eg.settlement_status    settleStatus,
        lsc.one_classify_id,
        lsc.spu_id,
        go.if_need_pt_sync_sce  ifNeedPtSyncSce
        FROM
        live_goods lg
        LEFT JOIN erp_goods eg ON lg.global_goods_id = eg.id
        LEFT JOIN live_spu_classify lsc on lsc.spu_id = eg.spu_id
        left join global_organization go on go.id = lg.org_id
        WHERE
            lg.deleted_at IS NULL
            AND lg.goods_status = 10
            AND ( lg.start_price IS NULL OR lg.increase_price IS NULL )
            AND lg.live_room_id = #{queryParam.liveRoomId}
        <if test="queryParam.keywords != null and queryParam.keywords != ''">
            AND
            (eg.name like concat('%',#{queryParam.keywords},'%')  OR lg.live_goods_code = #{queryParam.codeKeywords} OR eg.description like concat('%',#{queryParam.keywords},'%'))
        </if>
        <if test="queryParam.filterLockedOrSoldOutGoods != null and queryParam.filterLockedOrSoldOutGoods">
            AND (eg.if_locked = 0 AND eg.place_order_status = 0 AND eg.sale_status = 1)
        </if>
    </select>

    <select id="getSoldGoodsListByLiveRoom" resultType="com.bbh.live.dao.dto.livegoods.LiveGoodsSaleDTO">
        SELECT
            lg.id as                liveGoodsId,
            lg.live_goods_code as   liveGoodsCode,
            eg.name as              globalGoodsName,
            eg.quality as           qualify,
            eg.img_url_list as      imageUrlList,
            lg.sell_price as        sellPrice,
            lg.belong_seat_id as    belongSeatId,
            IF(gos.nick_name is null, gos.name, gos.nick_name) as belongUserName,
            gos.auction_code    as  belongUserAuctionCode
        FROM
            live_goods lg
        LEFT JOIN erp_goods eg ON lg.global_goods_id = eg.id
        LEFT JOIN global_org_seat gos on gos.id = lg.belong_seat_id
        WHERE
            lg.deleted_at IS NULL
            AND lg.live_room_id = #{liveRoomId}
            AND lg.goods_status = 50
    </select>

    <select id="getBaseGoodsInfo" resultType="com.bbh.live.service.msg.dto.base.BaseGoods">
        SELECT lg.id                   liveGoodsId,
               lg.global_goods_id      globalGoodsId,
               lg.live_goods_code      liveGoodsCode,
               lg.start_price          startPrice,
               lg.increase_price       increasePrice,
               lg.sell_price,
               lg.goods_status,
               lg.auction_duration     auctionDuration,
               lg.director_remark      directorRemark,
               lg.trade_type,
               eg.name                 globalGoodsName,
               eg.img_url_list         imgUrlList,
               eg.quality              quality
        FROM live_goods lg
                 LEFT JOIN erp_goods eg ON lg.global_goods_id = eg.id
        WHERE lg.id = #{liveGoodsId}
          AND lg.deleted_at IS NULL
    </select>

    <select id="getGodViewGoodsInfo" resultType="com.bbh.live.dao.dto.livegoods.LiveGoodsDTO">
        SELECT lg.id                   liveGoodsId,
               lg.global_goods_id      globalGoodsId,
               lg.live_goods_code      liveGoodsCode,
               lg.start_price          startPrice,
               lg.increase_price       increasePrice,
               lg.auction_duration     auctionDuration,
               lg.director_remark      directorRemark,
               eg.name                 globalGoodsName,
               eg.img_url_list         imgUrlList,
               eg.quality              quality
        FROM live_goods lg
                 LEFT JOIN erp_goods eg ON lg.global_goods_id = eg.id
        where lg.goods_status in (20,30)
          AND lg.deleted_at IS NULL
    </select>

    <select id="getEffectiveAuctionFailLiveGoodsCount" resultType="java.util.Map">
        SELECT
            lg.live_room_id         liveRoomId,
            count(1)                count
        FROM
            live_goods lg
        LEFT JOIN erp_goods eg ON lg.global_goods_id = eg.id
        WHERE
            lg.deleted_at IS NULL
            AND lg.goods_status = 40
            AND (eg.if_locked = 0 AND eg.place_order_status = 0 AND eg.sale_status = 1)
            AND lg.live_room_id IN
                <foreach collection="liveRoomIdList" item="liveRoomId" separator="," open="(" close=")">
                    #{liveRoomId}
                </foreach>
        GROUP BY lg.live_room_id
    </select>

    <select id="selectLiveShoppingCartList" resultType="com.bbh.live.dao.dto.LiveShoppingCartDTO">
        SELECT
        lg.id,
        lr.org_id as seller_org_id,
        lg.belong_seat_id as buyer_seat_id,
        lg.belong_org_id as buyer_org_id,
        go2.name as buyer_org_name,
        go2.logo_url as buyer_org_logo_url,
        lg.id as biz_goods_id,
        lg.id as live_goods_id,
        lg.live_room_id as biz_id,
        lg.live_room_id AS live_room_id,
        lg.end_at as created_at,
        lg.trade_type as trade_type,
        go.logo_url,
        go.name AS org_name,
        <!-- 成交人 -->
        gos.name AS buyer_name,
        <!-- 成交人，没有昵称就返名称 -->
        ifnull(gos.nick_name, gos.name) AS buyer_nick_name,
        gos.show_name AS buyer_show_name,
        gos.auction_code AS buyer_auction_code,
        gos.avatar AS buyer_avatar,
        lr.room_name,
        lr.if_special,
        lr.stream_status,
        lr.start_at as room_start_at,
        lr.buyer_service_rate,
        lg.sell_price,
        <!-- 成交价，为前端兼容 -->
        lg.sell_price as goods_price,
        <!-- 成交时间 -->
        lg.end_at AS sell_at,
        <!-- 成交类型 -->
        lg.belong_type,
        lg.end_at,
        lg.live_goods_code,
        if(lg.sce_goods_id > 0, 1 , 0) as ifSyncCe,

        lg.global_goods_id,
        lg.live_video_url,
        <!-- 上架时间 -->
        lg.putaway_at,
        eg.name AS goods_name,
        eg.quality,
        eg.img_url_list,
        lgbcr.cancel_status as buyer_cancel_status
        FROM live_goods lg
        <!-- 买手主动取消成交 -->
        left join live_goods_buyer_cancel_record lgbcr on lgbcr.live_goods_id = lg.id and lgbcr.deleted_at is null
        <!-- 成交人 -->
        LEFT JOIN global_org_seat gos ON lg.belong_seat_id = gos.id AND gos.deleted_at IS NULL
        <!-- 直播间 -->
        LEFT JOIN live_room lr ON lg.live_room_id = lr.id AND lr.deleted_at IS NULL
        <!-- 卖家/商家 -->
        LEFT JOIN global_organization go ON lr.org_id = go.id AND go.deleted_at IS NULL
        <!-- 买家主体 -->
        LEFT JOIN global_organization go2 ON lg.belong_org_id = go2.id AND go2.deleted_at IS NULL
        <!-- ERP货品 -->
        LEFT JOIN erp_goods eg ON lg.global_goods_id = eg.id AND eg.deleted_at IS NULL
        WHERE
        lg.deleted_at IS NULL
        and lg.cancel_status != 20
        and lg.goods_status = 50
        AND NOT EXISTS (
        SELECT 1
        FROM global_order_item goi
        WHERE goi.biz_goods_id = lg.id
        AND goi.deleted_at IS NULL
        AND goi.biz_type = 30
        AND goi.order_status != 60
        )
        <if test="query.buyerSeatId != null">
            and lg.belong_seat_id = #{query.buyerSeatId}
        </if>
        <if test="query.buyerOrgId != null">
            and lg.belong_org_id = #{query.buyerOrgId}
        </if>
        <if test="query.bizGoodsIdList != null">
            and lg.id in
            <foreach collection="query.bizGoodsIdList" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="query.bizIdList != null and !query.bizIdList.isEmpty()">
            and lg.live_room_id in
            <foreach collection="query.bizIdList" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="query.buyerOrgIdList != null">
            and lg.belong_org_id in
            <foreach collection="query.buyerOrgIdList" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <!-- 卖家商户id -->
        <if test="query.sellerOrgId != null">
            and lr.org_id = #{query.sellerOrgId}
        </if>
        <!-- 货品名称 -->
        <if test="query.globalGoodsName != null and query.globalGoodsName != ''">
            and eg.name like CONCAT('%',#{query.globalGoodsName},'%')
        </if>
        <!-- 直播商品的售出时间-开始 -->
        <if test="query.liveSellTimeStart != null">
            and lg.end_at &gt;= #{query.liveSellTimeStart}
        </if>
        <!-- 直播商品的售出时间-结束 -->
        <if test="query.liveSellTimeEnd != null">
            and lg.end_at &lt;= #{query.liveSellTimeEnd}
        </if>
        <choose>
            <when test="query.orderBy != null and query.orderBy != ''">
                order by ${query.orderBy}
            </when>
            <otherwise>
                <!-- 场次：开始时间倒序 商品：成交时间倒序 -->
                order by lr.start_at desc, lg.end_at desc
            </otherwise>
        </choose>
    </select>
    <select id="selectSellerLiveShoppingCartList" resultType="com.bbh.live.dao.dto.LiveShoppingCartDTO">
        SELECT
        lg.id,
        lr.org_id as seller_org_id,
        lg.belong_seat_id as buyer_seat_id,
        lg.belong_org_id as buyer_org_id,
        go2.name as buyer_org_name,
        go2.logo_url as buyer_org_logo_url,
        lg.id as biz_goods_id,
        lg.id as live_goods_id,
        lg.live_room_id as biz_id,
        lg.live_room_id AS live_room_id,
        lg.end_at as created_at,
        lg.trade_type as trade_type,
        go.logo_url,
        go.name AS org_name,
        <!-- 成交人 -->
        gos.name AS buyer_name,
        <!-- 成交人，没有昵称就返名称 -->
        ifnull(gos.nick_name, gos.name) AS buyer_nick_name,
        gos.show_name AS buyer_show_name,
        gos.auction_code AS buyer_auction_code,
        gos.avatar AS buyer_avatar,
        lr.room_name,
        lr.if_special,
        lr.stream_status,
        lr.start_at as room_start_at,
        lr.buyer_service_rate,
        lg.sell_price,
        <!-- 成交价，为前端兼容 -->
        lg.sell_price as goods_price,
        <!-- 成交时间 -->
        lg.end_at AS sell_at,
        <!-- 成交类型 -->
        lg.belong_type,
        lg.end_at,
        lg.live_goods_code,
        if(lg.sce_goods_id > 0, 1 , 0) as ifSyncCe,
        lg.global_goods_id,
        lg.live_video_url,
        <!-- 上架时间 -->
        lg.putaway_at,
        eg.name AS goods_name,
        eg.quality,
        eg.img_url_list,
        lgbcr.cancel_status as buyer_cancel_status
        FROM live_goods lg
        <!-- 买手主动取消成交 -->
        left join live_goods_buyer_cancel_record lgbcr on lgbcr.live_goods_id = lg.id and lgbcr.deleted_at is null
        <!-- 成交人 -->
        LEFT JOIN global_org_seat gos ON lg.belong_seat_id = gos.id AND gos.deleted_at IS NULL
        <!-- 直播间 -->
        LEFT JOIN live_room lr ON lg.live_room_id = lr.id AND lr.deleted_at IS NULL
        <!-- 卖家/商家 -->
        LEFT JOIN global_organization go ON lr.org_id = go.id AND go.deleted_at IS NULL
        <!-- 买家主体 -->
        LEFT JOIN global_organization go2 ON lg.belong_org_id = go2.id AND go2.deleted_at IS NULL
        <!-- ERP货品 -->
        LEFT JOIN erp_goods eg ON lg.global_goods_id = eg.id AND eg.deleted_at IS NULL
        <!-- 订单表 -->
        LEFT JOIN global_order_item goi ON lg.id = goi.biz_goods_id and goi.biz_id = lr.id AND goi.deleted_at IS NULL AND goi.order_status != 60
        WHERE
        lg.deleted_at IS NULL
        and lg.cancel_status != 20
        and lg.goods_status = 50
        AND ifnull(goi.order_status,0) in (0,10,20)
        <if test="query.orderStatus != null">
            AND ifnull(goi.order_status,0) =#{query.orderStatus}
        </if>
        AND ifnull(goi.order_status,0) in (0,10,20)
        <if test="query.buyerSeatId != null">
            and lg.belong_seat_id = #{query.buyerSeatId}
        </if>
        <if test="query.buyerOrgId != null">
            and lg.belong_org_id = #{query.buyerOrgId}
        </if>
        <if test="query.bizGoodsIdList != null">
            and lg.id in
            <foreach collection="query.bizGoodsIdList" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="query.bizIdList != null and !query.bizIdList.isEmpty()">
            and lg.live_room_id in
            <foreach collection="query.bizIdList" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="query.buyerOrgIdList != null">
            and lg.belong_org_id in
            <foreach collection="query.buyerOrgIdList" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <!-- 卖家商户id -->
        <if test="query.sellerOrgId != null">
            and lr.org_id = #{query.sellerOrgId}
        </if>
        <!-- 货品名称 -->
        <if test="query.globalGoodsName != null and query.globalGoodsName != ''">
            and eg.name like CONCAT('%',#{query.globalGoodsName},'%')
        </if>
        <!-- 直播商品的售出时间-开始 -->
        <if test="query.liveSellTimeStart != null">
            and lg.end_at &gt;= #{query.liveSellTimeStart}
        </if>
        <!-- 直播商品的售出时间-结束 -->
        <if test="query.liveSellTimeEnd != null">
            and lg.end_at &lt;= #{query.liveSellTimeEnd}
        </if>
        <choose>
            <when test="query.orderBy != null and query.orderBy != ''">
                order by ${query.orderBy}
            </when>
            <otherwise>
                <!-- 场次：开始时间倒序 商品：成交时间倒序 -->
                order by lr.start_at desc, lg.end_at desc
            </otherwise>
        </choose>
    </select>
    <select id="getLiveGoodsListForSyncEndLiveRoomToSce" resultType="com.bbh.model.LiveGoods">
        SELECT
        lg.*,eg.peer_price as sellPrice
        FROM
        live_goods lg
        LEFT JOIN erp_goods eg ON lg.global_goods_id = eg.id AND eg.deleted_at IS NULL
        WHERE
        lg.deleted_at IS NULL
        AND eg.sale_status = 1
        AND eg.if_locked = 0
        AND lg.sce_goods_id = 0
        AND lg.goods_status in  (10, 40)
        and lg.live_room_id in
        <foreach collection="liveRoomIds" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>

    </select>
    <select id="getNeedSyncLiveRoomIdsToSce" resultType="java.lang.Long">
        select id from live_room
                 where actual_end_at &lt; date_sub(now(), interval 30 minute )
                and end_at > date_sub(now(), interval 1 day) and end_at &lt; now()
                and if_sync_ce = 0
                and deleted_at is null
                and
                 (org_id in (select id from global_organization where if_need_live_sync_sce = 1 and deleted_at is null)
                      or if_need_sync_ce = 1)
    </select>


    <select id="getLiveGoodsToSceGoods" resultType="com.bbh.live.dao.dto.livegoods.LiveGoodsDTO">
        SELECT
        lg.*,
        lg.id                   liveGoodsId,
        lg.start_price          startPrice,
        lg.subscribe_count      subscribeCount,
        lg.end_at               endAt,
        eg.deleted_at       as  erp_deleted_at,
        eg.code                 globalGoodsCode,
        eg.name                 globalGoodsName,
        eg.description          globalGoodsDescription,
        eg.img_url_list         imgUrlList,
        eg.quality              quality,
        eg.place_order_status   placeOrderStatus,
        eg.if_locked            erpIfLocked,
        eg.cost_price           costPrice,
        eg.peer_price           peerPrice,
        eg.place_order_status   erpPlaceOrderStatus,
        eg.sale_status          erpSaleStatus,
        eg.settlement_status    settleStatus,
        lg.platform_classify_id,
        lr.buyer_service_rate as live_room_buyer_service_rate,
        sg.auction_id  as auctionId,
        go.if_need_live_sync_sce as ifNeedPtSyncSce
        FROM
        live_goods lg
        LEFT JOIN erp_goods eg ON lg.global_goods_id = eg.id
        LEFT JOIN live_room lr ON lg.live_room_id = lr.id
        left join  sce_goods sg on lg.global_goods_id = sg.global_goods_id and sg.deleted_at is null
        left join global_organization go ON lr.org_id = go.id
        WHERE
        lg.deleted_at IS NULL
        <if test="queryParam.liveRoomId != null">
            and lg.live_room_id = #{queryParam.liveRoomId}
        </if>
        <if test="queryParam.belongUserId != null">
            and lg.belong_user_id = #{queryParam.belongUserId}
        </if>
        <if test="queryParam.belongSeatId != null">
            and lg.belong_seat_id = #{queryParam.belongSeatId}
        </if>
        <if test="queryParam.keywords != null and queryParam.keywords != ''">
            AND
            (eg.name like concat('%',#{queryParam.keywords},'%')  OR lg.live_goods_code = #{queryParam.codeKeywords} OR eg.description like concat('%',#{queryParam.keywords},'%'))
        </if>
        <if test="queryParam.liveGoodsIdList != null and queryParam.liveGoodsIdList.size() > 0">
            AND lg.id in
            <foreach collection="queryParam.liveGoodsIdList" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="queryParam.liveGoodsStatusList != null and queryParam.liveGoodsStatusList.size() > 0">
            AND
            <foreach collection="queryParam.liveGoodsStatusList" open="(" close=")" separator=" or " item="status">
                lg.goods_status = #{status}
            </foreach>
        </if>
        <if test="queryParam.filterLockedOrSoldOutGoods != null and queryParam.filterLockedOrSoldOutGoods">
            AND (eg.if_locked = 0 AND eg.place_order_status = 0 AND eg.sale_status = 1)
        </if>
        <if test="queryParam.filterOffhandGoods != null and queryParam.filterOffhandGoods">
            AND lg.if_offhand_goods = 0
        </if>
        <if test="queryParam.filterIfSyncCe != null and queryParam.filterIfSyncCe == true">
            AND lg.sce_goods_id > 0
        </if>
        <if test="queryParam.filterIfSyncCe != null and queryParam.filterIfSyncCe == false">
            AND lg.sce_goods_id = 0
        </if>
    </select>

</mapper>
