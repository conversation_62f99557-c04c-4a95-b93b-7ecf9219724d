<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bbh.live.dao.mapper.GlobalOrgSeatMapper">

    <select id="selectPageWithVip" resultType="com.bbh.live.dao.dto.SimpleUserInfoDTO">
        SELECT
            gos.*,
            gos.id seat_id,
            gos.name seat_name,
            vbc.id vip_card_id,
            vbc.exp,
            vbc.time_vip_start,
            vbc.time_vip_end,
            vbc.is_annual_fee_vip,
            go.name org_name,
            go.logo_url org_logo_url,
            go.type org_type
        FROM
            global_org_seat gos
        left join vip_buyer_card vbc on vbc.seat_id=gos.id and vbc.deleted_at is null
        left join global_organization go on gos.org_id = go.id and go.deleted_at is null
        WHERE gos.deleted_at is null
          AND gos.`status` = 1
        <if test="query.keyword != null and query.keyword != ''">
            AND gos.auction_code like CONCAT('%',#{query.keyword},'%')
        </if>
        <if test="query.auctionCode != null and query.auctionCode != ''">
            AND gos.auction_code = #{query.auctionCode}
        </if>
        <if test="query.seatId != null">
            AND gos.id = #{query.seatId}
        </if>
        <if test="query.seatIdList != null">
            AND gos.id in
            <foreach collection="query.seatIdList" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>

    </select>
    <select id="getSeatIdListByOrgRoleList" resultType="java.lang.Long">
        SELECT gos.id
        FROM global_org_seat gos
        WHERE gos.deleted_at is null
           AND
        <foreach collection="roleList" open="(" close=")" separator="OR" item="item">
            JSON_CONTAINS(gos.role_id_list, '"${item}"')
        </foreach>
    </select>

</mapper>
