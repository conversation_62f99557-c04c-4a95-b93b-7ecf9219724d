<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bbh.live.dao.mapper.VipWeeklySpuGoodsMapper">

    <select id="selectAllByWeeklySpuId" resultType="com.bbh.live.dao.dto.vo.WeeklySpuGoodsVO">
        select *
        from vip_weekly_spu_goods
        where weekly_spu_id = #{weeklySpuId}
        and deleted_at is null
        order by FIND_IN_SET(quality, 'N级,S级,A级,B级,C级,D级') ,
                 belong_datetime desc
    </select>

</mapper>
