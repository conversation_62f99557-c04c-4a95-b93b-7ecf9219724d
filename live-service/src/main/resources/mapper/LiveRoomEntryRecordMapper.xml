<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bbh.live.dao.mapper.LiveRoomEntryRecordMapper">

    <select id="statisticsEntry" resultType="com.bbh.live.dao.dto.LiveRoomEntryStatisticsDTO">
        SELECT
            IFNULL(COUNT(DISTINCT seat_id), 0) AS totalUserCount,
            IFNULL(SUM(user_total_duration), 0) AS total_duration,
            IFNULL(SUM(user_total_duration) / NULLIF(COUNT(DISTINCT seat_id), 0), 0) AS average_duration_per_user,
            IFNULL(MAX(user_total_duration), 0) AS max_duration_per_user
        FROM (
                 SELECT
                     seat_id,
                     SUM(duration) AS user_total_duration
                 FROM
                     live_room_entry_record
                 WHERE
                     live_room_id = #{liveRoomId}
                   AND leave_at IS NOT NULL
                 GROUP BY
                     seat_id
             ) AS user_durations;
    </select>
</mapper>
