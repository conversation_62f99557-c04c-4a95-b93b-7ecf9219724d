<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bbh.live.dao.mapper.GlobalOrganizationMapper">

    <select id="getAnomalyNameOrg" resultType="com.bbh.model.GlobalOrganization">
        select *from  global_organization o left join global_user u on o.master_user_id = u.id
        where (o.name = u.phone or o.name=u.pure_phone) and o.type=1
    </select>

    <select id="ifOrgHasNewGoods" resultType="java.util.Map">
        SELECT
        *
        FROM
        (SELECT
            eg.org_id as orgId,
            IF(eg.updated_at >= now() - INTERVAL 7 DAY, 1, 0) as hasNewGoods,
            ROW_NUMBER() OVER (PARTITION BY eg.org_id ORDER BY eg.updated_at desc) AS rn
        FROM erp_goods eg
        WHERE eg.deleted_at IS NULL
        AND eg.org_id IN
        <foreach collection="orgIdList" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
        ) AS subquery
        WHERE rn&lt;= 1;
    </select>

    <select id="getOrgDetailById" resultType="com.bbh.live.feign.dto.GlobalOrgDetailDTO">
        select
            o.name as org_name,
            o.id as org_id,
            u.phone,
            u.pure_phone,
            os.name as seat_name,
            o.type
        from global_organization o
                 left join global_user u on u.id = o.master_user_id
                 left join global_org_seat os on os.org_id = o.id and os.user_id = o.master_user_id
        where o.id = #{orgId}
    </select>

    <update id="batchUpdateOrgFenbei">
        <foreach collection="orgFenbeiList" item="orgFenbei" separator=";">
            UPDATE global_organization
            SET fenbei = #{orgFenbei.fenbeiNum}
            WHERE id = #{orgFenbei.orgId}
        </foreach>
    </update>

    <select id="getOrgAddressInfoByIdList" resultType="com.bbh.live.service.buyer.orgmap.vo.OrgAddressInfoVO">
        select
            go.id as id,
            go.name as orgName,
            go.logo_url as logoUrl,
            if(gos.nick_name is null, gos.name, gos.nick_name) as bossName,
            gos.id as seatId,
            go.address_name_path as addressDesc,
            go.address_detail as addressDetail,
            go.address_coordinate->>'$.lat' AS lat,
            go.address_coordinate->>'$.lng' AS lng
        from global_organization go
             left join global_org_seat gos on go.id = gos.org_id
             left join global_merchant_auth_record gmar on gmar.id=go.auth_success_id and gmar.deleted_at is null
        where go.id in
        <foreach collection="orgIdSet" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
        and gos.if_master = 1
        and go.if_org_contract=1
        and go.org_contract_end_time >=now()
        and gmar.channel=1
    </select>
    <select id="getMapOrg" resultType="com.bbh.model.GlobalOrganization">
        select
        go.*
        from global_organization go
        left join global_org_seat gos on go.id = gos.org_id
        left join global_merchant_auth_record gmar on gmar.id=go.auth_success_id and gmar.deleted_at is null
        where go.id in
        <foreach collection="ids" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
        and gos.if_master = 1
        and go.if_org_contract=1
        and go.org_contract_end_time >=now()
        and gmar.channel=1
    </select>
</mapper>
