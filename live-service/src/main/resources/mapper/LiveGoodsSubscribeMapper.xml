<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bbh.live.dao.mapper.LiveGoodsSubscribeMapper">

    <select id="getSubscribeGoodsList" resultType="com.bbh.live.dao.dto.livegoods.LiveGoodsSubscribeDTO">
        select
            lgs.live_goods_id as liveGoodsId
        from live_goods_subscribe lgs
        <where>
            lgs.deleted_at is null
            <if test="createUserId != null">
                and lgs.create_user_id = #{createUserId}
            </if>
        </where>
    </select>

    <select id="getSubscribeGoodsIdListByRoomId" resultType="java.lang.Long">
        select
        lgs.live_goods_id
        from live_goods_subscribe lgs
        left join live_goods lg on lgs.live_goods_id = lg.id
        <where>
            lgs.deleted_at is null
            and lgs.live_room_id = #{roomId}
            and lg.deleted_at is null
        </where>
    </select>

    <select id="getSubscribeGoodsInfoByBuyerSeatId" resultType="com.bbh.live.dao.dto.livegoods.LiveGoodsSubscribeDTO">
        select
            lgs.live_goods_id as liveGoodsId,
            lgs.if_handled as ifHandled,
            lgs.remark as remark
        from live_goods_subscribe lgs
        left join live_goods lg on lgs.live_goods_id = lg.id
        left join live_room lr on lr.id = lgs.live_room_id
        <where>
            lgs.deleted_at is null
            and lgs.create_seat_id = #{createSeatId}
            and lg.goods_status in (10, 20, 30)
            and lg.deleted_at is null
            and lr.deleted_at is null
            and lr.end_at > #{now}
        </where>
    </select>

</mapper>
