<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bbh.live.dao.mapper.VipWeeklySpuMapper">

    <select id="selectByWeeklySpuId" resultType="com.bbh.live.dao.dto.vo.WeeklySpuVO">
        select ws.*,
               bs.`spu_base_value` as `type` ,
               bsi.`size` as `size` ,
               bsi.colour as colour
        from vip_weekly_spu ws
                 left join bbh_erpv2.bbh_spu bs on ws.spu_id = bs.id
                 left join bbh_erpv2.bbh_spu_item bsi on bsi.spu_id = bs.id and bsi.deleted = 1
        where ws.id = #{id} and ws.deleted_at is null
    </select>

</mapper>
