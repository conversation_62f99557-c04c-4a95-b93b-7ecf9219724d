<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bbh.live.dao.mapper.VipBuyerPeepLogMapper">

    <select id="getPeepLogsBySeatId" resultType="com.bbh.live.dao.dto.VipPeepLogDTO">
        select
            vbpl.id,
            vbpl.peep_seat_id,
            vbpl.created_at,
            vbpl.bid_amount,
            vbpl.goods_name,
            vbpl.biz_name,
            vbpl.bid_seat_name,
            gos.auction_code
        from vip_buyer_peep_log vbpl
            left join global_org_seat gos on gos.id = vbpl.peep_seat_id
        where vbpl.peep_seat_id = #{seatId}
        order by vbpl.created_at desc
    </select>

    <select id="getPeepLogsByVipId" resultType="com.bbh.live.dao.dto.VipPeepLogDTO">
        select
            vbpl.id,
            vbpl.peep_seat_id,
            vbpl.created_at,
            vbpl.bid_amount,
            vbpl.goods_name,
            vbpl.biz_name,
            vbpl.bid_seat_name,
            gos.auction_code
        from vip_buyer_peep_log vbpl
                 left join global_org_seat gos on gos.id = vbpl.peep_seat_id
        where vbpl.vip_buyer_card_id = #{vipId}
        <if test="startAt != null and endAt != null">
            and vbpl.created_at between #{startAt} and #{endAt}
        </if>
        order by vbpl.created_at desc
    </select>
</mapper>
