<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bbh.live.dao.mapper.LiveRoomSubscribeMapper">

    <select id="getSubscribeLiveRoomBySeatId" resultType="com.bbh.live.dao.dto.vo.SubscribeLiveRoomVO">
        select lr.id                             as roomId,
               lr.room_name                      as roomName,
               lr.if_special                     as ifSpecial,
               lr.start_at                       as startAt,
               lr.end_at                         as endAt,
               lr.anchor_seat_id                 as anchorSeatId,
               IF(lr.start_at is not null and lr.start_at &lt;= #{currentDate}, 1, 0) AS inLive,
               lr.if_goods_list_visible          as ifGoodsListVisible,
               lg.count                          as liveGoodsCount,
               lg.wait_auction_count,
               go.id                             as orgId,
               go.name                           as orgName,
               go.logo_url                       as logoUrl
        from live_room_subscribe lrs
                 left join live_room lr on lrs.live_room_id = lr.id
                 left join global_organization go on lr.org_id = go.id
                 left join (
		            select live_room_id,
		                   count(*) as count,
		                   sum(case when goods_status in (10) then 1 else 0 end) as wait_auction_count
		            from live_goods
		            where deleted_at is null
		            group by live_room_id
		         ) as lg on lg.live_room_id = lr.id
        where lrs.create_seat_id = #{seatId}
          and lrs.deleted_at is null
          and lr.deleted_at is null
          and not lr.end_at &lt;= now()
        order by lr.start_at asc
    </select>

    <select id="getAttentionOrgListBySeatId" resultType="com.bbh.live.dao.dto.vo.AttentionOrgVO">
        SELECT
            go.id AS orgId,
            go.NAME AS orgName,
            go.logo_url AS logoUrl,
            go.type AS orgType,
            gsa.created_at AS attentionDate
        FROM
            global_seat_attention gsa
            LEFT JOIN global_organization go ON go.id = gsa.be_attended_org_id
        WHERE
            gsa.create_seat_id = #{seatId}
            <if test="keywords != null and keywords != ''">
                AND go.name LIKE concat('%', #{keywords}, '%')
            </if>
          AND gsa.deleted_at IS NULL
          AND go.deleted_at IS NULL
        order by gsa.updated_at DESC, gsa.created_at DESC
        ;
    </select>

    <select id="getAttentionOrgLiveByOrgIdList" resultType="com.bbh.live.dao.dto.vo.AttentionOrgVO">
        SELECT
        *
        FROM
            (SELECT
                lr.id AS roomId,
                lr.org_id AS orgId,
                lr.room_name AS roomName,
                lr.if_special AS ifSpecial,
                IF(lr.start_at IS NOT NULL AND lr.start_at &lt;= #{currentDate}, 1 , 0) AS inLive,
                lr.start_at AS startAt,
                ROW_NUMBER() OVER (PARTITION BY lr.org_id ORDER BY lr.start_at) AS rn
            FROM live_room lr
            WHERE deleted_at IS NULL
                AND end_at > #{currentDate} IS NULL
                AND org_id IN
                <foreach collection="orgIdList" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            ) AS subquery
        WHERE rn &lt;= 1;
    </select>
</mapper>
