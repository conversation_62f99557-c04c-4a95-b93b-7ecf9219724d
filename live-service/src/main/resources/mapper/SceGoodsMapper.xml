<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bbh.live.dao.mapper.SceGoodsMapper">



<select id="queryScePtAuctionIdIfLiveSync" resultType="long">
    SELECT
        id
    FROM
        sce_pt_auction
    WHERE
        deleted_at IS NULL
      and  pt_type = 1
      AND (`start_at` IS NULL OR `start_at` > NOW())
    LIMIT 1

</select>


    <select id="queryScePtAuctionIdNew" resultType="long">
        SELECT
            id
        FROM
            sce_pt_auction
        WHERE
            deleted_at IS NULL
        ORDER BY start_at asc
        LIMIT 1
    </select>

</mapper>
