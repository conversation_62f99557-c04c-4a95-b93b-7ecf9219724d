<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bbh.live.dao.mapper.LiveGoodsBuyerCancelRecordMapper">


    <select id="selectRecordDetailPage" resultType="com.bbh.live.service.buyerCancel.dto.LiveGoodsBuyerCancelRecordDetailDTO">
        SELECT
            lgbcr.*,
            lg.sell_price,
            lg.end_at as sell_at,
            lg.belong_seat_id,
            lg.belong_user_id,
            lg.belong_org_id,
            lg.belong_type,
            lg.trade_type,
            lg.live_goods_code,
            lg.live_video_url,
            lg.live_room_id,
            eg.name as goods_name,
            eg.img_url_list,
            eg.created_at as global_created_at,
            eg.peer_price,
            eg.quality,
            gos.show_name as belong_show_name,
            gos.name as belong_name,
            gos.nick_name as belong_nick_name,
            gos.auction_code as belong_auction_code,
            gos.avatar as belong_avatar,
            go2.name as seller_org_name,
            go2.logo_url as seller_logo_url,
            go3.name as buyer_org_name,
            go3.logo_url as buyer_logo_url,
            lr.start_at as live_room_start_at,
            lr.room_name as live_room_name
        FROM live_goods_buyer_cancel_record lgbcr
        LEFT JOIN live_goods lg ON lg.id = lgbcr.live_goods_id
        LEFT JOIN live_room lr ON lr.id = lgbcr.live_room_id
        LEFT JOIN erp_goods eg ON eg.id = lgbcr.global_goods_id
        LEFT JOIN global_organization go2 ON go2.id = lgbcr.seller_org_id
        LEFT JOIN global_org_seat gos ON gos.id = lg.belong_seat_id
        LEFT JOIN global_organization go3 ON go3.id = lgbcr.buyer_org_id
        where lgbcr.deleted_at is null
        AND lgbcr.biz_type = #{query.bizType}
        <if test="query.cancelStatus != null">
            AND lgbcr.cancel_status = #{query.cancelStatus}
        </if>
        <if test="query.buyerOrgId != null">
            AND lgbcr.buyer_org_id = #{query.buyerOrgId}
        </if>
        <if test="query.sellerOrgId != null">
            AND lgbcr.seller_org_id = #{query.sellerOrgId}
        </if>
        <if test="query.buyerSeatId != null">
            AND lgbcr.buyer_seat_id = #{query.buyerSeatId}
        </if>
        <if test="query.recordId != null">
            AND lgbcr.id = #{query.recordId}
        </if>
        ORDER BY lgbcr.id DESC
    </select>

    <select id="sceRecordDetailPage" resultType="com.bbh.live.service.buyerCancel.dto.LiveGoodsBuyerCancelRecordDetailDTO">
        SELECT
        lgbcr.*,
        sg.sell_price,
        sg.sell_at as sell_at,
        sg.belong_seat_id,
        sg.belong_user_id,
        sg.belong_org_id,
        sg.belong_type,
        eg.name as goods_name,
        eg.img_url_list,
        eg.created_at as global_created_at,
        eg.peer_price,
        eg.quality,
        eg.video_url_list,
        gos.show_name as belong_show_name,
        gos.name as belong_name,
        gos.nick_name as belong_nick_name,
        gos.auction_code as belong_auction_code,
        gos.avatar as belong_avatar,
        go2.name as seller_org_name,
        go2.logo_url as seller_logo_url,
        go3.name as buyer_org_name,
        go3.logo_url as buyer_logo_url
        FROM live_goods_buyer_cancel_record lgbcr
        LEFT JOIN sce_goods sg ON sg.id = lgbcr.live_goods_id
        LEFT JOIN erp_goods eg ON eg.id = lgbcr.global_goods_id
        LEFT JOIN global_organization go2 ON go2.id = lgbcr.seller_org_id
        LEFT JOIN global_org_seat gos ON gos.id = sg.belong_seat_id
        LEFT JOIN global_organization go3 ON go3.id = lgbcr.buyer_org_id
        where lgbcr.deleted_at is null
        AND lgbcr.biz_type = #{query.bizType}
        <if test="query.cancelStatus != null">
            AND lgbcr.cancel_status = #{query.cancelStatus}
        </if>
        <if test="query.buyerOrgId != null">
            AND lgbcr.buyer_org_id = #{query.buyerOrgId}
        </if>
        <if test="query.sellerOrgId != null">
            AND lgbcr.seller_org_id = #{query.sellerOrgId}
        </if>
        <if test="query.buyerSeatId != null">
            AND lgbcr.buyer_seat_id = #{query.buyerSeatId}
        </if>
        <if test="query.recordId != null">
            AND lgbcr.id = #{query.recordId}
        </if>
        ORDER BY lgbcr.id DESC
    </select>

</mapper>
