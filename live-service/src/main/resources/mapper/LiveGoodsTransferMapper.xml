<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bbh.live.dao.mapper.LiveGoodsTransferMapper">

    <select id="getTransferredGoodsList" resultType="com.bbh.live.dao.dto.TransferredGoodsDTO">
        select
            lgt.id as transfer_id,
            lgt.transfer_price,
            lgt.target_user_id,
            lgt.target_user_id,
            lgt.target_org_id,
            lgt.if_handled,
            lgt.expire_at,
            lgt.created_at as transfer_created_at,
            lg.*,
            eg.code globalGoodsCode,
            eg.name globalGoodsName,
            eg.img_url_list imgUrlList,
            eg.quality quality,
            eg.place_order_status placeOrderStatus,
            eg.if_locked ifLocked,
            eg.cost_price costPrice,
            eg.peer_price peerPrice,
            eg.place_order_status as erp_place_order_status,
            eg.sale_status as erp_sale_status,
            eg.settlement_status settleStatus,
            eg.if_locked as erp_if_locked
        from live_goods_transfer lgt
        left join live_goods lg on lg.id = lgt.live_goods_id
        left join erp_goods eg on eg.id = lg.global_goods_id
        <where>
            <if test="queryParam.liveGoodsIdList != null">
                AND lgt.live_goods_id in
                <foreach collection="queryParam.liveGoodsIdList" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
            <if test="queryParam.liveRoomId != null">
                and lgt.live_room_id = #{queryParam.liveRoomId}
            </if>
            <if test="queryParam.targetUserId != null">
                and lgt.target_user_id = #{queryParam.targetUserId}
            </if>
            <if test="queryParam.ifHandled != null">
                and lgt.if_handled = #{queryParam.ifHandled}
            </if>
        </where>
    </select>

</mapper>
