<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bbh.live.dao.mapper.VipBuyerCardMapper">
    <update id="updateTimesBySeatId">
        UPDATE vip_buyer_card
        SET ${timesType}_used_times = ${timesType}_used_times + #{num}
        WHERE seat_id = #{seatId}
    </update>
    <update id="updateTimesBySeatIdAndFenbei">
        UPDATE vip_buyer_card
        SET ${timesType}_used_times = ${timesType}_used_times + #{num},
            total_get_fenbei=total_get_fenbei + #{refundFenbei}
        WHERE seat_id = #{seatId}
    </update>

    <select id="getUserBuyerVipInfoBySeatId" resultType="com.bbh.live.service.buyer.vip.vo.UserBuyerVipInfoVO">
        SELECT id,
               seat_id,
               org_id,
               time_vip_end,
               exp,
               is_annual_fee_vip,
               save_money,
               peep_buyer_used_times,
               lottery_used_times,
               modify_nickname_used_times,
               after_sale_service_used_times,
               underwater,
               save_fenbei,
               total_get_fenbei,
               vip_invite_code,
               black_card_no,
               offline_fb_deduct_num,
               offline_fb_deduct_times,
               create_from
        FROM vip_buyer_card
        WHERE seat_id = #{seatId}
        AND deleted_at IS NULL
    </select>
    <select id="getUserBuyerVipInfoBySeatIdList"
            resultType="com.bbh.live.service.buyer.vip.vo.UserBuyerVipInfoVO">
        SELECT id,
        seat_id,
        org_id,
        time_vip_end,
        exp,
        is_annual_fee_vip,
        save_money,
        peep_buyer_used_times,
        lottery_used_times,
        modify_nickname_used_times,
        after_sale_service_used_times,
        underwater,
        save_fenbei,
        total_get_fenbei,
        vip_invite_code,
        black_card_no
        FROM vip_buyer_card
        WHERE seat_id in
        <foreach collection="seatIdList" item="seatId" open="(" separator="," close=")">
            #{seatId}
        </foreach>
    </select>

    <select id="getOrgVipResources" resultType="com.bbh.live.dao.dto.OrgVipResourcePO">
        select
            vbc.id                  vipId,
            vbc.`exp`               exp,
            vbc.is_annual_fee_vip 	ifAnnualFeeVip,
            vbc.time_vip_end        vipEndTime,
            gos.id                  seatid,
            gos.avatar              avatar,
            concat(gos.show_name, "/", gu.pure_phone) as seatName,
            vbc.create_from
        from vip_buyer_card vbc
                 left join global_org_seat gos on gos.id=vbc.seat_id
                 left join global_user gu on gu.id = gos.user_id
        where vbc.org_id = #{orgId}
          and vbc.deleted_at is null
        order by first_open_time desc
    </select>

    <select id="getUserBuyerVipInfoByVipId" resultType="com.bbh.live.service.buyer.vip.vo.UserBuyerVipInfoVO">
        SELECT id,
               seat_id,
               org_id,
               time_vip_end,
               exp,
               is_annual_fee_vip,
               save_money,
               peep_buyer_used_times,
               lottery_used_times,
               modify_nickname_used_times,
               after_sale_service_used_times,
               underwater,
               save_fenbei,
               total_get_fenbei,
               vip_invite_code,
               black_card_no,
               offline_fb_deduct_num,
               offline_fb_deduct_times,
               create_from
        FROM vip_buyer_card
        WHERE id = #{vipId}
          AND deleted_at IS NULL
    </select>
</mapper>
