<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bbh.live.dao.mapper.GlobalOrderGoodsSnapshotMapper">

    <select id="selectSnapshotGoodsInfo" resultType="com.bbh.live.dao.dto.GlobalOrderGoodsSnapshotDTO">
        select
            eg.id as erpGoodsId,
            eg.org_id as orgId,
            eg.name as name,
            eg.code as code,
            eg.type as type,
            eg.classify_id as classifyId,
            ggc.name as classifyName,
            eg.storehouse_id as storehouseId,
            es.name as storehouseName,
            eg.brand_id as brandId,
            eg.spu_id as spuId,
            eg.img_url_list as imgUrlList,
            eg.description as description,
            eg.quality as quality,
            eg.remark as remark
        from erp_goods eg
            left join global_goods_classify ggc on ggc.id = eg.classify_id
            left join erp_storehouse es on es.id = eg.storehouse_id
        where eg.id in
        <foreach collection="erpGoodsIdSet" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        and eg.deleted_at is null
    </select>
</mapper>
