<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bbh.live.dao.mapper.LiveOrgFilterMapper">


    <select id="searchUser" resultType="com.bbh.live.dao.dto.vo.LiveOrgFilterVO">
        SELECT
        gos.id seat_id,
        gos.org_id,
        gos.user_id,
        gos.name userName,
        gos.avatar,
        gos.auction_code,
        gos.nick_name,
        vbc.id vip_card_id,
        vbc.exp,
        vbc.time_vip_start,
        vbc.time_vip_end,
        vbc.is_annual_fee_vip
        FROM
        global_org_seat gos
        left join vip_buyer_card vbc on vbc.seat_id=gos.id and vbc.deleted_at is null
        WHERE gos.deleted_at is null
        AND gos.`status`=1
        AND gos.auction_code like CONCAT('%',#{searchKey},'%')
    </select>

    <select id="pageListUser" resultType="com.bbh.live.dao.dto.vo.LiveOrgFilterVO">
        SELECT
        lof.id id,
        lof.source_type ,
        lof.filter_mode ,
        gos.id seat_id,
        gos.org_id,
        gos.user_id,
        gos.name userName,
        gos.avatar,
        gos.auction_code,
        gos.nick_name,
        gos.show_name,
        lof.remark,
        lof.created_at,
        vbc.id vip_card_id,
        vbc.exp,
        vbc.time_vip_start,
        vbc.time_vip_end,
        vbc.is_annual_fee_vip
        FROM
        live_org_filter lof
        INNER join   global_org_seat gos on lof.seat_id= gos.id
        left join vip_buyer_card vbc on vbc.seat_id=gos.id and vbc.deleted_at is null
        WHERE lof.deleted_at is null
        and gos.deleted_at is null
          <if test="sourceTypeCode != null">
              and lof.source_type= #{sourceTypeCode}
          </if>
          <if test="filterMode != null">
              and lof.filter_mode= #{filterMode}
          </if>
        AND lof.org_id =#{orgId}
        order by  lof.created_at desc
    </select>

    <select id="searchOrg" resultType="com.bbh.live.dao.dto.vo.LiveOrgFilterVO">
        SELECT
        go.id  orgId,
        go.name  orgName,
        go.logo_url orgLogoUrl,
        go.master_user_id  orgMasterUserId,
        lof.id  id,
        ( CASE WHEN lof.id IS NOT NULL THEN 1 ELSE 0 END ) has
        FROM
        global_organization  go
        LEFT JOIN  live_org_filter lof on lof.org_id=go.id AND lof.source_type=10 AND lof.user_id = #{userId}
        WHERE go.deleted_at is null
        AND lof.deleted_at is null
        <if test="searchKey != null and searchKey != ''">
              AND  go.name like CONCAT('%',#{searchKey},'%')
        </if>
    </select>

    <select id="pageListOrg" resultType="com.bbh.live.dao.dto.vo.LiveOrgFilterVO">
        SELECT
        go.id  orgId,
        go.name  orgName,
        go.logo_url orgLogoUrl,
        go.master_user_id  orgMasterUserId,
        lof.id  id
        FROM
        global_organization  go
        INNER JOIN  live_org_filter lof on lof.org_id=go.id AND lof.source_type=10 AND lof.user_id = #{userId}
        WHERE lof.deleted_at is null
        order by  lof.created_at desc
    </select>
</mapper>
