<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bbh.live.dao.mapper.LiveRateTemplateMapper">

    <select id="getRateTemplateItemList">
        select
            lrti.*,
            lrt.name as template_name,
            lr.id as room_id
        from live_rate_template_item lrti
                 left join live_rate_template lrt on lrti.template_id = lrt.id
                 left join live_room lr on lr.rate_template_id = lrt.id
        where lrti.deleted_at is null
          and lrt.deleted_at is null
          and lr.deleted_at is null
          and lr.id in
        <foreach collection="roomIdList" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>

</mapper>
