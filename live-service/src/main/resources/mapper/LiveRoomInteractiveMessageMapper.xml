<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bbh.live.dao.mapper.LiveRoomInteractiveMessageMapper">

    <select id="getUnhandledMessageList" resultType="com.bbh.live.dao.dto.UnHandleBargainGoodsMsg">
        select
            lrim.id as msgId,
            lg.id as liveGoodsId,
            lg.live_goods_code as goodsCode,
            lg.trade_type as tradeType,
            lg.start_price as startPrice,
            eg.name as goodsName,
            eg.img_url_list as imgUrlList,
            eg.quality as quality,
            eg.peer_price as peerPrice,
            eg.cost_price as costPrice,
            lrim.extra_data -> "$.bidPrice" as bargainPrice,
            lrim.create_seat_id as seatId
        from live_room_interactive_message lrim
            left join live_goods lg on lg.id = lrim.live_goods_id
            left join erp_goods eg on eg.id = lg.global_goods_id
        where lrim.live_room_id = #{roomId}
            and lrim.deleted_at is null
            and lrim.handle_status = 0
            and lrim.type = 60
        order by lrim.created_at desc
    </select>

    <select id="getUnhandledMessageCount" resultType="java.lang.Long">
        select
            count(lrim.id)
        from live_room_interactive_message lrim
        where lrim.live_room_id = #{roomId}
          and lrim.deleted_at is null
          and lrim.handle_status = 0
          and lrim.type = 60
    </select>
</mapper>
