<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bbh.live.dao.mapper.GlobalAuctionPremiumNumberMapper">


    <select id="getOrgAuctionNumberResources"
            resultType="com.bbh.live.service.organization.dto.OrgAuctionNumberResourcesDTO">
        select
            gapn.id         auctionNumberId,
            gapn.`number`   auctionNumber,
            gapn.org_id     orgId,
            gapn.seat_id    seatId,
            gos.avatar      avatar,
            concat(if (gos.nick_name is null, gos.name, gos.nick_name), "/", gu.pure_phone) as seatName
        from
            global_auction_premium_number gapn
        left join global_org_seat gos on gos.id = gapn.seat_id
        left join global_user gu on gu.id = gos.user_id
        where
            gapn.org_id = #{orgId}
        and gapn.deleted_at is null
    </select>
</mapper>
