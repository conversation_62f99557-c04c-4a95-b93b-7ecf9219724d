<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bbh.live.dao.mapper.LiveRoomMapper">

    <select id="selectRoomVOPage" resultType="com.bbh.live.dao.dto.vo.LiveRoomVO">
        select
            r.*,
            o.name as org_name,
            o.logo_url as org_logo_url,
            <!-- 关注状态 -->
            case when gsa.id is not null then 1 else 0 end as if_followed,
            <!-- 预约状态 -->
            case when lrs.id is not null then 1 else 0 end as if_subscribed,
            <!-- 费率模板名称 -->
            lrt.name as rate_template_name,
            <!-- 直播状态 -->
            CASE
                <!-- 未开播 -->
                WHEN r.start_at &gt; NOW() THEN 0
                <!-- 直播中 -->
                WHEN r.start_at &lt;= NOW() AND r.end_at &gt;= NOW() THEN 1
                <!-- 已结束 -->
                WHEN r.end_at &lt; NOW() THEN 2
                ELSE 99
            END AS room_status,
            <!-- 直播状态排序字段 -->
            CASE
                <!-- 未开播 -->
                WHEN r.start_at &gt; NOW() THEN 1
                <!-- 直播中 -->
                WHEN r.start_at &lt;= NOW() AND r.end_at &gt;= NOW() THEN 0
                <!-- 已结束 -->
                WHEN r.end_at &lt; NOW() THEN 2
                ELSE 99
            END AS room_sort_status
        from live_room r
        left join global_organization o on o.id = r.org_id
        <!-- 商户席位关注记录 -->
        left join global_seat_attention gsa
            on gsa.be_attended_org_id = r.org_id
                   and gsa.deleted_at is null
                   and gsa.create_user_id = #{query.currentUserId}
                   and gsa.create_seat_id = #{query.currentSeatId}
        <!-- 直播间预约记录 -->
        left join live_room_subscribe lrs
            on lrs.live_room_id = r.id
                   and lrs.deleted_at is null
                   and lrs.create_user_id = #{query.currentUserId}
                   and lrs.create_seat_id = #{query.currentSeatId}
        <!-- 费率 -->
        left join live_rate_template lrt
            on lrt.id = r.rate_template_id
                   and lrt.deleted_at is null
        <!-- 软删除字段 -->
        where r.deleted_at is null
        <if test="query.showable != null">
            and r.showable = #{query.showable}
        </if>
        <if test="query.roomName != null and query.roomName != ''">
            and r.room_name like concat('%',#{query.roomName},'%')
        </if>
        <if test="query.roomId != null">
            and r.id = #{query.roomId}
        </if>
        <if test="query.orgId != null">
            and r.org_id = #{query.orgId}
        </if>
        <if test="query.orgName != null and query.orgName != ''">
            and o.name like concat('%',#{query.orgName},'%')
        </if>
        <!-- 开播状态 - 优化为直接时间比较 -->
        <if test="query.status != null">
            <choose>
                <when test="query.status == 0">
                    <!-- 未开播 -->
                    and r.start_at &gt; NOW()
                </when>
                <when test="query.status == 1">
                    <!-- 直播中 -->
                    and r.start_at &lt;= NOW() AND r.end_at &gt;= NOW()
                </when>
                <when test="query.status == 2">
                    <!-- 已结束 -->
                    and r.end_at &lt; NOW()
                </when>
                <otherwise>
                    <!-- 其他状态 -->
                    and NOT (r.start_at &gt; NOW() OR (r.start_at &lt;= NOW() AND r.end_at &gt;= NOW()) OR r.end_at &lt; NOW())
                </otherwise>
            </choose>
        </if>
        <!-- 直播流状态 -->
        <if test="query.streamStatus != null">
            and r.stream_status = #{query.streamStatus}
        </if>
        <!-- 是否推荐 -->
        <choose>
            <when test="query.ifRecommend != null and query.ifRecommend">
                and r.if_recommend = 1
            </when>
            <when test="query.ifRecommend != null and !query.ifRecommend">
                and r.if_recommend = 0
            </when>
        </choose>
        <!-- 开播状态支持多选 - 优化为直接时间比较 -->
        <if test="query.statusList != null and query.statusList.size() &gt; 0">
            <choose>
                <!-- 如果只包含一个状态，直接用时间比较 -->
                <when test="query.statusList.size() == 1">
                    <choose>
                        <when test="query.statusList.contains(0)">
                            <!-- 未开播 -->
                            and r.start_at &gt; NOW()
                        </when>
                        <when test="query.statusList.contains(1)">
                            <!-- 直播中 -->
                            and r.start_at &lt;= NOW() AND r.end_at &gt;= NOW()
                        </when>
                        <when test="query.statusList.contains(2)">
                            <!-- 已结束 -->
                            and r.end_at &lt; NOW()
                        </when>
                    </choose>
                </when>
                <!-- 多个状态时用OR组合 -->
                <otherwise>
                    and (
                        <trim suffixOverrides="OR">
                            <if test="query.statusList.contains(0)">
                                <!-- 未开播 -->
                                r.start_at &gt; NOW() OR
                            </if>
                            <if test="query.statusList.contains(1)">
                                <!-- 直播中 -->
                                (r.start_at &lt;= NOW() AND r.end_at &gt;= NOW()) OR
                            </if>
                            <if test="query.statusList.contains(2)">
                                <!-- 已结束 -->
                                r.end_at &lt; NOW() OR
                            </if>
                        </trim>
                    )
                </otherwise>
            </choose>
        </if>
        <!-- 过滤模式支持多选 -->
        <if test="query.filterModeList != null and query.filterModeList.size() &gt; 0">
            and r.filter_mode in
            <foreach collection="query.filterModeList" index="index" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <!-- 排除掉主播 -->
        <if test="query.notInAnchorIdList != null and query.notInAnchorIdList.size() &gt; 0">
          and r.anchor_seat_id not in
          <foreach collection="query.notInAnchorIdList" index="index" item="item" open="(" close=")" separator=",">
            #{item}
          </foreach>
        </if>
        <!-- 排除掉导播 -->
        <if test="query.notInDirectorIdList != null and query.notInDirectorIdList.size() &gt; 0">
          and not exists(
            SELECT 1
            FROM live_room_director d
            WHERE d.live_room_id = r.id
            AND d.deleted_at IS NULL
            AND d.director_seat_id IN
            <foreach collection="query.notInDirectorIdList" index="index" item="item" open="(" close=")" separator=",">
              #{item}
            </foreach>
          )
        </if>
        <!-- 过滤白名单 -->
        <if test="query.filterWhitelist != null and query.filterWhitelist">
            AND (
                <!-- 不处理黑名单 -->
                r.filter_mode = 20
                OR (
                    r.filter_mode = 10
                    AND (
                        <choose>
                            <!-- 如果过滤白名单可见  -->
                            <when test="query.filterWhitelistShow != null and query.filterWhitelistShow">
                                <!-- 如果白名单可见，则总是包括 -->
                                r.whitelist_show = 1
                                <!-- 或者白名单不可见但用户在白名单中 -->
                                OR (
                                    r.whitelist_show = 0
                                    AND EXISTS (
                                        <!-- 子查询检查用户是否在白名单中 -->
                                        SELECT 1
                                        FROM live_org_filter lof
                                        WHERE lof.org_id = r.org_id
                                        AND lof.user_id = #{query.currentUserId}
                                        <!-- 确保是白名单模式 -->
                                        AND lof.filter_mode = 10
                                        AND lof.deleted_at IS NULL
                                    )
                                )
                            </when>
                            <otherwise>
                                EXISTS (
                                        <!-- 子查询检查用户是否在白名单中 -->
                                        SELECT 1
                                        FROM live_org_filter lof
                                        WHERE lof.org_id = r.org_id
                                        AND lof.user_id = #{query.currentUserId}
                                        <!-- 确保是白名单模式 -->
                                        AND lof.filter_mode = 10
                                        AND lof.deleted_at IS NULL
                                    )
                            </otherwise>
                        </choose>
                    )
                )
            )
        </if>
        <if test="query.filterBlacklist != null and query.filterBlacklist">
            AND NOT EXISTS (
                SELECT 1
                FROM live_org_filter lofb
                WHERE lofb.org_id = r.org_id
                AND lofb.user_id = #{query.currentUserId}
                AND lofb.filter_mode = 20
                AND lofb.deleted_at IS NULL
            )
        </if>
        <if test="query.ifDirector != null and query.ifDirector">
            <!-- 只显示主播或导播的直播间 -->
<!--            AND (-->
<!--                &lt;!&ndash; 如果是主播 &ndash;&gt;-->
<!--                r.anchor_seat_id = #{query.currentSeatId}-->
<!--                OR-->
<!--                &lt;!&ndash; 如果是导播 &ndash;&gt;-->
<!--                EXISTS (-->
<!--                    SELECT 1-->
<!--                    FROM live_room_director lrd-->
<!--                    WHERE lrd.live_room_id = r.id-->
<!--                    AND lrd.director_seat_id = #{query.currentSeatId}-->
<!--                    AND lrd.deleted_at IS NULL-->
<!--                )-->
<!--            )-->
        </if>

        <if test="query.hasDeal != null and query.hasDeal">
            <!-- 是否有成交过的 -->
            AND r.sold_count &gt; 0
        </if>
        <choose>
            <when test="query.ifDirector != null and query.ifDirector">
                <choose>
                    <when test="query.status != null and query.status == 2">
                        <!-- 历史场次 按开播时间降序 -->
                        order by r.start_at desc
                    </when>
                    <otherwise>
                        <!-- 直播管理，按开播时间升序 -->
                        order by r.start_at asc
                    </otherwise>
                </choose>
            </when>
            <otherwise>
                <choose>
                    <when test="query.status != null and query.status == 1">
                        order by r.if_special desc, r.if_recommend desc, r.sort desc, r.score desc, r.start_at desc, r.filter_mode desc
                    </when>
                    <when test="query.status != null and query.status == 0">
                        order by r.start_at asc, r.filter_mode desc, r.created_at asc, r.sort desc
                    </when>
                    <when test="query.status != null and query.status == 2">
                        order by r.start_at desc, r.filter_mode desc
                    </when>
                    <otherwise>
                        order by room_sort_status asc, r.if_special desc, r.if_recommend desc, r.sort desc, r.score desc, r.start_at desc, r.filter_mode desc
                    </otherwise>
                </choose>
            </otherwise>
        </choose>
    </select>


    <select id="selectMiniRoomVOPage" resultType="com.bbh.live.dao.dto.vo.LiveRoomVO">
        select
        r.*,
        o.name as org_name,
        o.logo_url as org_logo_url,
        <!-- 费率模板名称 -->
        lrt.name as rate_template_name,
        <!-- 直播状态 -->
        rs.room_status
        from live_room r
        left join global_organization o on o.id = r.org_id
        <!-- 费率 -->
        left join live_rate_template lrt
        on lrt.id = r.rate_template_id
        and lrt.deleted_at is null
        LEFT JOIN (
        SELECT
        r.id AS live_room_id,
        <!-- 直播状态 -->
        CASE
        <!-- 未开播 -->
        WHEN r.start_at &gt; NOW() THEN 0
        <!-- 直播中 -->
        WHEN r.start_at &lt;= NOW() AND r.end_at >= NOW() THEN 1
        <!-- 已结束 -->
        WHEN r.end_at &lt; NOW() THEN 2
        ELSE 99
        END AS room_status,
        <!-- 直播状态排序 -->
        CASE
        <!-- 未开播 -->
        WHEN r.start_at &gt; NOW() THEN 1
        <!-- 直播中 -->
        WHEN r.start_at &lt;= NOW() AND r.end_at >= NOW() THEN 0
        <!-- 已结束 -->
        WHEN r.end_at &lt; NOW() THEN 2
        ELSE 99
        END AS room_sort_status
        FROM live_room r
        WHERE r.deleted_at IS NULL
        ) rs ON rs.live_room_id = r.id
        <!-- 软删除字段 -->
        where r.deleted_at is null
        <if test="query.showable != null">
            and r.showable = #{query.showable}
        </if>
        <if test="query.roomName != null and query.roomName != ''">
            and r.room_name like concat('%',#{query.roomName},'%')
        </if>
        <if test="query.roomId != null">
            and r.id = #{query.roomId}
        </if>
        <if test="query.orgId != null">
            and r.org_id = #{query.orgId}
        </if>
        <if test="query.orgName != null and query.orgName != ''">
            and o.name like concat('%',#{query.orgName},'%')
        </if>
        <!-- 开播状态 -->
        <if test="query.status != null">
            and rs.room_status = #{query.status}
        </if>
        <!-- 直播流状态 -->
        <if test="query.streamStatus != null">
            and r.stream_status = #{query.streamStatus}
        </if>
        <choose>
            <when test="query.ifRecommend != null and query.ifRecommend">
                and r.if_recommend = 1
            </when>
            <when test="query.ifRecommend != null and !query.ifRecommend">
                and r.if_recommend = 0
            </when>
        </choose>
        <!-- 开播状态支持多选 -->
        <if test="query.statusList != null and query.statusList.size() &gt; 0">
            and rs.room_status in
            <foreach collection="query.statusList" index="index" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <!-- 过滤模式支持多选 -->
        <if test="query.filterModeList != null and query.filterModeList.size() &gt; 0">
            and r.filter_mode in
            <foreach collection="query.filterModeList" index="index" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <!-- 排除掉主播 -->
        <if test="query.notInAnchorIdList != null and query.notInAnchorIdList.size() &gt; 0">
            and r.anchor_seat_id not in
            <foreach collection="query.notInAnchorIdList" index="index" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <!-- 排除掉导播 -->
        <if test="query.notInDirectorIdList != null and query.notInDirectorIdList.size() &gt; 0">
            and not exists(
            SELECT 1
            FROM live_room_director d
            WHERE d.live_room_id = r.id
            AND d.deleted_at IS NULL
            AND d.director_seat_id IN
            <foreach collection="query.notInDirectorIdList" index="index" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
            )
        </if>
        <!-- 过滤白名单 -->
        <if test="query.filterWhitelist != null and query.filterWhitelist">
            AND (
            <!-- 不处理黑名单 -->
            r.filter_mode = 20
            OR (
            r.filter_mode = 10
            AND (
            <!-- 如果白名单可见，则总是包括 -->
            r.whitelist_show = 1
            <!-- 或者白名单不可见但用户在白名单中 -->
            OR (
            r.whitelist_show = 0
            AND EXISTS (
            <!-- 子查询检查用户是否在白名单中 -->
            SELECT 1
            FROM live_org_filter lof
            WHERE lof.org_id = r.org_id
            AND lof.user_id = #{query.currentUserId}
            <!-- 确保是白名单模式 -->
            AND lof.filter_mode = 10
            AND lof.deleted_at IS NULL
            )
            )
            )
            )
            )
        </if>
        <if test="query.filterBlacklist != null and query.filterBlacklist">
            AND NOT EXISTS (
            SELECT 1
            FROM live_org_filter lofb
            WHERE lofb.org_id = r.org_id
            AND lofb.user_id = #{query.currentUserId}
            AND lofb.filter_mode = 20
            AND lofb.deleted_at IS NULL
            )
        </if>
        <choose>
            <when test="query.ifDirector != null and query.ifDirector">
                <!-- 历史场次和直播管理，按开播时间升序 -->
                order by r.start_at asc
            </when>
            <otherwise>
                order by room_sort_status asc, r.if_special desc, r.if_recommend desc, r.sort desc, r.score desc, r.start_at desc, r.filter_mode desc
            </otherwise>
        </choose>
    </select>



    <select id="selectRecommendLiveRoom" resultType="com.bbh.live.dao.dto.vo.LiveRoomRecommendVO">
        select
            r.id,
            o.name as org_name,
            o.logo_url as org_logo_url,
            case when o.auth_success_id >0 then 1 else 0 end as if_authed
        from
        (
            select
                min(r.id) mid,
                max(r.score) score
            from live_room r
            where deleted_at is null and stream_status = 10
            group by org_id
            order by score desc
            limit 3
        ) ts
        join live_room r on r.id=ts.mid
        left join global_organization o on o.id = r.org_id
        order by ts.score desc
    </select>

    <!--  回放列表  -->
    <select id="selectPlaybackRoomVOPage" resultType="com.bbh.live.dao.dto.vo.PlayBackLiveRoomVO">
        select
            r.id as roomId,
            r.room_name as roomName,
            r.before_cover_img_url as roomCoverUrl,
            r.if_special as ifSpecial,
            r.org_id as orgId,
            r.start_at,
            go.name as orgName
        from live_room r
        left join global_organization go on go.id = r.org_id
        where r.if_sync_ce = 1 and r.actual_end_at > DATE_SUB(NOW(), INTERVAL r.goods_list_duration HOUR)
        and r.deleted_at is null
        and r.stream_status = 0
        <if test="filterBlacklist != null and filterBlacklist">
            <include refid="LIVE_ROOM_ORG_FILTER_SQL"/>
        </if>
        order by r.if_special desc, r.sort desc, r.score desc, r.start_at desc, r.filter_mode desc
    </select>

    <select id="getConflictLiveCount" resultType="java.lang.Long">
        select count(1)
        from live_room
        where org_id = #{orgId}
          and (
            (#{startAt} &lt;= end_at and #{endAt} >= end_at)
                or
            (#{startAt} &lt;= start_at and #{endAt} >= start_at)
                or
            (#{startAt} >= start_at and #{endAt} &lt;= end_at)
            );
    </select>

    <!--  正在直播中的直播间  -->
    <select id="getInLiveRoomList" resultType="com.bbh.model.LiveRoom">
        select
            r.id,
            r.room_name,
            r.stream_status,
            r.start_at,
            r.end_at,
            r.org_id,
            r.if_special
        from live_room r
        where r.stream_status = 10
        and r.deleted_at is null
        and r.start_at &lt;= #{now}
        and r.end_at >= #{now}
        <if test="filterBlacklist != null and filterBlacklist">
            <include refid="LIVE_ROOM_ORG_FILTER_SQL"/>
        </if>
        order by r.if_special desc, r.sort desc, r.score desc, r.start_at desc, r.filter_mode desc
    </select>

    <!--  专门给上帝视角用的  -->
    <select id="getGodViewRoomList" resultType="com.bbh.model.LiveRoom">
        select
        r.id,
        r.room_name,
        r.stream_status,
        r.start_at,
        r.end_at,
        r.org_id,
        r.if_special
        from live_room r
        where r.stream_status = 10
        and r.deleted_at is null
        and r.start_at &lt;= now()
        and r.end_at >= now()
        <!-- 只查黑名单，排除白名单 -->
        and r.filter_mode = 20
        AND NOT EXISTS (
            SELECT 1
            FROM live_org_filter lofb
            WHERE lofb.org_id = r.org_id
              AND lofb.user_id = #{currentUserId}
              AND lofb.filter_mode = 20
              AND lofb.deleted_at IS NULL
        )
        order by r.if_special desc, r.sort desc, r.score desc, r.start_at desc, r.filter_mode desc
    </select>


    <sql id="LIVE_ROOM_ORG_FILTER_SQL">
        AND (
        <!-- 不处理黑名单 -->
            r.filter_mode = 20
            OR (
                r.filter_mode = 10
                 AND (
                <!-- 如果白名单可见，则总是包括 -->
                    r.whitelist_show = 1
                 <!-- 或者白名单不可见但用户在白名单中 -->
                    OR (
                        r.whitelist_show = 0
                        AND EXISTS (
                        <!-- 子查询检查用户是否在白名单中 -->
                            SELECT 1
                            FROM live_org_filter lof
                            WHERE lof.org_id = r.org_id
                            AND lof.user_id = #{currentUserId}
                            <!-- 确保是白名单模式 -->
                            AND lof.filter_mode = 10
                            AND lof.deleted_at IS NULL
                        )
                    )
                )
            )
        )
        AND NOT EXISTS (
            SELECT 1
            FROM live_org_filter lofb
            WHERE lofb.org_id = r.org_id
            AND lofb.user_id = #{currentUserId}
            AND lofb.filter_mode = 20
            AND lofb.deleted_at IS NULL
        )
    </sql>

    <select id="getLiveRoomSaleStatisticsInfo" resultType="com.bbh.live.dao.dto.LiveRoomSaleStatisticsDTO">
        select sum(IF(lg.goods_status = 50, lg.sell_price, 0))       as soldAmount,
               count(IF(lg.goods_status = 50, 1, null))                 as soldCount,
               sum(IF(lg.putaway_at is not null, lg.start_price, 0)) as accumulatePutawayAmount,
               count(IF(lg.putaway_at is not null, 1, null))            as accumulatePutawayCount,
               sum(IF(lg.goods_status = 40, lg.start_price, 0))      as abortiveAmount,
               count(IF(lg.goods_status = 40, 1, null))                 as abortiveCount,
               COUNT(DISTINCT CASE WHEN goods_status = 50 THEN belong_seat_id END) as customerCount
        from bbh_htb.live_goods lg
        where lg.deleted_at is null
          and lg.live_room_id = #{liveRoomId}
    </select>
</mapper>
