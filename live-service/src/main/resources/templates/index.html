<!DOCTYPE html>
<html lang="">
<head>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>WebsSockets</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
</head>
<body>
<div id="app" class="w-screen mt-4 ">
    <div class="px-8 py-2">
        <div class="py-4 flex flex-col gap-2 border-t-2 border-t-solid border-t-black border-b-2 border-b-solid border-b-black">
            创建聊天室 | 全局聊天室：htbLrFi ｜ 单个聊天室：htbLr8、htbLrLr3
            <div class="flex flex-row items-center gap-2 ">
                <input placeholder="直播间ID"
                       v-model="roomId"
                       @click.stop="()=>{}"
                       class="w-full px-3 h-12 leading-12 bg-white border shadow-sm border-slate-300 placeholder-slate-400 disabled:bg-slate-50 disabled:text-slate-500 disabled:border-slate-200 focus:outline-none focus:border-sky-500 focus:ring-sky-500 block w-full rounded-md sm:text-sm focus:ring-1 invalid:border-pink-500 invalid:text-pink-600 focus:invalid:border-pink-500 focus:invalid:ring-pink-500 disabled:shadow-none">
                <div @click="handleCreateChatroom" class="w-28 h-12 leading-12 cursor-pointer inline-flex items-center justify-center bg-indigo-500 rounded-md shadow-lg text-white">
                    创建聊天室
                </div>
            </div>
            <div>
                {{ createRoomResp }}
            </div>
        </div>
        <div class="py-4 flex flex-col gap-2 border-t-2 border-t-solid border-t-black border-b-2 border-b-solid border-b-black">
            开通导播权限 | 输入席位ID和直播间ID
            <div class="flex flex-row items-center gap-2 ">
                <input placeholder="席位ID"
                       v-model="openDirector.seatId"
                       @click.stop="()=>{}"
                       class="w-full px-3 h-12 leading-12 bg-white border shadow-sm border-slate-300 placeholder-slate-400 disabled:bg-slate-50 disabled:text-slate-500 disabled:border-slate-200 focus:outline-none focus:border-sky-500 focus:ring-sky-500 block w-full rounded-md sm:text-sm focus:ring-1 invalid:border-pink-500 invalid:text-pink-600 focus:invalid:border-pink-500 focus:invalid:ring-pink-500 disabled:shadow-none">
                <input placeholder="直播间ID"
                       v-model="openDirector.liveRoomId"
                       @click.stop="()=>{}"
                       class="w-full px-3 h-12 leading-12 bg-white border shadow-sm border-slate-300 placeholder-slate-400 disabled:bg-slate-50 disabled:text-slate-500 disabled:border-slate-200 focus:outline-none focus:border-sky-500 focus:ring-sky-500 block w-full rounded-md sm:text-sm focus:ring-1 invalid:border-pink-500 invalid:text-pink-600 focus:invalid:border-pink-500 focus:invalid:ring-pink-500 disabled:shadow-none">
                <div @click="handleOpenDirector" class="w-[28rem] h-12 leading-12 cursor-pointer inline-flex items-center justify-center bg-indigo-500 rounded-md shadow-lg text-white">
                    开通导播权限
                </div>
            </div>
            <div>
                {{ openDirector.response }}
            </div>
        </div>
        <div class="py-4 flex flex-col gap-2 border-t-2 border-t-solid border-t-black border-b-2 border-b-solid border-b-black">
            开通买手权限 | 输入席位ID
            <div class="flex flex-row items-center gap-2 ">
                <input placeholder="席位ID"
                       v-model="openBuyer.seatId"
                       @click.stop="()=>{}"
                       class="w-full px-3 h-12 leading-12 bg-white border shadow-sm border-slate-300 placeholder-slate-400 disabled:bg-slate-50 disabled:text-slate-500 disabled:border-slate-200 focus:outline-none focus:border-sky-500 focus:ring-sky-500 block w-full rounded-md sm:text-sm focus:ring-1 invalid:border-pink-500 invalid:text-pink-600 focus:invalid:border-pink-500 focus:invalid:ring-pink-500 disabled:shadow-none">
                <div @click="handleOpenBuyer" class="w-[28rem] h-12 leading-12 cursor-pointer inline-flex items-center justify-center bg-indigo-500 rounded-md shadow-lg text-white">
                    开通买手权限
                </div>
            </div>
            <div>
                {{ openBuyer.response }}
            </div>
        </div>

        <div class="py-4 flex flex-col gap-2 border-t-2 border-t-solid border-t-black border-b-2 border-b-solid border-b-black">
            解析中台Sign | 输入sign
            <div class="flex flex-row items-center gap-2 ">
                <input placeholder="sign"
                       v-model="decodeSign.sign"
                       @click.stop="()=>{}"
                       class="w-full px-3 h-12 leading-12 bg-white border shadow-sm border-slate-300 placeholder-slate-400 disabled:bg-slate-50 disabled:text-slate-500 disabled:border-slate-200 focus:outline-none focus:border-sky-500 focus:ring-sky-500 block w-full rounded-md sm:text-sm focus:ring-1 invalid:border-pink-500 invalid:text-pink-600 focus:invalid:border-pink-500 focus:invalid:ring-pink-500 disabled:shadow-none">
                <div @click="handleDecodeSign" class="w-[28rem] h-12 leading-12 cursor-pointer inline-flex items-center justify-center bg-indigo-500 rounded-md shadow-lg text-white">
                    解析中台Sign
                </div>
            </div>
            <div>
                {{ decodeSign.response }}
            </div>
        </div>

        <div class="mt-2 text-sm font-medium text-slate-500 flex gap-2">
            <div>
                GoodsId:
                <input placeholder="Type your goods id"
                       v-model="goodsId"
                       class="w-full px-3 py-2 bg-white border shadow-sm border-slate-300 placeholder-slate-400 disabled:bg-slate-50 disabled:text-slate-500 disabled:border-slate-200 focus:outline-none focus:border-sky-500 focus:ring-sky-500 block w-full rounded-md sm:text-sm focus:ring-1 invalid:border-pink-500 invalid:text-pink-600 focus:invalid:border-pink-500 focus:invalid:ring-pink-500 disabled:shadow-none">
            </div>
            <div>
                RoomId:
                <input placeholder="Type your live room id"
                       v-model="roomId"
                       class="w-full px-3 py-2 bg-white border shadow-sm border-slate-300 placeholder-slate-400 disabled:bg-slate-50 disabled:text-slate-500 disabled:border-slate-200 focus:outline-none focus:border-sky-500 focus:ring-sky-500 block w-full rounded-md sm:text-sm focus:ring-1 invalid:border-pink-500 invalid:text-pink-600 focus:invalid:border-pink-500 focus:invalid:ring-pink-500 disabled:shadow-none">
            </div>
            <div>
                起拍价:
                <input placeholder="Type your 起拍价"
                       v-model="startAmount"
                       class="w-full px-3 py-2 bg-white border shadow-sm border-slate-300 placeholder-slate-400 disabled:bg-slate-50 disabled:text-slate-500 disabled:border-slate-200 focus:outline-none focus:border-sky-500 focus:ring-sky-500 block w-full rounded-md sm:text-sm focus:ring-1 invalid:border-pink-500 invalid:text-pink-600 focus:invalid:border-pink-500 focus:invalid:ring-pink-500 disabled:shadow-none">
            </div>
            <div>
                加价幅度:
                <input placeholder="Type your 加价幅度"
                       v-model="increaseAmount"
                       class="w-full px-3 py-2 bg-white border shadow-sm border-slate-300 placeholder-slate-400 disabled:bg-slate-50 disabled:text-slate-500 disabled:border-slate-200 focus:outline-none focus:border-sky-500 focus:ring-sky-500 block w-full rounded-md sm:text-sm focus:ring-1 invalid:border-pink-500 invalid:text-pink-600 focus:invalid:border-pink-500 focus:invalid:ring-pink-500 disabled:shadow-none">
            </div>
            <div>
                自动出价模式（0-顺序出价，1-同时）:
                <input placeholder="输入出价模式（0-顺序出价，1-同时）"
                       v-model="bidMode"
                       class="w-full px-3 py-2 bg-white border shadow-sm border-slate-300 placeholder-slate-400 disabled:bg-slate-50 disabled:text-slate-500 disabled:border-slate-200 focus:outline-none focus:border-sky-500 focus:ring-sky-500 block w-full rounded-md sm:text-sm focus:ring-1 invalid:border-pink-500 invalid:text-pink-600 focus:invalid:border-pink-500 focus:invalid:ring-pink-500 disabled:shadow-none">
            </div>
            <div v-if="false">
                是否循环出价 （0-否，1-是）:
                <input placeholder=""
                       v-model="bidLoopMode"
                       class="w-full px-3 py-2 bg-white border shadow-sm border-slate-300 placeholder-slate-400 disabled:bg-slate-50 disabled:text-slate-500 disabled:border-slate-200 focus:outline-none focus:border-sky-500 focus:ring-sky-500 block w-full rounded-md sm:text-sm focus:ring-1 invalid:border-pink-500 invalid:text-pink-600 focus:invalid:border-pink-500 focus:invalid:ring-pink-500 disabled:shadow-none">
            </div>
        </div>
        <div class="mt-4 flex gap-2">
            <div @click="handleAutoLogin" class="cursor-pointer w-full inline-flex items-center justify-center p-2 bg-indigo-500 rounded-md shadow-lg text-white">
                全部登录
            </div>
            <div @click="handleAuction" class="cursor-pointer w-full inline-flex items-center justify-center p-2 bg-indigo-500 rounded-md shadow-lg text-white">
                开启竞拍
            </div>
            <div v-if="!isRunning" @click="startAutoBid" class="cursor-pointer w-full inline-flex items-center justify-center p-2 bg-indigo-500 rounded-md shadow-lg text-white">
                自动出价
            </div>
            <div v-else @click="stopAutoBid" class="cursor-pointer w-full inline-flex items-center justify-center p-2 bg-indigo-500 rounded-md shadow-lg text-white">
                出价中, 停止下一轮
            </div>
        </div>
        <div class="mt-4">
            <div>当前最高价: {{ highestAmount }}</div>
            {{ auctionResult }}
        </div>
        <div class="relative rounded-xl overflow-auto mt-4 h-80">
            <div class="grid grid-cols-2 gap-4 rounded-lg text-center font-mono text-sm font-bold leading-6">
                <div v-for="(item, index) in users"
                     class="rounded-lg flex gap-2 items-start justify-between shadow-lg py-4 px-4 border-2 border-solid"
                     :class="[
                        item.isLogin ? 'border-green-500' : 'border-red-500',
                     ]"
                >
                    <div class="flex flex-col items-left text-left">
                        <div class="" :class="item.isLogin ? '' : 'text-red-500'">
                            isLogin = {{ item.isLogin }}
                        </div>
                        <div class="">
                            seatId = {{ item.seatId }}
                        </div>
                        <div class="flex flex-row items-center gap-2">
                            <input placeholder="出价金额"
                                   v-model="item.bidAmount"
                                   @click.stop="()=>{}"
                                   class="w-full px-3 h-12 leading-12 bg-white border shadow-sm border-slate-300 placeholder-slate-400 disabled:bg-slate-50 disabled:text-slate-500 disabled:border-slate-200 focus:outline-none focus:border-sky-500 focus:ring-sky-500 block w-full rounded-md sm:text-sm focus:ring-1 invalid:border-pink-500 invalid:text-pink-600 focus:invalid:border-pink-500 focus:invalid:ring-pink-500 disabled:shadow-none">
                            <div @click="handleBid(item, index)" class="w-28 h-12 leading-12 cursor-pointer inline-flex items-center justify-center bg-indigo-500 rounded-md shadow-lg text-white">
                                {{ item.bidLoading ? '出价中...' : '出价' }}
                            </div>
                        </div>
                    </div>
                    <div class="overflow-auto">
                        结果({{ item.bidDuration }}ms):
                        <div>
                            {{ item.bidResult }}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

</div>

<script>
    const { createApp, ref, onMounted, watch } = Vue
    createApp({
        setup() {
            const users = ref([
                {
                    seatId: 162,
                    isLogin: false,
                    loading: false,
                    bidAmount: 0
                },
                {
                    seatId: 3545,
                    isLogin: false,
                    loading: false,
                    bidAmount: 0
                },
                {
                    seatId: 3546,
                    isLogin: false,
                    loading: false
                },
                {
                    seatId: 3547,
                    isLogin: false,
                    loading: false
                },
                {
                    seatId: 3671,
                    isLogin: false,
                    loading: false
                },
                {
                    seatId: 3672,
                    isLogin: false,
                    loading: false
                },
                {
                    seatId: 3673,
                    isLogin: false,
                    loading: false
                },
                {
                    seatId: 3880,
                    isLogin: false,
                    loading: false
                },
            ])
            const goodsId = ref(12)
            const roomId = ref('htbLr8')
            const bidMode = ref('0')
            const bidLoopMode = ref('0')
            const startAmount = ref(100)
            const increaseAmount = ref(10)
            const highestAmount = ref(100)

            onMounted(() => {
                initUserSeat()

                highestAmount.value = startAmount.value
            })

            const initUserSeat = () => {
                // 3400 ~ 3450，追加user
                for (let i = 3400; i < 3500; i++) {
                    users.value.push({
                        seatId: i,
                        isLogin: false,
                        loading: false
                    })
                }
            }

            // 自动登录：生成jwt
            const handleAutoLogin = () => {
                users.value.forEach((item, index) => {
                    axios.get(`/test/generateJwtToken?seatId=${item.seatId}`).then(res => {
                        console.log(res)
                        if (res.data.code === 200) {
                            item.isLogin = true
                            item.token = res.data.data

                            // 递增默认的出价金额
                            // item.bidAmount = (index * increaseAmount.value) + startAmount.value
                        }
                    })
                })
            }

            // 开启竞拍
            const auctionResult = ref('')
            const handleAuction = () => {
                post('/director/liveGoods/auction', {
                    "live_room_id" : roomId.value,
                    "live_goods_id" : goodsId.value
                }, users.value[0].token).then(res => {
                    console.log(res)
                    auctionResult.value = JSON.stringify(res.data)
                })
            }

            // 自动出价
            const isRunning = ref(false)
            const handleAutoBid = async () => {
                const promises = users.value.map((item, index) => {
                    const delay = bidMode.value === '0' ? index * 300 : 0;
                    return new Promise((resolve) => {
                        setTimeout(() => {
                            if (bidMode === '1') {
                                item.bidAmount = (index * increaseAmount.value) + highestAmount.value
                            } else {
                                // 出价 = 最高价 + 递增
                                item.bidAmount = (increaseAmount.value) + highestAmount.value
                            }

                            handleBid(item).then(resolve);
                        }, delay);
                    });
                });
                await Promise.all(promises);
                console.log('All bids for this batch have completed.');
                auctionResult.value = 'All bids for this batch have completed.'
            }

            // 开始循环出价
            const autoBidLoop = () => {
                (async () => {
                    while (isRunning.value) {
                        await handleAutoBid();

                        // 更新出价金额
                        // users.value.forEach((item, index) => {
                        //     item.bidAmount = (index * increaseAmount.value) + highestAmount.value
                        // });

                        // 休息1秒
                        await new Promise(resolve => setTimeout(resolve, 1000));
                    }
                })();
            };
            // 开始自动出价
            const startAutoBid = () => {
                isRunning.value = true;
                handleAutoBid();
            };
            // 结束自动出价
            const stopAutoBid = () => {
                isRunning.value = false;
            };

            // 出价
            const handleBid = (item) => {
                return new Promise((resolve, reject) => {
                    if (!item.isLogin) {
                        item.bidResult = '请先登录'
                        reject(new Error('请先登录'));
                        return
                    }
                    item.bidLoading = true

                    if (highestAmount.value < item.bidAmount) {
                        highestAmount.value = item.bidAmount
                    }

                    const requestStartTime = new Date().getTime()

                    post('/buyer/liveGoods/auctionBid', {
                        "live_room_id" : roomId.value,
                        "live_goods_id" : goodsId.value,
                        "bid_price" : item.bidAmount
                    }, item.token).then(res => {
                        console.log(res)

                        const requestEndTime = new Date().getTime()
                        console.log('request time', requestEndTime - requestStartTime)
                        item.bidDuration = requestEndTime - requestStartTime

                        item.bidResult = JSON.stringify(res.data)
                        item.bidLoading = false

                        resolve();
                    }).catch(err => {
                        item.bidLoading = false
                        item.bidResult = '出价失败'
                        reject(err);
                    })
                })
            }

            // 创建聊天室
            const createRoomResp = ref('')
            const handleCreateChatroom = () => {
                post('/test/createChatroom/' + roomId.value).then(res => {
                    console.log(res)
                    createRoomResp.value = JSON.stringify(res.data)
                })
            }

            // 开通导播权限
            const openDirector = ref({
                seatId: '',
                liveRoomId: '',
                response: ''
            })
            const handleOpenDirector = () => {
                post(`/test/openDirectorPermission?seatId=${openDirector.value.seatId}&liveRoomId=${openDirector.value.roomId}`, openDirector.value).then(res => {
                    console.log(res)
                    openDirector.value.response = JSON.stringify(res.data)
                })
            }

            // 开通买手权限
            const openBuyer = ref({
                seatId: '',
                response: ''
            })
            const handleOpenBuyer = () => {
                post('/test/openBuyerAuctionPermission?seatId=' + openBuyer.value.seatId, openBuyer.value).then(res => {
                    console.log(res)
                    openBuyer.value.response = JSON.stringify(res.data)
                })
            }

            // 解析中台sign
            const decodeSign = ref({
                sign: '',
                response: ''
            })
            const handleDecodeSign = () => {
                post('/test/decodePayCenterSign', decodeSign.value).then(res => {
                    console.log(res)
                    decodeSign.value.response = JSON.stringify(res.data)
                })
            }

            const GLOBAL_TOKEN = 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpZl9tYXN0ZXIiOjEsIm5iZiI6MTcyNTcwNzc1NSwidXNlcl9pZCI6NTIyOSwib3JnX2lkIjoyMDgyLCJzZWF0X2lkIjozOTIwMDQyLCJleHAiOjE3MjgyOTk3NTUsImlhdCI6MTcyNTcwNzc1NX0.4XGE1cwBHDVX0lrWoJfyciA7zTIDazzWSomWFn7J7Vs'
            const post = async (url, data, token = GLOBAL_TOKEN) => {
                // 判断如果当前网页地址有bangbanghu，就给url拼接上，没有就不拼接
                if (window.location.href.indexOf('test.bangbanghu') > -1) {
                    url = '/htb-live-api' + url
                }
                return await axios.post(url, data, {
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': 'Bearer ' + token
                    }
                })
            }

            return {
                users,
                goodsId,
                roomId,
                bidMode,
                isRunning,
                bidLoopMode,
                auctionResult,
                handleAutoLogin,
                handleAuction,
                startAmount,
                increaseAmount,
                highestAmount,
                handleAutoBid,
                stopAutoBid,
                startAutoBid,
                handleBid,
                handleCreateChatroom,
                createRoomResp,
                openBuyer,
                openDirector,
                handleOpenBuyer,
                handleOpenDirector,
                decodeSign,
                handleDecodeSign
            }
        }
    }).mount('#app')
</script>
</body>
</html>
