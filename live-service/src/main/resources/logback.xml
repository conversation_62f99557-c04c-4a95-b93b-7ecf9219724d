<?xml version="1.0" encoding="UTF-8"?>
<configuration debug="false">
    <property name="LOG_HOME" value="./logs"/>

    <!-- 控制台输出 -->
    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <Pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread][链路ID: %X{traceId}] %-5level %logger{50} - %msg %n</Pattern>
        </encoder>

    </appender>

    <appender name="FILE"
              class="ch.qos.logback.core.rolling.RollingFileAppender">
        <File>${LOG_HOME}/LiveSystem/info.log</File>
        <rollingPolicy
                class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <FileNamePattern>${LOG_HOME}/LiveSystem/info.%d{yyyy-MM-dd}.%i.log.gz
            </FileNamePattern>
            <maxHistory>60</maxHistory>
            <maxFileSize>100MB</maxFileSize>
        </rollingPolicy>
        <encoder>
            <Pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread][链路ID: %X{traceId}] %-5level %logger{50} - %msg %n</Pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>


    <appender name="warn"
              class="ch.qos.logback.core.rolling.RollingFileAppender">
        <File>${LOG_HOME}/LiveSystem/warn.log</File>
        <rollingPolicy
                class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <FileNamePattern>${LOG_HOME}/LiveSystem/warn.%d{yyyy-MM-dd}.%i.log.gz
            </FileNamePattern>
            <maxHistory>60</maxHistory>
            <maxFileSize>100MB</maxFileSize>
        </rollingPolicy>
        <encoder>
            <Pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread][链路ID: %X{traceId}] %-5level %logger{50} - %msg %n</Pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>

    <appender name="error"
              class="ch.qos.logback.core.rolling.RollingFileAppender">
        <File>${LOG_HOME}/LiveSystem/error.log</File>
        <rollingPolicy
                class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <FileNamePattern>${LOG_HOME}/LiveSystem/error.%d{yyyy-MM-dd}.%i.log.gz
            </FileNamePattern>
            <maxHistory>60</maxHistory>
            <maxFileSize>100MB</maxFileSize>
        </rollingPolicy>
        <encoder>
            <Pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread][链路ID: %X{traceId}] %-5level %logger{50} - %msg %n</Pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>


    <!-- 配置日志 -->
    <logger name="com.bbh" level="INFO"/>

    <!--忽略提示 -->
    <logger name="com.alibaba.druid.pool.DruidDataSource" level="OFF"/>

    <!-- 日志输出级别 -->
    <root level="INFO">
        <appender-ref ref="STDOUT"/>
        <appender-ref ref="FILE"/>
    </root>

    <logger name="warn">
        <level value="trace"/>
        <appender-ref ref="warn"/>
    </logger>

    <logger name="error">
        <level value="trace"/>
        <appender-ref ref="error"/>
    </logger>

</configuration>
