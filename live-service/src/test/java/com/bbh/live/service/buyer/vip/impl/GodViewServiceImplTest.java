package com.bbh.live.service.buyer.vip.impl;

import cn.hutool.json.JSONUtil;
import com.bbh.live.dao.dto.GodViewTrialStatusDTO;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;

import java.util.Date;

import static org.junit.jupiter.api.Assertions.*;

@Slf4j
class GodViewServiceImplTest {

    @Test
    void getGodViewTrialStatus() {
        var godViewTrialStatusDTO = new GodViewTrialStatusDTO();
        godViewTrialStatusDTO.setCanFreeView(true);
        godViewTrialStatusDTO.setCreateTime(new Date());
        godViewTrialStatusDTO.setFreeViewUnUsedTimes(10);
        godViewTrialStatusDTO.setFreeCountSecondsCountdown(10 * 60L);

        log.info("{}", JSONUtil.toJsonPrettyStr(godViewTrialStatusDTO));
    }

}