package com.bbh.live.service;

import cn.hutool.core.date.DateUtil;
import com.bbh.live.LiveServiceApplication;
import com.bbh.live.service.buyer.sign.SignCacheManager;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = LiveServiceApplication.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@ActiveProfiles("dev")
@Slf4j
public class SignCacheManagerTest {

    @Resource
    private SignCacheManager signCacheManager;

    @Test
    public void testSignInTest() {
        long seatId = 3719l;

        signCacheManager.todaySignIn(seatId);
        signCacheManager.signIn(DateUtil.offsetDay(DateUtil.date(), -1), seatId);
        signCacheManager.signIn(DateUtil.offsetDay(DateUtil.date(), -2), seatId);
        signCacheManager.signIn(DateUtil.offsetDay(DateUtil.date(), -3), seatId);
        signCacheManager.signIn(DateUtil.offsetDay(DateUtil.date(), -4), seatId);

    }
}