package com.bbh.live.service.room;

import com.bbh.live.dao.dto.vo.LiveRoomVO;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
public class LiveRoomSortTest {

    @Test
    public void shuffleTest() {
        // 创建6个专场直播间
        LiveRoomVO special1 = new LiveRoomVO();
        special1.setId(1001L);
        special1.setIfSpecial(true);
        special1.setSort(100);  // 有sort值

        LiveRoomVO special2 = new LiveRoomVO();
        special2.setId(1002L);
        special2.setIfSpecial(true);
        special2.setSort(50);   // 有sort值

        LiveRoomVO special3 = new LiveRoomVO();
        special3.setId(1003L);
        special3.setIfSpecial(true);
        special3.setSort(0);    // 无sort值

        LiveRoomVO special4 = new LiveRoomVO();
        special4.setId(1004L);
        special4.setIfSpecial(true);
        special4.setSort(0);    // 无sort值

        LiveRoomVO special5 = new LiveRoomVO();
        special5.setId(1005L);
        special5.setIfSpecial(true);
        special5.setSort(0);    // 无sort值

        LiveRoomVO special6 = new LiveRoomVO();
        special6.setId(1006L);
        special6.setIfSpecial(true);
        special6.setSort(0);    // 无sort值

        // 创建4个普通直播间
        LiveRoomVO normal1 = new LiveRoomVO();
        normal1.setId(2001L);
        normal1.setIfSpecial(false);
        normal1.setSort(80);    // 有sort值

        LiveRoomVO normal2 = new LiveRoomVO();
        normal2.setId(2002L);
        normal2.setIfSpecial(false);
        normal2.setSort(0);     // 无sort值

        LiveRoomVO normal3 = new LiveRoomVO();
        normal3.setId(2003L);
        normal3.setIfSpecial(false);
        normal3.setSort(0);     // 无sort值

        LiveRoomVO normal4 = new LiveRoomVO();
        normal4.setId(2004L);
        normal4.setIfSpecial(false);
        normal4.setSort(0);     // 无sort值

        // 添加所有直播间到列表
        List<LiveRoomVO> rooms = new ArrayList<>(Arrays.asList(
                special1, special2, special3, special4, special5, special6,
                normal1, normal2, normal3, normal4
        ));

        // 测试不排除任何房间的情况
        log.info("-----------开始排序---------");
        shuffleLiveRoomGroupBySpecial(rooms, null);
        log.info("-----------结束排序---------\n\n");

        // 测试排除一个房间的情况
        log.info("-----------开始排序---------");
        shuffleLiveRoomGroupBySpecial(rooms, 2004L);
        log.info("-----------结束排序---------");
    }

    private void shuffleLiveRoomGroupBySpecial(List<LiveRoomVO> list, Long excludeRoomId) {
        if (list == null || list.size() <= 1) return;

        // 打印原始顺序
        log.info("原始顺序: {}", list.stream()
                .map(room -> "(" + room.getIfSpecial() + ":" + room.getId() + ":sort=" + room.getSort() + ")")
                .collect(Collectors.joining(", ")));

        // 分组并处理，排除指定ID的直播间
        Map<Boolean, ArrayList<LiveRoomVO>> rooms = list.stream()
                .filter(room -> excludeRoomId == null || !excludeRoomId.equals(room.getId()))
                .collect(Collectors.groupingBy(
                        LiveRoomVO::getIfSpecial,
                        Collectors.collectingAndThen(
                                Collectors.toCollection(ArrayList::new),
                                grouped -> {
                                    // 将列表分成两部分：sort>0的和其他的
                                    List<LiveRoomVO> sortedRooms = grouped.stream()
                                            .filter(room -> room.getSort() != null && room.getSort() > 0)
                                            .sorted((a, b) -> b.getSort().compareTo(a.getSort()))  // 按sort降序排序
                                            .collect(Collectors.toList());

                                    List<LiveRoomVO> unsortedRooms = grouped.stream()
                                            .filter(room -> room.getSort() == null || room.getSort() <= 0)
                                            .collect(Collectors.toList());

                                    // 只打乱unsortedRooms部分
                                    Collections.shuffle(unsortedRooms);

                                    // 合并结果
                                    ArrayList<LiveRoomVO> result = new ArrayList<>();
                                    result.addAll(sortedRooms);    // 先添加有sort值的
                                    result.addAll(unsortedRooms);  // 再添加随机排序的
                                    return result;
                                }
                        )
                ));

        // 打印分组后的情况
        rooms.forEach((isSpecial, groupList) -> {
            log.info("{}: {}",
                    isSpecial ? "专场直播间处理后" : "普通直播间处理后",
                    groupList.stream()
                            .map(room -> room.getId() + "(sort=" + room.getSort() + ")")
                            .collect(Collectors.joining(", ")));
        });

        // 保留需要排除的直播间
        List<LiveRoomVO> excludedRooms = excludeRoomId == null ? Collections.emptyList() :
                list.stream()
                        .filter(room -> excludeRoomId.equals(room.getId()))
                        .collect(Collectors.toList());

        list.clear();
        // 首先添加排除的直播间
        list.addAll(excludedRooms);
        // 然后添加其他处理后的直播间
        list.addAll(Stream.of(Boolean.TRUE, Boolean.FALSE)
                .flatMap(key -> rooms.getOrDefault(key, new ArrayList<>()).stream())
                .collect(Collectors.toList()));

        // 打印最终顺序
        log.info("最终顺序: {}", list.stream()
                .map(room -> "(" + room.getIfSpecial() + ":" + room.getId() + ":sort=" + room.getSort() + ")")
                .collect(Collectors.joining(", ")));
    }

}
