package com.bbh.live.service.order.impl;

import cn.hutool.core.collection.CollUtil;
import com.bbh.service.pay.bean.PayRequest;
import com.bbh.service.pay.dto.PayDTO;
import com.bbh.service.pay.enums.MethodEnum;
import com.bbh.service.pay.enums.PayModeEnum;
import com.bbh.service.pay.enums.PayTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;

import java.util.List;

@Slf4j
class PayServiceImplTest {

    @Test
    void orderPay() {

        MethodEnum methodEnum = MethodEnum.PAY;
        PayDTO payDTO = new PayDTO();

        // 时间戳
        payDTO.setTimestamp("1726027045000");
        // 流水号
        payDTO.setOutTradeNo("PT_G_O_20240911115754593640");
        // 订单标题
        payDTO.setSubject("购买放心拍商品");
        // 总金额，单位分
        payDTO.setTotalAmount(663600L);
        // 支付类型
        payDTO.setPayType(PayTypeEnum.JN);
        // 支付模式
        payDTO.setPayMode(PayModeEnum.ZFB);
        // 异步回调地址
        payDTO.setNotifyUrl("http://htb-api-test.bangbanghu.com.cn/htb-pt-api/order/pay/notify");
        // 同步回调地址
        payDTO.setAsyncNotify("");
        // 微信用户ID
        payDTO.setWxOpenId("2088122087816641");
        // 微信用户ID
        payDTO.setAliPayUserId("2088122087816641");
        // 订单数据
        List<PayDTO.BizDataDTO> bizDataList = CollUtil.newArrayList();
        PayDTO.BizDataDTO bizDataDTO = new PayDTO.BizDataDTO();
        bizDataDTO.setOrderParentNo("PT_G_O_SUB_20240911115754655742");
        bizDataDTO.setPlatMerchantNo("0000029711");
        bizDataDTO.setOpTotalAmount(663600L);
        bizDataList.add(bizDataDTO);
        payDTO.setBizData(bizDataList);

        PayRequest<PayDTO> payRequest = new PayRequest<>();
        payRequest.setData(payDTO);

    }
}