package com.bbh.live.service.livegoods.cache.redis;

import com.bbh.live.service.livegoods.cache.LiveGoodsAuctionCacheService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;

import java.math.BigDecimal;

@Slf4j
@SpringBootTest
@AutoConfigureMockMvc
class RedisLiveGoodsAuctionCacheServiceImplTest {

    @Autowired
    private LiveGoodsAuctionCacheService liveGoodsAuctionCacheService;

    @Test
    void insertLiveGoodsBidInfo() {
        liveGoodsAuctionCacheService.insertLiveGoodsBidInfo(1L, 2L, 3014L, BigDecimal.ONE);
        liveGoodsAuctionCacheService.insertLiveGoodsBidInfo(1L, 2L, 3014L, BigDecimal.ONE);
        liveGoodsAuctionCacheService.insertLiveGoodsBidInfo(1L, 2L, 3018L, BigDecimal.ONE);
        liveGoodsAuctionCacheService.insertLiveGoodsBidInfo(1L, 3L, 3018L, BigDecimal.ONE);
    }

    @Test
    void getAuctionGoodsBiddenSeatIds() {
        var val = liveGoodsAuctionCacheService.getAuctionGoodsBidInfo(1L, 2L);
        log.info("{}", val);
    }

}