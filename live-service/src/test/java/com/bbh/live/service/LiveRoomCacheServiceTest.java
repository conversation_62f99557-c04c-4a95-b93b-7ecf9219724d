package com.bbh.live.service;

import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.bbh.live.LiveServiceApplication;
import com.bbh.live.dao.service.LiveRoomService;
import com.bbh.live.thread.ThreadPoolManager;
import com.bbh.model.LiveRoom;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.math.BigDecimal;
import java.util.concurrent.CountDownLatch;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = LiveServiceApplication.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@ActiveProfiles("dev")
@Slf4j
public class LiveRoomCacheServiceTest {

    @Test
    public void updateRoomScore() throws InterruptedException {
        Long roomId = 5L;
        BigDecimal goodsSoldAmount = new BigDecimal("3500");
        BigDecimal goodsTradeCount = new BigDecimal(4);

        CountDownLatch countDownLatch = new CountDownLatch(1);
        ThreadPoolManager.getGlobalBizExecutor().execute(() -> {
            // 成交额在直播中只会增不会减，因此来把小锁，先查当前分数，如果算出来的分数更高再去更新
            LiveRoomService liveRoomService = SpringUtil.getBean(LiveRoomService.class);
            RedissonClient redissonClient = SpringUtil.getBean(RedissonClient.class);
            RLock lock = redissonClient.getLock("updateRoomScore" + roomId);
            lock.lock();
            try {
                BigDecimal score = goodsSoldAmount.multiply(BigDecimal.valueOf(0.00033)).add(goodsTradeCount);
                // 先查当前分数
                LiveRoom liveRoom = liveRoomService.getOne(Wrappers
                        .lambdaQuery(LiveRoom.class)
                        .select(LiveRoom::getScore)
                        .eq(LiveRoom::getId, roomId));
                log.info("分数比较结果: {}", score.compareTo(liveRoom.getScore()));
                // 如果算出来的分数更高再去更新
                if (score.compareTo(liveRoom.getScore()) > 0) {
                    liveRoomService.update(Wrappers.lambdaUpdate(LiveRoom.class).eq(LiveRoom::getId, roomId).set(LiveRoom::getScore, score));
                }
            } catch (Exception e) {
                throw new RuntimeException(e);
            } finally {
                lock.unlock();
                countDownLatch.countDown();
            }
        });
        countDownLatch.await();
        log.info("done");
    }
}