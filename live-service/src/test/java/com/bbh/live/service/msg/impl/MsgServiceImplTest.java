package com.bbh.live.service.msg.impl;

import com.bbh.live.LiveServiceApplication;
import com.bbh.live.service.msg.MsgService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = LiveServiceApplication.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@ActiveProfiles("dev")
@Slf4j
class MsgServiceImplTest {

    @Autowired
    MsgService msgService;

    @Test
    void userEnterRoom() {
        msgService.userEnterRoom(28L, 180L, 229L);
    }
}