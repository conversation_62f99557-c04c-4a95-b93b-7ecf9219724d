package com.bbh.live.service.room.impl;

import com.bbh.live.dao.dto.vo.LiveRoomVO;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.*;

@Slf4j
class LiveRoomBizServiceImplTest {

    private List<LiveRoomVO> liveRooms;

    private void shuffleLiveRoomGroupBySpecial(List<LiveRoomVO> list, Long excludeRoomId) {
        if (list == null || list.size() <= 1) return;

        // 打印原始顺序
        log.info("原始顺序: {}", list.stream()
                .map(room -> "(" + room.getIfSpecial() + ":" + room.getId() + ":sort=" + room.getSort() + ")")
                .collect(Collectors.joining(", ")));

        // 分组并处理，排除指定ID的直播间
        Map<Boolean, ArrayList<LiveRoomVO>> rooms = list.stream()
                .filter(room -> excludeRoomId == null || !excludeRoomId.equals(room.getId()))
                .collect(Collectors.groupingBy(
                        LiveRoomVO::getIfSpecial,
                        Collectors.collectingAndThen(
                                Collectors.toCollection(ArrayList::new),
                                grouped -> {
                                    // 先按sort降序排序整个列表
                                    List<LiveRoomVO> sortedRooms = grouped.stream()
                                            .sorted((a, b) -> {
                                                Long sortA = a.getSort() != null ? a.getSort() : 0L;
                                                Long sortB = b.getSort() != null ? b.getSort() : 0L;
                                                return sortB.compareTo(sortA); // 降序排序
                                            })
                                            .collect(Collectors.toList());

                                    // 按sort值分组
                                    Map<Long, List<LiveRoomVO>> roomsBySort = sortedRooms.stream()
                                            .collect(Collectors.groupingBy(
                                                    room -> room.getSort() != null ? room.getSort() : 0L,
                                                    LinkedHashMap::new, // 保持原来的排序顺序
                                                    Collectors.toList()
                                            ));

                                    // 对每组相同sort值的房间进行随机打乱
                                    roomsBySort.forEach((sort, sameSortRooms) -> {
                                        Collections.shuffle(sameSortRooms);
                                    });

                                    // 将所有组扁平化成最终结果
                                    return roomsBySort.values().stream()
                                            .flatMap(Collection::stream)
                                            .collect(Collectors.toCollection(ArrayList::new));
                                }
                        )
                ));

        // 打印分组后的情况
        rooms.forEach((isSpecial, groupList) -> {
            log.info("{}: {}",
                    isSpecial ? "专场直播间处理后" : "普通直播间处理后",
                    groupList.stream()
                            .map(room -> room.getId() + "(sort=" + room.getSort() + ")")
                            .collect(Collectors.joining(", ")));
        });

        // 保留需要排除的直播间
        List<LiveRoomVO> excludedRooms = excludeRoomId == null ? Collections.emptyList() :
                list.stream()
                        .filter(room -> excludeRoomId.equals(room.getId()))
                        .collect(Collectors.toList());

        list.clear();
        // 首先添加排除的直播间
        list.addAll(excludedRooms);
        // 然后添加其他处理后的直播间
        list.addAll(Stream.of(Boolean.TRUE, Boolean.FALSE)
                .flatMap(key -> rooms.getOrDefault(key, new ArrayList<>()).stream())
                .collect(Collectors.toList()));

        // 打印最终顺序
        log.info("最终顺序: {}", list.stream()
                .map(room -> "(" + room.getIfSpecial() + ":" + room.getId() + ":sort=" + room.getSort() + ")")
                .collect(Collectors.joining(", ")));
    }

    private LiveRoomVO createLiveRoom(Long id, Boolean ifSpecial, Integer sort) {
        LiveRoomVO room = new LiveRoomVO();
        room.setId(id);
        room.setIfSpecial(ifSpecial);
        room.setSort(sort);
        room.setRoomName("TestRoom-" + id); // 设置一个默认名称
        return room;
    }

    @BeforeEach
    void setUp() {
        // 初始化测试数据
        liveRooms = new ArrayList<>();

        // 专场直播间
        liveRooms.add(createLiveRoom(1L, true, 2));    // sort=2
        liveRooms.add(createLiveRoom(2L, true, 2));    // sort=2
        liveRooms.add(createLiveRoom(3L, true, 1));    // sort=1
        liveRooms.add(createLiveRoom(4L, true, 0));    // sort=0
        liveRooms.add(createLiveRoom(5L, true, 0));    // sort=0

        // 普通直播间
        liveRooms.add(createLiveRoom(6L, false, 3));   // sort=3
        liveRooms.add(createLiveRoom(7L, false, 2));   // sort=2
        liveRooms.add(createLiveRoom(8L, false, null)); // sort=null
    }

    @Test
    void testShuffleLiveRoomGroupBySpecial() {
        // 执行排序
        shuffleLiveRoomGroupBySpecial(liveRooms, null);

        // 分离专场和普通直播间
        List<LiveRoomVO> specialRooms = liveRooms.stream()
                .filter(LiveRoomVO::getIfSpecial)
                .collect(Collectors.toList());
        List<LiveRoomVO> normalRooms = liveRooms.stream()
                .filter(room -> !room.getIfSpecial())
                .collect(Collectors.toList());

        // 验证专场直播间排序
        assertEquals(5, specialRooms.size(), "专场直播间数量应为5");
        assertTrue(isSortDescending(specialRooms), "专场直播间应按sort降序排列");

        // 验证普通直播间排序
        assertEquals(3, normalRooms.size(), "普通直播间数量应为3");
        assertTrue(isSortDescending(normalRooms), "普通直播间应按sort降序排列");

        // 验证null值被当作0处理
        LiveRoomVO nullSortRoom = normalRooms.stream()
                .filter(room -> room.getId() == 8L)
                .findFirst()
                .orElse(null);
        assertNotNull(nullSortRoom);
        assertTrue(normalRooms.indexOf(nullSortRoom) >= normalRooms.indexOf(normalRooms.stream()
                .filter(r -> r.getSort() != null && r.getSort() == 2)
                .findFirst()
                .orElse(null)), "sort=null的房间应排在sort=2之后");

        // 运行多次验证相同sort值的随机性
        List<String> firstRunOrder = runAndGetOrder(liveRooms);
        List<String> secondRunOrder = runAndGetOrder(liveRooms);
        assertNotEquals(firstRunOrder, secondRunOrder, "多次运行应产生不同的顺序（相同sort值随机）");
    }
    // 辅助方法：检查是否按sort降序排列
    private boolean isSortDescending(List<LiveRoomVO> rooms) {
        for (int i = 0; i < rooms.size() - 1; i++) {
            Integer sort1 = rooms.get(i).getSort() != null ? rooms.get(i).getSort() : 0;
            Integer sort2 = rooms.get(i + 1).getSort() != null ? rooms.get(i + 1).getSort() : 0;
            if (sort1 < sort2) {
                log.info("Sort order failed at index {}: {} < {}", i, sort1, sort2);
                return false;
            }
        }
        return true;
    }

    // 辅助方法：运行排序并返回顺序
    private List<String> runAndGetOrder(List<LiveRoomVO> rooms) {
        List<LiveRoomVO> copy = new ArrayList<>(rooms);
        shuffleLiveRoomGroupBySpecial(copy, null);
        return copy.stream()
                .map(room -> room.getId() + ":" + (room.getSort() != null ? room.getSort() : 0))
                .collect(Collectors.toList());
    }

}