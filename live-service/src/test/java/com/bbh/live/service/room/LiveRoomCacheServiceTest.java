package com.bbh.live.service.room;

import com.bbh.live.enums.LiveRoomCacheKeys;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;

@Slf4j
@SpringBootTest
@AutoConfigureMockMvc
class LiveRoomCacheServiceTest {

    @Autowired
    private LiveRoomCacheService liveRoomCacheService;

    @Test
    void incrementCache() {
        liveRoomCacheService.incrementCache(84L, LiveRoomCacheKeys.UNSOLD_COUNT, 1);
        liveRoomCacheService.incrementCache(84L, LiveRoomCacheKeys.GOODS_COUNT, 1);
        liveRoomCacheService.incrementCache(84L, LiveRoomCacheKeys.GOODS_COUNT, 1);
        liveRoomCacheService.incrementCache(84L, LiveRoomCacheKeys.GOODS_COUNT, 1);
        liveRoomCacheService.incrementCache(84L, LiveRoomCacheKeys.SOLD_AMOUNT, 1000);
        liveRoomCacheService.incrementCache(84L, LiveRoomCacheKeys.SOLD_COUNT, 1000);
        liveRoomCacheService.incrementCache(84L, LiveRoomCacheKeys.GOODS_COUNT, -1);
        liveRoomCacheService.incrementCache(84L, LiveRoomCacheKeys.GOODS_COUNT, -1);
        liveRoomCacheService.incrementCache(84L, LiveRoomCacheKeys.GOODS_COUNT, -1);
        liveRoomCacheService.incrementCache(84L, LiveRoomCacheKeys.GOODS_COUNT, -1);
    }
}