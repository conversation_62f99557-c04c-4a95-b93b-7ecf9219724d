package com.bbh.live.service.buyer.weekly.impl;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;

import java.util.Date;

@Slf4j
class WeeklyServiceImplTest {


    @Test
    void calcNextSendDay() {
        Date now = new Date();
        // 获取下个周一的日期
        DateTime nextWeek = DateUtil.nextWeek();
        log.info("{}", nextWeek);
        DateTime dateTime = DateUtil.beginOfWeek(nextWeek);
        log.info("{}", dateTime);
        String formatted = DateUtil.format(dateTime, "M月d号");
        log.info("{}", formatted);
    }

}