package com.bbh.live.service.order.impl;

import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONConfig;
import cn.hutool.json.JSONUtil;
import com.bbh.live.constant.LiveGoodsConstant;
import com.bbh.live.dao.dto.AggregatedUserDTO;
import com.bbh.model.GlobalOrderItem;
import com.bbh.model.GlobalOrgSeat;
import com.bbh.model.GlobalOrganization;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;

@SuppressWarnings("all")
@Slf4j
class FenbeiCalculatorTest {

    private List<GlobalOrderItem> items = new ArrayList<>();

    @BeforeEach
    public void before() {
        // 商品2=17301元
        GlobalOrderItem ite2 = new GlobalOrderItem();
        BigDecimal sellPrice2 = BigDecimal.valueOf(17301);
        ite2.setId(2L);
        ite2.setGoodsPrice(sellPrice2);
        ite2.setChannelAmount(sellPrice2.multiply(LiveGoodsConstant.CHANNEL_RATE).setScale(2, RoundingMode.HALF_UP));
        ite2.setNeedPayAmount(ite2.getGoodsPrice().add(ite2.getChannelAmount()));
        items.add(ite2);
    }

    void addItem(int price) {
        // 商品1=100元
        GlobalOrderItem item1 = new GlobalOrderItem();
        BigDecimal sellPrice = BigDecimal.valueOf(price);
        item1.setId(1L);
        item1.setGoodsPrice(sellPrice);
        item1.setChannelAmount(sellPrice.multiply(LiveGoodsConstant.CHANNEL_RATE).setScale(2, RoundingMode.HALF_UP));
        item1.setNeedPayAmount(item1.getGoodsPrice().add(item1.getChannelAmount()));
        items.add(item1);
    }

    AggregatedUserDTO getVip() {
        AggregatedUserDTO userInfo = new AggregatedUserDTO();
        GlobalOrgSeat seat = new GlobalOrgSeat();
        //seat.setAuctionBuyerStartAt(DateUtil.offsetDay(new Date(), -1));
        //seat.setAuctionBuyerEndAt(DateUtil.offsetDay(new Date(), 12));
        userInfo.setSeat(seat);
        GlobalOrganization organization = new GlobalOrganization();
        organization.setFenbei(9988);
        userInfo.setOrganization(organization);
        return userInfo;
    }

    AggregatedUserDTO getNoVip() {
        AggregatedUserDTO userInfo = new AggregatedUserDTO();
        GlobalOrgSeat seat = new GlobalOrgSeat();
        //seat.setAuctionBuyerStartAt(DateUtil.offsetDay(new Date(), -30));
        //seat.setAuctionBuyerEndAt(DateUtil.offsetDay(new Date(), -1));
        userInfo.setSeat(seat);
        GlobalOrganization organization = new GlobalOrganization();
        organization.setFenbei(9988);
        userInfo.setOrganization(organization);
        return userInfo;
    }

    // 单件商品测试
    @Test
    void vipTest() {
        FenbeiCalculator calculator = SpringUtil.getBean(FenbeiCalculator.class);
        BigDecimal totalChannelAmount = items.stream().map(GlobalOrderItem::getChannelAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        log.info("通道费: {}", totalChannelAmount);
        BigDecimal totalNeedPayAmount = items.stream().map(GlobalOrderItem::getNeedPayAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        log.info("应支付: {}", totalNeedPayAmount);
        FenbeiCalculator.OnlineResult result = calculator.calculateFenbeiDeduction(items, getVip(), true);
        log.info("分贝抵扣: {}", result);
        JSONConfig jsonConfig = new JSONConfig().setIgnoreNullValue(true);
        log.info("最终的items: {}", JSONUtil.toJsonStr(items, jsonConfig));

        Assertions.assertEquals(totalChannelAmount, BigDecimal.valueOf(103.81));
        Assertions.assertEquals(totalNeedPayAmount, BigDecimal.valueOf(17404.81));
        Assertions.assertEquals(result.getTotalFenbeiDeductionCount(), 416);
        Assertions.assertEquals(result.getTotalFenbeiDeductionAmount(), BigDecimal.valueOf(103.81));
    }

    // 2件商品测试
    @Test
    void vipTest2() {
        addItem(200);
        FenbeiCalculator calculator = SpringUtil.getBean(FenbeiCalculator.class);

        BigDecimal totalNeedPayAmount = items.stream().map(GlobalOrderItem::getNeedPayAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        log.info("应支付: {}", totalNeedPayAmount);

        FenbeiCalculator.OnlineResult result = calculator.calculateFenbeiDeduction(items, getVip(), true);
        log.info("分贝抵扣: {}", result);

        Assertions.assertEquals(totalNeedPayAmount, BigDecimal.valueOf(17606.01));
        Assertions.assertEquals(result.getTotalFenbeiDeductionCount(), 421);
        Assertions.assertEquals(result.getTotalFenbeiDeductionAmount(), BigDecimal.valueOf(105.01));
    }

    @Test
    void vipTest3() {
        items = new ArrayList<>();
        addItem(200);
        addItem(900);
        addItem(100);
        FenbeiCalculator calculator = SpringUtil.getBean(FenbeiCalculator.class);

        BigDecimal totalNeedPayAmount = items.stream().map(GlobalOrderItem::getNeedPayAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        log.info("应支付: {}", totalNeedPayAmount);

        FenbeiCalculator.OnlineResult result = calculator.calculateFenbeiDeduction(items, getVip(), true);
        log.info("分贝抵扣: {}", result);

        Assertions.assertEquals(totalNeedPayAmount, new BigDecimal("1207.20"));
        Assertions.assertEquals(result.getTotalFenbeiDeductionCount(), 29);
        Assertions.assertEquals(result.getTotalFenbeiDeductionAmount(), new BigDecimal("7.20"));
        Assertions.assertEquals(result.getOverDeductionFenbeiAmount(), new BigDecimal("4.80"));

    }

    @Test
    void noVipTest() {
        FenbeiCalculator calculator = SpringUtil.getBean(FenbeiCalculator.class);
        BigDecimal totalChannelAmount = items.stream().map(GlobalOrderItem::getChannelAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        log.info("通道费: {}", totalChannelAmount);
        BigDecimal totalNeedPayAmount = items.stream().map(GlobalOrderItem::getNeedPayAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        log.info("应支付: {}", totalNeedPayAmount);
        FenbeiCalculator.OnlineResult result = calculator.calculateFenbeiDeduction(items, getNoVip(), false);
        log.info("分贝抵扣: {}", result);
        JSONConfig jsonConfig = new JSONConfig().setIgnoreNullValue(true);
        log.info("最终的items: {}", JSONUtil.toJsonStr(items, jsonConfig));

        Assertions.assertEquals(totalChannelAmount, BigDecimal.valueOf(103.81));
        Assertions.assertEquals(totalNeedPayAmount, BigDecimal.valueOf(17404.81));
        Assertions.assertEquals(result.getTotalFenbeiDeductionCount(), 416);
        Assertions.assertEquals(result.getTotalFenbeiDeductionAmount(), BigDecimal.valueOf(103.81));
    }

}