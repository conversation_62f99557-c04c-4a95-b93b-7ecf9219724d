package com.bbh.live.controller;

import cn.hutool.json.JSONUtil;
import com.bbh.live.LiveServiceApplication;
import com.bbh.live.dao.dto.QueryLiveRoomDTO;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.web.server.LocalServerPort;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.ResultActions;
import org.springframework.test.web.servlet.request.MockHttpServletRequestBuilder;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;

/**
 * <AUTHOR>
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = LiveServiceApplication.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@ActiveProfiles("dev")
@AutoConfigureMockMvc
@Slf4j
public class LiveRoomControllerTest {

    @LocalServerPort
    private Integer port;

    @Autowired
    private MockMvc mockMvc;

    @Before
    public void beforeMock() {
        log.info("port: {}", port);
    }

    @Test
    public void getRoomListTest() throws Exception {
        // 准备请求体
        QueryLiveRoomDTO query = new QueryLiveRoomDTO();
        query.setStatus(2);
        query.setCurrentPage(1);
        query.setPerPage(10);
        String requestBody = JSONUtil.toJsonStr(query);
        // 构建请求
        MockHttpServletRequestBuilder builder = MockMvcRequestBuilders.post("/room/getLiveRoomList")
                .contentType(MediaType.APPLICATION_JSON)
                .content(requestBody);
        // 获取执行结果
        ResultActions actions = mockMvc.perform(builder);
        // 验证
        actions.andExpect(MockMvcResultMatchers.status().isOk())
                        .andExpect(MockMvcResultMatchers.content().json("{\"code\":401,\"msg\":\"请求未授权\",\"data\":[]}"));
    }

}
