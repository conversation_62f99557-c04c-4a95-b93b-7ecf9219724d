import cn.hutool.extra.spring.SpringUtil
import cn.hutool.json.JSONObject
import com.bbh.util.LogExUtil
import org.springframework.jdbc.core.JdbcTemplate

def process() {
    def jdbcTemplate = SpringUtil.getBean(JdbcTemplate.class)
    def coreMqService = SpringUtil.getBean(CoreMqService.class)
    def sql = 'select id, putaway_at from live_goods where putaway_at is not null and putaway_at > \'2024-12-01 00:00:00\' and deleted_at is null order by putaway_at asc'
    def batchSize = 100
    def baseDelay = 1000
    def count = 0
    def batch = []

    jdbcTemplate.queryForList(sql).each { row ->
        batch << row.id
        count++

        if (count % batchSize == 0) {
            processBatch(batch, count, coreMqService, baseDelay)
            batch.clear()
            sleep(100)
        }
    }

    if (batch) {
        processBatch(batch, count, coreMqService, baseDelay)
    }

    LogExUtil.infoLog("Total processed records: ${count}")
}

def processBatch(batch, count, coreMqService, baseDelay) {
    batch.each { id ->
        try {
            def delay = baseDelay * (count.intdiv(100) + 1)
            def jsonObject = new JSONObject()
            jsonObject.set('liveGoodsId', id)
            coreMqService.send(MqTopicEnum.LIVE_GOODS_PUTAWAY, jsonObject.toString(), delay)
            LogExUtil.infoLog("Send mq success, liveGoodsId: ${id}, delay: ${delay}ms")
        } catch (e) {
            LogExUtil.errorLog("Failed to send mq for liveGoodsId: ${id}", e)
        }
    }
}

try {
    process()
} catch (e) {
    LogExUtil.errorLog('Error processing live goods', e)
    throw e
}