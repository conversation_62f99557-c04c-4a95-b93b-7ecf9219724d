package com.bbh.live.dao.service.impl;

import com.bbh.live.constant.LiveGoodsConstant;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;

import java.math.BigDecimal;
import java.math.RoundingMode;

import static org.junit.jupiter.api.Assertions.*;

@Slf4j
public class OrderServiceImplTest {

    @Test
    public void bigDecimalTest() {
        // 通道费保留N位小数
        BigDecimal sellPrice = new BigDecimal("1.11");
        log.info("商品价格: {}", sellPrice);
        BigDecimal multiply = sellPrice.multiply(LiveGoodsConstant.CHANNEL_RATE);
        log.info("通道费-原始: {}", multiply);
        BigDecimal channelAmount = sellPrice.multiply(LiveGoodsConstant.CHANNEL_RATE).setScale(2, RoundingMode.HALF_UP);
        log.info("通道费-处理: {}", channelAmount);

        log.info("-------------------------");

        // 通道费不足1分，直接记为零
        sellPrice = new BigDecimal("0.1");
        log.info("商品价格: {}", sellPrice);
        multiply = sellPrice.multiply(LiveGoodsConstant.CHANNEL_RATE);
        log.info("通道费-原始: {}", multiply);
        if (multiply.compareTo(new BigDecimal("0.01")) < 0) {
            channelAmount = BigDecimal.ZERO;
        } else {
            channelAmount = sellPrice.multiply(LiveGoodsConstant.CHANNEL_RATE).setScale(2, RoundingMode.HALF_UP);
        }
        log.info("通道费-处理(不足1分): {}", channelAmount);
    }

}