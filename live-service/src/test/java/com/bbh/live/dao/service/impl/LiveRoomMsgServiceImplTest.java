package com.bbh.live.dao.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import com.bbh.live.core.msg.ImProperties;
import com.bbh.live.core.msg.MsgDTO;
import com.bbh.live.service.msg.MsgContext;
import com.bbh.live.service.msg.dto.SystemNoticeMsgDTO;
import com.bbh.live.service.msg.dto.base.IMsg;
import com.bbh.live.service.msg.dto.base.SourceType;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;

import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

@Slf4j
class LiveRoomMsgServiceImplTest {

    @Test
    void buildSystemNotice() {
        SystemNoticeMsgDTO systemNoticeMsgDTO = new SystemNoticeMsgDTO();
        systemNoticeMsgDTO.setLiveRoomId(1L);
        systemNoticeMsgDTO.setContent("测试");
        systemNoticeMsgDTO.setSourceType(SourceType.SYSTEM_NOTICE.getCode());
        MsgDTO<SystemNoticeMsgDTO> noticeMsg = buildMsgDTO(systemNoticeMsgDTO, new MsgContext().setSenderUserId(0L).setSenderSeatId(0L).setLiveRoomId(1L));
        noticeMsg.setMsgId("-1");
        // 转Map，同时把所有的key转成下划线
        ObjectMapper mapper = new ObjectMapper();
        mapper.setPropertyNamingStrategy(PropertyNamingStrategies.SNAKE_CASE);
        try {
            Map<String, Object> objectMap = Convert.toMap(String.class, Object.class, mapper.readValue(mapper.writeValueAsString(noticeMsg), Map.class));
            log.info("{}", objectMap);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }

    public <T extends IMsg>MsgDTO<T> buildMsgDTO(T t, MsgContext context) {
        // 组装消息
        MsgDTO<T> msgDTO = new MsgDTO<>();
        msgDTO.setSenderUserId(context.getSenderUserId());
        msgDTO.setSenderSeatId(context.getSenderSeatId());
        msgDTO.setMsgChannel(ImProperties.LIVE_ROOM_CHAT_ROOM_ID_PREFIX);
        msgDTO.setMsgType(t.type());
        msgDTO.setData(t);

        // 如果上下文中没有直播间id，那只要全局聊天室
        if (context.getLiveRoomId() == null) {
            msgDTO.setChatRoomIds(CollectionUtil.newHashSet(ImProperties.LIVE_ROOM_CHAT_ROOM_FULL_INFO));
        } else {
            msgDTO.setChatRoomIds(CollectionUtil.newHashSet(ImProperties.LIVE_ROOM_CHAT_ROOM_ID_PREFIX + context.getLiveRoomId(), ImProperties.LIVE_ROOM_CHAT_ROOM_FULL_INFO));
            msgDTO.setLiveRoomIds(CollectionUtil.newHashSet(context.getLiveRoomId()));
        }
        return msgDTO;
    }

}