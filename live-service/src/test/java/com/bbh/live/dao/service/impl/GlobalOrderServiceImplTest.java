package com.bbh.live.dao.service.impl;

import com.bbh.live.service.order.dto.OrderPaidInfo;
import com.bbh.model.GlobalOrder;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.Date;

@Slf4j
@SpringBootTest
@AutoConfigureMockMvc
class GlobalOrderServiceImplTest {

    @Autowired
    private GlobalOrderServiceImpl globalOrderServiceImpl;

    @Test
    void orderPaid() {
        GlobalOrder globalOrder = new GlobalOrder();
        globalOrder.setOrderNo("LIVE_G_O_20240918092219356265");
        globalOrder.setId(953L);

        OrderPaidInfo orderPaidInfo = new OrderPaidInfo();
        orderPaidInfo.setPayAt(new Date());
        orderPaidInfo.setTransAmt("1000");

        //globalOrderServiceImpl.orderPaid(globalOrder, orderPaidInfo);
    }
}