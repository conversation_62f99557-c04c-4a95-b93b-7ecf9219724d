package com.bbh.live.flows;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;

import java.io.UnsupportedEncodingException;
import java.util.function.Consumer;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * 商品清单流程测试
 *
 * <AUTHOR>
 */
@Slf4j
@SpringBootTest
@AutoConfigureMockMvc
public class LiveGoodsListFlowTest {

    @Autowired
    private MockMvc mockMvc;

    private static final int LIVE_ROOM_ID = 45;
    private static final String BUYER_TOKEN = "Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.6UD2dcO9pna9K0qCZxF_uYYwKW_TFx8QIVhD_vpprjg";

    @BeforeEach
    void setUp() {
        log.info("Starting test: {}", this.getClass().getSimpleName());
    }

    @Test
    public void testLiveGoodsFlow() throws Exception {
        log.info("Starting live goods flow test");

        // 0. 清空商品清单
        clearGoods();

        // 1. 从直播仓库添加3个商品到清单
        addGoodsToList(48105, 48106, 48107);

        // 2. 检查商品清单
        JSONObject data = checkGoodsList((json) -> {
            assertEquals(3, json.getJSONObject("data").getJSONArray("live_goods_list").size());
        });

        // 3. 更新任意两个的顺序
        JSONArray liveGoodsList = data.getJSONObject("data").getJSONArray("live_goods_list");
        JSONArray sortList = new JSONArray();
        for (int i = 0; i < liveGoodsList.size(); i++) {
            JSONObject liveGoods = liveGoodsList.getJSONObject(i);
            // 互换第一个和第二个的顺序
            if (i == 0) {
                JSONObject x = new JSONObject();
                x.set("live_goods_id", liveGoods.getLong("id"));
                x.set("sort", 2);
                sortList.add(x);
            } else if (i == 1) {
                JSONObject x = new JSONObject();
                x.set("live_goods_id", liveGoods.getLong("id"));
                x.set("sort", 1);
                sortList.add(x);
            }
        }
        updateGoodsOrder(sortList);

        // 4. 检查商品清单各商品顺序
        data = checkGoodsList(json -> {
            JSONArray goodsList = json.getJSONObject("data").getJSONArray("live_goods_list");
            // 获取顺序并打印
            goodsList.forEach(goods -> {
                JSONObject goodsJson = (JSONObject) goods;
                Integer sort = goodsJson.getInt("sort");
                log.info("goods_id:{} , sort:{}", goodsJson.getLong("id"), sort);
            });
        });

        // 5. 更新其中1个的三要素
        JSONObject goodsObj = liveGoodsList.getJSONObject(0);
        Long goodsId = goodsObj.getLong("id");
        updateGoodsInfo(goodsId);

        // 6. 检查这个商品的信息
        checkGoodsInfo(goodsId, json -> {
            JSONObject entries = json.getJSONObject("data");
            assertEquals(300, entries.getInt("start_price"));
            assertEquals(100, entries.getInt("increase_price"));
            assertEquals(30, entries.getInt("auction_duration"));
        });

        // 7. 更新任意两个的顺序
        liveGoodsList = data.getJSONObject("data").getJSONArray("live_goods_list");
        sortList = new JSONArray();
        for (int i = 0; i < liveGoodsList.size(); i++) {
            JSONObject liveGoods = liveGoodsList.getJSONObject(i);
            // 互换第一个和第二个的顺序
            if (i == 0) {
                JSONObject x = new JSONObject();
                x.set("live_goods_id", liveGoods.getLong("id"));
                x.set("sort", 2);
                sortList.add(x);
            } else if (i == 1) {
                JSONObject x = new JSONObject();
                x.set("live_goods_id", liveGoods.getLong("id"));
                x.set("sort", 1);
                sortList.add(x);
            }
        }
        updateGoodsOrder(sortList);

        // 8. 检查商品清单各商品顺序
        checkGoodsList(json -> {
            JSONArray goodsList = json.getJSONObject("data").getJSONArray("live_goods_list");
            // 获取顺序并打印
            goodsList.forEach(goods -> {
                JSONObject goodsJson = (JSONObject) goods;
                Integer sort = goodsJson.getInt("sort");
                log.info("goods_id:{} , sort:{}", goodsJson.getLong("id"), sort);
            });
        });

        // 9. 从直播仓库添加2个商品到清单，这里要所有的id
        addGoodsToList(48105, 48106, 48107, 48214, 48206, 48220, 48178);

        // 10. 检查商品清单
        data = checkGoodsList(json -> {
            assertEquals(7, json.getJSONObject("data").getJSONArray("live_goods_list").size());
        });

        // 11. 批量删除2个商品
        JSONArray goodsList = data.getJSONObject("data").getJSONArray("live_goods_list");
        removeGoodsFromList(goodsList.getJSONObject(0).getInt("id"), goodsList.getJSONObject(1).getInt("id"));

        // 12. 检查商品清单
        checkGoodsList(json -> {
            assertEquals(5, json.getJSONObject("data").getJSONArray("live_goods_list").size());
        });

        log.info("Live goods flow test completed");
    }

    private void clearGoods() throws Exception  {
        log.info("===========> clear all goods");
        JSONObject requestBody = new JSONObject();
        // 添加必要的请求参数
        requestBody.set("live_room_id", LIVE_ROOM_ID);

        MvcResult result = performPostRequest("/test/clearGoods", requestBody);

        // 解析响应并进行断言
        log.info("===========> clear all goods");
        assertTrue(is200(result));
    }

    private void addGoodsToList(int...globalGoodsId) throws Exception {
        log.info("===========> Adding {} goods to the list", globalGoodsId);
        JSONObject requestBody = new JSONObject();
        // 添加必要的请求参数
        requestBody.set("live_room_id", LIVE_ROOM_ID);
        requestBody.set("goods_id_list", globalGoodsId);

        MvcResult result = performPostRequest("/director/liveGoods/addGoodsList", requestBody);

        // 解析响应并进行断言
        log.info("===========> Added {} goods to the list. Response: {}", globalGoodsId, result.getResponse().getContentAsString());
        assertTrue(is200(result));
    }

    private JSONObject checkGoodsList(Consumer<JSONObject> assertConsumer) throws Exception {
        log.info("Checking goods list");
        JSONObject requestBody = new JSONObject();
        // 添加必要的请求参数
        requestBody.set("live_room_id", LIVE_ROOM_ID);
        // "{\"column\" : \"sort\", \"direction\" : \"ASC\"}"
        requestBody.set("sort", new JSONObject().set("column", "sort").set("direction", "ASC"));

        MvcResult result = performPostRequest("/director/liveGoods/getGoodsCheckList", requestBody);

        // 解析响应并进行断言
        log.info("Checked goods list. Response: {}", result.getResponse().getContentAsString());
        JSONObject jsonObject = JSONUtil.parseObj(result.getResponse().getContentAsString());
        assertConsumer.accept(jsonObject);
        return jsonObject;
    }

    private void updateGoodsOrder(JSONArray sortList) throws Exception {
        log.info("Updating order for {} goods", sortList);
        JSONObject requestBody = new JSONObject();
        // 添加必要的请求参数
        requestBody.set("live_room_id", LIVE_ROOM_ID);
        requestBody.set("sort_list", sortList);

        MvcResult result = performPostRequest("/director/liveGoods/batchUpdateLiveGoodsSort", requestBody);

        // 解析响应并进行断言
        log.info("Updated order for {} goods. Response: {}", sortList, result.getResponse().getContentAsString());
        assertTrue(is200(result));
    }

    private void updateGoodsInfo(long goodsId) throws Exception {
        log.info("Updating info for goods ID: {}", goodsId);
        JSONObject requestBody = new JSONObject();
        // 添加必要的请求参数
        requestBody.put("live_room_id", LIVE_ROOM_ID);
        requestBody.put("live_goods_id", goodsId);
        requestBody.set("start_price", 300);
        requestBody.set("increase_price", 100);
        requestBody.set("auction_duration", 30);

        MvcResult result = performPostRequest("/director/liveGoods/completePutAwayInfo", requestBody);

        // 解析响应并进行断言
        log.info("Updated info for goods ID: {}. Response: {}", goodsId, result.getResponse().getContentAsString());
        assertTrue(is200(result));
    }

    private void checkGoodsInfo(long goodsId, Consumer<JSONObject> assertConsumer) throws Exception {
        log.info("Checking info for goods ID: {}", goodsId);
        JSONObject requestBody = new JSONObject();
        // 添加必要的请求参数
        requestBody.set("live_room_id", LIVE_ROOM_ID);
        requestBody.set("live_goods_id", goodsId);

        MvcResult result = performPostRequest("/buyer/liveGoods/getGoodsInfo", requestBody);

        // 解析响应并进行断言
        log.info("Checked info for goods ID: {}. Response: {}", goodsId, result.getResponse().getContentAsString());
        assertConsumer.accept(JSONUtil.parseObj(result.getResponse().getContentAsString()));
    }

    private void removeGoodsFromList(int...liveGoodsId) throws Exception {
        log.info("Removing {} goods from the list", liveGoodsId);
        JSONObject requestBody = new JSONObject();
        // 添加必要的请求参数
        requestBody.set("live_room_id", LIVE_ROOM_ID);
        requestBody.set("goods_id_list", liveGoodsId);

        MvcResult result = performPostRequest("/director/liveGoods/removeGoodsList", requestBody);

        // 解析响应并进行断言
        log.info("Removed {} goods from the list. Response: {}", liveGoodsId, result.getResponse().getContentAsString());
        assertTrue(is200(result));
    }

    private MvcResult performPostRequest(String url, JSONObject requestBody) throws Exception {
        String jsonBody = requestBody.toString();
        log.debug("Sending POST request to {}. Request body: {}", url, jsonBody);

        MvcResult result = mockMvc.perform(post(url)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(jsonBody)
                        .header("Authorization", BUYER_TOKEN)
                )
                .andExpect(status().isOk())
                .andReturn();

        log.debug("Received response from {}. Response body: {}", url, result.getResponse().getContentAsString());
        return result;
    }

    private boolean is200(MvcResult result) {
        try {
            return JSONUtil.parseObj(result.getResponse().getContentAsString()).getInt("code") == 200;
        } catch (UnsupportedEncodingException e) {
            throw new RuntimeException(e);
        }
    }

}
