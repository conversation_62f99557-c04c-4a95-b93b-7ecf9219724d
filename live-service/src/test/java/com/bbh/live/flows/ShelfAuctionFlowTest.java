package com.bbh.live.flows;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;

import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.util.List;
import java.util.function.Consumer;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@Slf4j
@SpringBootTest
@AutoConfigureMockMvc
public class ShelfAuctionFlowTest {

    @Autowired
    private MockMvc mockMvc;

    private static final int LIVE_ROOM_ID = 44;
    private static final String DIRECTOR_TOKEN = "Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpZl9tYXN0ZXIiOjEsIm5iZiI6MTcyNTcwNzc3NCwidXNlcl9pZCI6NTIyNSwib3JnX2lkIjoyMDc4LCJzZWF0X2lkIjozOTIwMDM4LCJleHAiOjE3MjgyOTk3NzQsImlhdCI6MTcyNTcwNzc3NH0.w-5WOE0O5wd0zepvW9QFw7nowvjXTWAkrptBsvFiAY8";
    // seatId = 645
    private static final String BUYER_A_TOKEN = "Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.PXwQctX2vKppHaZS-hROd1-JTx-wBblCiayzRiJlgoM";
    // seatId = 3920042, orgId = 2082
    private static final String BUYER_B_TOKEN = "Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpZl9tYXN0ZXIiOjEsIm5iZiI6MTcyNTcwNzc1NSwidXNlcl9pZCI6NTIyOSwib3JnX2lkIjoyMDgyLCJzZWF0X2lkIjozOTIwMDQyLCJleHAiOjE3MjgyOTk3NTUsImlhdCI6MTcyNTcwNzc1NX0.4XGE1cwBHDVX0lrWoJfyciA7zTIDazzWSomWFn7J7Vs";

    @SneakyThrows
    @BeforeEach
    void setUp() {
        log.info("Starting test: {}", this.getClass().getSimpleName());

        // 所有商品都是待上架
//        resetGoodsStatus();
        // 清空收银台
//        resetShoppingCart(BUYER_A_TOKEN);
    }

    @Test
    public void testShelfAuctionFlow() throws Exception {
        // 商品状态: 00-待完善, 10-待上架, 20-上架中, 30-竞拍中, 40-已流拍, 50-已成交, 60-已下架
        log.info("Starting shelf auction flow test");

        // 所有商品都是待上架
//        resetGoodsStatus();
        // 清空收银台
//        resetShoppingCart(BUYER_A_TOKEN);

        // 1. 检查商品列表
        JSONObject goodsListResp = checkGoodsList(json -> {
            // Add assertions for goods list
        });
        JSONArray goodsArray = goodsListResp.getJSONObject("data").getJSONArray("live_goods_list");
        List<JSONObject> goodsList = goodsArray.toList(JSONObject.class);

        // 2. 上架讲解一个没有起拍价的商品
        goodsList.stream().filter(x -> x.getBigDecimal("start_price") == null).findAny().ifPresent(x -> {
            Long goodsWithoutStartPrice = x.getLong("id");
            try {
                putAwayAndExplainGoods(goodsWithoutStartPrice, false);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        });

        // 3. 上架讲解一个有起拍价的商品
        Long goodsWithStartPrice = goodsList.stream().filter(x -> x.getBigDecimal("start_price") != null).findFirst().get().getLong("id");
        putAwayAndExplainGoods(goodsWithStartPrice, true);

        // 4. 修改它的三要素，应该要报错
        updateGoodsInfo(goodsWithStartPrice, false);

        // 5. 检查数据
        checkGoodsInfo(goodsWithStartPrice, json -> {
            Integer status = json.getJSONObject("data").getInt("goods_status");
            // 20-上架中
            assertEquals(20, status);
        });

        // 6. 开始竞拍
        startAuction(goodsWithStartPrice);

        // 7. 无人出价，自动流拍（等待40秒）
        log.info("Waiting for 40 seconds...");
        Thread.sleep(40000);

        // 8. 检查数据
        checkGoodsInfo(goodsWithStartPrice, json -> {
            var status = json.getJSONObject("data").getInt("goods_status");
            // 40-已流拍
            assertEquals(40, status);
        });

        // 9. 重新上架
        reshelveGoods(goodsWithStartPrice);

        // 10. 检查数据（是否在待上架）
        checkGoodsInfo(goodsWithStartPrice, json -> {
            Integer status = json.getJSONObject("data").getInt("goods_status");
            // 10-待上架
            assertEquals(10, status);
        });

        // 11. 上架讲解
        putAwayAndExplainGoods(goodsWithStartPrice, true);

        // 12. 检查数据
        checkGoodsInfo(goodsWithStartPrice, json -> {
            Integer status = json.getJSONObject("data").getInt("goods_status");
            // 20-上架中
            assertEquals(20, status);
        });

        // 13. 买手A发弹幕消息
        sendDanmaku(BUYER_A_TOKEN, "Hello from Buyer A");

        // 14. 开始竞拍
        startAuction(goodsWithStartPrice);

        // 15. 买手A竞拍出价
        placeBid(BUYER_A_TOKEN, goodsWithStartPrice, 1000);

        // 16. 检查数据
        checkGoodsInfo(goodsWithStartPrice, json -> {
            var status = json.getJSONObject("data").getDouble("goods_status");
            // 30-竞拍中
            assertEquals(30, status);
        });

        // 17. 买手B竞拍出价
        placeBid(BUYER_B_TOKEN, goodsWithStartPrice, 1200);

        // 18. 检查数据
        checkGoodsInfo(goodsWithStartPrice, json -> {
            var status = json.getJSONObject("data").getInt("goods_status");
            // 30-竞拍中
            assertEquals(30, status);
        });

        // 19. 买手A在最后几秒竞拍出价
        log.info("Waiting for X seconds...");
        Thread.sleep(20000);  // Wait for 20 seconds
        placeBid(BUYER_A_TOKEN, goodsWithStartPrice, 1400);

        // 20. 检查数据
        checkGoodsInfo(goodsWithStartPrice, json -> {
            // Add assertions for goods status after Buyer A's last-second bid
        });

        // 21. 无人出价，买手A竞拍成功（等待40秒）
        log.info("Waiting for 40 seconds...");
        Thread.sleep(40000);

        // 22. 检查商品
        checkGoodsInfo(goodsWithStartPrice, json -> {
            var status = json.getJSONObject("data").getInt("goods_status");
            // 50-竞拍成功
            assertEquals(50, status);
            var sellPrice = json.getJSONObject("data").getBigDecimal("sell_price");
            // 1400
            assertEquals(new BigDecimal("1400.00"), sellPrice);
        });

        // 23. 检查买手A保证金
//        checkBuyerDeposit(BUYER_A_TOKEN, json -> {
//            // Add assertions for Buyer A's deposit
//        });

        // 24. 检查买手A收银台
        checkBuyerCashierDesk(BUYER_A_TOKEN, json -> {
            var listWaitPayInfo = json.getJSONObject("data").getJSONArray("list_wait_pay_info");
            assertEquals(1, listWaitPayInfo.size());
        });

        // 25. 检查买手B收银台
        checkBuyerCashierDesk(BUYER_B_TOKEN, json -> {
            var listWaitPayInfo = json.getJSONObject("data").getJSONArray("list_wait_pay_info");
            assertEquals(0, listWaitPayInfo.size());
        });

        // 26. 检查买手B保证金
//        checkBuyerDeposit(BUYER_B_TOKEN, json -> {
//            // Add assertions for Buyer B's deposit
//        });

        log.info("Shelf auction flow test completed");
    }

    private JSONObject checkGoodsList(Consumer<JSONObject> assertConsumer) throws Exception {
        log.info("Checking goods list");
        JSONObject requestBody = new JSONObject();
        requestBody.set("live_room_id", LIVE_ROOM_ID);
        requestBody.set("sort", new JSONObject().set("column", "sort").set("direction", "ASC"));

        MvcResult result = performPostRequest("/director/liveGoods/getGoodsCheckList", requestBody, DIRECTOR_TOKEN);

        log.info("Checked goods list. Response: {}", result.getResponse().getContentAsString());
        JSONObject jsonObject = JSONUtil.parseObj(result.getResponse().getContentAsString());
        assertConsumer.accept(jsonObject);
        return jsonObject;
    }

    private void putAwayAndExplainGoods(Long goodsId, boolean expectSuccess) throws Exception {
        log.info("Putting away and explaining goods ID: {}", goodsId);
        JSONObject requestBody = new JSONObject();
        requestBody.set("live_room_id", LIVE_ROOM_ID);
        requestBody.set("live_goods_id", goodsId);

        MvcResult result = performPostRequest("/director/liveGoods/putAwayAndExplain", requestBody, DIRECTOR_TOKEN);

        log.info("Put away and explained goods ID: {}. Response: {}", goodsId, result.getResponse().getContentAsString());
        assertEquals(expectSuccess, is200(result));
    }

    private void resetGoodsStatus() throws Exception {
        JSONObject requestBody = new JSONObject();
        requestBody.set("room_id", LIVE_ROOM_ID);
        MvcResult result = performPostRequest("/test/resetGoodsStatus", requestBody, DIRECTOR_TOKEN);
        assertTrue(is200(result));
    }

    private void resetShoppingCart(String token) throws Exception {
        MvcResult result = performPostRequest("/test/resetShoppingCart", new JSONObject(), token);
        assertTrue(is200(result));
    }

    private void updateGoodsInfo(Long goodsId, boolean expectSuccess) throws Exception {
        log.info("Updating info for goods ID: {}", goodsId);
        JSONObject requestBody = new JSONObject();
        requestBody.set("live_room_id", LIVE_ROOM_ID);
        requestBody.set("live_goods_id", goodsId);
        requestBody.set("start_price", 300);
        requestBody.set("increase_price", 100);
        requestBody.set("auction_duration", 30);

        MvcResult result = performPostRequest("/director/liveGoods/completePutAwayInfo", requestBody, DIRECTOR_TOKEN);

        log.info("Updated info for goods ID: {}. Response: {}", goodsId, result.getResponse().getContentAsString());
        assertEquals(expectSuccess, is200(result));
    }

    private void checkGoodsInfo(Long goodsId, Consumer<JSONObject> assertConsumer) throws Exception {
        log.info("Checking info for goods ID: {}", goodsId);
        JSONObject requestBody = new JSONObject();
        requestBody.set("live_room_id", LIVE_ROOM_ID);
        requestBody.set("live_goods_id", goodsId);

        MvcResult result = performPostRequest("/buyer/liveGoods/getGoodsInfo", requestBody, BUYER_A_TOKEN);

        log.info("Checked info for goods ID: {}. Response: {}", goodsId, result.getResponse().getContentAsString());
        assertConsumer.accept(JSONUtil.parseObj(result.getResponse().getContentAsString()));
    }

    private void startAuction(Long goodsId) throws Exception {
        log.info("Starting auction for goods ID: {}", goodsId);
        JSONObject requestBody = new JSONObject();
        requestBody.set("live_room_id", LIVE_ROOM_ID);
        requestBody.set("live_goods_id", goodsId);

        MvcResult result = performPostRequest("/director/liveGoods/auction", requestBody, DIRECTOR_TOKEN);

        log.info("Started auction for goods ID: {}. Response: {}", goodsId, result.getResponse().getContentAsString());
        assertTrue(is200(result));
    }

    private void reshelveGoods(Long goodsId) throws Exception {
        log.info("Reshelving goods ID: {}", goodsId);
        JSONObject requestBody = new JSONObject();
        requestBody.set("live_room_id", LIVE_ROOM_ID);
        requestBody.set("live_goods_id", goodsId);

        MvcResult result = performPostRequest("/director/liveGoods/reShelve", requestBody, DIRECTOR_TOKEN);

        log.info("Reshelved goods ID: {}. Response: {}", goodsId, result.getResponse().getContentAsString());
        assertTrue(is200(result));
    }

    private void sendDanmaku(String token, String message) throws Exception {
        log.info("Sending danmaku message: {}", message);
        JSONObject requestBody = new JSONObject();
        requestBody.set("live_room_id", LIVE_ROOM_ID);
        requestBody.set("content", message);

        MvcResult result = performPostRequest("/room/message/send/text", requestBody, token);

        log.info("Sent danmaku message. Response: {}", result.getResponse().getContentAsString());
        assertTrue(is200(result));
    }

    private void placeBid(String token, Long goodsId, int price) throws Exception {
        log.info("Placing bid for goods ID: {} with price: {}", goodsId, price);
        JSONObject requestBody = new JSONObject();
        requestBody.set("live_room_id", LIVE_ROOM_ID);
        requestBody.set("live_goods_id", goodsId);
        requestBody.set("bid_price", price);

        MvcResult result = performPostRequest("/buyer/liveGoods/auctionBid", requestBody, token);

        log.info("Placed bid for goods ID: {}. Response: {}", goodsId, result.getResponse().getContentAsString());
        assertTrue(is200(result));
    }

    private void checkBuyerDeposit(String token, Consumer<JSONObject> assertConsumer) throws Exception {
        log.info("Checking buyer deposit");
        JSONObject requestBody = new JSONObject();
        requestBody.set("live_room_id", LIVE_ROOM_ID);

        MvcResult result = performPostRequest("/buyer/deposit/getInfo", requestBody, token);

        log.info("Checked buyer deposit. Response: {}", result.getResponse().getContentAsString());
        assertConsumer.accept(JSONUtil.parseObj(result.getResponse().getContentAsString()));
    }

    private void checkBuyerCashierDesk(String token, Consumer<JSONObject> assertConsumer) throws Exception {
        log.info("Checking buyer cashier desk");
        JSONObject requestBody = new JSONObject();
        requestBody.set("live_room_id", LIVE_ROOM_ID);

        MvcResult result = performPostRequest("/order/getCashierDeskInfo", requestBody, token);

        log.info("Checked buyer cashier desk. Response: {}", result.getResponse().getContentAsString());
        assertConsumer.accept(JSONUtil.parseObj(result.getResponse().getContentAsString()));
    }

    private MvcResult performPostRequest(String url, JSONObject requestBody, String token) throws Exception {
        String jsonBody = requestBody.toString();
        log.debug("Sending POST request to {}. Request body: {}", url, jsonBody);

        MvcResult result = mockMvc.perform(post(url)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(jsonBody)
                        .header("Authorization", token)
                )
                .andExpect(status().isOk())
                .andReturn();

        log.debug("Received response from {}. Response body: {}", url, result.getResponse().getContentAsString());
        return result;
    }

    private boolean is200(MvcResult result) {
        try {
            return JSONUtil.parseObj(result.getResponse().getContentAsString()).getInt("code") == 200;
        } catch (UnsupportedEncodingException e) {
            throw new RuntimeException(e);
        }
    }
}