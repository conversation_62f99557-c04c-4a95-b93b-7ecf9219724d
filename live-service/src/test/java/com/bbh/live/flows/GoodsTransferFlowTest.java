package com.bbh.live.flows;

import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.bbh.live.dao.mapper.LiveGoodsTransferMapper;
import com.bbh.model.LiveGoodsTransfer;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

import java.io.UnsupportedEncodingException;
import java.util.function.Consumer;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@Slf4j
@SpringBootTest
@AutoConfigureMockMvc
public class GoodsTransferFlowTest {

    @Autowired
    private MockMvc mockMvc;

    private static final int LIVE_ROOM_ID = 44;
    private static final String DIRECTOR_TOKEN = "Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpZl9tYXN0ZXIiOjEsIm5iZiI6MTcyNTcwNzc3NCwidXNlcl9pZCI6NTIyNSwib3JnX2lkIjoyMDc4LCJzZWF0X2lkIjozOTIwMDM4LCJleHAiOjE3MjgyOTk3NzQsImlhdCI6MTcyNTcwNzc3NH0.w-5WOE0O5wd0zepvW9QFw7nowvjXTWAkrptBsvFiAY8";
    // seatId = 41
    private static final Integer BUYER_A_SEAT_ID = 41;
    private static final String BUYER_A_TOKEN = "Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.6UD2dcO9pna9K0qCZxF_uYYwKW_TFx8QIVhD_vpprjg";
    // seatId = 3920042, orgId = 2082
    private static final Integer BUYER_B_SEAT_ID = 3920042;
    private static final String BUYER_B_TOKEN = "Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpZl9tYXN0ZXIiOjEsIm5iZiI6MTcyNTcwNzc1NSwidXNlcl9pZCI6NTIyOSwib3JnX2lkIjoyMDgyLCJzZWF0X2lkIjozOTIwMDQyLCJleHAiOjE3MjgyOTk3NTUsImlhdCI6MTcyNTcwNzc1NX0.4XGE1cwBHDVX0lrWoJfyciA7zTIDazzWSomWFn7J7Vs";

    @SneakyThrows
    @BeforeEach
    void setUp() {
        // 初始化设置
    }

    @Test
    public void testGoodsTransferFlow() throws Exception {
        // 所有商品都是待上架
        resetGoodsStatus();
        // 清空收银台
        resetShoppingCart(BUYER_A_TOKEN);
        // 清理传送记录
        clearTransferRecord();

        log.info("Starting goods transfer flow test");

        // 1. 找一个待上架商品
        Long goodsId = 248L;

        // 2. 导播发起传送给买手A
        initiateTransfer(goodsId, BUYER_A_SEAT_ID, true);

        // 3. 导播取消传送
        cancelTransfer(goodsId);

        // 4. 导播再次发起传送给买手A
        initiateTransfer(goodsId, BUYER_A_SEAT_ID, true);

        // 5. 导播发起传送给买手B（应出现报错提示）
        try {
            initiateTransfer(goodsId, BUYER_B_SEAT_ID, false);
        } catch (Exception e) {
            log.info("Expected error when initiating transfer to Buyer B: {}", e.getMessage());
        }

        // 6. 买手A关闭卡片消息
        closeBuyerCardMessage(goodsId, BUYER_A_TOKEN);

        // 7. 导播发起传送给买手A
        initiateTransfer(goodsId, BUYER_A_SEAT_ID, true);

        // 8. 买手A离开直播间甲
        buyerLeaveLiveRoom(BUYER_A_TOKEN);

        // 9. 2秒后，买手A进入直播间甲
        Thread.sleep(2000);
        buyerEnterLiveRoom(BUYER_A_TOKEN, LIVE_ROOM_ID);

        // 10. 检查初始化的卡片消息
        checkCardMessageReminder(BUYER_A_TOKEN, LIVE_ROOM_ID, json -> {
            // 添加断言逻辑
            log.info("Card message reminder: {}", json);
            JSONArray jsonArray = json.getJSONObject("data").getJSONArray("transfer");
            assertEquals(1, jsonArray.size());
            Long anLong = jsonArray.getJSONObject(0).getJSONObject("data").getJSONObject("goods").getLong("id");
            assertEquals(goodsId, anLong);
        });

        // 11. 买手A离开直播间甲
        buyerLeaveLiveRoom(BUYER_A_TOKEN);

        // 12. 买手A进入直播间乙
        int roomB = LIVE_ROOM_ID + 1; // 假设乙房间ID
        buyerEnterLiveRoom(BUYER_A_TOKEN, roomB);

        // 13. 导播发起传送给买手A（应出现报错提示）
        try {
            initiateTransfer(goodsId, BUYER_A_SEAT_ID, false);
        } catch (Exception e) {
            log.info("Expected error when initiating transfer to Buyer A in room B: {}", e.getMessage());
        }

        // 14. 检查直播间乙的卡片消息
        checkCardMessageReminder(BUYER_A_TOKEN, roomB, json -> {
            // 添加断言逻辑
        });

        // 15. 买手A离开直播间乙
        buyerLeaveLiveRoom(BUYER_A_TOKEN);

        // 16. 1秒后，买手A进入直播间乙
        Thread.sleep(1000);
        buyerEnterLiveRoom(BUYER_A_TOKEN, roomB);

        // 17. 检查直播间乙的卡片消息
        checkCardMessageReminder(BUYER_A_TOKEN, roomB, json -> {
            // 添加断言逻辑
        });

        // 18. 买手A传送成交
        buyerTakeTransferGoods(goodsId, BUYER_A_TOKEN);

        // 19. 检查商品
        checkGoodsInfo(goodsId, BUYER_A_TOKEN, json -> {
            // 添加断言逻辑
        });

        // 20. 检查买手A保证金
        checkBuyerDeposit(BUYER_A_TOKEN, json -> {
            // 添加断言逻辑
        });

        // 21. 检查买手A收银台
        checkBuyerCashierDesk(BUYER_A_TOKEN, json -> {
            // 添加断言逻辑
        });

        log.info("Goods transfer flow test completed");

    }

    private void clearTransferRecord() {
        LiveGoodsTransferMapper goodsTransferMapper = SpringUtil.getBean(LiveGoodsTransferMapper.class);
        goodsTransferMapper.delete(Wrappers.lambdaQuery(LiveGoodsTransfer.class).eq(LiveGoodsTransfer::getLiveRoomId, LIVE_ROOM_ID));
    }

    private void resetGoodsStatus() throws Exception {
        JSONObject requestBody = new JSONObject();
        requestBody.set("room_id", LIVE_ROOM_ID);
        MvcResult result = performPostRequest("/test/resetGoodsStatus", requestBody, DIRECTOR_TOKEN);
        assertTrue(is200(result));
    }

    private void resetShoppingCart(String token) throws Exception {
        MvcResult result = performPostRequest("/test/resetShoppingCart", new JSONObject(), token);
        assertTrue(is200(result));
    }

    private void initiateTransfer(Long goodsId, Integer targetSeatId, boolean doAssert) throws Exception {
        JSONObject requestBody = new JSONObject();
        requestBody.set("live_room_id", LIVE_ROOM_ID);
        requestBody.set("live_goods_id", goodsId);
        requestBody.set("transfer_price", 1000);
        requestBody.set("target_seat_id", targetSeatId);

        MvcResult result = performPostRequest("/goodsTransfer/add", requestBody, DIRECTOR_TOKEN);
        if (doAssert) {
            assertTrue(is200(result));
        }
    }

    private void cancelTransfer(Long goodsId) throws Exception {
        JSONObject requestBody = new JSONObject();
        requestBody.set("live_room_id", LIVE_ROOM_ID);
        requestBody.set("live_goods_id", goodsId);

        MvcResult result = performPostRequest("/goodsTransfer/cancel", requestBody, DIRECTOR_TOKEN);
        assertTrue(is200(result));
    }

    private void closeBuyerCardMessage(Long goodsId, String buyerToken) throws Exception {
        JSONObject requestBody = new JSONObject();
        requestBody.set("live_room_id", LIVE_ROOM_ID);
        requestBody.set("live_goods_id", goodsId);

        MvcResult result = performPostRequest("/goodsTransfer/cancel", requestBody, buyerToken);
        assertTrue(is200(result));
    }

    private void buyerLeaveLiveRoom(String buyerToken) throws Exception {

    }

    private void buyerEnterLiveRoom(String buyerToken, int roomId) throws Exception {
        JSONObject requestBody = new JSONObject();
        requestBody.set("room_id", roomId);

        MvcResult result = performPostRequest("/room/getWatcherRoomDetail", requestBody, buyerToken);
        assertTrue(is200(result));
    }

    private void checkCardMessageReminder(String buyerToken, int roomId, Consumer<JSONObject> assertConsumer) throws Exception {
        MvcResult result = performPostRequest("/room/message/getGlobalMsgGroup", new JSONObject(), buyerToken);
        JSONObject response = JSONUtil.parseObj(result.getResponse().getContentAsString());
        assertConsumer.accept(response);
    }

    private void buyerTakeTransferGoods(Long goodsId, String buyerToken) throws Exception {
        JSONObject requestBody = new JSONObject();
        requestBody.set("live_room_id", LIVE_ROOM_ID);
        requestBody.set("live_goods_id", goodsId);

        MvcResult result = performPostRequest("/buyer/liveGoods/takeTransferGoods", requestBody, buyerToken);
        assertTrue(is200(result));
    }

    private void checkGoodsInfo(Long goodsId, String buyerToken, Consumer<JSONObject> assertConsumer) throws Exception {
        JSONObject requestBody = new JSONObject();
        requestBody.set("live_room_id", LIVE_ROOM_ID);
        requestBody.set("live_goods_id", goodsId);

        MvcResult result = performPostRequest("/buyer/liveGoods/getGoodsInfo", requestBody, buyerToken);
        JSONObject response = JSONUtil.parseObj(result.getResponse().getContentAsString());
        assertConsumer.accept(response);
    }

    private void checkBuyerDeposit(String buyerToken, Consumer<JSONObject> assertConsumer) throws Exception {
        JSONObject requestBody = new JSONObject();
        requestBody.set("live_room_id", LIVE_ROOM_ID);

        MvcResult result = performPostRequest("/buyer/deposit/getInfo", requestBody, buyerToken);
        JSONObject response = JSONUtil.parseObj(result.getResponse().getContentAsString());
        assertConsumer.accept(response);
    }

    private void checkBuyerCashierDesk(String buyerToken, Consumer<JSONObject> assertConsumer) throws Exception {
        JSONObject requestBody = new JSONObject();
        requestBody.set("live_room_id", LIVE_ROOM_ID);

        MvcResult result = performPostRequest("/order/getCashierDeskInfo", requestBody, buyerToken);
        JSONObject response = JSONUtil.parseObj(result.getResponse().getContentAsString());
        assertConsumer.accept(response);
    }

    private MvcResult performPostRequest(String url, JSONObject requestBody, String token) throws Exception {
        String jsonBody = requestBody.toString();
        log.debug("Sending POST request to {}. Request body: {}", url, jsonBody);

        MvcResult result = mockMvc.perform(MockMvcRequestBuilders.post(url)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(jsonBody)
                        .header("Authorization", token)
                )
                .andExpect(status().isOk())
                .andReturn();

        log.debug("Received response from {}. Response body: {}", url, result.getResponse().getContentAsString());
        return result;
    }

    private boolean is200(MvcResult result) {
        try {
            return JSONUtil.parseObj(result.getResponse().getContentAsString()).getInt("code") == 200;
        } catch (UnsupportedEncodingException e) {
            throw new RuntimeException(e);
        }
    }
}