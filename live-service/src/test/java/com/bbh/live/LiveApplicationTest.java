package com.bbh.live;

import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = LiveServiceApplication.class)
@ActiveProfiles("dev")
public class LiveApplicationTest {

    @Test
    public void test() {
        log.info("test");
        Assert.assertTrue(true);
    }

}
