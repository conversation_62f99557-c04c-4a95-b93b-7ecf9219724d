package com.bbh.live.util;

import com.bbh.model.LiveRoom;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.BeanWrapper;
import org.springframework.beans.PropertyAccessorFactory;

import java.beans.PropertyDescriptor;
import java.math.BigDecimal;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;

@Slf4j
class BeanUtilTest {

    @Test
    void loadPropertyTest() {
        LiveRoom liveRoom = new LiveRoom();
        liveRoom.setGoodsCount(100);
        liveRoom.setSoldAmount(BigDecimal.TWO);
        liveRoom.setSoldCount(1);
        liveRoom.setSort(10);
        liveRoom.setIfTestBroadcast(false);

        List<Function<LiveRoom, Object>> propertyGetters = List.of(
                LiveRoom::getSoldAmount,
                LiveRoom::getSoldCount,
                LiveRoom::getGoodsCount,
                LiveRoom::getSubscribeCount,
                LiveRoom::getUnsoldCount,
                LiveRoom::getViewCount,
                LiveRoom::getRealCount
        );

        BeanWrapper beanWrapper = PropertyAccessorFactory.forBeanPropertyAccess(liveRoom);
        Map<String, String> propertiesToCache = new LinkedHashMap<>();
        PropertyDescriptor[] descriptors = beanWrapper.getPropertyDescriptors();
        for (PropertyDescriptor descriptor : descriptors) {
            if (descriptor.getReadMethod() != null) {
                String propertyName = descriptor.getName();
                Object propertyValue = beanWrapper.getPropertyValue(propertyName);
                log.info("propertyName: {}, propertyValue: {}", propertyName, propertyValue);

                if (propertyGetters.stream().anyMatch(getter -> Objects.equals(propertyValue, getter.apply(liveRoom)))) {
                    if (propertyValue != null) {
                        log.info("propertyValue: {}", propertyValue);
                        propertiesToCache.put(propertyName, propertyValue.toString());
                    }
                }
            }
        }

        log.info("propertiesToCache: {}", propertiesToCache);
    }

}
