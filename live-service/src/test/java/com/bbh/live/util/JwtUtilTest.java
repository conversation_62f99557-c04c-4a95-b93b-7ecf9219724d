package com.bbh.live.util;

import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.jwt.JWTPayload;
import cn.hutool.jwt.JWTUtil;
import com.bbh.vo.AuthUser;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;

import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

@Slf4j
public class JwtUtilTest {

    @Test
    void generateTest() {
        AuthUser authUser = new AuthUser();
        authUser.setUserId(2551L);
        authUser.setOrgId(2130L);
        authUser.setSeatId(3802L);
        authUser.setIfMaster(true);

        DateTime now = DateTime.now();
        DateTime expTime = now.offsetNew(DateField.HOUR, 24*30);
        Map<String, Object> payload = new HashMap<>(8);
        // 签发时间
        payload.put(JWTPayload.ISSUED_AT, now);
        // 过期时间
        payload.put(JWTPayload.EXPIRES_AT, expTime);
        // 生效时间
        payload.put(JWTPayload.NOT_BEFORE, now);
        // 内容
        payload.put("user_id", authUser.getUserId());
        payload.put("org_id", authUser.getOrgId());
        payload.put("seat_id", authUser.getSeatId());
        payload.put("if_master", authUser.getIfMaster() ? 1 : 0);

        String res = JWTUtil.createToken(payload, "ac10a98f995c380e4dff8833c95b47htb".getBytes(StandardCharsets.UTF_8));

        log.info("{}", res);
    }
}
