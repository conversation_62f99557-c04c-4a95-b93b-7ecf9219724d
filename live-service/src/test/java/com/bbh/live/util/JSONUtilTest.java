package com.bbh.live.util;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.bbh.live.core.msg.MsgDTO;
import com.bbh.live.service.msg.dto.GoodsTransferMsgDTO;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;

import java.util.Date;

@Slf4j
class JSONUtilTest {

    @Test
    void toUnderline() {
        MsgDTO<GoodsTransferMsgDTO> msgDTO = new MsgDTO<>();
        msgDTO.setMsgType("goodsTransfer");
        msgDTO.setData(new GoodsTransferMsgDTO().setExpiredAt(new Date()));
        String json = JSONUtil.toJsonStr(msgDTO);
        log.info("json:{}", json);
        String toUnderline = JSONUtils.toUnderlineJsonStr(msgDTO);
        log.info("toUnderline:{}", toUnderline);
    }

    @Test
    void parseDeepJson() {
        String content = "{\"nonce\": \"492560087\", \"appKey\": \"82hegw5u86gex\", \"msgUID\": \"CHEV-V8UR-E72G-GNLN\", \"source\": \"Server\", \"content\": \"{\\\"content\\\":\\\"{\\\\\\\"msgId\\\\\\\":\\\\\\\"202408221050571777012\\\\\\\",\\\\\\\"msgChannel\\\\\\\":\\\\\\\"htbLr\\\\\\\",\\\\\\\"msgType\\\\\\\":\\\\\\\"userEnter\\\\\\\",\\\\\\\"senderUserId\\\\\\\":0,\\\\\\\"chatRoomIds\\\\\\\":[\\\\\\\"htbLrFi\\\\\\\",\\\\\\\"htbLr3\\\\\\\"],\\\\\\\"liveRoomIds\\\\\\\":[3],\\\\\\\"data\\\\\\\":{\\\\\\\"liveRoomId\\\\\\\":3,\\\\\\\"userId\\\\\\\":25,\\\\\\\"seatId\\\\\\\":3507,\\\\\\\"isVip\\\\\\\":false,\\\\\\\"avatar\\\\\\\":\\\\\\\"https://bang-file.oss-cn-shanghai.aliyuncs.com/htb/headimg/10.png\\\\\\\"}}\\\",\\\"extra\\\":\\\"userEnter\\\"}\", \"toUserId\": \"htbLr3\", \"signature\": \"804df076d38bb244ec06e67274ea2f7ce3a1f4ff\", \"timestamp\": \"1724295057268\", \"fromUserId\": \"0\", \"objectName\": \"RC:TxtMsg\", \"channelType\": \"TEMPGROUP\", \"msgTimestamp\": \"1724295057268\", \"sensitiveType\": \"0\", \"signTimestamp\": \"1724295057268\"}";
        JSONObject jsonObject = JSONUtil.parseObj(content);
        log.info("{}", jsonObject.toStringPretty());
        String CONTENT = "content";
        if (jsonObject.containsKey(CONTENT)) {
            JSONObject contentObj = JSONUtil.parseObj(jsonObject.get(CONTENT));
            log.info("{}", contentObj.toStringPretty());
            jsonObject.set(CONTENT, contentObj);
        }
        log.info("{}", jsonObject.toStringPretty());

        JSONObject nestedJson = parseNestedJson(content);
        log.info("{}", nestedJson.toStringPretty());
    }

    @Test
    void parseDeepJson2() {
        // 测试用例1：多层嵌套的 JSON
        String jsonString1 = "{\"level1\":\"{\\\"level2\\\":\\\"{\\\\\\\"level3\\\\\\\":{\\\\\\\"msgId\\\\\\\":\\\\\\\"202408221050571777012\\\\\\\",\\\\\\\"msgChannel\\\\\\\":\\\\\\\"htbLr\\\\\\\",\\\\\\\"msgType\\\\\\\":\\\\\\\"userEnter\\\\\\\",\\\\\\\"data\\\\\\\":{\\\\\\\"liveRoomId\\\\\\\":3,\\\\\\\"userId\\\\\\\":25}}}\\\",\\\"extra\\\":\\\"nestedData\\\"}\",\"topLevel\":\"value\"}";

        // 测试用例2：单层 JSON
        String jsonString2 = "{\"msgId\":\"202408221050571777012\",\"msgChannel\":\"htbLr\",\"msgType\":\"userEnter\",\"data\":{\"liveRoomId\":3,\"userId\":25}}";

        System.out.println("解析多层嵌套的 JSON:");
        JSONObject result1 = parseNestedJson(jsonString1);
        System.out.println(result1.toStringPretty());

        System.out.println("\n解析单层 JSON:");
        JSONObject result2 = parseNestedJson(jsonString2);
        System.out.println(result2.toStringPretty());
    }

    public static JSONObject parseNestedJson(String jsonString) {
        JSONObject result = JSONUtil.parseObj(jsonString);
        return parseNestedJsonObject(result);
    }

    private static JSONObject parseNestedJsonObject(JSONObject jsonObject) {
        JSONObject result = new JSONObject();

        for (String key : jsonObject.keySet()) {
            Object value = jsonObject.get(key);
            if (value instanceof String) {
                String strValue = (String) value;
                try {
                    // 尝试解析字符串值为 JSON
                    JSONObject nestedJson = JSONUtil.parseObj(strValue);
                    // 如果成功解析，递归处理嵌套的 JSON
                    result.put(key, parseNestedJsonObject(nestedJson));
                } catch (Exception e) {
                    // 如果解析失败，保留原始字符串值
                    result.put(key, strValue);
                }
            } else {
                // 对于非字符串值，直接保留
                result.put(key, value);
            }
        }

        return result;
    }


}
