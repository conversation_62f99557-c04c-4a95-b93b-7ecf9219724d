package com.bbh.live.util;

import lombok.extern.slf4j.Slf4j;
import org.junit.Test;

import java.math.BigDecimal;
import java.math.RoundingMode;

@Slf4j
public class BigDecimalTest {

    @Test
    public void bigDecimalTest() {
        BigDecimal goodsSoldAmount = new BigDecimal("350");
        BigDecimal goodsTradeCount = new BigDecimal(4);

        BigDecimal score = goodsSoldAmount.multiply(BigDecimal.valueOf(0.00033)).add(goodsTradeCount);

        log.info("goodsSoldAmount: {}", goodsSoldAmount);
        log.info("goodsTradeCount: {}", goodsTradeCount);
        log.info("score: {}", score);
    }

    @Test
    public void numberTest() {
        BigDecimal bidPrice = new BigDecimal("1.00001");
        bidPrice = bidPrice.setScale(0, RoundingMode.UP);
        log.info("bidPrice: {}", bidPrice);
        BigDecimal divide = bidPrice.divide(BigDecimal.valueOf(50), 0, RoundingMode.UP);
        log.info("divide: {}", divide);
    }
}
