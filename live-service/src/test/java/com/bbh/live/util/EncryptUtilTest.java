package com.bbh.live.util;

import com.bbh.util.EncryptUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;

@Slf4j
class EncryptUtilTest {

    @Test
    void ed() {
        String data = "1877";
        String key = "salt0088";
        int expire = 2592000;

        String encrypted = EncryptUtil.encrypt(data, expire, key);
        System.out.println("Encrypted data: " + encrypted);
    }

    @Test
    void edWithDefault() {
        String data = "1877";

        String encrypted = EncryptUtil.encrypt(data);
        System.out.println("Encrypted data: " + encrypted);
    }

    @Test
    void encrypt() {
        String data = "1877";
        String encrypted = EncryptUtil.encrypt(data);
        System.out.println("Encrypted data: " + encrypted);
    }

}