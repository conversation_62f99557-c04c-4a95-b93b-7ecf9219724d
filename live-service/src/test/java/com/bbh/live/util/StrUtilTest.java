package com.bbh.live.util;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;

import java.util.List;

@Slf4j
public class StrUtilTest {

    @Test
    public void splitTest() {
        String str = "hello world";
        List<String> strings = StrUtil.split(str, " ");
        log.info("{}", strings);
        Assert.assertEquals(2, strings.size());
    }

}
