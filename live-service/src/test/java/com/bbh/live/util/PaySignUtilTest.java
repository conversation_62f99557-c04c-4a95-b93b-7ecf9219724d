package com.bbh.live.util;

import com.bbh.util.SignUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;

@Slf4j
class PaySignUtilTest {

    @Test
    void decodeSign() {
        String sign = "eyJub3RpZnlVcmwiOiJodHRwOi8vaHRiLWFwaS10ZXN0LmJhbmdiYW5naHUuY29tLmNuL2h0Yi1wdC1hcGkvb3JkZXIvcGF5L25vdGlmeSIsIm91dFRyYWRlTm8iOiJQVF9HX09fMjAyNDA5MTQwODMxMjYwNDE3MDUiLCJ0aW1lc3RhbXAiOiIxNzI2MjczODgzMDAwIiwic3ViamVjdCI6Iui0reS5sOaUvuW/g aLjeWVhuWTgSIsInRvdGFsQW1vdW50IjoxMDE2MDAsInBheVR5cGUiOiJKTiIsInBheU1vZGUiOiJaRkIiLCJhc3luY05vdGlmeSI6IiIsImFsaVBheVVzZXJJZCI6IjIwODg5MDI5NTY0NDc4MzUiLCJiaXpEYXRhIjpbeyJvcmRlclBhcmVudE5vIjoiUFRfR19PX1NVQl8yMDI0MDkxNDA4MzEyNjUwNzExMyIsInBsYXRNZXJjaGFudE5vIjoiMDAwMDAyOTcxMSIsIm9wVG90YWxBbW91bnQiOjEwMTYwMH1dLCJzaWduIjoiMzNCODFCMjg4NEVDMUZFQjkyQUVDNzJCNUE3QjlBMkIifQ==";
        sign = "eyJub3RpZnlVcmwiOiJodHRwOi8vaHRiLWFwaS10ZXN0LmJhbmdiYW5naHUuY29tLmNuL2h0Yi1wdC1hcGkvb3JkZXIvcGF5L25vdGlmeSIsIm91dFRyYWRlTm8iOiJQVF9HX09fMjAyNDA5MTQwODMxMjYwNDE3MDUiLCJ0aW1lc3RhbXAiOiIxNzI2MjczODgzMDAwIiwic3ViamVjdCI6Iui0reS5sOaUvuW/g aLjeWVhuWTgSIsInRvdGFsQW1vdW50IjoxMDE2MDAsInBheVR5cGUiOiJKTiIsInBheU1vZGUiOiJaRkIiLCJhc3luY05vdGlmeSI6IiIsImFsaVBheVVzZXJJZCI6IjIwODg5MDI5NTY0NDc4MzUiLCJiaXpEYXRhIjpbeyJvcmRlclBhcmVudE5vIjoiUFRfR19PX1NVQl8yMDI0MDkxNDA4MzEyNjUwNzExMyIsInBsYXRNZXJjaGFudE5vIjoiMDAwMDAyOTcxMSIsIm9wVG90YWxBbW91bnQiOjEwMTYwMH1dLCJzaWduIjoiMzNCODFCMjg4NEVDMUZFQjkyQUVDNzJCNUE3QjlBMkIifQ==";
        sign = "eyJub3RpZnlVcmwiOiJodHRwOi8vaHRiLWFwaS10ZXN0LmJhbmdiYW5naHUuY29tLmNuL2h0Yi1wdC1hcGkvb3JkZXIvcGF5L25vdGlmeSIsIm91dFRyYWRlTm8iOiJQVF9HX09fMjAyNDA5MTQwODMxMjYwNDE3MDUiLCJ0aW1lc3RhbXAiOiIxNzI2MjczODgzMDAwIiwic3ViamVjdCI6Iui0reS5sOaUvuW/g+aLjeWVhuWTgSIsInRvdGFsQW1vdW50IjoxMDE2MDAsInBheVR5cGUiOiJKTiIsInBheU1vZGUiOiJaRkIiLCJhc3luY05vdGlmeSI6IiIsImFsaVBheVVzZXJJZCI6IjIwODg5MDI5NTY0NDc4MzUiLCJiaXpEYXRhIjpbeyJvcmRlclBhcmVudE5vIjoiUFRfR19PX1NVQl8yMDI0MDkxNDA4MzEyNjUwNzExMyIsInBsYXRNZXJjaGFudE5vIjoiMDAwMDAyOTcxMSIsIm9wVG90YWxBbW91bnQiOjEwMTYwMH1dLCJzaWduIjoiMzNCODFCMjg4NEVDMUZFQjkyQUVDNzJCNUE3QjlBMkIifQ==";
        sign = "eyJub3RpZnlVcmwiOiJodHRwOi8vaHRiLWFwaS10ZXN0LmJhbmdiYW5naHUuY29tLmNuL2h0Yi1wdC1hcGkvb3JkZXIvcGF5L25vdGlmeSIsIm91dFRyYWRlTm8iOiJQVF9HX09fMjAyNDA5MTQwODMxMjYwNDE3MDUiLCJ0aW1lc3RhbXAiOiIxNzI2MjczODgzMDAwIiwic3ViamVjdCI6Iui0reS5sOaUvuW/g+aLjeWVhuWTgSIsInRvdGFsQW1vdW50IjoxMDE2MDAsInBheVR5cGUiOiJKTiIsInBheU1vZGUiOiJaRkIiLCJhc3luY05vdGlmeSI6IiIsImFsaVBheVVzZXJJZCI6IjIwODg5MDI5NTY0NDc4MzUiLCJiaXpEYXRhIjpbeyJvcmRlclBhcmVudE5vIjoiUFRfR19PX1NVQl8yMDI0MDkxNDA4MzEyNjUwNzExMyIsInBsYXRNZXJjaGFudE5vIjoiMDAwMDAyOTcxMSIsIm9wVG90YWxBbW91bnQiOjEwMTYwMH1dLCJzaWduIjoiMzNCODFCMjg4NEVDMUZFQjkyQUVDNzJCNUE3QjlBMkIifQ==";
        sign = "eyJub3RpZnlVcmwiOiJodHRwOi8vaHRiLWFwaS10ZXN0LmJhbmdiYW5naHUuY29tLmNuL2h0Yi1saXZlLWFwaS9vcmRlci9wYXkvbm90aWZ5Iiwib3V0VHJhZGVObyI6IkxJVkVfR19PXzIwMjQwOTE0MTQ0NDIwNzYzNzAyIiwidGltZXN0YW1wIjoiMTcyNjI5NjI1NjAwMCIsInN1YmplY3QiOiLotK3kubDnm7Tmkq3llYblk4EiLCJ0b3RhbEFtb3VudCI6NjAzNjAsInBheVR5cGUiOiJaRkIiLCJwYXlNb2RlIjoiWkZCIiwiYXN5bmNOb3RpZnkiOiIiLCJhbGlQYXlVc2VySWQiOiIyMDg4ODEyMDU5MzkyNjU0IiwiYml6RGF0YSI6W3sib3JkZXJQYXJlbnRObyI6IkxJVkVfR19PXzIwMjQwOTE0MTQ0NDIwNzYzNzAyIiwicGxhdE1lcmNoYW50Tm8iOiIwMDAwMDI5NzExIiwib3BUb3RhbEFtb3VudCI6NjAzNjB9XSwic2lnbiI6IjQ2MUYxNzg5RTJENkFFNTRBODcwQ0Y2RkJDNjU1OUEyIn0=";
        sign = "eyJub3RpZnlVcmwiOiJodHRwOi8vaHRiLWFwaS10ZXN0LmJhbmdiYW5naHUuY29tLmNuL2h0Yi1wdC1hcGkvb3JkZXIvcGF5L25vdGlmeSIsIm91dFRyYWRlTm8iOiJQVF9HX09fMjAyNDA5MjAwODQxMDM4NzIxMzgiLCJ0aW1lc3RhbXAiOiIxNzI2NzkyNzg2MDAwIiwic3ViamVjdCI6Iui0reS5sOaUvuW/g+aLjeWVhuWTgSIsInRvdGFsQW1vdW50IjoxLCJwYXlUeXBlIjoiSk4iLCJwYXlNb2RlIjoiWkZCIiwiYXN5bmNOb3RpZnkiOiIiLCJhbGlQYXlVc2VySWQiOiIyMDg4MDEyMjUxMDcyMzEwIiwiYml6RGF0YSI6W3sib3JkZXJQYXJlbnRObyI6IlBUX0dfT19TVUJfMjAyNDA5MjAwODQxMDMxMDk2OTIiLCJwbGF0TWVyY2hhbnRObyI6IjAwMDAwMjk3MTEiLCJvcFRvdGFsQW1vdW50IjoxfV0sInNpZ24iOiIwNzk2NEEzQjkyMUMxMTFDMUY4MDI5QzU1RUNCRTdBNyJ9";
        sign = "eyJub3RpZnlVcmwiOiJodHRwOi8vaHRiLWFwaS10ZXN0LmJhbmdiYW5naHUuY29tLmNuL2h0Yi1saXZlLWFwaS92aXJ0dWFsR29vZHMvcGF5Q2FsbGJhY2siLCJvdXRUcmFkZU5vIjoiTElWRV9WR19PXzIwMjQxMDA5MTQzOTAxMTE3Mzc3IiwidGltZXN0YW1wIjoiMTcyODQ1NTk0MjEzMCIsInN1YmplY3QiOiLluK7luK7omY5WSVDvvIgx5pyI77yJKjEiLCJ0b3RhbEFtb3VudCI6MSwicGF5VHlwZSI6IkpOIiwicGF5TW9kZSI6IlpGQiIsImFzeW5jTm90aWZ5IjoiIiwiYWxpUGF5VXNlcklkIjoiMjA4ODAyMjQxMDk3Mjc2MCIsImJpekRhdGEiOlt7Im9yZGVyUGFyZW50Tm8iOiJMSVZFX1ZHX09fU1VCXzIwMjQxMDA5MTQzOTAxMjQyNjgxIiwicGxhdE1lcmNoYW50Tm8iOiIwMDAwMDI5NzExIiwib3BUb3RhbEFtb3VudCI6MX1dLCJzaWduIjoiRjRBNzBDODdFQjcyMjk0NjA2RkNBRjlBMTY1NUE5MzgifQ==";
        sign = "eyJvdXRObyI6InRyYWRlX25vXzY1NjIzNDg3NzM1Mjg4NjI3MyIsInRyYW5zVHlwZSI6IjAwIiwic2lnbiI6IjkwNEVEMDUzM0NBMUQzQzUwMjg1MzNBQzIyNzNERTRFIn0=";
        sign = "eyJub3RpZnlVcmwiOiJodHRwOi8vaHRiLWFwaS10ZXN0LmJhbmdiYW5naHUuY29tLmNuL2h0Yi1saXZlLWFwaS9vcmRlci9wYXkvbm90aWZ5Iiwib3V0VHJhZGVObyI6IkxJVkVfR19PXzIwMjQxMjA0MTY1OTI1OTk5MDU5IiwidGltZXN0YW1wIjoiMTczMzMwMjc2NTkwOCIsInN1YmplY3QiOiLotK3kubDnm7Tmkq3llYblk4EiLCJ0b3RhbEFtb3VudCI6MTAyLCJwYXlUeXBlIjoiSk4iLCJwYXlNb2RlIjoiWkZCIiwiYXN5bmNOb3RpZnkiOiIiLCJhbGlQYXlVc2VySWQiOiIyMDg4MDIyNDEwOTcyNzYwIiwiYml6RGF0YSI6W3sib3JkZXJQYXJlbnRObyI6IkxJVkVfR19PX1NVQl8yMDI0MTIwNDE2NTkyNTkzODgxNCIsInBsYXRNZXJjaGFudE5vIjoiMjMyMTMxMjMxMiIsIm9wVG90YWxBbW91bnQiOjEwMn1dLCJzaWduIjoiNUVFNDM3Mzc5QzIwRUY0NjhGMkM2QUQyOTNEQzZBM0MifQ==";
        sign = "eyJub3RpZnlVcmwiOiJodHRwOi8vaHRiLWFwaS10ZXN0LmJhbmdiYW5naHUuY29tLmNuL2h0Yi1saXZlLWFwaS92aXJ0dWFsR29vZHMvcGF5Q2FsbGJhY2siLCJvdXRUcmFkZU5vIjoiR19WR19PXzIwMjQxMjA0MTcwNjMzMDQxMDc3IiwidGltZXN0YW1wIjoiMTczMzMwMzE5NDA4MSIsInN1YmplY3QiOiLluK7luK7omY5WSVDvvIgx5pyI77yJKjEiLCJ0b3RhbEFtb3VudCI6MSwicGF5VHlwZSI6IkpOIiwicGF5TW9kZSI6IlpGQiIsImFzeW5jTm90aWZ5IjoiIiwiYWxpUGF5VXNlcklkIjoiMjA4ODAyMjQxMDk3Mjc2MCIsImJpekRhdGEiOlt7Im9yZGVyUGFyZW50Tm8iOiJHX1ZHX09fU1VCXzIwMjQxMjA0MTcwNjMzMDEzNDk1IiwicGxhdE1lcmNoYW50Tm8iOiIwMDAwMDI5OTAyIiwib3BUb3RhbEFtb3VudCI6MX1dLCJzaWduIjoiODM0M0JGRDNFOTNDNDVDOUI0QjlBRkM1OEY0QTAwOTMifQ==";
        sign = "eyJub3RpZnlVcmwiOiJodHRwOi8vaHRiLWFwaS10ZXN0LmJhbmdiYW5naHUuY29tLmNuL2h0Yi1saXZlLWFwaS92aXJ0dWFsR29vZHMvcGF5Q2FsbGJhY2siLCJvdXRUcmFkZU5vIjoiR19WR19PXzIwMjQxMjA0MTcyNDEyNDk4Mzk1IiwidGltZXN0YW1wIjoiMTczMzMwNDI1Mjc4OCIsInN1YmplY3QiOiLluK7luK7omY5WSVDvvIgx5pyI77yJKjEiLCJ0b3RhbEFtb3VudCI6MSwicGF5VHlwZSI6IkpOIiwicGF5TW9kZSI6IlpGQiIsImFzeW5jTm90aWZ5IjoiIiwiYWxpUGF5VXNlcklkIjoiMjA4ODAyMjQxMDk3Mjc2MCIsImJpekRhdGEiOlt7Im9yZGVyUGFyZW50Tm8iOiJHX1ZHX09fU1VCXzIwMjQxMjA0MTcyNDEyNzg3NTkxIiwicGxhdE1lcmNoYW50Tm8iOiI4NjQzNTE4NzI2Iiwib3BUb3RhbEFtb3VudCI6MX1dLCJzaWduIjoiRTg3RjYxNjIyOUJGMzMyNTdGODY0ODk1NkRCQ0JBODkifQ==";
        sign = "eyJtZXJjaGFudFNlcU5vIjoiTElWRV9HX09fMjAyNDEyMDkwODI3MzMwODk1ODkiLCJvcmRlclRpbWUiOiIyMDI0MTIwOTA4MjczNCIsInRyYW5zQW10Ijo1MDUwMCwic3ViT3JkZXJJbmZvIjpbeyJzdWJNZXJjaGFudFNlcU5vIjoiTElWRV9HX09fU1VCXzIwMjQxMjA5MDgyNzM0MjYyOTAwIiwicHRNZXJjaGFudElkIjoiODY0NzY4NDQwNiIsInN1Yk9yZGVyQW10Ijo1MDUwMCwicGF5ZWVOYW1lIjoi5a2Z5pumIiwicGF5ZWVDYXJkIjoiNTUzNjU4NTc1NzUiLCJyZXZCYW5rSWQiOiIzMDMxMDAwMDAwMDYiLCJyZXZCYW5rTmFtZSI6IuS4reWbveWFieWkp+mTtuihjCJ9XSwiYmJoTWVyY2hhbnRObyI6Ijk4NzY1NDMyMTAiLCJub3RpZnlVcmwiOiJudWxsL29yZGVyL3BheS9ub3RpZnkiLCJzaWduIjoiMEQ5M0IzOUYzMzA4MEQxNkIzRkM1NDhGQTg5ODNDQUUifQ==";
        String key = "112233";
        Object o = SignUtil.decodeSign(sign, key);
        log.info("{}", o);
    }

    @Test
    void decodeProd() {
        String salt = "c2982cc05653aa7caf8bfda741ebe6e4";
        String sign = "eyJvdXRObyI6InRyYWRlX25vXzY1NjIzNDg3NzM1Mjg4NjI3MyIsInRyYW5zVHlwZSI6IjAwIiwic2lnbiI6IjkwNEVEMDUzM0NBMUQzQzUwMjg1MzNBQzIyNzNERTRFIn0=";
        sign = "eyJub3RpZnlVcmwiOiJodHRwczovL2h0Yi1hcGkuYmFuZ2JhbmdodS5jb20uY24vaHRiLWxpdmUtYXBpL29yZGVyL3BheS9ub3RpZnkiLCJvdXRUcmFkZU5vIjoiTElWRV9HX09fMjAyNDEyMDIwMDEyMTg5ODUzMjIiLCJ0aW1lc3RhbXAiOiIxNzMzMDY5NTM4MjM2Iiwic3ViamVjdCI6Iui0reS5sOebtOaSreWVhuWTgSIsInRvdGFsQW1vdW50Ijo0MDI0MCwicGF5VHlwZSI6IkpOIiwicGF5TW9kZSI6IlpGQiIsImFzeW5jTm90aWZ5IjoiIiwiYWxpUGF5VXNlcklkIjoiMjA4ODczMjg0NjkwMDk0MCIsImJpekRhdGEiOlt7Im9yZGVyUGFyZW50Tm8iOiJMSVZFX0dfT19TVUJfMjAyNDEyMDIwMDEyMTgxNDU3NjciLCJwbGF0TWVyY2hhbnRObyI6IjAwMDAwMTE2MDAiLCJvcFRvdGFsQW1vdW50Ijo0MDI0MH1dLCJzaWduIjoiMEI5RUQwMDlBOEIyMUM1ODU5MjRBNDIzREQzOUExMTEifQ==";
        sign = "eyJub3RpZnlVcmwiOiJodHRwczovL2h0Yi1hcGkuYmFuZ2JhbmdodS5jb20uY24vaHRiLWxpdmUtYXBpL29yZGVyL3BheS9ub3RpZnkiLCJvdXRUcmFkZU5vIjoiTElWRV9HX09fMjAyNDEyMDIwNzM2MTcyNzU0NzIiLCJ0aW1lc3RhbXAiOiIxNzMzMDk2MTc3NzQ0Iiwic3ViamVjdCI6Iui0reS5sOebtOaSreWVhuWTgSIsInRvdGFsQW1vdW50Ijo3MDQyMCwicGF5VHlwZSI6IkpOIiwicGF5TW9kZSI6IlpGQiIsImFzeW5jTm90aWZ5IjoiIiwiYWxpUGF5VXNlcklkIjoiMjA4ODY0MjEwMTU1NjAyNiIsImJpekRhdGEiOlt7Im9yZGVyUGFyZW50Tm8iOiJMSVZFX0dfT19TVUJfMjAyNDEyMDIwNzM2MTc0Njg3MjgiLCJwbGF0TWVyY2hhbnRObyI6IjAwMDAwMDU4MTgiLCJvcFRvdGFsQW1vdW50Ijo3MDQyMH1dLCJzaWduIjoiOEI4RDQ2MTg5NTI0NjI0MzI5MTRDNjE4QTVFQjk5QzMifQ==";
        sign = "eyJub3RpZnlVcmwiOiJodHRwczovL2h0Yi1hcGkuYmFuZ2JhbmdodS5jb20uY24vaHRiLWxpdmUtYXBpL29yZGVyL3BheS9ub3RpZnkiLCJvdXRUcmFkZU5vIjoiTElWRV9HX09fMjAyNDEyMDIwMTM5NDExNjYzMTciLCJ0aW1lc3RhbXAiOiIxNzMzMDc0NzgxNzA5Iiwic3ViamVjdCI6Iui0reS5sOebtOaSreWVhuWTgSIsInRvdGFsQW1vdW50IjoyNTE1MCwicGF5VHlwZSI6IkpOIiwicGF5TW9kZSI6IlpGQiIsImFzeW5jTm90aWZ5IjoiIiwiYWxpUGF5VXNlcklkIjoiMjA4ODEwMjM5ODgxODEzMCIsImJpekRhdGEiOlt7Im9yZGVyUGFyZW50Tm8iOiJMSVZFX0dfT19TVUJfMjAyNDEyMDIwMTM5NDEzOTA5NTciLCJwbGF0TWVyY2hhbnRObyI6IjAwMDAwMTE2MDAiLCJvcFRvdGFsQW1vdW50IjoyNTE1MH1dLCJzaWduIjoiOTJBNDU5OERERjI2QjJFMzkyMDFFM0ZEMDAxMDg4ODAifQ==";
        sign = "eyJub3RpZnlVcmwiOiJodHRwczovL2h0Yi1hcGkuYmFuZ2JhbmdodS5jb20uY24vaHRiLWxpdmUtYXBpL29yZGVyL3BheS9ub3RpZnkiLCJvdXRUcmFkZU5vIjoiTElWRV9HX09fMjAyNDEyMDIwNzQ1NDg5Njc5NTUiLCJ0aW1lc3RhbXAiOiIxNzMzMDk2NzQ4NTE3Iiwic3ViamVjdCI6Iui0reS5sOebtOaSreWVhuWTgSIsInRvdGFsQW1vdW50IjoyMzEzODAsInBheVR5cGUiOiJKTiIsInBheU1vZGUiOiJaRkIiLCJhc3luY05vdGlmeSI6IiIsImFsaVBheVVzZXJJZCI6IjIwODg3MDIwMTc0NjQxNTMiLCJiaXpEYXRhIjpbeyJvcmRlclBhcmVudE5vIjoiTElWRV9HX09fU1VCXzIwMjQxMjAyMDc0NTQ4NDkwMjI4IiwicGxhdE1lcmNoYW50Tm8iOiIwMDAwMDEwMDI5Iiwib3BUb3RhbEFtb3VudCI6MjMxMzgwfV0sInNpZ24iOiIxQzFBNkZBMDEwRkM2NTQ0NTJFNkNBMDBDMEUzM0VFNyJ9";
        sign = "eyJvdXRObyI6IkxJVkVfR19PXzIwMjQxMjA1MDk1NjMyMTA0NDQwIiwic2lnbiI6IkYxRjY1QjgzQ0M2RTREMjJCOUUwOTJFNTU1OUFFMzJEIn0=";
        Object o = SignUtil.decodeSign(sign, salt);
        log.info("{}", o);
    }

}
